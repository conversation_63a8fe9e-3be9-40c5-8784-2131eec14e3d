<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
</head>
<body>
    <h1>API测试页面</h1>
    <button onclick="testAPIs()">测试所有API</button>
    <div id="results"></div>

    <script>
        async function testAPIs() {
            const results = document.getElementById('results');
            results.innerHTML = '<h2>测试结果:</h2>';
            
            // 测试部门API
            try {
                const deptResponse = await fetch('/api/system/dept/list');
                const deptData = await deptResponse.json();
                results.innerHTML += `<h3>部门API:</h3><pre>${JSON.stringify(deptData, null, 2)}</pre>`;
            } catch (error) {
                results.innerHTML += `<h3>部门API错误:</h3><pre>${error.message}</pre>`;
            }
            
            // 测试桥梁API
            try {
                const bridgeResponse = await fetch('/api/system/bridgeArchive/list?pageNum=1&pageSize=10');
                const bridgeData = await bridgeResponse.json();
                results.innerHTML += `<h3>桥梁API:</h3><pre>${JSON.stringify(bridgeData, null, 2)}</pre>`;
            } catch (error) {
                results.innerHTML += `<h3>桥梁API错误:</h3><pre>${error.message}</pre>`;
            }
            
            // 测试车辆API
            try {
                const vehicleResponse = await fetch('/api/system/hazardousVehicle/list?pageNum=1&pageSize=10');
                const vehicleData = await vehicleResponse.json();
                results.innerHTML += `<h3>车辆API:</h3><pre>${JSON.stringify(vehicleData, null, 2)}</pre>`;
            } catch (error) {
                results.innerHTML += `<h3>车辆API错误:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
