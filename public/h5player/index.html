<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title></title>
  <style>
    /* * {
      padding: 0;
      margin: 0;
      font-size: 12px;
    }

    #playWind {
      display: inline-block;
    }

    body {
      margin: 10px;
    }

    input,
    select {
      padding: 3px 6px;
      font-size: 16px;
    }

    input:focus,
    select:focus {
      outline: 1px solid rgba(0, 153, 255, 1);
      border-color: rgba(0, 153, 255, 0);
    }

    button {
      margin: 10px 10px;
      padding: 2px 5px;
    }

    .button_box {
      width: 800px;
      float: left;
    }

    .button_box div {
      margin: 10px 10px;
    }

    #volume {
      position: relative;
      top: 5px;
    }

    .items {
      border: 1px solid #444444;
    } */
  </style>
</head>

<body style="margin: 0;">
  <!-- <div id="playWind" onclick="FullScreen()" style="width: 300px; height: 160px;"></div>-->
  <div>
    <div id="playWind" style="width: 100vw;height: 100vh;display: none;"></div>
    <!-- <div id="playWind" onclick="FullScreen()" style="width: 300px; height: 160px;"></div> -->
    <div id="loading" style="font-size: 12px;">加载中...</div>
  </div>

  <script>

    

    function getScript(url, fn)
    {
      if ("string" === typeof (url)) {
        url = [url]; //如果不是数组带个套
      };
      var ok = 0;            //加载成功几个js
      var len = url.length;  //一共几个js
      var head = document.getElementsByTagName("head").item(0);
      var js = null;
      var _url;
      create = function (url)
      {//创建js
        var oScript = null;
        oScript = document.createElement("script");
        oScript.type = "text/javascript";
        oScript.src = url;
        head.appendChild(oScript);
        return oScript;
      };
      for (var i = 0; i < len; i++) {
        _url = url[i];
        js = create(_url);//创建js
        fn && (js.onload = function ()
        {
          if (++ok >= len) {//如果加载完所有的js则执行回调
            fn();
          }
        });
      };
    }
    var oPlugin = null;
    //var szBrowserVersion = "";
    //var iBrowserVersion = -1;
    var aScript = [];
    var szUserAgent = navigator.userAgent.toLowerCase();
    // if (szUserAgent.match(/chrome\/([\d.]+)/) || szUserAgent.match(/Firefox\/([\d.]+)/)) {
    //szBrowserVersion = szUserAgent.match(/chrome\/([\d.]+)/)[1];
    //iBrowserVersion = parseInt(szBrowserVersion.split(".")[0], 10);
    if (szUserAgent.indexOf('win64') > -1 || szUserAgent.indexOf('x64') > -1) {
      aScript = ["./h5player.min.js"];
    } else {
      aScript = ["./h5player.min.js"];
    }
    // }

    getScript(aScript, function ()
    {
      //初始化插件
      oPlugin = new JSPlugin({
        szId: "playWind",
        iWidth: 320,
        iHeight: 180,
        iMaxSplit: 1,
        iCurrentSplit: 2,
        szBasePath: "./",
        oStyle: {
          border: "#343434",
          // borderSelect: "#FFCC00",
          background: "#000"
        },
        openDebug: true
      });
      initPlugin();
      if(window.location.search){
        var searchURL = window.location.search;
        var str=searchURL.substring(searchURL.indexOf("=")+1)
        // console.log("searchURL========",str)
        var url = str;
        // console.log(url);
        realplay(url);
      }else{
        document.getElementById("loading").innerHTML="加载中..."
      }
     
    });

    
    var szWebsocketSessionID = "ws://**************:559/openUrl/IPqUgjC";  //设备直连取流uuid， 流媒体取流不需要该参数
    var szToken = "";
    var iWind = 0;  //窗口索引
    function initPlugin()
    {
      oPlugin.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex)
        {  //插件选中窗口回调
          iWind = iWndIndex;
          // console.log(iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError)
        {  //插件错误回调
          // console.error(`window-${iWndIndex}, errorCode: ${iErrorCode}`, oError);
        },
        windowEventOver: function (iWndIndex)
        {  //鼠标移过回调
          //console.log(iWndIndex);
        },
        windowEventOut: function (iWndIndex)
        {  //鼠标移出回调
          //console.log(iWndIndex);
        },
        windowEventUp: function (iWndIndex)
        {  //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull)
        {  //全屏切换回调
          // console.log(bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight)
        {  //首帧显示回调
          // console.log(iWndIndex, iWidth, iHeight);
        },
        performanceLack: function ()
        {  //性能不足回调

        }
      });
      oPlugin.JS_SetOptions({
        // bSupportSound: false  //是否支持音频，默认支持
        bSupporDoubleClickFull: false  //是否双击窗口全屏，默认支持
        // bOnlySupportMSE: true  //只支持MSE
        // bOnlySupportJSDecoder: true  //只支持JSDecoder
      }).then(function ()
      {
        // console.log("JS_SetOptions");
      });
    }

    function getVersion()
    {
      oPlugin.JS_GetPluginVersion().then(function (szVersion)
      {
        // console.log(szVersion);
      });
    }
    function Destroy()
    {
      oPlugin.JS_DestroyWorker().then(function ()
      {
        // console.log("destroyWorker success");
      });
    }
    function SetSecretKey()
    {
      var secretKey = document.getElementById("secretKey").value;
      oPlugin.JS_SetSecretKey(iWind, secretKey).then(function ()
      {
        // console.log("JS_SetSecretKey success");
      }, function ()
      {
        // console.log("JS_SetSecretKey failed");
      });
    }

    function realplay(url)
    {
      let streamType = getRadioValue('streamType');
      oPlugin.JS_Play(url, {
        playURL: url,
        mode: 1,
        //session: szWebsocketSessionID,  //定制设备
        //token: szToken,
      }, iWind).then(function ()
      {
        document.getElementById("loading").style.display="none"
        document.getElementById("playWind").style.display="block"
        // console.log("realplay success");
      }, function ()
      {
        document.getElementById("loading").innerHTML="加载失败"
        // console.log("realplay failed");
      });
    }
    function getRadioValue(radioName)
    {
      let value = '';
      document.getElementsByName(radioName).forEach(el =>
      {
        if (el.checked) {
          value = el.value
        }
      })
      return value
    }

    function playback()
    {
      var url = document.getElementById("urlPlayback").value;  //"ws://10.19.141.64:7314/EUrl/ybcwxHO";
      var szStartDate = document.getElementById("sDate").value;
      var szEndDate = document.getElementById("eDate").value;
      let streamType = getRadioValue('streamType');
      if (document.getElementById("urlPlayback").value && szStartDate && szEndDate) {
        oPlugin.JS_Play(url, {
          // token: szToken,
          //流媒体
          playURL: url,  //联网共享的取流url
          mode: parseInt(streamType),
          //proxy: "10.5.6.8",  //proxy后面的属性为代理的目标地址，根据实际情况配置，联网共享https下需要用到该参数
          //mode: "media",  //建立连接的url新增一个media节点, 联网共享https下需要用到该参数
        }, iWind, szStartDate, szEndDate).then(function ()
        {
          // console.log("playback success");
        }, function ()
        {
          // console.log("playback failed");
        });
      } else {
        return;
      }
    }
    function playbackLocation()
    {
      var szStartDate = document.getElementById("sDate1").value;
      var szEndDate = document.getElementById("eDate1").value;
      oPlugin.JS_Seek(iWind, szStartDate, szEndDate).then(function ()
      {
        // console.log("playbackLocation success");
      }, function ()
      {
        // console.log("playbackLocation failed");
      });
    }
    function stop()
    {
      oPlugin.JS_Stop(iWind).then(function ()
      {
        // console.log("stop success");
      }, function (e)
      {
        // console.error("stop failed", e);
      });
    }
    function arrangeWindow(i)
    {
      oPlugin.JS_ArrangeWindow(i).then(function ()
      {
        // console.log("JS_ArrangeWindow success");
      });
    }

    function Pause()
    {
      oPlugin.JS_Pause(iWind).then(function ()
      {
        // console.log("Pause success");
      }, function (e)
      {
        // console.error("Pause failed", e);
      });
    }
    function Resume()
    {
      oPlugin.JS_Resume(iWind).then(function ()
      {
        // console.log("Resume success");
      }, function (e)
      {
        // console.error("Resume failed", e);
      });
    }
    function Slow()
    {
      oPlugin.JS_Slow(iWind).then(function (iRate)
      {
        // console.log("Slow success, current rate", iRate);
      }, function (e)
      {
        // console.error("Slow failed", e);
      });
    }
    function Fast()
    {
      oPlugin.JS_Fast(iWind).then(function (iRate)
      {
        // console.log("Fast success, current rate", iRate);
      }, function (e)
      {
        // console.error("Fast failed", e);
      });
    }
    function FrameForward()
    {
      oPlugin.JS_FrameForward(iWind).then(function ()
      {
        // console.log("FrameForward success");
      }, function ()
      {
        // console.log("FrameForward failed");
      });
    }
    function GetOSDTime()
    {
      oPlugin.JS_GetOSDTime(iWind).then(function (time)
      {
        // console.log(new Date(time));
      });
    }

    function CloseSound()
    {
      oPlugin.JS_CloseSound(iWind).then(function ()
      {
        // console.log("JS_CloseSound success");
      }, function ()
      {
        // console.log("JS_CloseSound failed");
      });
    }
    function EnableZoom()
    {
      oPlugin.JS_EnableZoom(iWind).then(function ()
      {
        // console.log("EnableZoom success");
      }).catch(err =>
      {
        // console.warn(`EnableZoom failed: `, err)
      });
    }
    function DisableZoom()
    {
      oPlugin.JS_DisableZoom(iWind).then(function ()
      {
        // console.log("DisableZoom success");
      }).catch(err =>
      {
        // console.error("DisableZoom failed: ", err)
      });
    }
    function Enable3DZoom()
    {
      oPlugin.JS_Enable3DZoom(iWind, function (oRect)
      {
        // console.log(oRect);
      })
    }
    function Disable3DZoom()
    {
      oPlugin.JS_Disable3DZoom(iWind).then(function ()
      {
        // console.log("JS_Disable3DZoom success");
      }, function ()
      {
        // console.log("JS_Disable3DZoom failed");
      });
    }
    function OpenSound()
    {
      var iRet = oPlugin.JS_OpenSound(iWind).then(function ()
      {
        // console.log("JS_OpenSound success");
      }, function ()
      {
        // console.log("JS_OpenSound failed");
      });
    }
    function SetVolume()
    {
      // oPlugin.JS_SetVolume (iWind, 32500).then(function () {
      //     console.log("JS_SetVolume success");
      // }, function () {
      //     console.log("JS_SetVolume failed");
      // });
      let volume = document.getElementById('volume').value;
      oPlugin.JS_SetVolume(iWind, parseFloat(volume)).then(
        () =>
        {
          // console.log("JS_SetVolume success", volume);
        },
        (err) =>
        {
          // console.error("JS_SetVolume failed", err);
        }
      );
    }
    function selectWnd()
    {
      oPlugin.JS_SelectWnd(3).then(function ()
      {
        // console.log("JS_SelectWnd success");
      }, function ()
      {
        // console.log("JS_SelectWnd failed");
      });
    }
    function GetVolume()
    {
      oPlugin.JS_GetVolume(iWind).then(function (i)
      {
        // console.log(i);
      });
    }
    function CapturePicture(szType)
    {
      oPlugin.JS_CapturePicture(iWind, "img", szType).then(function ()
      {
        // console.log("CapturePicture success");
      }, function ()
      {
        // console.log("CapturePicture failed");
      });
    }
    function StopRealPlayAll()
    {
      oPlugin.JS_StopRealPlayAll().then(function ()
      {
        // console.log("JS_StopRealPlayAll success");
      }, function ()
      {
        // console.log("JS_StopRealPlayAll failed");
      });
    }
    function dateFormat(oDate, fmt)
    {
      var o = {
        "M+": oDate.getMonth() + 1, //月份
        "d+": oDate.getDate(), //日
        "h+": oDate.getHours(), //小时
        "m+": oDate.getMinutes(), //分
        "s+": oDate.getSeconds(), //秒
        "q+": Math.floor((oDate.getMonth() + 3) / 3), //季度
        "S": oDate.getMilliseconds()//毫秒
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (oDate.getFullYear() + "").substr(4 - RegExp.$1.length));
      }
      for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
      }
      return fmt;
    }
    function record()
    {
      var szTime = dateFormat(new Date(), "yyyyMMddhhmmssS");
      oPlugin.JS_StartSave(iWind, "10.17.137.229_01_" + szTime + ".mp4").then(function ()
      {
        // console.log("record success");
      }, function ()
      {
        // console.log("record failed");
      });;
    }
    function stopRecord()
    {
      oPlugin.JS_StopSave(iWind).then(function ()
      {
        // console.log("stopRecord success");
      }, function ()
      {
        // console.log("stopRecord failed");
      });;
    }
    function startTalk()
    {
      var talkurl = document.getElementById("talkurl").value
      oPlugin.JS_StartTalk(talkurl).then(function ()
      {
        // console.log("startTalk success");
      }, function ()
      {
        // console.log("startTalk failed");
      });
    }

    function stopTalk()
    {
      oPlugin.JS_StopTalk().then(function ()
      {
        // console.log("stopTalk success");
      }, function ()
      {
        // console.log("stopTalk failed");
      });
    }

    function TalkSetVolume()
    {
      let volume = document.getElementById('talkvolume').value;
      oPlugin.JS_TalkSetVolume(parseFloat(volume)).then(
        () =>
        {
          // console.log("JS_TalkSetVolume success", volume);
        },
        (err) =>
        {
          // console.error("JS_TalkSetVolume failed", err);
        }
      );
    }

    function TalkGetVolume()
    {
      oPlugin.JS_TalkGetVolume().then(function (i)
      {
        // console.log(i);
      });
    }
    function fullSreen()
    {
      oPlugin.JS_FullScreenDisplay(true).then(function ()
      {
        // console.log("JS_FullScreenDisplay success");
      }, function ()
      {
        // console.log("JS_FullScreenDisplay failed");
      });
    }
    function fullScreenSingle()
    {
      oPlugin.JS_FullScreenSingle(iWind).then(function ()
      {
        // console.log("JS_FullScreenSingle success");
      }, function ()
      {
        // console.log("JS_FullScreenSingle failed");
      });
    }

    function triggerPrivateData(bTrue)
    {
      let nIntelType = parseInt(document.getElementById('nIntelType').value);
      let result = oPlugin.JS_RenderPrivateData(nIntelType, bTrue);
      // console.info('triggerPrivateData', result)
    }

    function nIntelTypeChange()
    {
      let nIntelType = document.getElementById('nIntelType').value;
      subSelectElment = document.getElementById('nSubType')
      subSelectElment.innerHTML = '<option value="0" disabled>全部</option>'
      let subMap = {
        '0x00000010': [
          { value: 0x00000001, label: '火点框显示' },
          { value: 0x00000002, label: '火点最高温显示' },
          { value: 0x00000004, label: '最高温位置显示' },
          { value: 0x00000008, label: '最高温度距离' },
        ],
        '0x00000020': [
          { value: 0x00000001, label: '框测温' },
          { value: 0x00000002, label: '线测温' },
          { value: 0x00000004, label: '点测温' },
        ],
        '0x00000040': [
          { value: 0x00000001, label: '人的轨迹信息' },
          { value: 0x00000002, label: '车的轨迹信息' },
        ],
        '0x00000080': [
          { value: 0x00000001, label: '烟火屏蔽' },
          { value: 0x00000002, label: '规则废气检测' },
          { value: 0x00000004, label: '目标废气检测' },
        ]
      }
      subMap[nIntelType].forEach(item =>
      {
        let optionElement = document.createElement('option')
        optionElement.value = item.value
        optionElement.innerText = item.label
        subSelectElment.appendChild(optionElement)
      });
    }

    function triggerPrivateDataSub(bTrue)
    {
      let nIntelType = parseInt(document.getElementById('nIntelType').value);
      let nSubType = parseInt(document.getElementById('nSubType').value);
      let result = oPlugin.JS_RenderPrivateDataEx(nIntelType, nSubType, bTrue);
      // console.info('triggerPrivateDataSub', result)
    }

    function triggerJSDecoderLog(bFlag)
    {
      oPlugin.JS_SetLogFlag(bFlag);
    }
    //进入全屏
    function FullScreen()
    {
      var ele = document.getElementById('playWindow0');
      if (ele.requestFullscreen) {
        ele.requestFullscreen();
      } else if (ele.mozRequestFullScreen) {
        ele.mozRequestFullScreen();
      } else if (ele.webkitRequestFullScreen) {
        ele.webkitRequestFullScreen();
      }
    }
    //退出全屏
    function exitFullscreen()
    {
      var de = document;
      if (de.exitFullscreen) {
        de.exitFullscreen();
      } else if (de.mozCancelFullScreen) {
        de.mozCancelFullScreen();
      } else if (de.webkitCancelFullScreen) {
        de.webkitCancelFullScreen();
      }
    }
  </script>
</body>

</html>
