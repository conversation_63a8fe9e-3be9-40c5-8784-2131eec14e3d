<template>
    <div class="resource-file">
        <el-button type="primary" @click="openDialog">上传文件</el-button>
        <el-dialog v-model="dialogVisible" title="上传文件">
            <el-form :model="form">
                <el-form-item label="文件名">
                    <el-upload action="#" :auto-upload="false" :show-file-list="false" accept="*"
                        @change="handleFileChange">
                        <el-input v-model="form.fileName" readonly></el-input>
                    </el-upload>
                </el-form-item>
                <el-form-item label="文件类型">
                    <el-select multiple filterable allow-create v-model="form.fileType" placeholder="请选择文件类型">
                        <el-option label="Java类" value="Java类"></el-option>
                        <el-option label="Python类" value="Python类"></el-option>
                        <el-option label="vue类" value="vue类"></el-option>
                        <el-option label="TS类" value="TS类"></el-option>
                        <el-option label="其他知识类" value="其他知识类"></el-option>
                        <el-option label="Bug解决类" value="Bug解决类"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="form.remark"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </el-dialog>
        <el-table max-height="650px" :data="fileList" style="width: 100%">
            <el-table-column type="index" label="序号" align="center" width="80">
                <template #default="{ $index }">
                    {{ $index + 1 }}
                </template>
            </el-table-column>
            <el-table-column label="文件名" align="center" width="280">
                <template #default="{ row }">
                    <component :is="getFileIcon(row.fileName)" class="icon" />
                    {{ row.fileName }}
                </template>
            </el-table-column>
            <el-table-column prop="fileType" label="文件类型" align="center" width="180" />
            <el-table-column prop="fileSize" label="文件大小" align="center" width="120" />
            <el-table-column prop="createUser" label="上传人" align="center" width="180" />
            <el-table-column prop="createTime" label="上传时间" align="center" width="200" />
            <el-table-column prop="remark" label="备注" align="center" />
            <el-table-column label="操作 " align="center" width="180">
                <template #default="{ row }">
                    <div style="display: flex;justify-content: center;">
                        <el-button link type="primary" @click="downloadFile(row)">下载</el-button>
                        <el-button link type="danger" @click="deleteFile(row)">删除</el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <div class="demo-pagination-block">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 15, 20]"
                :size="size" :background="true" layout="total, sizes, prev, pager, next, jumper" :total="total"
                @size-change="onPageSize" @current-change="onPageChange" />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Download, Document, Picture, VideoCamera } from '@element-plus/icons-vue';
import {
    deleteResourceFile,
    uploadResourceFile,
    downloadResourceFile,
    getResourceFile
} from '@/api/resourceSharing';
import useUserInfoStore from '@/stores/userInfo';
const userInfoStore = useUserInfoStore();
const tableData = ref([]);
const currentPage = ref(1);
const total = ref(0);
const pageSize = ref(10);
const size = ref('default');
const pageVo = ref({
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    id: userInfoStore.info.id,
});


const onPageChange = (num) => {
    currentPage.value = num;
    pageVo.value.pageNum = num;
    fetchFiles();
};

const onPageSize = (num) => {
    pageSize.value = num;
    pageVo.value.pageSize = num;
    fetchFiles();
};



const fileList = ref([]);
const dialogVisible = ref(false);
const form = ref({
    fileName: '',
    fileType: '其他知识类',
    remark: '',
    file: null
});

function handleFileChange(event) {
    const file = event;
    form.value.file = file.raw;
    form.value.fileName = file.name;
}

const fetchFiles = async () => {
    try {
        const response = await getResourceFile(pageVo.value);
        fileList.value = response.data.rows;
    } catch (error) {
        console.error('Fetch files error:', error);
        ElMessage.error('获取文件列表失败');
    }
};

const handleUploadSuccess = (response, file, fileList) => {
    ElMessage.success('文件上传成功');
    fetchFiles();
};

const downloadFile = async (file) => {
    console.log('file:', file);
    const file1 = {
        id: file.id,
        fileName: file.fileName,
    }
    try {
        const response = await downloadResourceFile(file1);
        console.log('response:', response);
        const url = window.URL.createObjectURL(new Blob([response.data], { type: 'application/octet-stream' }));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', file.fileName);
        // link.download = file.fileName;
        document.body.appendChild(link);
        link.click();
        // 释放临时 URL 对象
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
    } catch (error) {
        console.error('文件下载失败:', error);
    }
};
const deleteFile = async (file) => {
    try {
        await deleteResourceFile(file.id);
        ElMessage.success('文件删除成功');
        await fetchFiles();
    } catch (error) {
        console.error('文件删除失败:', error);
    }
};
const getFileIcon = (fileName) => {
    const ext = fileName.split('.').pop().toLowerCase();
    switch (ext) {
        case 'jpg':
        case 'png':
        case 'gif':
            return Picture;
        case 'mp4':
        case 'avi':
            return VideoCamera;
        case 'pdf':
        case 'doc':
        case 'docx':
        case 'xlsx':
        case 'txt':
            return Document;
        default:
            return Download;
    }
};

const openDialog = () => {
    dialogVisible.value = true;
};

const submitForm = async () => {
    const formData = new FormData();
    formData.append('createUser', userInfoStore.info.id);
    formData.append('file', form.value.file);
    formData.append('fileName', form.value.fileName);
    formData.append('fileType', form.value.fileType);
    formData.append('remark', form.value.remark);

    try {
        await uploadResourceFile(formData);
        ElMessage.success('文件上传成功');
        await fetchFiles();
        dialogVisible.value = false;
    } catch (error) {
        console.error('Upload error:', error);
        ElMessage.error('文件上传失败');
    }
};

onMounted(() => {
    fetchFiles();
});
</script>

<style>
/* 此处不做scope */
.demo-pagination-block {
    margin-top: 20px;
    text-align: left;
    display: block;
}

.resource-file {
    padding: 20px;
}

.upload-demo {
    margin-bottom: 20px;
}

.el-icon {
    margin-right: 10px;
}

.icon {
    width: 20px;
    margin: 5px 0 -5px 0;
}
</style>