# 页面标题
VITE_APP_TITLE = 舟山大桥综管指挥平台

# 开发环境配置
VITE_APP_ENV = 'development'

# 开发环境
VITE_APP_BASE_API = '/dev-api'

# 应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH = '/'

# 跳转地址
VITE_APP_REDIRECT_URL = 'http://192.168.31.167:3000'
VITE_APP_PYTHON_API = 'http://20.23.100.67:9001'


# 钉钉登录地址
# VITE_APP_DINGDING_API = 'https://login.on-premises.dingtalk.com/oauth2/auth.htm?response_type=code&client_id=bridge_test_dingoa&redirect_uri=http://**************:8083&scope=get_user_info&authType=QRCODE&embedMode=true'
VITE_APP_DINGDING_API = 'https://login-pro.ding.zj.gov.cn/oauth2/auth.htm?response_type=code&client_id=zsBridge_dingoa&redirect_uri=http://***********:8080/system/ding/callback/scanCode&scope=get_user_info&authType=QRCODE&embedMode=true'

# 监控地址
VITE_APP_MONITOR_ADMIN = 'http://localhost:9090/admin/applications'

# SnailJob 控制台地址
VITE_APP_SNAILJOB_ADMIN = 'http://localhost:8800/snail-job'

VITE_APP_PORT = 8081

# 接口加密功能开关(如需关闭 后端也必须对应关闭)
VITE_APP_ENCRYPT = true
# 接口加密传输 RSA 公钥与后端解密私钥对应 如更换需前后端一同更换
VITE_APP_RSA_PUBLIC_KEY = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='

# 客户端id
VITE_APP_CLIENT_ID = 'e5cd7e4891bf95d1d19206ce24a7b32e'

# websocket 开关 默认使用sse推送
VITE_APP_WEBSOCKET = false

# sse 开关
VITE_APP_SSE = true
