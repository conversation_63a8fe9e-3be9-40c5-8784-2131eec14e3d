import { createApp } from 'vue';
// global css
import 'virtual:uno.css';
import '@/assets/styles/index.scss';
import 'element-plus/theme-chalk/dark/css-vars.css';
import '@/styles/dark.scss'

// 导入Element Plus的InfiniteScroll
import { ElInfiniteScroll } from 'element-plus';

// import Antd from 'ant-design-vue';
// import * as Icons from '@ant-design/icons-vue';
// import 'ant-design-vue/dist/reset.css';

// App、router、store
import App from './App.vue';
import store from './store';
import router from './router';

// 自定义指令
import directive from './directive';

// 注册插件
import plugins from './plugins/index'; // plugins

// 高亮组件
// import 'highlight.js/styles/a11y-light.css';
import 'highlight.js/styles/atom-one-dark.css';
import 'highlight.js/lib/common';
import HighLight from '@highlightjs/vue-plugin';

// svg图标
import 'virtual:svg-icons-register';
import ElementIcons from '@/plugins/svgicon';

// permission control
import './permission';

// 国际化
import i18n from '@/lang/index';

// vxeTable
import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
VXETable.config({
  zIndex: 999999
});

// 全局信息窗口组件
import InfoWindow from '@/views/WindPower/components/InfoWindow.vue';

// 修改 el-dialog 默认点击遮照为不关闭
import { ElDialog } from 'element-plus';
ElDialog.props.closeOnClickModal.default = false;

const app = createApp(App);

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue Error:', err);
  console.log('Vue Error Info:', info);
};

// 捕获未处理的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason);
  // 阻止默认处理
  event.preventDefault();
});

// app.use(Antd);
app.use(HighLight);
app.use(ElementIcons);
app.use(router);
app.use(store);
app.use(i18n);
app.use(VXETable);
app.use(plugins);
// 自定义指令
directive(app);

// 手动注册InfiniteScroll指令
app.directive('infinite-scroll', ElInfiniteScroll);

// 注册全局组件
app.component('InfoWindow', InfoWindow);

// Object.keys(Icons).forEach(key => {
//   app.component(key, Icons[key]);
// });

app.mount('#app');
