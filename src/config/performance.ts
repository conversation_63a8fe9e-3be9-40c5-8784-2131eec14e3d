/**
 * 性能配置管理
 * 提供可配置的性能参数和模式切换
 */

// 性能模式枚举
export enum PerformanceMode {
  HIGH_PERFORMANCE = 'high_performance',  // 高性能模式
  BALANCED = 'balanced',                  // 平衡模式
  POWER_SAVE = 'power_save'              // 省电模式
}

// 性能监控配置
export interface PerformanceMonitorConfig {
  enabled: boolean;                       // 是否启用监控
  sampleRate: number;                    // 采样率 (0-1)
  memoryCheckInterval: number;           // 内存检查间隔(ms)
  apiMonitorEnabled: boolean;            // API监控开关
  canvasMonitorEnabled: boolean;         // Canvas监控开关
  interactionMonitorEnabled: boolean;    // 交互监控开关
  maxLogEntries: number;                 // 最大日志条数
  alertThresholds: {
    memoryUsage: number;                 // 内存使用阈值(MB)
    apiResponseTime: number;             // API响应时间阈值(ms)
    renderTime: number;                  // 渲染时间阈值(ms)
    interactionDelay: number;            // 交互延迟阈值(ms)
  };
}

// 性能优化配置
export interface PerformanceOptimizationConfig {
  aisDataRefreshInterval: number;        // AIS数据刷新间隔(ms)
  toolbarExpandedInterval: number;       // 工具栏展开时的刷新间隔(ms)
  maxShipCount: number;                  // 最大船舶数量
  blobCleanupInterval: number;           // Blob清理间隔(ms)
  blobMaxAge: number;                    // Blob最大存活时间(ms)
  // Canvas优化间隔已移除
  featurePoolMaxSize: number;            // 要素池最大大小
  debounceDelays: {
    search: number;                      // 搜索防抖延迟(ms)
    toolbarToggle: number;               // 工具栏切换防抖延迟(ms)
    mapInteraction: number;              // 地图交互防抖延迟(ms)
    shipHover: number;                   // 船舶悬浮防抖延迟(ms)
    resize: number;                      // 窗口调整防抖延迟(ms)
    apiCall: number;                     // API调用防抖延迟(ms)
  };
}

// 默认性能配置
const DEFAULT_CONFIGS: Record<PerformanceMode, {
  monitor: PerformanceMonitorConfig;
  optimization: PerformanceOptimizationConfig;
}> = {
  [PerformanceMode.HIGH_PERFORMANCE]: {
    monitor: {
      enabled: true,
      sampleRate: 1.0,
      memoryCheckInterval: 10000,
      apiMonitorEnabled: true,
      canvasMonitorEnabled: false, // Canvas监控已禁用
      interactionMonitorEnabled: true,
      maxLogEntries: 1000,
      alertThresholds: {
        memoryUsage: 512,
        apiResponseTime: 3000,
        renderTime: 16,
        interactionDelay: 100
      }
    },
    optimization: {
      aisDataRefreshInterval: 5000,
      toolbarExpandedInterval: 10000,
      maxShipCount: 1500,
      blobCleanupInterval: 20000,
      blobMaxAge: 180000,

      featurePoolMaxSize: 300,
      debounceDelays: {
        search: 200,
        toolbarToggle: 100,
        mapInteraction: 50,
        shipHover: 30,
        resize: 150,
        apiCall: 300
      }
    }
  },
  [PerformanceMode.BALANCED]: {
    monitor: {
      enabled: true,
      sampleRate: 0.5,
      memoryCheckInterval: 30000,
      apiMonitorEnabled: true,
      canvasMonitorEnabled: false, // Canvas监控已禁用
      interactionMonitorEnabled: true,
      maxLogEntries: 500,
      alertThresholds: {
        memoryUsage: 256,
        apiResponseTime: 5000,
        renderTime: 33,
        interactionDelay: 200
      }
    },
    optimization: {
      aisDataRefreshInterval: 7000,
      toolbarExpandedInterval: 15000,
      maxShipCount: 1000,
      blobCleanupInterval: 30000,
      blobMaxAge: 300000,

      featurePoolMaxSize: 200,
      debounceDelays: {
        search: 300,
        toolbarToggle: 150,
        mapInteraction: 100,
        shipHover: 50,
        resize: 200,
        apiCall: 500
      }
    }
  },
  [PerformanceMode.POWER_SAVE]: {
    monitor: {
      enabled: false,
      sampleRate: 0.1,
      memoryCheckInterval: 60000,
      apiMonitorEnabled: false,
      canvasMonitorEnabled: false,
      interactionMonitorEnabled: false,
      maxLogEntries: 100,
      alertThresholds: {
        memoryUsage: 128,
        apiResponseTime: 10000,
        renderTime: 50,
        interactionDelay: 500
      }
    },
    optimization: {
      aisDataRefreshInterval: 10000,
      toolbarExpandedInterval: 30000,
      maxShipCount: 500,
      blobCleanupInterval: 60000,
      blobMaxAge: 600000,

      featurePoolMaxSize: 100,
      debounceDelays: {
        search: 500,
        toolbarToggle: 300,
        mapInteraction: 200,
        shipHover: 100,
        resize: 300,
        apiCall: 1000
      }
    }
  }
};

// 性能配置管理类
export class PerformanceConfigManager {
  private currentMode: PerformanceMode = PerformanceMode.BALANCED;
  private customConfig: Partial<PerformanceMonitorConfig & PerformanceOptimizationConfig> = {};

  constructor() {
    this.loadFromStorage();
  }

  /**
   * 获取当前性能模式
   */
  getCurrentMode(): PerformanceMode {
    return this.currentMode;
  }

  /**
   * 设置性能模式
   */
  setMode(mode: PerformanceMode): void {
    this.currentMode = mode;
    this.saveToStorage();
    console.log(`性能模式已切换至: ${mode}`);
  }

  /**
   * 获取监控配置
   */
  getMonitorConfig(): PerformanceMonitorConfig {
    const baseConfig = DEFAULT_CONFIGS[this.currentMode].monitor;
    return { ...baseConfig, ...this.customConfig };
  }

  /**
   * 获取优化配置
   */
  getOptimizationConfig(): PerformanceOptimizationConfig {
    const baseConfig = DEFAULT_CONFIGS[this.currentMode].optimization;
    return { ...baseConfig, ...this.customConfig };
  }

  /**
   * 更新自定义配置
   */
  updateCustomConfig(config: Partial<PerformanceMonitorConfig & PerformanceOptimizationConfig>): void {
    this.customConfig = { ...this.customConfig, ...config };
    this.saveToStorage();
  }

  /**
   * 重置为默认配置
   */
  resetToDefault(): void {
    this.customConfig = {};
    this.saveToStorage();
  }

  /**
   * 从本地存储加载配置
   */
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem('windpower_performance_config');
      if (stored) {
        const data = JSON.parse(stored);
        this.currentMode = data.mode || PerformanceMode.BALANCED;
        this.customConfig = data.customConfig || {};
      }
    } catch (error) {
      console.warn('加载性能配置失败:', error);
    }
  }

  /**
   * 保存配置到本地存储
   */
  private saveToStorage(): void {
    try {
      const data = {
        mode: this.currentMode,
        customConfig: this.customConfig
      };
      localStorage.setItem('windpower_performance_config', JSON.stringify(data));
    } catch (error) {
      console.warn('保存性能配置失败:', error);
    }
  }
}

// 导出单例实例
export const performanceConfig = new PerformanceConfigManager();
