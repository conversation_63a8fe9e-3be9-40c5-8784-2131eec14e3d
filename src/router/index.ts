import { createWebHistory, createRouter, RouteRecordRaw } from 'vue-router';
/* Layout */
import Layout from '@/layout/index.vue';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
 noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
 activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
 }
 */

// 公共路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
  {
    path: '/social-callback',
    hidden: true,
    component: () => import('@/layout/components/SocialCallback/index.vue')
  },
  {
    path: '/login',
    component: () => import('@/views/login.vue'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register.vue'),
    hidden: true
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404.vue'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401.vue'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    // redirect: '/index',
    redirect: '/SSO',

    children: [
      {
        path: '/index',
        // component: () => import('@/views/index.vue'),
        component: () => import('@/views/WindPower/index.vue'),
        // component: () => import('@/views/dashboard/index.vue'),
        // component: () => import('@/views/dashboard/index.vue'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true,noCache: true    }
      }
    ]
  },
  {
    path: '/dashboard',
    component: Layout,
    hidden: true,
    // redirect: 'noredirect',
    redirect: '/dashboard/dashboard-index',
    children: [
      {
        path: 'dashboard-index',
        // component: () => import('@/views/index.vue'),
        component: () => import('@/views/dashboard/index.vue'),
        name: 'DashboardIndex',
        meta: { title: '首页1', icon: 'dashboard' }
      }
    ]
  },
  {
    path: '/SSO',
    // hidden: true,
    component: () => import('@/views/SSO-Portal.vue'),
    name: '统一登录门户',
    // meta: { title: '统一登录门户', icon: 'dashboard', affix: true }
  },

  // {
  //   path: '/bridge-monitoring',
  //   component: Layout,
  //   meta: { title: '桥梁监控', icon: 'monitor' },
  //   redirect: '/bridge-monitoring/dashboard',
  //   children: [
  //     {
  //       path: 'dashboard',
  //       component: () => import('@/views/Bridge/dashboard/index.vue'),
  //       name: 'BridgeDashboard',
  //       meta: { title: '监控工作台', icon: 'dashboard' }
  //     },
  //     // {
  //     //   path: 'realtime',
  //     //   component: () => import('@/views/Bridge/monitoring-dashboard/index.vue'),
  //     //   name: 'BridgeRealtime',
  //     //   meta: { title: '实时监控', icon: 'chart' }
  //     // }
  //   ]
  // },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index.vue'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/early-warning',
    component: Layout,
    hidden: true,
    meta: { title: '预警管理', icon: 'warning' },
    children: [
      {
        path: 'classification',
        component: () => import('@/views/Bridge/early-warning/classification.vue'),
        name: 'EarlyWarningClassification',
        meta: { title: '预警分类', icon: 'category' }
      },
      {
        path: 'group',
        component: () => import('@/layout/components/AppMain.vue'),
        meta: { title: '预警分类组', hidden: true },
        redirect: 'noRedirect',
        children: [
          {
            path: 'accident-effct',
            component: () => import('@/views/Bridge/early-warning/group/accident-effct.vue'),
            name: 'AccidentEffect',
            meta: { title: '交通事故预警', activeMenu: '/early-warning/classification' }
          },
          {
            path: 'environmental',
            component: () => import('@/views/Bridge/early-warning/group/environmental.vue'),
            name: 'Environmental',
            meta: { title: '环境污染预警', activeMenu: '/early-warning/classification' }
          },
          {
            path: 'geology',
            component: () => import('@/views/Bridge/early-warning/group/geology.vue'),
            name: 'Geology',
            meta: { title: '地质灾害预警', activeMenu: '/early-warning/classification' }
          },
          {
            path: 'health',
            component: () => import('@/views/Bridge/early-warning/group/health.vue'),
            name: 'Health',
            meta: { title: '公共卫生灾害预警', activeMenu: '/early-warning/classification' }
          },
          {
            path: 'weather',
            component: () => import('@/views/Bridge/early-warning/group/weather.vue'),
            name: 'Weather',
            meta: { title: '气象灾害预警', activeMenu: '/early-warning/classification' }
          },
          {
            path: 'ship',
            component: () => import('@/views/Bridge/early-warning/group/ship.vue'),
            name: 'Ship',
            meta: { title: '船舶预警', activeMenu: '/early-warning/classification' }
          },
          {
            path: 'vehicle-checkpoint-warning',
            component: () => import('@/views/Bridge/early-warning/group/vehicle-checkpoint-warning.vue'),
            name: 'VehicleCheckpointWarning',
            meta: { title: '车辆卡口预警', activeMenu: '/early-warning/classification' }
          }
        ]
      }
    ]
  },
  {
    path: '/coordination-command',
    component: Layout,
    hidden: true,
    meta: { title: '协调指挥', icon: 'coordination' },
    children: [
      {
        path: 'material-categories',
        component: () => import('@/views/Bridge/coordination-command/material-categories/index.vue'),
        name: 'MaterialCategories',
        meta: { title: '物资分类', activeMenu: '/coordination-command/emergency-resources' }
      },
      {
        path: 'emergency-knowledge',
        component: () => import('@/views/Bridge/coordination-command/emergency-knowledge.vue'),
        name: 'EmergencyKnowledge',
        meta: { title: '应急知识条目', activeMenu: '/coordination-command/emergency-resources' }
      },
      {
        path: 'rescue-teams',
        component: () => import('@/views/Bridge/coordination-command/rescue-teams.vue'),
        name: 'RescueTeams',
        meta: { title: '救援队伍', activeMenu: '/coordination-command/emergency-resources' }
      },
      {
        path: 'medical-ledger',
        component: () => import('@/views/Bridge/coordination-command/medical-ledger.vue'),
        name: 'MedicalLedger',
        meta: { title: '医疗台账', activeMenu: '/coordination-command/emergency-resources' }
      },
      {
        path: 'maintenance-teams',
        component: () => import('@/views/Bridge/coordination-command/maintenance-teams.vue'),
        name: 'MaintenanceTeams',
        meta: { title: '养护队伍', activeMenu: '/coordination-command/emergency-resources' }
      },
      {
        path: 'tugboat-info',
        component: () => import('@/views/Bridge/coordination-command/tugboat-info.vue'),
        name: 'TugboatInfo',
        meta: { title: '拖轮信息', activeMenu: '/coordination-command/emergency-resources' }
      },
      {
        path: 'emergency-response',
        component: () => import('@/views/Bridge/coordination-command/emergency-response.vue'),
        name: 'EmergencyResponse',
        meta: { title: '应急预案', activeMenu: '/coordination-command/emergency-response' }
      },
      {
        path: 'event-record',
        component: () => import('@/views/Bridge/coordination-command/event-record.vue'),
        name: 'EventRecord',
        meta: { title: '事件处置记录', activeMenu: '/coordination-command/event' }
      },
      {
        path: 'evaluation-rules',
        component: () => import('@/views/Bridge/coordination-command/evaluation/evaluation-rule.vue'),
        name: 'EvaluationRules',
        meta: { title: '评分规则', activeMenu: '/coordination-command/evaluation-rules' }
      }
    ]
  },
  {
    path: '/basic-data',
    component: Layout,
    hidden: true,
    meta: { title: '健康数据', icon: 'basic-data' },
    children: [
      {
        path: 'two-pages/bridge',
        component: () => import('@/views/Bridge/basic-data/tow-pages/bridge.vue'),
        name: 'twoBridge',
        meta: { title: '桥梁健康数据', activeMenu: '/basic-data' }
      },
      {
        path: 'three-pages/bridge',
        component: () => import('@/views/Bridge/basic-data/three-pages/bridge.vue'),
        name: 'threeBridge',
        meta: { title: '桥梁保养周期', activeMenu: '/basic-data' }
      },
      {
        path: 'four-pages/bridge',
        component: () => import('@/views/Bridge/basic-data/four-pages/bridge.vue'),
        name: 'fourBridge',
        meta: { title: '维护保养记录', activeMenu: '/basic-data' }
      },
      {
        path: 'bridge-maintain',
        component: () => import('@/views/Bridge/basic-data/bridge-maintain.vue'),
        name: 'BridgeMaintain',
        meta: { title: '桥梁运维记录', activeMenu: '/basic-data' }
      },
      {
        path: 'car',
        component: () => import('@/views/Bridge/basic-data/car.vue'),
        name: 'Car',
        meta: { title: '危化品车辆', activeMenu: '/basic-data' }
      },
      {
        path: 'two-pages/car',
        component: () => import('@/views/Bridge/basic-data/tow-pages/car.vue'),
        name: 'towCar',
        meta: { title: '危化品运输信息', activeMenu: '/basic-data' }
      },
      {
        path: 'three-pages/car-location',
        component: () => import('@/views/Bridge/basic-data/three-pages/car-location.vue'),
        name: 'carLocation',
        meta: { title: '车辆定位', activeMenu: '/basic-data' }
      },
      {
        path: 'four-pages/car-route',
        component: () => import('@/views/Bridge/basic-data/four-pages/car-route.vue'),
        name: 'carRoute',
        meta: { title: '运输路线', activeMenu: '/basic-data' }
      },
      {
        path: 'engineering-vehicle',
        component: () => import('@/views/Bridge/basic-data/engineering-vehicle.vue'),
        name: 'EngineeringVehicle',
        meta: { title: '工程车辆', activeMenu: '/basic-data' }
      },
      {
        path: 'ship-radar',
        component: () => import('@/views/Bridge/basic-data/tow-pages/ship-radar.vue'),
        name: 'ShipRadar',
        meta: { title: '船舶雷达扫描数据', activeMenu: '/basic-data' }
      },
      {
        path: 'ship-ais',
        component: () => import('@/views/Bridge/basic-data/three-pages/ship-ais.vue'),
        name: 'ShipAIS',
        meta: { title: '船舶AIS数据', activeMenu: '/basic-data' }
      },
      {
        path: 'public-opinion-source',
        component: () => import('@/views/Bridge/basic-data/tow-pages/public-opinion-source.vue'),
        name: 'PublicOpinionSource',
        meta: { title: '舆情来源管理', activeMenu: '/basic-data' }
      },
      {
        path: 'public-opinion-hotwords',
        component: () => import('@/views/Bridge/basic-data/three-pages/public-opinion-hotwords.vue'),
        name: 'PublicOpinionHotwords',
        meta: { title: '舆情热词管理', activeMenu: '/basic-data' }
      },
      {
        path: 'bridge-safety-equip',
        component: () => import('@/views/Bridge/basic-data/bridge-safety-equip.vue'),
        name: 'BridgeSafetyEquip',
        meta: { title: '桥梁安全设备', activeMenu: '/basic-data' }
      },
      {
        path: 'equipment',
        component: () => import('@/views/Bridge/basic-data/equipment.vue'),
        name: 'Equipment',
        meta: { title: '设备管理', activeMenu: '/basic-data' }
      },
      {
        path: 'checkpoint-device',
        component: () => import('@/views/Bridge/basic-data/checkpoint-device.vue'),
        name: 'CheckpointDevice',
        meta: { title: '车辆卡口设备', activeMenu: '/basic-data' }
      }
    ]
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes: RouteRecordRaw[] = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole.vue'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user', icon: '', noCache: true }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser.vue'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role', icon: '', noCache: true }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data.vue'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict', icon: '', noCache: true }
      }
    ]
  },
  {
    path: '/system/oss-config',
    component: Layout,
    hidden: true,
    permissions: ['system:ossConfig:list'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/system/oss/config.vue'),
        name: 'OssConfig',
        meta: { title: '配置管理', activeMenu: '/system/oss', icon: '', noCache: true }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable.vue'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen', icon: '', noCache: true }
      }
    ]
  },
  {
    path: '/workflow/leaveEdit',
    component: Layout,
    hidden: true,
    permissions: ['workflow:leave:edit'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/workflow/leave/leaveEdit.vue'),
        name: 'leaveEdit',
        meta: { title: '请假申请', activeMenu: '/workflow/leave', noCache: true }
      }
    ]
  },
  {
    path: '/workflow/design',
    component: Layout,
    hidden: true,
    permissions: ['workflow:leave:edit'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/workflow/processDefinition/design.vue'),
        name: 'design',
        meta: { title: '流程设计', activeMenu: '/workflow/processDefinition', noCache: true }
      }
    ]
  }
];

// 添加路由导航防护措施 - 将router创建移到路由声明之后
const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_APP_CONTEXT_PATH),
  routes: constantRoutes,
  // 处理滚动行为
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  }
});

// 添加全局导航守卫，处理可能的路由错误
router.beforeEach((to, from, next) => {
  try {
    next();
  } catch (error) {
    console.error('Navigation error:', error);
    next(false);
  }
});

export default router;
