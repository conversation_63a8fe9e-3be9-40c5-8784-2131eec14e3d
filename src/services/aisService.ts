import { getShipAis } from '@/api/wan/shipAis';
import { determineShipIconType } from '@/utils/geoUtils';
import { clearAllFeatures, addPoint } from '@/views/WindPower/demo/mapMethods';

// 存储电子围栏多边形数据
let fencePolygons: number[][][] = [];

// SVG图标路径映射
const iconPaths = {
  red: '/src/assets/svg/红三角形.svg',
  yellow: '/src/assets/svg/黄三角形.svg',
  green: '/src/assets/svg/绿三角形.svg'
};

/**
 * 设置电子围栏数据
 * @param polygons 电子围栏多边形数组
 */
export function setFencePolygons(polygons: number[][][]): void {
  fencePolygons = polygons;
}

/**
 * 获取船舶AIS数据并在地图上标记
 * @returns 船舶数据数组
 */
export async function fetchAndDisplayShips(): Promise<any[]> {
  try {
    // 调用API获取所有船舶AIS数据
    const response = await getShipAis({});
    
    // 清除地图上现有的所有点位
    clearAllFeatures();
    
    if (response && response.data && Array.isArray(response.data)) {
      const shipList = response.data;
      
      // 遍历船舶数据，在地图上标记
      shipList.forEach(ship => {
        // 确保船舶有有效的经纬度
        if (ship.lon && ship.lat) {
          // 确定船舶图标类型
          const iconType = determineShipIconType(
            [ship.lon, ship.lat], 
            fencePolygons
          );
          
          // 获取对应图标路径
          const iconPath = iconPaths[iconType];
          
          // 创建船舶信息弹窗内容
          const popupContent = createShipPopupContent(ship);
          
          // 在地图上添加船舶点位
          addPoint([ship.lon, ship.lat], iconPath, popupContent);
        }
      });
      
      return shipList;
    }
    
    return [];
  } catch (error) {
    console.error('获取船舶AIS数据失败:', error);
    return [];
  }
}

/**
 * 创建船舶信息弹窗内容
 * @param ship 船舶AIS数据
 * @returns HTML格式的弹窗内容
 */
function createShipPopupContent(ship: any): string {
  // 格式化时间
  const time = ship.time ? new Date(ship.time * 1000).toLocaleString() : '未知';
  
  // 格式化船舶类型
  const shipType = formatShipType(ship.shipType);
  
  return `
    <div class="ship-popup">
      <h3>${ship.name || ship.chineseName || ship.englishName || 'Unknown'}</h3>
      <p><strong>MMSI:</strong> ${ship.mmsi || 'Unknown'}</p>
      <p><strong>船舶类型:</strong> ${shipType}</p>
      <p><strong>航速:</strong> ${ship.sog ? ship.sog.toFixed(1) + '节' : '未知'}</p>
      <p><strong>航向:</strong> ${ship.cog ? ship.cog.toFixed(1) + '°' : '未知'}</p>
      <p><strong>更新时间:</strong> ${time}</p>
    </div>
  `;
}

/**
 * 格式化船舶类型
 * @param type 船舶类型代码
 * @returns 船舶类型描述
 */
function formatShipType(type: number | string): string {
  if (!type) return '未知';
  
  // 转为数字处理
  const typeNum = typeof type === 'string' ? parseInt(type, 10) : type;
  
  if (typeNum >= 60 && typeNum <= 69) {
    return "客船";
  } else if (typeNum >= 70 && typeNum <= 74) {
    return "货船";
  } else if (typeNum === 33) {
    return "作业船";
  } else if (typeNum === 52) {
    return "拖船";
  } else if (typeNum === 30) {
    return "渔船";
  } else {
    return "其他";
  }
} 