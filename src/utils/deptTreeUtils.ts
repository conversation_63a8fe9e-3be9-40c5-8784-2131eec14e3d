/**
 * 部门树数据处理工具函数
 */

import type { DeptInfo, UserInfo, TreeNode, TreeBuildOptions } from '@/components/DeptTreeSelector/types'

/**
 * 构建部门树数据，将用户作为部门的子节点
 * @param deptData 部门数据
 * @param userData 用户数据
 * @param options 构建选项
 * @returns 树形数据
 */
export function buildDeptTreeWithUsers(
  deptData: any[],
  userData: any[],
  options: TreeBuildOptions = {}
): DeptInfo[] {
  const { includeUsers = true } = options

  // 构建用户映射表
  const userMap: Record<string, UserInfo[]> = {}
  if (includeUsers && userData) {
    userData.forEach(user => {
      const deptId = String(user.deptId)
      if (!userMap[deptId]) {
        userMap[deptId] = []
      }
      userMap[deptId].push({
        userId: String(user.userId),
        userName: user.userName,
        nickName: user.nickName,
        phonenumber: user.phonenumber,
        deptId: deptId,
        deptName: user.deptName,
        type: 'user',
        selected: false
      })
    })
  }

  // 标准化部门数据
  const normalizedDepts = deptData.map(item => ({
    ...item,
    deptId: String(item.deptId),
    parentId: String(item.parentId),
    type: 'dept' as const,
    users: userMap[String(item.deptId)] || [],
    children: [] as DeptInfo[],
    selected: false,
    indeterminate: false
  }))

  // 构建部门映射表
  const deptMap: Record<string, DeptInfo> = {}
  normalizedDepts.forEach(dept => {
    deptMap[dept.deptId] = dept
  })

  // 构建树形结构
  const tree: DeptInfo[] = []
  normalizedDepts.forEach(dept => {
    const parentId = dept.parentId
    if (deptMap[parentId]) {
      deptMap[parentId].children!.push(dept)
    } else if (parentId === '0' || !parentId) {
      tree.push(dept)
    }
  })

  // 将用户作为部门的子节点添加到children中
  if (includeUsers) {
    function addUsersToTree(nodes: DeptInfo[]) {
      nodes.forEach(node => {
        if (node.users && node.users.length > 0) {
          // 将用户添加到children数组中
          node.users.forEach(user => {
            node.children!.push(user as any)
          })
        }
        if (node.children) {
          // 递归处理子部门
          const deptChildren = node.children.filter(child => child.type === 'dept') as DeptInfo[]
          addUsersToTree(deptChildren)
        }
      })
    }
    addUsersToTree(tree)
  }

  return tree
}

/**
 * 获取所有用户节点
 * @param treeData 树形数据
 * @returns 用户列表
 */
export function getAllUsers(treeData: DeptInfo[]): UserInfo[] {
  const users: UserInfo[] = []
  
  function traverse(nodes: DeptInfo[]) {
    nodes.forEach(node => {
      if (node.users) {
        users.push(...node.users)
      }
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  
  traverse(treeData)
  return users
}

/**
 * 获取选中的用户
 * @param treeData 树形数据
 * @returns 选中的用户列表
 */
export function getSelectedUsers(treeData: DeptInfo[]): UserInfo[] {
  const selectedUsers: UserInfo[] = []
  
  function traverse(nodes: DeptInfo[]) {
    nodes.forEach(node => {
      if (node.users) {
        selectedUsers.push(...node.users.filter(user => user.selected))
      }
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  
  traverse(treeData)
  return selectedUsers
}

/**
 * 更新用户选中状态
 * @param treeData 树形数据
 * @param userId 用户ID
 * @param selected 是否选中
 */
export function updateUserSelection(
  treeData: DeptInfo[],
  userId: string,
  selected: boolean
): void {
  function traverse(nodes: DeptInfo[]) {
    nodes.forEach(node => {
      if (node.users) {
        const user = node.users.find(u => u.userId === userId)
        if (user) {
          user.selected = selected
          updateDeptSelectionState(node)
        }
      }
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  
  traverse(treeData)
}

/**
 * 更新部门选中状态（基于子用户的选中情况）
 * @param dept 部门节点
 */
export function updateDeptSelectionState(dept: DeptInfo): void {
  if (!dept.users || dept.users.length === 0) {
    dept.selected = false
    dept.indeterminate = false
    return
  }

  const selectedCount = dept.users.filter(user => user.selected).length
  const totalCount = dept.users.length

  if (selectedCount === 0) {
    dept.selected = false
    dept.indeterminate = false
  } else if (selectedCount === totalCount) {
    dept.selected = true
    dept.indeterminate = false
  } else {
    dept.selected = false
    dept.indeterminate = true
  }
}

/**
 * 切换部门下所有用户的选中状态
 * @param dept 部门节点
 * @param selected 是否选中
 */
export function toggleDeptUsers(dept: DeptInfo, selected: boolean): void {
  if (dept.users) {
    dept.users.forEach(user => {
      user.selected = selected
    })
    updateDeptSelectionState(dept)
  }
}

/**
 * 根据用户ID列表设置选中状态
 * @param treeData 树形数据
 * @param userIds 用户ID列表
 */
export function setSelectedUserIds(treeData: DeptInfo[], userIds: string[]): void {
  const userIdSet = new Set(userIds)
  
  function traverse(nodes: DeptInfo[]) {
    nodes.forEach(node => {
      if (node.users) {
        node.users.forEach(user => {
          user.selected = userIdSet.has(user.userId)
        })
        updateDeptSelectionState(node)
      }
      if (node.children) {
        traverse(node.children)
      }
    })
  }
  
  traverse(treeData)
}

/**
 * 搜索过滤树形数据
 * @param treeData 树形数据
 * @param keyword 搜索关键词
 * @returns 过滤后的树形数据
 */
export function filterTreeData(treeData: DeptInfo[], keyword: string): DeptInfo[] {
  if (!keyword.trim()) {
    return treeData
  }

  const filtered: DeptInfo[] = []
  
  function traverse(nodes: DeptInfo[]): DeptInfo[] {
    const result: DeptInfo[] = []
    
    nodes.forEach(node => {
      const deptMatch = node.deptName.toLowerCase().includes(keyword.toLowerCase())
      const userMatch = node.users?.some(user => 
        (user.userName?.toLowerCase().includes(keyword.toLowerCase())) ||
        (user.nickName?.toLowerCase().includes(keyword.toLowerCase()))
      )
      
      let filteredChildren: DeptInfo[] = []
      if (node.children) {
        filteredChildren = traverse(node.children)
      }
      
      if (deptMatch || userMatch || filteredChildren.length > 0) {
        result.push({
          ...node,
          children: filteredChildren
        })
      }
    })
    
    return result
  }
  
  return traverse(treeData)
}
