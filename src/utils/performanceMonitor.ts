/**
 * 性能监控工具
 * 提供内存、API和交互性能的监控功能
 * Canvas监控已移除以避免与OpenLayers冲突
 */

import { performanceConfig, PerformanceMonitorConfig } from '@/config/performance';

// 性能指标接口
export interface PerformanceMetrics {
  timestamp: number;
  memoryUsage?: {
    used: number;
    total: number;
    percentage: number;
  };
  apiMetrics?: {
    url: string;
    method: string;
    duration: number;
    status: number;
    timestamp: number;
  };
  // Canvas性能指标已移除 - 避免与OpenLayers冲突
  interactionMetrics?: {
    type: string;
    duration: number;
    timestamp: number;
  };
}

// 性能警告接口
export interface PerformanceAlert {
  type: 'memory' | 'api' | 'interaction'; // Canvas类型已移除
  level: 'warning' | 'error';
  message: string;
  value: number;
  threshold: number;
  timestamp: number;
  suggestions?: string[];
}

// 性能监控管理器
export class PerformanceMonitor {
  private config: PerformanceMonitorConfig;
  private metrics: PerformanceMetrics[] = [];
  private alerts: PerformanceAlert[] = [];
  private timers: Map<string, number> = new Map();
  private observers: Map<string, Function[]> = new Map();
  private isRunning = false;

  constructor() {
    this.config = performanceConfig.getMonitorConfig();
    this.setupEventListeners();
  }

  /**
   * 启动性能监控
   */
  start(): void {
    if (!this.config.enabled || this.isRunning) return;

    this.isRunning = true;
    this.startMemoryMonitoring();
    this.startAPIMonitoring();
    // Canvas监控已移除 - 避免与OpenLayers冲突
    this.startInteractionMonitoring();

    console.log('性能监控已启动');
  }

  /**
   * 停止性能监控
   */
  stop(): void {
    if (!this.isRunning) return;

    this.isRunning = false;
    this.timers.forEach(timer => clearInterval(timer));
    this.timers.clear();

    console.log('性能监控已停止');
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * 获取性能警告
   */
  getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  /**
   * 清除历史数据
   */
  clearHistory(): void {
    this.metrics = [];
    this.alerts = [];
  }

  /**
   * 订阅性能事件
   */
  subscribe(event: string, callback: Function): void {
    if (!this.observers.has(event)) {
      this.observers.set(event, []);
    }
    this.observers.get(event)!.push(callback);
  }

  /**
   * 取消订阅
   */
  unsubscribe(event: string, callback: Function): void {
    const callbacks = this.observers.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 手动记录API调用
   */
  recordAPICall(url: string, method: string, duration: number, status: number): void {
    if (!this.config.apiMonitorEnabled) return;

    const metric: PerformanceMetrics = {
      timestamp: Date.now(),
      apiMetrics: {
        url,
        method,
        duration,
        status,
        timestamp: Date.now()
      }
    };

    this.addMetric(metric);

    // 检查API响应时间警告
    if (duration > this.config.alertThresholds.apiResponseTime) {
      this.addAlert({
        type: 'api',
        level: duration > this.config.alertThresholds.apiResponseTime * 2 ? 'error' : 'warning',
        message: `API响应时间过长: ${url}`,
        value: duration,
        threshold: this.config.alertThresholds.apiResponseTime,
        timestamp: Date.now(),
        suggestions: [
          '检查网络连接',
          '优化API查询参数',
          '考虑添加缓存机制'
        ]
      });
    }
  }

  /**
   * Canvas性能记录已移除 - 避免与OpenLayers冲突
   */
  recordCanvasMetrics(renderTime: number, canvasCount: number, optimizedCount: number): void {
    // Canvas性能监控已禁用
    return;
  }

  /**
   * 手动记录交互性能
   */
  recordInteraction(type: string, duration: number): void {
    if (!this.config.interactionMonitorEnabled) return;

    const metric: PerformanceMetrics = {
      timestamp: Date.now(),
      interactionMetrics: {
        type,
        duration,
        timestamp: Date.now()
      }
    };

    this.addMetric(metric);

    // 检查交互延迟警告
    if (duration > this.config.alertThresholds.interactionDelay) {
      this.addAlert({
        type: 'interaction',
        level: duration > this.config.alertThresholds.interactionDelay * 2 ? 'error' : 'warning',
        message: `交互响应延迟: ${type}`,
        value: duration,
        threshold: this.config.alertThresholds.interactionDelay,
        timestamp: Date.now(),
        suggestions: [
          '启用防抖处理',
          '优化事件处理函数',
          '减少DOM操作'
        ]
      });
    }
  }

  /**
   * 启动内存监控
   */
  private startMemoryMonitoring(): void {
    const checkMemory = () => {
      if (!this.isRunning) return;

      try {
        // 使用performance.memory API（仅Chrome支持）
        const memory = (performance as any).memory;
        if (memory) {
          const used = Math.round(memory.usedJSHeapSize / 1024 / 1024);
          const total = Math.round(memory.totalJSHeapSize / 1024 / 1024);
          const percentage = Math.round((used / total) * 100);

          const metric: PerformanceMetrics = {
            timestamp: Date.now(),
            memoryUsage: { used, total, percentage }
          };

          this.addMetric(metric);

          // 检查内存使用警告
          if (used > this.config.alertThresholds.memoryUsage) {
            this.addAlert({
              type: 'memory',
              level: used > this.config.alertThresholds.memoryUsage * 1.5 ? 'error' : 'warning',
              message: `内存使用过高: ${used}MB`,
              value: used,
              threshold: this.config.alertThresholds.memoryUsage,
              timestamp: Date.now(),
              suggestions: [
                '清理未使用的对象',
                '检查内存泄漏',
                '启用要素池化'
              ]
            });
          }
        }
      } catch (error) {
        console.warn('内存监控失败:', error);
      }
    };

    const timer = setInterval(checkMemory, this.config.memoryCheckInterval);
    this.timers.set('memory', timer);
  }

  /**
   * 启动API监控
   */
  private startAPIMonitoring(): void {
    if (!this.config.apiMonitorEnabled) return;

    // 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = args[0].toString();
      const method = (args[1]?.method || 'GET').toUpperCase();

      try {
        const response = await originalFetch(...args);
        const duration = performance.now() - startTime;
        
        if (Math.random() < this.config.sampleRate) {
          this.recordAPICall(url, method, duration, response.status);
        }

        return response;
      } catch (error) {
        const duration = performance.now() - startTime;
        this.recordAPICall(url, method, duration, 0);
        throw error;
      }
    };
  }

  /**
   * Canvas监控已完全移除 - 避免与OpenLayers冲突
   * 此方法已不再使用，保留注释说明
   */

  /**
   * 启动交互监控
   */
  private startInteractionMonitoring(): void {
    if (!this.config.interactionMonitorEnabled) return;

    // 监控点击事件
    const handleClick = (event: Event) => {
      const startTime = performance.now();
      setTimeout(() => {
        const duration = performance.now() - startTime;
        if (Math.random() < this.config.sampleRate) {
          this.recordInteraction('click', duration);
        }
      }, 0);
    };

    document.addEventListener('click', handleClick, { passive: true });
  }

  /**
   * 添加性能指标
   */
  private addMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);

    // 限制指标数量
    if (this.metrics.length > this.config.maxLogEntries) {
      this.metrics = this.metrics.slice(-this.config.maxLogEntries);
    }

    // 通知订阅者
    this.emit('metric', metric);
  }

  /**
   * 添加性能警告
   */
  private addAlert(alert: PerformanceAlert): void {
    this.alerts.push(alert);

    // 限制警告数量
    if (this.alerts.length > this.config.maxLogEntries) {
      this.alerts = this.alerts.slice(-this.config.maxLogEntries);
    }

    // 通知订阅者
    this.emit('alert', alert);
    console.warn('性能警告:', alert);
  }

  /**
   * 触发事件
   */
  private emit(event: string, data: any): void {
    const callbacks = this.observers.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('性能监控事件回调失败:', error);
        }
      });
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听配置变化
    const updateConfig = () => {
      this.config = performanceConfig.getMonitorConfig();
      if (this.isRunning) {
        this.stop();
        this.start();
      }
    };

    // 可以添加配置变化监听器
    // performanceConfig.subscribe('configChanged', updateConfig);
  }
}

// 导出单例实例
export const performanceMonitor = new PerformanceMonitor();

/**
 * 性能调试面板（仅开发环境）
 */
export class PerformanceDebugPanel {
  private panel: HTMLElement | null = null;
  private isVisible = false;

  constructor() {
    if (import.meta.env.VITE_APP_ENV === 'development') {
      this.createPanel();
      this.setupKeyboardShortcut();
    }
  }

  /**
   * 显示/隐藏调试面板
   */
  toggle(): void {
    if (!this.panel) return;

    this.isVisible = !this.isVisible;
    this.panel.style.display = this.isVisible ? 'block' : 'none';

    if (this.isVisible) {
      this.updatePanel();
      this.startAutoUpdate();
    } else {
      this.stopAutoUpdate();
    }
  }

  /**
   * 创建调试面板
   */
  private createPanel(): void {
    this.panel = document.createElement('div');
    this.panel.id = 'performance-debug-panel';
    this.panel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      max-height: 600px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      font-family: monospace;
      font-size: 12px;
      padding: 10px;
      border-radius: 5px;
      z-index: 10000;
      overflow-y: auto;
      display: none;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    `;

    document.body.appendChild(this.panel);
  }

  /**
   * 设置键盘快捷键
   */
  private setupKeyboardShortcut(): void {
    document.addEventListener('keydown', (event) => {
      // Ctrl + Shift + P 切换调试面板
      if (event.ctrlKey && event.shiftKey && event.key === 'P') {
        event.preventDefault();
        this.toggle();
      }
    });
  }

  /**
   * 更新面板内容
   */
  private updatePanel(): void {
    if (!this.panel) return;

    const metrics = performanceMonitor.getMetrics();
    const alerts = performanceMonitor.getAlerts();
    const config = performanceConfig.getMonitorConfig();

    const latestMetrics = metrics.slice(-10);
    const recentAlerts = alerts.slice(-5);

    this.panel.innerHTML = `
      <div style="border-bottom: 1px solid #333; padding-bottom: 10px; margin-bottom: 10px;">
        <h3 style="margin: 0; color: #4CAF50;">性能监控面板</h3>
        <div style="font-size: 10px; color: #888;">按 Ctrl+Shift+P 切换显示</div>
      </div>

      <div style="margin-bottom: 15px;">
        <h4 style="margin: 5px 0; color: #2196F3;">配置状态</h4>
        <div>模式: ${performanceConfig.getCurrentMode()}</div>
        <div>监控: ${config.enabled ? '启用' : '禁用'}</div>
        <div>采样率: ${(config.sampleRate * 100).toFixed(0)}%</div>
      </div>

      <div style="margin-bottom: 15px;">
        <h4 style="margin: 5px 0; color: #FF9800;">最新警告 (${recentAlerts.length})</h4>
        ${recentAlerts.length === 0 ? '<div style="color: #4CAF50;">无警告</div>' :
          recentAlerts.map(alert => `
            <div style="color: ${alert.level === 'error' ? '#F44336' : '#FF9800'}; margin: 2px 0;">
              [${alert.type.toUpperCase()}] ${alert.message}
            </div>
          `).join('')
        }
      </div>

      <div style="margin-bottom: 15px;">
        <h4 style="margin: 5px 0; color: #9C27B0;">最新指标 (${latestMetrics.length})</h4>
        ${latestMetrics.map(metric => {
          const time = new Date(metric.timestamp).toLocaleTimeString();
          let content = `<div style="margin: 2px 0;">[${time}]`;

          if (metric.memoryUsage) {
            content += ` 内存: ${metric.memoryUsage.used}MB (${metric.memoryUsage.percentage}%)`;
          }
          if (metric.apiMetrics) {
            content += ` API: ${metric.apiMetrics.duration.toFixed(0)}ms`;
          }
          // Canvas指标显示已移除 - 避免与OpenLayers冲突
          if (metric.interactionMetrics) {
            content += ` 交互: ${metric.interactionMetrics.duration.toFixed(0)}ms`;
          }

          content += '</div>';
          return content;
        }).join('')}
      </div>

      <div style="margin-bottom: 15px;">
        <h4 style="margin: 5px 0; color: #607D8B;">操作</h4>
        <button onclick="window.performanceDebugPanel.clearHistory()" style="margin: 2px; padding: 4px 8px; background: #333; color: white; border: 1px solid #555; border-radius: 3px; cursor: pointer;">清除历史</button>
        <button onclick="window.performanceDebugPanel.exportData()" style="margin: 2px; padding: 4px 8px; background: #333; color: white; border: 1px solid #555; border-radius: 3px; cursor: pointer;">导出数据</button>
      </div>
    `;
  }

  /**
   * 清除历史数据
   */
  clearHistory(): void {
    performanceMonitor.clearHistory();
    this.updatePanel();
  }

  /**
   * 导出性能数据
   */
  exportData(): void {
    const data = {
      metrics: performanceMonitor.getMetrics(),
      alerts: performanceMonitor.getAlerts(),
      config: performanceConfig.getMonitorConfig(),
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-data-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  /**
   * 开始自动更新
   */
  private startAutoUpdate(): void {
    this.stopAutoUpdate();
    (this as any).updateTimer = setInterval(() => {
      if (this.isVisible) {
        this.updatePanel();
      }
    }, 2000);
  }

  /**
   * 停止自动更新
   */
  private stopAutoUpdate(): void {
    if ((this as any).updateTimer) {
      clearInterval((this as any).updateTimer);
      (this as any).updateTimer = null;
    }
  }
}

// 在开发环境下创建调试面板实例
if (import.meta.env.VITE_APP_ENV === 'development') {
  (window as any).performanceDebugPanel = new PerformanceDebugPanel();
}
