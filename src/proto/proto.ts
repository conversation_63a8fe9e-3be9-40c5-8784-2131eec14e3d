/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
"use strict";

import * as $protobuf from "protobufjs/light";

const $root = ($protobuf.roots["default"] || ($protobuf.roots["default"] = new $protobuf.Root()))
.addJSON({
  litong: {
    nested: {
      Ship: {
        fields: {
          id: {
            type: "string",
            id: 1
          },
          name: {
            type: "string",
            id: 2
          },
          time: {
            type: "sint64",
            id: 3
          },
          version: {
            type: "uint64",
            id: 4
          },
          lon: {
            type: "double",
            id: 5
          },
          lat: {
            type: "double",
            id: 6
          },
          sog: {
            type: "float",
            id: 7
          },
          cog: {
            type: "float",
            id: 8
          },
          rot: {
            type: "float",
            id: 9
          },
          heading: {
            type: "float",
            id: 10
          },
          mmsi: {
            type: "uint32",
            id: 11
          },
          imo: {
            type: "uint32",
            id: 12
          },
          callsign: {
            type: "string",
            id: 13
          },
          englishName: {
            type: "string",
            id: 14
          },
          chineseName: {
            type: "string",
            id: 15
          },
          shipType: {
            type: "uint32",
            id: 16
          },
          aisShipType: {
            type: "uint32",
            id: 42
          },
          fileShipType: {
            type: "uint32",
            id: 43
          },
          width: {
            type: "float",
            id: 17
          },
          length: {
            type: "float",
            id: 18
          },
          toBow: {
            type: "float",
            id: 19
          },
          toStern: {
            type: "float",
            id: 20
          },
          toPort: {
            type: "float",
            id: 21
          },
          toStarboard: {
            type: "float",
            id: 22
          },
          timeout: {
            type: "uint32",
            id: 23
          },
          region: {
            type: "string",
            id: 24
          },
          draught: {
            type: "float",
            id: 25
          },
          eta: {
            type: "sint64",
            id: 26
          },
          destination: {
            type: "string",
            id: 27
          },
          status: {
            type: "uint32",
            id: 28
          },
          fileId: {
            type: "string",
            id: 40
          },
          shipNo: {
            type: "string",
            id: 41
          },
          alarms: {
            type: "uint32",
            id: 52
          },
          tags: {
            type: "uint64",
            id: 53
          },
          specialTag: {
            type: "uint32",
            id: 54
          },
          tagsTwo: {
            type: "uint64",
            id: 55
          },
          origins: {
            type: "uint32",
            id: 56
          },
          track: {
            type: "uint64",
            id: 30
          },
          reliability: {
            type: "uint32",
            id: 70
          }
        }
      },
      ShipDynamic: {
        fields: {
          id: {
            type: "string",
            id: 1
          },
          time: {
            type: "sint64",
            id: 3
          },
          version: {
            type: "uint64",
            id: 4
          },
          lon: {
            type: "double",
            id: 5
          },
          lat: {
            type: "double",
            id: 6
          },
          sog: {
            type: "float",
            id: 7
          },
          cog: {
            type: "float",
            id: 8
          },
          rot: {
            type: "float",
            id: 9
          },
          heading: {
            type: "float",
            id: 10
          },
          timeout: {
            type: "uint32",
            id: 23
          },
          draught: {
            type: "float",
            id: 25
          },
          status: {
            type: "uint32",
            id: 28
          },
          alarms: {
            type: "uint32",
            id: 52
          },
          tags: {
            type: "uint64",
            id: 53
          },
          specialTag: {
            type: "uint32",
            id: 54
          },
          tagsTwo: {
            type: "uint64",
            id: 55
          },
          origins: {
            type: "uint32",
            id: 56
          }
        }
      },
      ShipStatic: {
        fields: {
          id: {
            type: "string",
            id: 1
          },
          name: {
            type: "string",
            id: 2
          },
          mmsi: {
            type: "uint32",
            id: 11
          },
          imo: {
            type: "uint32",
            id: 12
          },
          callsign: {
            type: "string",
            id: 13
          },
          englishName: {
            type: "string",
            id: 14
          },
          chineseName: {
            type: "string",
            id: 15
          },
          shipType: {
            type: "uint32",
            id: 16
          },
          aisShipType: {
            type: "uint32",
            id: 42
          },
          fileShipType: {
            type: "uint32",
            id: 43
          },
          width: {
            type: "float",
            id: 17
          },
          length: {
            type: "float",
            id: 18
          },
          toBow: {
            type: "float",
            id: 19
          },
          toStern: {
            type: "float",
            id: 20
          },
          toPort: {
            type: "float",
            id: 21
          },
          toStarboard: {
            type: "float",
            id: 22
          },
          region: {
            type: "string",
            id: 24
          },
          eta: {
            type: "sint64",
            id: 26
          },
          destination: {
            type: "string",
            id: 27
          },
          fileId: {
            type: "string",
            id: 40
          },
          shipNo: {
            type: "string",
            id: 41
          },
          track: {
            type: "uint64",
            id: 30
          }
        }
      }
    }
  }
});

export default $root;
