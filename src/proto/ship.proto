syntax = "proto3";

package litong;

message Ship {
    string    id = 1;
    string    name = 2;
    sint64    time = 3;
    uint64    version = 4;

    double    lon = 5;
    double    lat = 6;
    float     sog = 7;
    float     cog = 8;
    float     rot = 9;
    float     heading = 10;

    uint32    mmsi = 11;
    uint32    imo = 12;
    string    callsign = 13;
    string    english_name = 14;
    string    chinese_name = 15;
    uint32    ship_type = 16;
    uint32    ais_ship_type = 42;
    uint32    file_ship_type = 43;

    float     width = 17;
    float     length = 18;
    float     to_bow = 19;
    float     to_stern = 20;
    float     to_port = 21;
    float     to_starboard = 22;

    uint32    timeout = 23;
    string    region = 24;
    float     draught = 25;
    sint64    eta = 26;
    string    destination = 27;
    uint32    status = 28;

    string    file_id = 40;
    string    ship_no = 41;

    uint32    alarms = 52;
    uint64    tags = 53;
    uint32    special_tag = 54;
    uint64    tags_two = 55;
    uint32    origins = 56;

    uint64    track = 30;

    uint32    reliability = 70;
}

message ShipDynamic {
    string    id = 1;
    sint64    time = 3;
    uint64    version = 4;
    double    lon = 5;
    double    lat = 6;
    float     sog = 7;
    float     cog = 8;
    float     rot = 9;
    float     heading = 10;
    uint32    timeout = 23;
    float     draught = 25;
    uint32    status = 28;

    uint32    alarms = 52;
    uint64    tags = 53;
    uint32    special_tag = 54;
    uint64    tags_two = 55;
    uint32    origins = 56;
}

message ShipStatic {
    string    id = 1;
    string    name = 2;
    uint32    mmsi = 11;
    uint32    imo = 12;
    string    callsign = 13;
    string    english_name = 14;
    string    chinese_name = 15;
    uint32    ship_type = 16;
    uint32    ais_ship_type = 42;
    uint32    file_ship_type = 43;
    float     width = 17;
    float     length = 18;
    float     to_bow = 19;
    float     to_stern = 20;
    float     to_port = 21;
    float     to_starboard = 22;
    string    region = 24;
    sint64    eta = 26;
    string    destination = 27;
    string    file_id = 40;
    string    ship_no = 41;
    uint64    track = 30;
}
