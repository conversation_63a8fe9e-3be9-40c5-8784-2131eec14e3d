<template>
  <el-config-provider :locale="appStore.locale" :size="appStore.size">
<!--    <StagewiseToolbar v-if="isLocalEnvironment" :config="config" />-->
    <router-view />
    <InfoWindow />
  </el-config-provider>
</template>

<script setup lang="ts">
import { onMounted, nextTick, ref } from 'vue';
import useSettingsStore from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import useAppStore from '@/store/modules/app';
import InfoWindow from '@/views/WindPower/components/InfoWindow.vue';
import emitter from '@/utils/bus';

const appStore = useAppStore();
import { StagewiseToolbar, type ToolbarConfig } from '@stagewise/toolbar-vue';

const config: ToolbarConfig = {
  plugins: [], // Add your custom plugins here
};

function isLocalEnvironment() {
  if(import.meta.env.VITE_APP_ENV=== 'development') {
    return true;
  }
  return false;
}


onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
  });
});
</script>
