<template>
  <div class="video-player-container" :style="{ width: width, height: height }" @dblclick="handleVideoDoubleClick">
    <video
      :id="playerId"
      ref="videoElement"
      class="video-player"
      :controls="showControls"
      :autoplay="autoplay"
      :style="{ width: '100%', height: '100%' }"
      @dblclick="handleVideoDoubleClick"
    >
      不支持播放
    </video>
    
    <!-- Canvas overlay for drawing detection boxes -->
    <canvas
      v-if="enableOverlay"
      ref="overlayCanvas"
      class="video-overlay"
      :style="{ width: '100%', height: '100%' }"
      @click="handleCanvasClick"
    ></canvas>
    
    <!-- Loading indicator -->
    <div v-if="loading" class="video-loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>加载中...</span>
    </div>

    <!-- Error message -->
    <div v-if="error" class="video-error">
      <el-icon>
        <Warning />
      </el-icon>
      <span>{{ error }}</span>
    </div>

    <!-- Video controls overlay -->
    <div v-if="showCustomControls" class="video-controls-overlay">
      <div class="controls-top">
        <div class="video-title">{{ videoTitle }}</div>
        <div class="video-time">{{ currentTimeDisplay }}</div>
      </div>
      <div class="controls-bottom">
        <div class="control-buttons">
          <el-button
            :icon="isPlaying ? VideoPause : VideoPlay"
            circle
            size="small"
            @click="togglePlay"
          />
          <el-button
            :icon="Mute"
            circle
            size="small"
            @click="toggleMute"
          />
          <el-button
            :icon="FullScreen"
            circle
            size="small"
            @click="toggleFullscreen"
          />
        </div>
        <div class="progress-bar">
          <el-slider
            v-model="progress"
            :max="100"
            :show-tooltip="false"
            :disabled="!isFinite(duration) || duration <= 0"
            @change="handleProgressChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
import flvjs from 'flv.js';
import Hls from 'hls.js';
import {
  Loading,
  Warning,
  VideoPlay,
  VideoPause,
  Mute,
  FullScreen
} from '@element-plus/icons-vue';

interface DetectionBox {
  x: number;
  y: number;
  width: number;
  height: number;
  label?: string;
  confidence?: number;
  color?: string;
}

interface Props {
  src?: string;
  width?: string;
  height?: string;
  showControls?: boolean;
  showCustomControls?: boolean;
  autoplay?: boolean;
  enableOverlay?: boolean;
  detectionBoxes?: DetectionBox[];
  playerId?: string;
  videoTitle?: string;
}

const props = withDefaults(defineProps<Props>(), {
  src: '',
  width: '100%',
  height: '400px',
  showControls: true,
  showCustomControls: false,
  autoplay: true,
  enableOverlay: false,
  detectionBoxes: () => [],
  playerId: () => `video-player-${Date.now()}`,
  videoTitle: '监控视频'
});

const emit = defineEmits(['play', 'pause', 'error', 'canvasClick']);

const videoElement = ref<HTMLVideoElement>();
const overlayCanvas = ref<HTMLCanvasElement>();
const flvPlayer = ref<any>(null);
const hlsPlayer = ref<Hls | null>(null);
const loading = ref(false);
const error = ref('');
const isPlaying = ref(false);
const isMuted = ref(false);
const progress = ref(0);
const currentTime = ref(0);
const duration = ref(0);

// 计算属性
const currentTimeDisplay = computed(() => {
  // 格式化时间显示
  const formatTime = (seconds: number) => {
    if (!isFinite(seconds) || isNaN(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const current = formatTime(currentTime.value);

  // 如果是实时流（duration为无穷大或未定义），只显示已播放时间
  if (!isFinite(duration.value) || duration.value === 0) {
    return `${current} (实时)`;
  }

  // 如果是录制视频，显示当前时间/总时长
  const total = formatTime(duration.value);
  return `${current} / ${total}`;
});

// 播放视频流
const playVideo = async (url: string) => {
  if (!url) {
    error.value = '视频地址不能为空';
    return;
  }

  try {
    loading.value = true;
    error.value = '';

    // 清理之前的播放器
    destroyPlayer();

    await nextTick();

    if (!videoElement.value) {
      throw new Error('视频元素未找到');
    }

    // 判断视频流类型并选择合适的播放器
    if (url.includes('.m3u8') || url.includes('hls')) {
      // 使用HLS.js播放HLS流
      if (Hls.isSupported()) {
        hlsPlayer.value = new Hls({
          // 优化配置防止内存溢出并支持实时流
          maxBufferLength: 30,        // 最大缓存长度(秒) - 增加以支持更稳定播放
          maxMaxBufferLength: 60,     // 最大最大缓存长度(秒)
          maxBufferSize: 60 * 1000 * 1000, // 最大缓存大小(60MB)
          maxBufferHole: 0.5,         // 最大缓存空洞
          lowLatencyMode: false,      // 关闭低延迟模式以提高稳定性
          backBufferLength: 10,       // 后缓存长度
          liveSyncDurationCount: 5,   // 实时同步持续时间计数 - 增加以提高稳定性
          liveMaxLatencyDurationCount: 15, // 实时最大延迟持续时间计数
          enableWorker: true,         // 启用Web Worker以提高性能
          startLevel: -1,             // 自动选择起始质量
          capLevelToPlayerSize: true, // 根据播放器大小限制质量
        });

        hlsPlayer.value.loadSource(url);
        hlsPlayer.value.attachMedia(videoElement.value);

        hlsPlayer.value.on(Hls.Events.MANIFEST_PARSED, () => {
          console.log('HLS manifest parsed successfully');
          loading.value = false;
          error.value = ''; // 清除之前的错误信息
          if (props.autoplay) {
            videoElement.value?.play().then(() => {
              isPlaying.value = true;
              emit('play');
              console.log('HLS视频开始播放');
            }).catch((playError) => {
              console.warn('HLS自动播放失败:', playError);
              // 自动播放失败不算错误，用户可以手动播放
            });
          }
        });

        // 添加更多事件监听以便调试
        hlsPlayer.value.on(Hls.Events.MEDIA_ATTACHED, () => {
          console.log('HLS media attached');
          // 重置时间相关变量
          currentTime.value = 0;
          duration.value = 0;
          progress.value = 0;
        });

        hlsPlayer.value.on(Hls.Events.FRAG_LOADED, () => {
          // 片段加载成功，清除错误信息
          if (error.value) {
            error.value = '';
          }
        });

        hlsPlayer.value.on(Hls.Events.LEVEL_LOADED, (event, data) => {
          // 当加载级别时，检查是否是实时流
          if (data.details && data.details.live) {
            console.log('检测到实时HLS流');
            duration.value = Infinity;
          }
        });

        hlsPlayer.value.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS Player Error:', event, data);
          if (data.fatal) {
            switch (data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                error.value = '网络错误，正在尝试恢复...';
                console.log('尝试恢复网络错误...');
                hlsPlayer.value?.startLoad();
                // 3秒后如果还没恢复，尝试重新加载
                setTimeout(() => {
                  if (error.value.includes('网络错误')) {
                    console.log('网络错误恢复超时，重新加载视频...');
                    playVideo(props.src);
                  }
                }, 3000);
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                error.value = '媒体错误，正在尝试恢复...';
                console.log('尝试恢复媒体错误...');
                hlsPlayer.value?.recoverMediaError();
                // 3秒后如果还没恢复，尝试重新加载
                setTimeout(() => {
                  if (error.value.includes('媒体错误')) {
                    console.log('媒体错误恢复超时，重新加载视频...');
                    playVideo(props.src);
                  }
                }, 3000);
                break;
              default:
                error.value = `播放错误: ${data.details}`;
                loading.value = false;
                emit('error', data);
                break;
            }
          } else {
            // 非致命错误，只记录日志
            console.warn('HLS非致命错误:', data);
          }
        });
      } else if (videoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari原生支持HLS
        videoElement.value.src = url;
        if (props.autoplay) {
          await videoElement.value.play();
          isPlaying.value = true;
          emit('play');
        }
        loading.value = false;
      } else {
        throw new Error('浏览器不支持HLS播放');
      }
    } else if (url.includes('.flv') || url.includes('flv')) {
      // 使用FLV.js播放FLV流
      if (flvjs.isSupported()) {
        flvPlayer.value = flvjs.createPlayer({
          type: 'flv',
          url: url,
          isLive: true,
          hasAudio: true,
          hasVideo: true,
          enableWorker: false,
          enableStashBuffer: false,
          stashInitialSize: 128
        });

        flvPlayer.value.attachMediaElement(videoElement.value);

        flvPlayer.value.on('error', (errorType: string, errorDetail: string) => {
          console.error('FLV Player Error:', errorType, errorDetail);
          error.value = `播放错误: ${errorDetail}`;
          loading.value = false;
          emit('error', { errorType, errorDetail });
        });

        flvPlayer.value.on('loadstart', () => {
          loading.value = true;
        });

        flvPlayer.value.on('loadeddata', () => {
          loading.value = false;
        });

        await flvPlayer.value.load();

        if (props.autoplay) {
          await flvPlayer.value.play();
          isPlaying.value = true;
          emit('play');
        }
      } else {
        throw new Error('浏览器不支持FLV播放');
      }
    } else {
      // 使用原生video标签播放其他格式（包括MJPEG流）
      videoElement.value.src = url;

      // 为MJPEG流添加特殊处理
      if (url.includes('mjpeg')) {
        // MJPEG流通常不需要缓冲，设置为实时播放
        videoElement.value.preload = 'none';
        videoElement.value.controls = false;
      }

      if (props.autoplay) {
        try {
          await videoElement.value.play();
          isPlaying.value = true;
          emit('play');
        } catch (playError: any) {
          // 如果自动播放失败，不抛出错误，只是记录日志
          console.warn('Autoplay failed:', playError);
          isPlaying.value = false;
        }
      }
      loading.value = false;
    }
  } catch (err: any) {
    console.error('Video play error:', err);
    error.value = err.message || '播放失败';
    loading.value = false;
    emit('error', err);
  }
};

// 销毁播放器
const destroyPlayer = () => {
  // 清理HLS播放器
  if (hlsPlayer.value) {
    try {
      hlsPlayer.value.destroy();
    } catch (err) {
      console.warn('Error destroying HLS player:', err);
    }
    hlsPlayer.value = null;
  }

  // 清理FLV播放器
  if (flvPlayer.value) {
    try {
      flvPlayer.value.pause();
      flvPlayer.value.unload();
      flvPlayer.value.detachMediaElement();
      flvPlayer.value.destroy();
    } catch (err) {
      console.warn('Error destroying FLV player:', err);
    }
    flvPlayer.value = null;
  }

  // 清理原生video元素
  if (videoElement.value) {
    try {
      videoElement.value.pause();
      videoElement.value.src = '';
      videoElement.value.load();
    } catch (err) {
      console.warn('Error clearing video element:', err);
    }
  }
};

// 暂停播放
const pause = () => {
  if (videoElement.value) {
    videoElement.value.pause();
    isPlaying.value = false;
    emit('pause');
  }
};

// 继续播放
const play = () => {
  if (videoElement.value) {
    videoElement.value.play();
    isPlaying.value = true;
    emit('play');
  }
};

// 切换播放/暂停
const togglePlay = () => {
  if (isPlaying.value) {
    pause();
  } else {
    play();
  }
};

// 切换静音
const toggleMute = () => {
  if (videoElement.value) {
    videoElement.value.muted = !videoElement.value.muted;
    isMuted.value = videoElement.value.muted;
  }
};

// 切换全屏
const toggleFullscreen = () => {
  if (videoElement.value) {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      videoElement.value.requestFullscreen();
    }
  }
};

// 处理进度条变化
const handleProgressChange = (value: number) => {
  if (!videoElement.value) return;

  // 只有在有明确时长的情况下才允许拖拽
  if (isFinite(duration.value) && duration.value > 0) {
    const newTime = (value / 100) * duration.value;
    videoElement.value.currentTime = newTime;
    currentTime.value = newTime;
    console.log('进度条拖拽到:', newTime, '秒');
  } else {
    // 实时流不支持拖拽，恢复原来的进度
    progress.value = 0;
    console.log('实时流不支持进度拖拽');
  }
};

// 绘制检测框
const drawDetectionBoxes = () => {
  if (!props.enableOverlay || !overlayCanvas.value || !videoElement.value) {
    return;
  }

  const canvas = overlayCanvas.value;
  const video = videoElement.value;
  const ctx = canvas.getContext('2d');
  
  if (!ctx) return;

  // 设置canvas尺寸与视频一致
  canvas.width = video.videoWidth || video.clientWidth;
  canvas.height = video.videoHeight || video.clientHeight;

  // 清除之前的绘制
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 绘制检测框
  props.detectionBoxes.forEach((box) => {
    ctx.strokeStyle = box.color || '#00ff00';
    ctx.lineWidth = 2;
    ctx.strokeRect(box.x, box.y, box.width, box.height);

    // 绘制标签
    if (box.label) {
      ctx.fillStyle = box.color || '#00ff00';
      ctx.font = '14px Arial';
      ctx.fillText(
        `${box.label}${box.confidence ? ` (${(box.confidence * 100).toFixed(1)}%)` : ''}`,
        box.x,
        box.y - 5
      );
    }
  });
};

// 处理canvas点击
const handleCanvasClick = (event: MouseEvent) => {
  if (!overlayCanvas.value) return;

  const rect = overlayCanvas.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;

  emit('canvasClick', { x, y });
};

// 处理视频双击事件
const handleVideoDoubleClick = () => {
  console.log('视频双击事件触发');
  togglePlay();
};

// 监听src变化
watch(() => props.src, (newSrc) => {
  if (newSrc) {
    playVideo(newSrc);
  }
}, { immediate: true });

// 监听检测框变化
watch(() => props.detectionBoxes, () => {
  drawDetectionBoxes();
}, { deep: true });

// 监听视频加载完成事件
watch(videoElement, (video) => {
  if (video) {
    video.addEventListener('loadedmetadata', () => {
      drawDetectionBoxes();
      // 更新视频时长
      duration.value = video.duration || 0;
      console.log('视频元数据加载完成, duration:', duration.value);
    });

    video.addEventListener('timeupdate', () => {
      // 更新当前播放时间和进度
      currentTime.value = video.currentTime || 0;

      // 更新进度条（只有在有明确时长时才更新）
      if (isFinite(duration.value) && duration.value > 0) {
        progress.value = (currentTime.value / duration.value) * 100;
      } else {
        // 实时流没有明确进度，可以显示缓冲进度
        progress.value = 0;
      }
    });

    video.addEventListener('durationchange', () => {
      // 时长变化时更新
      duration.value = video.duration || 0;
      console.log('视频时长变化:', duration.value);
    });

    video.addEventListener('resize', drawDetectionBoxes);

    video.addEventListener('play', () => {
      isPlaying.value = true;
    });

    video.addEventListener('pause', () => {
      isPlaying.value = false;
    });
  }
});

onMounted(() => {
  // 初始化时间相关变量
  currentTime.value = 0;
  duration.value = 0;
  progress.value = 0;

  if (props.src) {
    playVideo(props.src);
  }
});

onUnmounted(() => {
  destroyPlayer();
});

// 暴露方法给父组件
defineExpose({
  playVideo,
  pause,
  play,
  destroyPlayer
});
</script>

<style lang="scss" scoped>
.video-player-container {
  position: relative;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  user-select: none; // 防止双击时选中文本
}

.video-player {
  display: block;
  background: #000;
  cursor: pointer;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: auto;
  z-index: 10;
}

.video-loading,
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-size: 14px;
  z-index: 20;
}

.video-error {
  color: #f56c6c;
}

.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.video-controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.7) 0%,
    transparent 20%,
    transparent 80%,
    rgba(0, 0, 0, 0.7) 100%
  );
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
  z-index: 15;

  &:hover {
    opacity: 1;
    pointer-events: auto;
  }
}

.video-player-container:hover .video-controls-overlay {
  opacity: 1;
  pointer-events: auto;
}

.controls-top {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;

  .video-title {
    font-size: 16px;
    font-weight: 500;
  }

  .video-time {
    font-size: 14px;
    font-family: monospace;
  }
}

.controls-bottom {
  position: absolute;
  bottom: 16px;
  left: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 12px;

  .control-buttons {
    display: flex;
    gap: 8px;

    :deep(.el-button) {
      background: rgba(0, 0, 0, 0.6);
      border: none;
      color: #fff;

      &:hover {
        background: rgba(0, 0, 0, 0.8);
      }
    }
  }

  .progress-bar {
    flex: 1;

    :deep(.el-slider) {
      .el-slider__runway {
        background-color: rgba(255, 255, 255, 0.3);
      }

      .el-slider__bar {
        background-color: var(--el-color-primary);
      }

      .el-slider__button {
        border-color: var(--el-color-primary);
      }

      // 禁用状态样式
      &.is-disabled {
        .el-slider__runway {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .el-slider__bar {
          background-color: rgba(255, 255, 255, 0.2);
        }

        .el-slider__button {
          border-color: rgba(255, 255, 255, 0.2);
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
