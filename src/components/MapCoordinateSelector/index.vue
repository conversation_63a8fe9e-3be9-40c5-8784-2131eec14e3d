<template>
  <div class="map-coordinate-selector">
    <div id="coordinateMap" class="map-container"></div>
    <div class="coordinate-info">
      <div class="info-content">
        <span class="label">当前坐标：</span>
        <span class="value">{{ selectedLat }}，{{ selectedLng }}</span>
      </div>
      <div class="actions">
        <el-button type="primary" size="small" @click="confirmCoordinate">确认选择</el-button>
        <el-button size="small" @click="cancel">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import Map from 'ol/Map';
import View from 'ol/View';
import TileLayer from 'ol/layer/Tile';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import XYZ from 'ol/source/XYZ';
import { Style, Icon, Fill, Stroke, Circle as CircleStyle } from 'ol/style';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import { defaults as defaultControls } from 'ol/control';
import { fromLonLat, toLonLat } from 'ol/proj';

const props = defineProps({
  initialLat: {
    type: Number,
    default: 30.5
  },
  initialLng: {
    type: Number,
    default: 121.9
  }
});

const emit = defineEmits(['confirm', 'cancel']);

const map = ref(null);
const selectedLat = ref(props.initialLat);
const selectedLng = ref(props.initialLng);
let markerFeature = null;
let vectorSource = null;

const initMap = () => {
  // 创建矢量数据源
  vectorSource = new VectorSource();

  // 创建矢量图层
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: new Style({
      image: new Icon({
        src: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(
          '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16">' +
          '<path fill="#409EFF" stroke="#FFFFFF" stroke-width="1" d="M8 0C4.6 0 2 2.6 2 6c0 4.1 5.2 9.6 5.4 9.8.4.4 1 .4 1.3 0C9.3 15 14 10.4 14 6c0-3.4-2.6-6-6-6zm0 8.5c-1.4 0-2.5-1.1-2.5-2.5S6.6 3.5 8 3.5s2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5z"/>' +
          '</svg>'
        ),
        anchor: [0.5, 1],
        scale: 1.5  // 调整图标大小
      })
    })
  });

  // 天地图图层 - 矢量底图
  const tileLayer = new TileLayer({
    source: new XYZ({
      url: 'http://t4.tianditu.com/DataServer?T=vec_w&tk=9dc4d6ee856d047e1a0caf2eec1c17f8&x={x}&y={y}&l={z}',
      crossOrigin: 'anonymous'
    })
  });

  // 天地图图层 - 标注图层
  const labelLayer = new TileLayer({
    source: new XYZ({
      url: 'http://t4.tianditu.com/DataServer?T=cva_w&tk=9dc4d6ee856d047e1a0caf2eec1c17f8&x={x}&y={y}&l={z}',
      crossOrigin: 'anonymous'
    })
  });

  // 创建地图
  map.value = new Map({
    target: 'coordinateMap',
    layers: [tileLayer, labelLayer, vectorLayer],
    view: new View({
      projection: 'EPSG:4326',
      center: [props.initialLng, props.initialLat],
      zoom: 10
    }),
    controls: defaultControls({
      zoom: true,
      rotate: false
    })
  });

  // 添加初始标记点
  addMarker(props.initialLng, props.initialLat);

  // 添加点击事件
  map.value.on('click', (evt) => {
    const coordinate = evt.coordinate;
    selectedLng.value = parseFloat(coordinate[0].toFixed(6));
    selectedLat.value = parseFloat(coordinate[1].toFixed(6));
    
    // 更新标记点位置
    updateMarkerPosition(selectedLng.value, selectedLat.value);
  });
};

// 添加标记点
const addMarker = (lng, lat) => {
  if (vectorSource) {
    markerFeature = new Feature({
      geometry: new Point([lng, lat])
    });
    markerFeature.setStyle(new Style({
      image: new Icon({
        src: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(
          '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16">' +
          '<path fill="#409EFF" stroke="#FFFFFF" stroke-width="1" d="M8 0C4.6 0 2 2.6 2 6c0 4.1 5.2 9.6 5.4 9.8.4.4 1 .4 1.3 0C9.3 15 14 10.4 14 6c0-3.4-2.6-6-6-6zm0 8.5c-1.4 0-2.5-1.1-2.5-2.5S6.6 3.5 8 3.5s2.5 1.1 2.5 2.5-1.1 2.5-2.5 2.5z"/>' +
          '</svg>'
        ),
        anchor: [0.5, 1],
        scale: 1.5  // 保持与图层样式一致的大小
      })
    }));
    vectorSource.addFeature(markerFeature);
  }
};

// 更新标记点位置
const updateMarkerPosition = (lng, lat) => {
  if (markerFeature) {
    vectorSource.removeFeature(markerFeature);
  }
  addMarker(lng, lat);
};

// 确认选择坐标
const confirmCoordinate = () => {
  emit('confirm', {
    lat: selectedLat.value,
    lng: selectedLng.value
  });
};

// 取消选择
const cancel = () => {
  emit('cancel');
};

onMounted(() => {
  initMap();
});

onUnmounted(() => {
  if (map.value) {
    map.value.setTarget(null);
    map.value = null;
  }
});
</script>

<style scoped lang="scss">
.map-coordinate-selector {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.map-container {
  flex-grow: 1;
  width: 100%;
  min-height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.coordinate-info {
  background: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #dcdfe6;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .info-content {
    .label {
      font-weight: 500;
      margin-right: 5px;
    }
    .value {
      color: #409EFF;
    }
  }

  .actions {
    display: flex;
    gap: 10px;
  }
}
</style> 