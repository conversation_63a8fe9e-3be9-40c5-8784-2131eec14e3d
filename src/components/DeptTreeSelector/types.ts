/**
 * 部门树选择器相关类型定义
 */

// 用户信息接口
export interface UserInfo {
  userId: string
  userName: string
  nickName?: string
  phonenumber?: string
  deptId: string
  deptName?: string
  type: 'user'
  selected?: boolean
}

// 部门信息接口
export interface DeptInfo {
  deptId: string
  deptName: string
  parentId: string
  type: 'dept'
  children?: TreeNode[]
  users?: UserInfo[]
  selected?: boolean
  indeterminate?: boolean // 半选状态
}

// 树节点联合类型
export type TreeNode = DeptInfo | UserInfo

// 树形数据构建选项
export interface TreeBuildOptions {
  userMap?: Record<string, UserInfo[]>
  includeUsers?: boolean
  defaultExpanded?: boolean
}

// 选中状态变化事件
export interface SelectionChangeEvent {
  selectedUsers: UserInfo[]
  selectedDepts: DeptInfo[]
  allSelected: (UserInfo | DeptInfo)[]
}

// 组件属性接口
export interface DeptTreeSelectorProps {
  // 部门数据
  deptData?: DeptInfo[]
  // 用户数据
  userData?: UserInfo[]
  // 已选中的用户ID列表
  selectedUserIds?: string[]
  // 已选中的部门ID列表
  selectedDeptIds?: string[]
  // 是否显示用户
  showUsers?: boolean
  // 是否支持多选
  multiple?: boolean
  // 是否只能选择用户（不能选择部门）
  userOnly?: boolean
  // 是否只读模式
  readonly?: boolean
  // 自定义样式类名
  customClass?: string
  // 树的高度
  height?: string | number
}

// 组件事件接口
export interface DeptTreeSelectorEmits {
  'selection-change': [event: SelectionChangeEvent]
  'user-select': [user: UserInfo, selected: boolean]
  'dept-select': [dept: DeptInfo, selected: boolean]
}

// 选中人员展示组件属性
export interface SelectedPersonnelProps {
  personnel: UserInfo[]
  readonly?: boolean
  showRemove?: boolean
  maxHeight?: string | number
}

// 选中人员展示组件事件
export interface SelectedPersonnelEmits {
  'remove-user': [user: UserInfo]
  'clear-all': []
}
