<template>
  <div class="dept-tree-selector">
    <el-row :gutter="20">
      <!-- 左侧：部门树选择区域 -->
      <el-col :lg="14" :xs="24">
        <div class="tree-selection-panel">
          <div class="panel-header">
            <h4>选择部门/人员</h4>
          </div>
          <div class="tree-container">
           <el-tree
              ref="deptTreeRef"
              :data="deptOptions"
              :props="treeProps"
              show-checkbox
              node-key="nodeKey"
              default-expand-all
              :render-content="renderContent"
              @check="handleCheck"
              @check-change="handleCheckChange"
              class="w-full"
              :disabled="readonly"
            />
          </div>
           
        </div>
      </el-col>
      
      <!-- 右侧：选中人员展示区域 -->
      <el-col :lg="10" :xs="24">
        <div class="selected-personnel-panel">
          <div class="panel-header">
            <h4>已选人员 ({{ selectedPersonnelCount }})</h4>
          </div>
          <div class="personnel-container">
            <div v-if="selectedPersonnelCount === 0" class="empty-state">
              <el-empty description="暂无选中人员" :image-size="80" />
            </div>
            <div v-else class="personnel-list">
              <div
                v-for="userKey in selectedPersonnel"
                :key="userKey"
                class="personnel-item"
              >
                <div class="user-info">
                  <div class="user-name">{{ getUserDisplayName(userKey) }}</div>
                  <div class="user-dept">{{ getUserDeptName(userKey) }}</div>
                  <div v-if="getUserPhone(userKey)" class="user-phone">{{ getUserPhone(userKey) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div v-if="deptOptions.length === 0" class="text-red-500 text-sm mt-1">
      暂无部门数据可选
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElTree } from 'element-plus'
import type {
  DeptTreeSelectorProps,
  DeptTreeSelectorEmits,
  DeptInfo,
  UserInfo,
  SelectionChangeEvent
} from './types'

// 组件属性
const props = withDefaults(defineProps<DeptTreeSelectorProps>(), {
  deptData: () => [],
  userData: () => [],
  selectedUserIds: () => [],
  selectedDeptIds: () => [],
  showUsers: true,
  multiple: true,
  userOnly: false,
  readonly: false,
  customClass: '',
  height: '400px'
})

// 组件事件
const emit = defineEmits<DeptTreeSelectorEmits>()

// 响应式数据
const deptTreeRef = ref()
const deptOptions = ref([])
const allUsers = ref([])
const selectedPersonnel = ref([])

// 计算已选择的人员数量
const selectedPersonnelCount = computed(() => {
  return selectedPersonnel.value.length
})

// 树形控件属性配置
const treeProps = {
  label: (data) => {
    if (data.type === 'user') {
      return data.nickName || data.userName
    } else {
      return data.deptName
    }
  },
  children: 'children'
}

// 自定义节点渲染
const renderContent = (h, { node, data }) => {
  // 如果是用户节点，渲染用户信息
  if (data.type === 'user') {
    return h('div', { class: 'custom-user-node' }, [
      h('el-checkbox', {
        modelValue: isUserSelected(data),
        'onUpdate:modelValue': (val) => handleUserCheck(data, val),
        onClick: (e) => e.stopPropagation(),
        size: 'small'
      }, [
        h('span', { class: 'user-label' }, [
          h('span', { class: 'user-name' }, data.nickName || data.userName),
          data.phonenumber ? h('span', { class: 'user-phone' }, ` (${data.phonenumber})`) : null
        ])
      ])
    ])
  }

  // 如果是部门节点，渲染部门信息
  return h('div', { class: 'custom-dept-node' }, [
    h('span', { class: 'dept-label' }, [
      h('span', { class: 'dept-name' }, node.label),
      h('span', { class: 'user-count' }, ` (${data.users?.length || 0}人)`)
    ])
  ])
}

// 处理部门选择
const handleCheck = (data, { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }) => {
  updateSelectedUsers()
}

// 处理部门选择状态变化
const handleCheckChange = (data, checked, indeterminate) => {
  updateParentNodeStatus(data)
}

// 处理用户复选框变化
const handleUserCheck = (user, checked) => {
  const userKey = `${user.deptId}-${user.userId}`
  if (checked) {
    if (!selectedPersonnel.value.includes(userKey)) {
      selectedPersonnel.value.push(userKey)
    }
  } else {
    selectedPersonnel.value = selectedPersonnel.value.filter(key => key !== userKey)
  }

  updateDeptNodeStatus(user.deptId)

  // 触发选择变化事件
  const selectedUserObjects = selectedPersonnel.value.map(userKey => {
    const [deptId, userId] = userKey.split('-')
    return findUserInfo(deptId, userId)
  }).filter(Boolean)

  const event = {
    selectedUsers: selectedUserObjects,
    selectedDepts: [],
    allSelected: selectedUserObjects
  }
  emit('selection-change', event)
  emit('user-select', user, checked)
}

// 检查用户是否被选中
const isUserSelected = (user) => {
  const userKey = `${user.deptId}-${user.userId}`
  return selectedPersonnel.value.includes(userKey)
}

// 更新选中的用户列表
const updateSelectedUsers = () => {
  const tree = deptTreeRef.value
  if (!tree) return

  const checkedNodes = tree.getCheckedNodes()
  const halfCheckedNodes = tree.getHalfCheckedNodes()

  const selectedUsers = []

  // 处理完全选中的节点
  checkedNodes.forEach(node => {
    if (node.type === 'user') {
      // 如果是用户节点，直接添加
      const userKey = `${node.deptId}-${node.userId}`
      if (!selectedUsers.includes(userKey)) {
        selectedUsers.push(userKey)
      }
    } else if (node.users) {
      // 如果是部门节点，添加其下所有用户
      node.users.forEach(user => {
        const userKey = `${user.deptId}-${user.userId}`
        if (!selectedUsers.includes(userKey)) {
          selectedUsers.push(userKey)
        }
      })
    }
  })

  // 处理半选中的节点（主要是部门节点）
  halfCheckedNodes.forEach(node => {
    if (node.users) {
      node.users.forEach(user => {
        const userKey = `${user.deptId}-${user.userId}`
        if (isUserSelected(user) && !selectedUsers.includes(userKey)) {
          selectedUsers.push(userKey)
        }
      })
    }
  })

  selectedPersonnel.value = selectedUsers

  // 触发选择变化事件
  const selectedUserObjects = selectedUsers.map(userKey => {
    const [deptId, userId] = userKey.split('-')
    return findUserInfo(deptId, userId)
  }).filter(Boolean)

  const event = {
    selectedUsers: selectedUserObjects,
    selectedDepts: [],
    allSelected: selectedUserObjects
  }
  emit('selection-change', event)
}

// 更新部门节点的选中状态
const updateDeptNodeStatus = (deptId) => {
  const tree = deptTreeRef.value
  if (!tree) return

  const node = tree.getNode(deptId)
  if (!node) return

  // 获取该部门下所有选中的用户
  const selectedUsers = selectedPersonnel.value.filter(key =>
    key.startsWith(`${deptId}-`)
  )

  // 获取该部门下所有用户
  const allUsers = node.data.users || []

  // 更新节点状态
  if (selectedUsers.length === allUsers.length && allUsers.length > 0) {
    tree.setChecked(node, true, false)
  } else if (selectedUsers.length > 0) {
    tree.setChecked(node, false, false)
    node.indeterminate = true
  } else {
    tree.setChecked(node, false, false)
  }

  // 递归更新父节点状态
  updateParentNodeStatus(node.data)
}

// 更新父节点状态
const updateParentNodeStatus = (data) => {
  const tree = deptTreeRef.value
  if (!tree) return

  const node = tree.getNode(data.deptId)
  if (!node || !node.parent || node.parent.key === 0) return

  const parentNode = node.parent
  const siblings = parentNode.childNodes

  // 检查所有兄弟节点的状态
  const allChecked = siblings.every(sibling => sibling.checked)
  const someChecked = siblings.some(sibling => sibling.checked || sibling.indeterminate)

  if (allChecked) {
    tree.setChecked(parentNode, true, false)
  } else {
    tree.setChecked(parentNode, false, false)
    parentNode.indeterminate = someChecked
  }

  // 递归更新上级节点
  updateParentNodeStatus(parentNode.data)
}

// 获取用户显示名称
const getUserDisplayName = (userKey) => {
  if (!userKey.includes('-')) return userKey
  const [deptId, userId] = userKey.split('-')
  const user = findUserInfo(deptId, userId)
  return user ? (user.nickName || user.userName) : '未知用户'
}

// 获取用户部门名称
const getUserDeptName = (userKey) => {
  if (!userKey.includes('-')) return ''
  const [deptId, userId] = userKey.split('-')
  const user = findUserInfo(deptId, userId)
  return user ? user.deptName : '未知部门'
}

// 获取用户电话
const getUserPhone = (userKey) => {
  if (!userKey.includes('-')) return ''
  const [deptId, userId] = userKey.split('-')
  const user = findUserInfo(deptId, userId)
  return user ? user.phonenumber : ''
}

// 根据部门ID和用户ID查找用户信息
const findUserInfo = (deptId, userId) => {
  // 从allUsers中查找用户信息
  const user = allUsers.value.find(u =>
    String(u.deptId) === String(deptId) && String(u.userId) === String(userId)
  )
  return user || null
}

// 清空所有选择
function clearAllSelection() {
  if (props.readonly) return
  
  selectedPersonnel.value = []
  
  // 清空树的选中状态
  const tree = deptTreeRef.value
  if (tree) {
    tree.setCheckedKeys([])
  }
  
  // 触发选择变化事件
  const event = {
    selectedUsers: [],
    selectedDepts: [],
    allSelected: []
  }
  emit('selection-change', event)
}

// 初始化树形数据
function initTreeData() {
  if (props.deptData && props.userData) {
    // 保存所有用户数据以便查找
    allUsers.value = props.userData

    // 构建用户映射表
    const userMap = {}
    props.userData.forEach(user => {
      const deptId = String(user.deptId)
      if (!userMap[deptId]) {
        userMap[deptId] = []
      }
      userMap[deptId].push({
        userId: String(user.userId),
        userName: user.userName,
        nickName: user.nickName,
        phonenumber: user.phonenumber,
        deptId: deptId,
        deptName: user.deptName,
        type: 'user',
        selected: false
      })
    })

    // 标准化部门数据并构建树形结构
    const normalizedDepts = props.deptData.map(item => ({
      ...item,
      deptId: String(item.deptId),
      parentId: String(item.parentId),
      type: 'dept',
      users: userMap[String(item.deptId)] || [],
      children: [],
      selected: false,
      indeterminate: false
    }))

    // 构建部门映射表
    const deptMap = {}
    normalizedDepts.forEach(dept => {
      deptMap[dept.deptId] = dept
    })

    // 构建树形结构
    const tree = []
    normalizedDepts.forEach(dept => {
      const parentId = dept.parentId
      if (deptMap[parentId]) {
        deptMap[parentId].children.push(dept)
      } else if (parentId === '0' || !parentId) {
        tree.push(dept)
      }
    })

    // 将用户作为部门的子节点添加到children中
    if (props.showUsers) {
      function addUsersToTree(nodes) {
        nodes.forEach(node => {
          if (node.users && node.users.length > 0) {
            // 将用户添加到children数组中
            node.users.forEach(user => {
              node.children.push(user)
            })
          }
          if (node.children) {
            // 递归处理子部门
            const deptChildren = node.children.filter(child => child.type === 'dept')
            addUsersToTree(deptChildren)
          }
        })
      }
      addUsersToTree(tree)
    }

    deptOptions.value = tree

    // 设置初始选中状态
    if (props.selectedUserIds.length > 0) {
      selectedPersonnel.value = props.selectedUserIds.map(userId => {
        const user = allUsers.value.find(u => String(u.userId) === String(userId))
        return user ? `${user.deptId}-${user.userId}` : ''
      }).filter(Boolean)
    }
  }
}

// 监听数据变化
watch(
  () => [props.deptData, props.userData],
  () => {
    initTreeData()
  },
  { immediate: true, deep: true }
)

watch(
  () => props.selectedUserIds,
  (newIds) => {
    if (newIds && newIds.length > 0) {
      selectedPersonnel.value = newIds.map(userId => {
        const user = allUsers.value.find(u => String(u.userId) === String(userId))
        return user ? `${user.deptId}-${user.userId}` : ''
      }).filter(Boolean)
    }
  },
  { immediate: true }
)

// 移除这个 watch，避免递归更新
// 改为在具体的操作方法中触发事件

// 暴露方法
defineExpose({
  getSelectedUsers: () => {
    return selectedPersonnel.value.map(userKey => {
      const [deptId, userId] = userKey.split('-')
      return findUserInfo(deptId, userId)
    }).filter(Boolean)
  },
  clearSelection: clearAllSelection,
  setSelectedUserIds: (userIds) => {
    selectedPersonnel.value = userIds.map(userId => {
      const user = allUsers.value.find(u => String(u.userId) === String(userId))
      return user ? `${user.deptId}-${user.userId}` : ''
    }).filter(Boolean)
  }
})

onMounted(() => {
  initTreeData()
})
onUnmounted(() => {
  clearAllSelection()
})
</script>

<style scoped lang="scss">
.dept-tree-selector {
  width: 100%;
  .w-full {
    width: 100%;
    height: 450px;
  }

  // 两栏布局样式
  .tree-selection-panel, .selected-personnel-panel {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 16px;
    background: var(--el-bg-color-overlay);
    height: 450px;
    width: 100%;

    .panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--el-border-color-light);

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .tree-container, .personnel-container {
      height: calc(100% - 30px);
      overflow-y: auto;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
      padding: 8px;
      background: var(--el-fill-color-blank);
    }
  }

  .personnel-list {
    .personnel-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 10px;
      margin-bottom: 4px;
      background: var(--el-bg-color-overlay);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary-light-7);
        background: var(--el-color-primary-light-9);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .user-info {
        flex: 1;

        .user-name {
          font-size: 13px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 1px;
          line-height: 1.2;
        }

        .user-dept {
          font-size: 11px;
          color: var(--el-text-color-regular);
          margin-bottom: 1px;
          line-height: 1.2;
        }

        .user-phone {
          font-size: 11px;
          color: var(--el-text-color-secondary);
          line-height: 1.2;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
  }

  // 自定义树节点样式
  :deep(.el-tree) {
    height: auto;
    .el-tree-node__content {
      height: auto;
      min-height: 28px;
    }
  }

  .custom-dept-node {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-right: 8px;
    width: 100%;

    .dept-label {
      display: flex;
      align-items: center;
      gap: 8px;

      .dept-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .user-count {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .custom-user-node {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-right: 8px;
    width: 100%;
    margin-left: 20px;

    .user-label {
      display: flex;
      align-items: center;
      gap: 4px;

      .user-name {
        color: var(--el-text-color-primary);
      }

      .user-phone {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dept-tree-selector {
    .tree-selection-panel,
    .selected-personnel-panel {
      margin-bottom: 16px;
    }
  }
}
</style>
