<template>
  <div v-if="isDevelopment && isVisible" class="performance-debug-panel">
    <div class="panel-header">
      <h3>性能监控面板</h3>
      <div class="panel-controls">
        <el-button size="small" @click="toggleMonitoring">
          {{ isMonitoring ? '停止监控' : '启动监控' }}
        </el-button>
        <el-button size="small" @click="clearHistory">清除历史</el-button>
        <el-button size="small" @click="exportData">导出数据</el-button>
        <el-button size="small" @click="close">关闭</el-button>
      </div>
    </div>

    <div class="panel-content">
      <!-- 配置状态 -->
      <div class="section">
        <h4>配置状态</h4>
        <div class="config-info">
          <div>模式: {{ currentMode }}</div>
          <div>监控: {{ isMonitoring ? '启用' : '禁用' }}</div>
          <div>采样率: {{ (sampleRate * 100).toFixed(0) }}%</div>
          <div>警告数量: {{ alertCount }}</div>
        </div>
        <div class="mode-switcher">
          <el-radio-group v-model="selectedMode" @change="switchMode" size="small">
            <el-radio-button label="high_performance">高性能</el-radio-button>
            <el-radio-button label="balanced">平衡</el-radio-button>
            <el-radio-button label="power_save">省电</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 最新警告 -->
      <div class="section">
        <h4>最新警告 ({{ recentAlerts.length }})</h4>
        <div v-if="recentAlerts.length === 0" class="no-alerts">无警告</div>
        <div v-else class="alerts-list">
          <div 
            v-for="alert in recentAlerts" 
            :key="alert.timestamp"
            :class="['alert-item', alert.level]"
          >
            <span class="alert-type">[{{ alert.type.toUpperCase() }}]</span>
            <span class="alert-message">{{ alert.message }}</span>
            <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
          </div>
        </div>
      </div>

      <!-- 最新指标 -->
      <div class="section">
        <h4>最新指标 ({{ latestMetrics.length }})</h4>
        <div class="metrics-list">
          <div 
            v-for="metric in latestMetrics" 
            :key="metric.timestamp"
            class="metric-item"
          >
            <span class="metric-time">[{{ formatTime(metric.timestamp) }}]</span>
            <span v-if="metric.memoryUsage" class="metric-memory">
              内存: {{ metric.memoryUsage.used }}MB ({{ metric.memoryUsage.percentage }}%)
            </span>
            <span v-if="metric.apiMetrics" class="metric-api">
              API: {{ metric.apiMetrics.duration.toFixed(0) }}ms
            </span>
            <!-- Canvas指标显示已移除 - 避免与OpenLayers冲突 -->
            <span v-if="metric.interactionMetrics" class="metric-interaction">
              交互: {{ metric.interactionMetrics.duration.toFixed(0) }}ms
            </span>
          </div>
        </div>
      </div>

      <!-- 实时统计 -->
      <div class="section">
        <h4>实时统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">总指标数</div>
            <div class="stat-value">{{ totalMetrics }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">总警告数</div>
            <div class="stat-value">{{ totalAlerts }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">最后更新</div>
            <div class="stat-value">{{ formatTime(lastUpdate) }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">运行时间</div>
            <div class="stat-value">{{ formatDuration(uptime) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { ElButton, ElRadioGroup, ElRadioButton, ElMessage } from 'element-plus';
import { performanceMonitor } from '@/utils/performanceMonitor';
import { performanceConfig, PerformanceMode } from '@/config/performance';

// Props
interface Props {
  visible?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
});

// Emits
const emit = defineEmits<{
  close: [];
  export: [data: any];
}>();

// 响应式数据
const isVisible = ref(props.visible);
const isDevelopment = import.meta.env.VITE_APP_ENV === 'development';
const selectedMode = ref<PerformanceMode>(performanceConfig.getCurrentMode());
const startTime = ref(Date.now());

// 性能数据
const metrics = ref(performanceMonitor.getMetrics());
const alerts = ref(performanceMonitor.getAlerts());
const updateTimer = ref<number | null>(null);

// 计算属性
const isMonitoring = computed(() => {
  const config = performanceConfig.getMonitorConfig();
  return config.enabled;
});

const currentMode = computed(() => performanceConfig.getCurrentMode());

const sampleRate = computed(() => {
  const config = performanceConfig.getMonitorConfig();
  return config.sampleRate;
});

const alertCount = computed(() => alerts.value.length);

const recentAlerts = computed(() => alerts.value.slice(-5));

const latestMetrics = computed(() => metrics.value.slice(-10));

const totalMetrics = computed(() => metrics.value.length);

const totalAlerts = computed(() => alerts.value.length);

const lastUpdate = computed(() => {
  if (metrics.value.length === 0) return 0;
  return Math.max(...metrics.value.map(m => m.timestamp));
});

const uptime = computed(() => Date.now() - startTime.value);

// 方法
const toggleMonitoring = () => {
  if (isMonitoring.value) {
    performanceMonitor.stop();
    ElMessage.success('性能监控已停止');
  } else {
    performanceMonitor.start();
    ElMessage.success('性能监控已启动');
  }
};

const switchMode = (mode: PerformanceMode) => {
  performanceConfig.setMode(mode);
  ElMessage.success(`性能模式已切换至: ${mode}`);
};

const clearHistory = () => {
  performanceMonitor.clearHistory();
  metrics.value = [];
  alerts.value = [];
  ElMessage.success('历史数据已清除');
};

const exportData = () => {
  const data = {
    metrics: metrics.value,
    alerts: alerts.value,
    config: {
      monitor: performanceConfig.getMonitorConfig(),
      optimization: performanceConfig.getOptimizationConfig()
    },
    timestamp: new Date().toISOString()
  };
  
  emit('export', data);
  
  // 同时下载文件
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `performance-debug-${Date.now()}.json`;
  a.click();
  URL.revokeObjectURL(url);
  
  ElMessage.success('性能数据已导出');
};

const close = () => {
  isVisible.value = false;
  emit('close');
};

const formatTime = (timestamp: number): string => {
  if (!timestamp) return '--';
  return new Date(timestamp).toLocaleTimeString();
};

const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

const updateData = () => {
  metrics.value = performanceMonitor.getMetrics();
  alerts.value = performanceMonitor.getAlerts();
};

// 生命周期
onMounted(() => {
  // 订阅性能事件
  performanceMonitor.subscribe('metric', updateData);
  performanceMonitor.subscribe('alert', updateData);
  
  // 定时更新数据
  updateTimer.value = window.setInterval(updateData, 2000);
  
  // 初始化数据
  updateData();
});

onBeforeUnmount(() => {
  if (updateTimer.value) {
    clearInterval(updateTimer.value);
    updateTimer.value = null;
  }
});

// 监听props变化
watch(() => props.visible, (newVal) => {
  isVisible.value = newVal;
});
</script>

<style scoped>
.performance-debug-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 450px;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.95);
  color: white;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  z-index: 10000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 12px;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
}

.panel-header h3 {
  margin: 0;
  color: #4CAF50;
  font-size: 14px;
}

.panel-controls {
  display: flex;
  gap: 8px;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.section {
  margin-bottom: 16px;
}

.section h4 {
  margin: 0 0 8px 0;
  color: #2196F3;
  font-size: 12px;
  border-bottom: 1px solid #333;
  padding-bottom: 4px;
}

.config-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
  margin-bottom: 8px;
}

.mode-switcher {
  margin-top: 8px;
}

.no-alerts {
  color: #4CAF50;
  font-style: italic;
}

.alerts-list {
  max-height: 120px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  gap: 8px;
  margin: 2px 0;
  padding: 2px 4px;
  border-radius: 3px;
}

.alert-item.warning {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
}

.alert-item.error {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

.alert-type {
  font-weight: bold;
  min-width: 60px;
}

.alert-message {
  flex: 1;
}

.alert-time {
  font-size: 10px;
  opacity: 0.7;
}

.metrics-list {
  max-height: 150px;
  overflow-y: auto;
}

.metric-item {
  margin: 2px 0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.metric-time {
  color: #888;
  min-width: 80px;
}

.metric-memory { color: #9C27B0; }
.metric-api { color: #FF9800; }
.metric-canvas { color: #4CAF50; }
.metric-interaction { color: #2196F3; }

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.05);
  padding: 8px;
  border-radius: 4px;
  text-align: center;
}

.stat-label {
  font-size: 10px;
  color: #888;
  margin-bottom: 4px;
}

.stat-value {
  font-weight: bold;
  color: #4CAF50;
}
</style>
