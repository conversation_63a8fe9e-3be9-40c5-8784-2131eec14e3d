<!-- 
  优化Element Plus插槽渲染，解决"Slot invoked outside of the render function"警告
  该组件用于替换Element Plus组件中可能导致警告的插槽调用方式
-->
<template>
  <slot></slot>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, useSlots } from 'vue';

// 导出插槽，以便在组件内部使用
const slots = useSlots();

// 定义组件属性
defineProps({
  // 组件可以接收任何属性，并将它们传递给内部插槽
  content: {
    type: [String, Number, Object, Array],
    default: null
  }
});

// 定义事件
defineEmits(['update', 'change']);
</script>

<style scoped>
/* 组件不需要额外样式 */
</style> 