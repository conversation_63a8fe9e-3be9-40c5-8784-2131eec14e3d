.icon-text {
    margin-left: 15px;
}

.icon-shendufengxianfenxi {
    color: #9b9fae;
}

.icon-anchor-full {
    color: #9b9fae;
    margin-right: 6px;
}

.icon-yichangshuju<PERSON>xun {
    color: #9b9fae;
    margin-right: 6px;
}

.admT {
    width: 100%;
    height: calc(100vh - 50px);
}

.admF {
    width: 100%;
    height: 100vh;
}

.el-header {
    width: 100%;
    background-color: #454a6c;
    position: absolute;
    top: 0;
    z-index: 999;
    height: 60px;
}

.head {
    width: 100%;
    background-color: #3c4167;
    position: absolute;
    top: 0;
    z-index: 1000;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.headleft {
    width: 40%;
    height: 60px;
    /* background: #457ae6;
  border: 1px solid #308ff1; */
    /* -webkit-clip-path: polygon(0 0, 100% 0, 90% 100%, 0% 100%);
  clip-path: polygon(0 0, 100% 0, 90% 100%, 0% 100%); */
    display: flex;
    align-items: center;
    font-size: 25px;
    font-family: SourceHanSansCN-Heavy, SourceHanSansCN;
    font-weight: 800;
    color: #ffffff;
    letter-spacing: 1px;
}

.headMain {
    width: 1186px;
    height: 60px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    font-size: 15px;
    font-family: SourceHanSansCN-Medium, SourceHanSansCN;
    font-weight: 500;
    color: #ffffff;
    justify-content: space-between;
}

.headMain_item {
    background: url("@/assets/images/tab.png") no-repeat;
    background-size: 100% 100%;
    min-width: 121px;
    height: 38px;
    padding-bottom: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: nowrap;
}

.headMainContent {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.headRight {
    width: 312px;
    height: 60px;
    padding: 0 0 0 40px;
    /* background: #457ae6;
  border: 1px solid #308ff1; */
    -webkit-clip-path: polygon(10% 0, 100% 0, 100% 100%, 0 100%, 0 100%);
    clip-path: polygon(10% 0, 100% 0, 100% 100%, 0 100%, 0 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: #ffffff;
}

.head-inp {
    width: 230px;
    height: 32px;
    border-radius: 30px;
    overflow: hidden;
    background-color: #333758;
    display: flex;
    align-items: center;
    color: #ffffff;
}

.head-inp input::-webkit-input-placeholder {
    /* WebKit browsers，webkit内核浏览器 */
    color: #c4c4c4;
    font-size: 14px;
}

.head-inp-img {
    width: 16px;
    height: 16px;
    margin: 0 10px;
}

.el-aside {
    background-color: #29354e;
    position: absolute;
    top: 60px;
    height: 100%;
    z-index: 999;
}

.el-main {
    background-color: #eeeeee;
    margin-top: 60px;
    padding: 0;
    /* margin-left: 180px; */
}

.navList {
    /* z-index: 999; */
    width: 200px !important;
    margin-top: 60px;
    background-color: #3c4167;
}

.layout {
    /* height: calc(100vh - 50px); */
    position: relative;
    font-size: 16px;
    display: flex;
    height: 100vh !important;
    /* justify-content:space-between; */
}

.layout_whole {
    height: 100vh;
    position: relative;
}

.flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    cursor: pointer;
    margin-right: 20px;
}

.head-txt {
    font-size: 26px;
    font-weight: bold;
    font-family: PangMenZhengDao;
    color: #ffffff;
    margin: 0 30px 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.head-txt1 {
    font-size: 14px;
    font-family: PangMenZhengDao;
    color: #ffffff;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.icon-class {
    color: #ffffff;
    font-size: 14px;
    margin: 0 5px;
}

.el-dropdown-link {
    cursor: pointer;
    color: #409eff;
}

.el-icon-arrow-down {
    font-size: 12px;
}

.last-message-box {
    width: 100%;
    position: fixed;
    bottom: 50px;
    left: 0;
    z-index: 999;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.last-message {
    padding: 5px 20px;
    background: rgba(0, 0, 0, 0.5);
    color: #ffffff;
    font-size: 14px;
    border-radius: 20px;
}

.icon-hover :hover {
    color: #409eff;
}

.infoWindow {
    padding: 10px;
    position: fixed;
    /* top: 50%;
  left: 50%; */
    width: auto;
    min-width: 280px;
    background-color: rgba(31, 63, 130, 0.8);
    z-index: 999;
}

.infoWindow_title {
    height: 30px;
    font-weight: bold;
    color: #ffffff;
    border-bottom: 2px solid #5fd3fc;
}

.infoWindow_text {
    height: 24px;
    line-height: 24px;
    display: flex;
    align-items: center;
    color: #5fd3fc;
    font-size: 14px;
}

.infoWindow_text>div:first-child {
    width: 80px;
    color: #ffffff;
}

/* ::v-deep .el-tabs--left.el-tabs--border-card .el-tabs__header.is-left{
  background: #1c77eb;
} */

/* 左侧导航栏 */
.mainNav {
    position: absolute;
    top: 60px;
    left: 0;
    z-index: 999;
    height: 100%;
}

.mainNav .el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 300px;
    padding: 0 20px;
    background-color: #454a6c;
    /* height: 100%; */
    /* min-height: 400px; */
}

.mainNav .el-menu-vertical-demo {
    background-color: #454a6c;
    height: 100%;
    /* padding: 10px 0; */
}

.mainNavSwi {
    position: absolute;
    top: 50%;
    right: -20px;
    width: 20px;
    height: 40px;
    background-color: gainsboro;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mainNavSwi:hover {
    cursor: pointer;
    background-color: grey;
}

.mainNav_s_icon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    position: relative;
}

.mainNav_s_icon:hover {
    cursor: pointer;
}

.mainNavInfo {
    position: absolute;
    min-width: 150px;
    padding: 10px;
    top: 0;
    left: 60px;
    background-color: #454a6c;
    color: #ffffff;
}

.mainNavInfo_list {
    height: 30px;
    line-height: 30px;
}

.mainNavInfo_list:hover {
    cursor: pointer;
}

.mainNavInfo_list>span {
    color: #ffffff;
}

.titleInp>input {
    background-color: #ffffff !important;
    color: #000;
}

.leftTabs {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1000;
}

.el-collapse .el-collapse-item {
    background-color: #454a6c !important;
    color: #ffffff;
}

.el-collapse-item .el-collapse-item__header {
    background-color: #454a6c !important;
    color: #ffffff;
}

.el-collapse-item .el-collapse-item__wrap {
    background-color: #454a6c !important;
    color: #ffffff;
}

.eTabs {
    width: 380px;
    height: 100%;
}

.leftBox {
    position: absolute;
    top: 60px;
    left: 0;
    z-index: 1000;
}
.el-menu {
    list-style: none;
    position: relative;
    margin: 0;
    padding-left: 0;
    border: none;
}

.el-menu-vertical-demo {
    width: 200px !important;
}

::v-deep .el-menu-item-group__title {
    padding: 0;
}
.shipInfo {
    display: flex;
    flex-wrap: wrap;
    color: #ffffff;
    margin-bottom: 10px;
}

.shipInfo_item {
    flex: 0 0 50%;
    margin-bottom: 20px;
}

.warnType {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ffffff;
    margin-bottom: 20px;
}

.elwidth {
    width: 150px;
}

.elinp {
    width: 450px;
}

.describe {
    width: 100%;
    height: 180px;
    display: flex;
    color: #ffffff;
    margin-bottom: 20px;
}

.describeText {
    width: 450px;
    background-color: #253a5e;
}

.textArea {
    width: 100%;
    height: 180px;
    background-color: transparent;
    border: none;
    outline: none;
    color: #ffffff;
    line-height: 30px;
    padding: 10px 20px;
}

.measure {
    margin-bottom: 20px;
    color: #ffffff;
    display: flex;
    align-items: center;
}

.title {
    width: 80px;
}

.diaBox {
    height: 570px;
    overflow: auto;
}

::-webkit-scrollbar {
    width: 4px;
    height: 10px;
    background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #f5f5f5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #4376ec;
}

.mmsi {}

.textmmsi {}

.aper {}