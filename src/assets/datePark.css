::v-deep .el-range-input {
  background-color: transparent;
  color: #333333;
}

::v-deep .el-range-input::placeholder {
  background-color: transparent;
  color: #606266;
}

::v-deep .el-range-separator {
  color: #333333;
}


.el-date-picker.has-sidebar.has-time {
  background: #ffffff !important;
  color: #333333;
  border: 1px solid #e4e7ed;
}

.el-date-picker__header-label {
  color: #303133;
}

.el-date-table th {
  color: #606266;
}

/* .el-icon-d-arrow-left:before {
  color: #fff;
}

.el-icon-arrow-left:before {
  color: #fff;
}

.el-icon-arrow-right:before {
  color: #fff;
}

.el-icon-d-arrow-right:before {
  color: #fff;
} */

::v-deep .el-picker-panel {
  background-color: #ffffff !important;
  border: 1px solid #e4e7ed;
  color: #333333 !important;
  position: absolute !important;
  top: 40px !important;
  left: 0 !important;
}

::v-deep .el-picker-panel tr th {
  color: #333333 !important;
}

::v-deep .el-picker-panel .in-range div {
  background-color: rgba(64, 158, 255, 0.1) !important;
}


::v-deep .el-date-table td.in-range div,
.el-date-table td.in-range div:hover,
.el-date-table.is-week-mode .el-date-table__row.current div,
.el-date-table.is-week-mode .el-date-table__row:hover div {
  background-color: #f5f7fa;
}


::v-deep .el-textarea__inner {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #dcdfe6;
}

::v-deep .el-input__inner {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #dcdfe6;
}


::v-deep .el-input__suffix-inner {
  position: absolute;
  left: -25px;
}


::v-deep .el-select__caret.el-input__icon.el-icon-arrow-up {
  /* line-height: 40px; */
}

::v-deep .el-input__inner::placeholder {
  color: #c0c4cc;
  font-size: 14px;
}

.el-icon-arrow-up:before {
  color: #606266;
}

::v-deep .el-select--medium {
  background: #ffffff;
}

::v-deep .el-input--suffix {
  height: 100%;
}

/* 去除点击时候的淡蓝色边框 */
::v-deep  .el-input .el-input__inner {
  border-color: #dcdfe6 !important;
  background-color: #ffffff;
}

::v-deep .el-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e4e7ed;
  position: absolute !important;
  top: 30px !important;
  left: 0 !important;
}

::v-deep .el-select-dropdown__item {
  color: #333333 !important;
}

::v-deep .el-select-dropdown__item:hover {
  background-color: rgba(64, 158, 255, 0.1) !important;
  color: #333333 !important;
}

::v-deep .el-select-dropdown .hover {
  background-color: rgba(64, 158, 255, 0.1) !important;
  color: #333333 !important;
}

::v-deep .el-table__header th {
  border: 1px solid #ebeef5;
}

::v-deep .el-table__fixed-right {
  border: none !important;
}

::v-deep .el-table,
.el-table__expanded-cell {
  background-color: #ffffff !important;
  border-color: #ebeef5;
}

::v-deep  .el-table__row {
  background-color: #ffffff;
  color: #303133;
}

/* 表格-表头 */
::v-deep .el-table th,
.el-table tr {
  background-color: #f5f7fa !important;
  color: #303133;
}

::v-deep .has-gutter>tr>th {
  /* border: none !important; */

}

::v-deep .el-table__body td {
  border: 1px solid #ebeef5;
}

/* el-table 修改滚动条样式开始 */

::v-deep  .el-table__body-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* 轨道颜色 */
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

/* //滚动条的滑块 */

::v-deep  .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #c0c4cc !important;
}


::v-deep .el-table__body tr:hover>td {
  background-color: rgba(64, 158, 255, 0.1) !important;
  color: #409EFF;
}

::v-deep .el-tabs {
  background-color: #ffffff;
  color: #303133;
}

::v-deep .el-tabs .el-tabs__header {
  background-color: #f5f7fa;
  color: #303133;
}

::v-deep .el-tabs .el-tabs__nav-scroll {
  background-color: #f5f7fa;
  color: #303133;
}

::v-deep .el-tabs__item {
  background-color: transparent !important;
  color: #303133 !important;
}

::v-deep .is-active {
  background-color: #ffffff !important;
  color: #409EFF !important;
}

::v-deep .el-collapse {
  border: none;
  background-color: transparent;
}

::v-deep .el-collapse .el-collapse-item {
  margin-bottom: 5px;
}

::v-deep .el-collapse-item .el-collapse-item__header {
  background-color: transparent;
  border: none;
  padding: 0;
  height: auto;
}

::v-deep .el-collapse-item .el-collapse-item__wrap {
  background-color: transparent;
  border: none;
}

::v-deep .el-checkbox {
  color: #333333;
}

::v-deep .el-tree {
  background-color: transparent;
  color: #333333;
}

/* 改变被点击节点背景颜色，字体颜色 */
::v-deep .el-tree-node:focus>.el-tree-node__content {
  background-color: rgba(64, 158, 255, 0.1);
}

::v-deep .el-tree-node:focus>.el-tree-node__content .el-tree-node__label {
  color: #409EFF;
}
/* 改变被鼠标悬浮节点背景颜色，字体颜色 */
::v-deep .el-tree-node:hover>.el-tree-node__content  {
  background-color: rgba(64, 158, 255, 0.1);
}
::v-deep .el-tree-node:hover>.el-tree-node__content .el-tree-node__label {
  color: #409EFF;
}
/*节点失焦时的背景颜色*/
::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: rgba(64, 158, 255, 0.1);
}


.el-menu {
  background-color: transparent;
  border-right: 1px solid #e4e7ed;
}



::v-deep .el-dropdown-menu,
.el-popper,
.el-dropdown-menu--medium {
  background-color: #ffffff;
  border: 1px solid #e4e7ed;
}

::v-deep .el-dropdown-menu__item {
  color: #333333;
}

::v-deep .el-dropdown-menu__item:hover {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

::v-deep .el-pagination__jump {
  color: #333333;
}

::v-deep .el-pagination__total {
  color: #333333;
}

/* 分页：中间数字样式 */
::v-deep  .el-pager li {
  background-color: #ffffff;
  color: #333333;
}

/* 二个小耳朵样式 */
::v-deep  .el-pagination .btn-prev {
  background-color: #ffffff;
  color: #333333;
}

::v-deep  .el-pagination .btn-next {
  background-color: #ffffff;
  color: #333333;
}

::v-deep  .el-dialog {
  background-color: #ffffff;
}

::v-deep .el-dialog__title {
  color: #303133;
}