{"name": "船类型", "shipTypes": [{"id": 0, "label": "客船", "color": "#0000FF", "children": [{"name": "Passenger", "valueList": [60, 61, 62, 63, 64, 65, 66, 67, 68, 69], "label": "客船"}]}, {"id": 1, "label": "货船", "color": "#F1F153", "children": [{"name": "Cargo", "valueList": [70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "label": "货船"}]}, {"id": 2, "label": "油船", "color": "#FF0000", "children": [{"name": "Tanker", "valueList": [80, 81, 82, 83, 84, 85, 86, 87, 88, 89], "label": "油船"}]}, {"id": 3, "label": "渔船", "color": "#008200", "children": [{"name": "Fishing", "valueList": [30], "label": "渔船"}]}, {"id": 4, "label": "工程船", "color": "#FF9900", "children": [{"name": "DredgingOrUnderwaterOps", "valueList": [33], "label": "挖泥或水下作业船"}, {"name": "DivingOps", "valueList": [34], "label": "潜水作业船"}, {"name": "MilitaryOps", "valueList": [35], "label": "军事作业船"}]}, {"id": 5, "label": "其他", "color": "#AFCCB1", "children": [{"name": "WingInGround", "valueList": [20, 21, 22, 23, 24], "label": "地效翼船"}, {"name": "Towing", "valueList": [31], "label": "拖带船"}, {"name": "LargeTowing", "valueList": [32], "label": "大型拖带船"}, {"name": "Sailing", "valueList": [36], "label": "帆船"}, {"name": "PleasureCraft", "valueList": [37], "label": "休闲游艇"}, {"name": "HighSpeedCraft", "valueList": [40, 41, 42, 43, 44], "label": "高速船"}, {"name": "PilotVessel", "valueList": [50], "label": "引航船"}, {"name": "SearchAndRescueVessel", "valueList": [51], "label": "搜救船"}, {"name": "Tug", "valueList": [52], "label": "拖轮"}, {"name": "Port<PERSON><PERSON>", "valueList": [53], "label": "港口供应船"}, {"name": "AntiPollutionEquipment", "valueList": [54], "label": "有防污染设施或设备的船舶"}, {"name": "LawEnforcement", "valueList": [55], "label": "法律强制船"}, {"name": "SpareLocalVessel1", "valueList": [56, 57], "label": "备用本地船只"}, {"name": "MedicalTransport", "valueList": [58], "label": "医务运输船"}, {"name": "ShipAccordingToRRResolutionNo18", "valueList": [59], "label": "按18号决议规定的船舶"}, {"name": "Other", "valueList": [90, 91, 92, 93, 94, 95, 96, 97, 98, 99], "label": "其他"}]}, {"id": 6, "label": "未知", "color": "#9C9A9C", "children": [{"name": "NotAvailable", "valueList": [0], "label": "未知"}]}]}