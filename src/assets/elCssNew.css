::v-deep .el-dialog__title {
  color: #fff;
}

::v-deep .el-dialog.customDialog {
  margin-top: 10vh !important;
  background: #3c4167 !important;
}

::v-deep .el-form-item__label {
  color: #fff;
}

::v-deep .el-textarea__inner {
  border: 1px solid rgb(94, 102, 160) !important;
  background-color: rgba(43, 46, 73) !important;
}

::v-deep .el-textarea__inner::placeholder {
  color: #A9A9A9 !important;
}

::v-deep .el-input__inner::placeholder {
  color: #A9A9A9 !important;
}

::v-deep .el-button--primary:hover,
.el-button--primary:focus {
  background: rgb(103, 113, 183, .2) !important;
  border: 1px solid rgb(103, 113, 183, .2) !important;
}

::v-deep .el-button--primary {
  background: rgb(103, 113, 183) !important;
  border: 1px solid rgb(103, 113, 183) !important;
  border-color: rgb(103, 113, 183) !important;
}



::v-deep .el-select__caret.el-input__icon.el-icon-arrow-up {
  height: 100% !important;
  line-height: 100% !important;
}

::v-deep .pagination-container {
  background: none !important;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

/* 表格 */
::v-deep .el-table::before {
  height: 0;
}

::v-deep .el-table__fixed-right::before {
  height: 0;
}

::v-deep .el-table th.el-table__cell.is-leaf {
  border: none;
}

::v-deep .el-table td.el-table__cell {
  border: none;
}

::v-deep .el-table::before {
  height: 0;
}

::v-deep .el-table__fixed::before {
  height: 0;
}

::v-deep .el-table__fixed-right::before {
  height: 0;
}

::v-deep .el-form-item__label {
  color: #ffffff;
  margin-right: 10px;
}

::v-deep .el-table__body-wrapper {
  background: #3c4167;
}

::v-deep .el-table__empty-text {
  color: #ffffff;
}

::v-deep .el-table .el-table__body tr.hover-row>td {
  background-color: #3c4167 !important;
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover>td {
  background-color: #3c4167 !important;
}


.elDatePicker.el-picker-panel {
  color: #fff;
  background: #3c4167;


}

.el-picker-panel__sidebar {
  color: #fff;
  background: #3c4167;
}

.el-picker-panel__shortcut {
  color: #ffffff;
}

.el-picker-panel__shortcut:hover {
  color: #1890ff;
}

.el-picker-panel__icon-btn {
  color: #ffffff;
}

.el-date-picker__header-label {
  color: #ffffff;
}

.el-date-table th {
  color: #ffffff;
}

.el-input__inner {
  color: #fff;
  background: #3c4167;
}

.el-picker-panel__footer {
  color: #fff;
  background: #3c4167;
}

.el-button--text {
  color: #fff;
}

.el-button--mini.is-plain {
  color: #fff;
  background: #3c4167;
  border: 1px solid #fff;
}

.el-time-panel {
  background: #3c4167;
}

.el-time-spinner__item {
  color: #fff;
}

.el-time-spinner__item.active {
  color: #1890ff;
}

.el-time-spinner__item:hover {
  color: #1890ff;
  background: #3c4167;
}

.el-time-panel__btn.cancel {
  color: #fff;
}

.el-time-panel__btn.confirm {
  color: #fff;
  background: #3c4167;
  border: 1px solid #fff;
}

.available.in-range {
  color: #1890ff;
}

.el-year-table td .cell {
  color: #fff;
}

.el-year-table td.current:not(.disabled) .cell {
  color: #1890ff;
}

.elDateSeletc.el-select-dropdown {
  color: #fff;
  background: #3c4167;
}

.el-select-dropdown__item {
  color: #fff;
  background: #3c4167;
}

.el-select-dropdown__item:hover {
  color: #1890ff;
  background: #3c4167;
}

.selected {
  color: #1890ff;
  background: #3c4167;
}
