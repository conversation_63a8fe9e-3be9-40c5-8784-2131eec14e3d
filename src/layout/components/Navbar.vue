<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggle-click="toggleSideBar" />
    <breadcrumb v-if="!settingsStore.topNav" id="breadcrumb-container" class="breadcrumb-container" />
    <top-nav v-if="settingsStore.topNav" id="topmenu-container" class="topmenu-container" />

    <div class="right-menu flex align-center">
      <template v-if="appStore.device !== 'mobile'">
        <!-- <el-select
          v-if="userId === 1 && tenantEnabled"
          v-model="companyName"
          class="min-w-244px"
          clearable
          filterable
          reserve-keyword
          :placeholder="proxy.$t('navbar.selectTenant')"
          @change="dynamicTenantEvent"
          @clear="dynamicClearEvent"
        >
          <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"> </el-option>
          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
        </el-select> -->

        <!-- <header-search id="header-search" class="right-menu-item" /> -->
        <search-menu ref="searchMenuRef" />
        <el-tooltip content="搜索" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect" @click="openSearchMenu">
            <svg-icon class-name="search-icon" icon-class="search" />
          </div>
        </el-tooltip>
        <!-- 消息 -->
        <el-tooltip :content="proxy.$t('navbar.message')" effect="dark" placement="bottom">
          <div>
            <el-popover placement="bottom" trigger="click" transition="el-zoom-in-top" :width="300" :persistent="false">
              <template #reference>
                <el-badge :value="newNotice > 0 ? newNotice : ''" :max="99">
                  <svg-icon icon-class="message" />
                </el-badge>
              </template>
              <template #default>
                <notice></notice>
              </template>
            </el-popover>
          </div>
        </el-tooltip>
        <!-- <el-tooltip content="Github" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip :content="proxy.$t('navbar.document')" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <el-tooltip :content="proxy.$t('navbar.full')" effect="dark" placement="bottom">
          <screenfull id="screenfull" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip :content="proxy.$t('navbar.language')" effect="dark" placement="bottom">
          <lang-select id="lang-select" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip :content="proxy.$t('navbar.layoutSize')" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>

        <!-- 周报/日报生成按钮 -->
        <el-tooltip content="生成报表" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect" @click="showReportDialog">
            <svg-icon icon-class="documentation" />
          </div>
        </el-tooltip>
      </template>
      <div class="avatar-container">
        <el-dropdown class="right-menu-item hover-effect" trigger="click" @command="handleCommand">
          <div class="avatar-wrapper">
            <img :src="userStore.avatar" class="user-avatar" />
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link v-if="!dynamic" to="/user/profile">
                <el-dropdown-item>{{ proxy.$t('navbar.personalCenter') }}</el-dropdown-item>
              </router-link>
              <el-dropdown-item v-if="settingsStore.showSettings" command="setLayout">
                <span>{{ proxy.$t('navbar.layoutSetting') }}</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span>{{ proxy.$t('navbar.logout') }}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>

  <!-- 报表类型选择弹窗 -->
  <el-dialog v-model="reportTypeDialogVisible" title="选择报表类型" width="30%" :before-close="handleClose">
    <div class="report-type-container">
      <el-button type="primary" @click="selectReportType('daily')">生成日报</el-button>
      <el-button type="primary" @click="selectReportType('weekly')">生成周报</el-button>
    </div>
  </el-dialog>

  <!-- 报表数据填写弹窗 -->
  <el-dialog v-model="reportFormDialogVisible" :title="reportType === 'daily' ? '日报数据' : '周报数据'" width="50%" :before-close="handleClose">
    <el-form :model="reportForm" label-width="120px">
      <el-form-item label="报表日期">
        <el-date-picker v-model="reportForm.date" type="date" placeholder="选择日期" format="YYYY.MM.DD" value-format="YYYY.MM.DD" />
      </el-form-item>

      <el-form-item label="值班组长">
        <el-input v-model="reportForm.leader" placeholder="请输入值班组长姓名" />
      </el-form-item>

      <el-form-item label="值班员">
        <el-input v-model="reportForm.members" placeholder="请输入值班员姓名，多个用逗号分隔" />
      </el-form-item>

      <el-form-item label="值班状态">
        <el-select v-model="reportForm.status" placeholder="请选择值班状态">
          <el-option label="良好" value="良好" />
          <el-option label="正常" value="正常" />
          <el-option label="异常" value="异常" />
        </el-select>
      </el-form-item>

      <el-form-item label="通行状态">
        <el-input v-model="reportForm.trafficStatus" type="textarea" :rows="2" placeholder="通行状态描述" />
      </el-form-item>

      <el-form-item label="事故数量">
        <el-input-number v-model="reportForm.accidents.total" :min="0" />
      </el-form-item>

      <el-form-item label="主线事故">
        <el-input-number v-model="reportForm.accidents.mainLine" :min="0" />
      </el-form-item>

      <el-form-item label="收费站事故">
        <el-input-number v-model="reportForm.accidents.tollStation" :min="0" />
      </el-form-item>

      <el-form-item label="服务区事故">
        <el-input-number v-model="reportForm.accidents.serviceArea" :min="0" />
      </el-form-item>

      <el-form-item label="金塘大桥断面流量">
        <div class="flow-container">
          <div class="flow-item">
            <div class="flow-label">舟向断面流量</div>
            <el-input-number v-model="reportForm.flow.jintang.toZhoushan" :min="0" />
          </div>
          <div class="flow-item">
            <div class="flow-label">宁向断面流量</div>
            <el-input-number v-model="reportForm.flow.jintang.toNingbo" :min="0" />
          </div>
          <div class="flow-item">
            <div class="flow-label">合计</div>
            <el-input-number v-model="reportForm.flow.jintang.total" :min="0" disabled />
          </div>
        </div>
      </el-form-item>

      <el-form-item label="舟岱大桥断面流量">
        <div class="flow-container">
          <div class="flow-item">
            <div class="flow-label">舟向断面流量</div>
            <el-input-number v-model="reportForm.flow.zhoudai.toZhoushan" :min="0" />
          </div>
          <div class="flow-item">
            <div class="flow-label">宁向断面流量</div>
            <el-input-number v-model="reportForm.flow.zhoudai.toNingbo" :min="0" />
          </div>
          <div class="flow-item">
            <div class="flow-label">合计</div>
            <el-input-number v-model="reportForm.flow.zhoudai.total" :min="0" disabled />
          </div>
        </div>
      </el-form-item>

      <el-form-item label="巡航情况">
        <el-input v-model="reportForm.patrol" type="textarea" :rows="3" placeholder="巡航情况描述" />
      </el-form-item>

      <el-form-item label="其他信息">
        <el-input v-model="reportForm.otherInfo" type="textarea" :rows="3" placeholder="其他情况描述" />
      </el-form-item>

      <el-form-item label="报送">
        <el-input v-model="reportForm.sendTo" placeholder="报送对象，多个用逗号分隔" />
      </el-form-item>

      <el-form-item label="抄送">
        <el-input v-model="reportForm.copyTo" placeholder="抄送对象，多个用逗号分隔" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="reportFormDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="generateReport">生成并下载</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import SearchMenu from './TopBar/search.vue';
import useAppStore from '@/store/modules/app';
import useUserStore from '@/store/modules/user';
import useSettingsStore from '@/store/modules/settings';
import useNoticeStore from '@/store/modules/notice';
import { getTenantList } from '@/api/login';
import { dynamicClear, dynamicTenant } from '@/api/system/tenant';
import { TenantVO } from '@/api/types';
import notice from './notice/index.vue';
import router from '@/router';
import { storeToRefs } from 'pinia';
import { ComponentInternalInstance, ref, watch, getCurrentInstance, watchEffect } from 'vue';
import { ElMessageBox, ElLoading } from 'element-plus';
import { getLogFile } from '@/api/bridge/work';

const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const noticeStore = storeToRefs(useNoticeStore());
const newNotice = ref(<number>0);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const userId = ref(userStore.userId);
const companyName = ref(undefined);
const tenantList = ref<TenantVO[]>([]);
// 是否切换了租户
const dynamic = ref(false);
// 租户开关
const tenantEnabled = ref(true);
// 搜索菜单
const searchMenuRef = ref<InstanceType<typeof SearchMenu>>();

// 报表相关变量
const reportTypeDialogVisible = ref(false);
const reportFormDialogVisible = ref(false);
const reportType = ref<'daily' | 'weekly'>('daily');

// 定义报表表单接口
interface ReportFormData {
  date: string;
  leader: string;
  members: string;
  status: string;
  trafficStatus: string;
  accidents: {
    total: number;
    mainLine: number;
    tollStation: number;
    serviceArea: number;
  };
  flow: {
    jintang: {
      total: number;
      toZhoushan: number;
      toNingbo: number;
    };
    zhoudai: {
      total: number;
      toZhoushan: number;
      toNingbo: number;
    };
  };
  patrol: string;
  otherInfo: string;
  sendTo: string;
  copyTo: string;
}

// 报表表单数据
const reportForm = ref<ReportFormData>({
  date: new Date().toISOString().split('T')[0].replace(/-/g, '.'),
  leader: '',
  members: '',
  status: '良好',
  trafficStatus: 'G9211甬舟高速、S6定岱高速全桥通行正常。',
  accidents: {
    total: 18,
    mainLine: 17,
    tollStation: 0,
    serviceArea: 1
  },
  flow: {
    jintang: {
      total: 68783,
      toZhoushan: 34006,
      toNingbo: 34777
    },
    zhoudai: {
      total: 12994,
      toZhoushan: 6239,
      toNingbo: 6755
    }
  },
  patrol: '舟桥巡3号、6号对金塘大桥两侧安全警戒区各巡航检查一次，舟桥巡9号对岑港大桥水域巡航检查一次，没有发现违反大桥水域安全管理情况，大桥及附属设施正常。',
  otherInfo: '16时00分， 警戒船回港避风，由舟港拖21在沥港值守。\n监控中心其它设备运行正常。',
  sendTo: '中心领导、副县处级干部',
  copyTo: '办公室、安全管理处、执法协调处、监控指挥处'
});

const openSearchMenu = () => {
  searchMenuRef.value?.openSearch();
};

// 动态切换
const dynamicTenantEvent = async (tenantId: string) => {
  if (companyName.value != null && companyName.value !== '') {
    await dynamicTenant(tenantId);
    dynamic.value = true;
    await router.push('/');
    // Fix router reference
    if (proxy && proxy.$tab) {
      await proxy.$tab.closeAllPage();
      await proxy.$tab.refreshPage();
    }
  }
};

const dynamicClearEvent = async () => {
  await dynamicClear();
  dynamic.value = false;
  await router.push('/');
  // Fix router reference
  if (proxy && proxy.$tab) {
    await proxy.$tab.closeAllPage();
    await proxy.$tab.refreshPage();
  }
};

/** 租户列表 */
const initTenantList = async () => {
  const { data } = await getTenantList(true);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
  }
};

defineExpose({
  initTenantList
});

const toggleSideBar = () => {
  appStore.toggleSideBar(false);
};

const logout = async () => {
  await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  });
  userStore.logout().then(() => {
    router.replace({
      path: '/login',
      // query: {
      //   redirect: encodeURIComponent(router.currentRoute.value.fullPath || '/')
      // }
    });
  });
};

const emits = defineEmits(['setLayout']);
const setLayout = () => {
  emits('setLayout');
};
// 定义Command方法对象 通过key直接调用方法
const commandMap: { [key: string]: any } = {
  setLayout,
  logout
};
const handleCommand = (command: string) => {
  // 判断是否存在该方法
  if (commandMap[command]) {
    commandMap[command]();
  }
};
//用深度监听 消息
watch(
  () => noticeStore.state.value.notices,
  (newVal) => {
    newNotice.value = newVal.filter((item: any) => !item.read).length;
  },
  { deep: true }
);

// 报表相关方法
const showReportDialog = () => {
  reportTypeDialogVisible.value = true;
};

const handleClose = () => {
  reportTypeDialogVisible.value = false;
  reportFormDialogVisible.value = false;
};

// 监听断面流量变化并计算总计
watchEffect(() => {
  // 金塘大桥流量计算
  reportForm.value.flow.jintang.total = 
    reportForm.value.flow.jintang.toZhoushan + 
    reportForm.value.flow.jintang.toNingbo;
  
  // 舟岱大桥流量计算
  reportForm.value.flow.zhoudai.total = 
    reportForm.value.flow.zhoudai.toZhoushan + 
    reportForm.value.flow.zhoudai.toNingbo;
});

const selectReportType = (type: 'daily' | 'weekly') => {
  reportType.value = type;
  reportTypeDialogVisible.value = false;
  reportFormDialogVisible.value = true;
  
  // 设置默认日期格式为 YYYY.MM.DD
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  reportForm.value.date = `${year}.${month}.${day}`;
};

const generateReport = async () => {
  try {
    // 构建请求参数
    const requestData = {
      reportType: reportType.value,
      date: reportForm.value.date,
      leader: reportForm.value.leader,
      members: reportForm.value.members,
      status: reportForm.value.status,
      trafficStatus: reportForm.value.trafficStatus,
      accidents: reportForm.value.accidents,
      flow: reportForm.value.flow,
      patrol: reportForm.value.patrol,
      otherInfo: reportForm.value.otherInfo,
      sendTo: reportForm.value.sendTo,
      copyTo: reportForm.value.copyTo
    };

    // 创建文件名
    const fileName = reportType.value === 'daily' 
      ? `监控指挥中心工作日报_${reportForm.value.date}.docx` 
      : `监控指挥中心工作周报_${reportForm.value.date}.docx`;

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在生成报表，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 调用API生成报表
      const response = await getLogFile(requestData);
      
      // 处理文件下载
      if (response) {
        // 使用FileSaver库或原生下载
        const blob = new Blob([response as any], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        // 显示成功消息
        ElMessageBox.alert('报表生成成功，文件已下载', '成功', {
          confirmButtonText: '确定',
          type: 'success'
        });
      } else {
        // 显示错误消息
        ElMessageBox.alert('报表生成失败，请稍后重试', '错误', {
          confirmButtonText: '确定',
          type: 'error'
        });
      }
    } finally {
      // 关闭加载状态
      loading.close();
    }
  } catch (error) {
    console.error('生成报表错误:', error);
    // 显示错误消息
    ElMessageBox.alert('报表生成失败，请稍后重试', '错误', {
      confirmButtonText: '确定',
      type: 'error'
    });
  } finally {
    reportFormDialogVisible.value = false;
  }
};

const formatReportContent = () => {
  const { date, leader, members, status, trafficStatus, accidents, flow, patrol, otherInfo, sendTo, copyTo } = reportForm.value;
  
  // 根据报表类型生成不同内容
  if (reportType.value === 'daily') {
    return `监控指挥中心工作日报（${date}）
${date.split('.')[0]}年${date.split('.')[1]}月${date.split('.')[2]}日，监控中心值班组长${leader}，值班员${members}，值班状态${status}。
${trafficStatus}事故${accidents.total}起（其中主线事故${accidents.mainLine}起、收费站事故${accidents.tollStation}起、服务区事故${accidents.serviceArea}起）。当日金塘大桥断面合计${flow.jintang.total}辆次（舟向断面流量${flow.jintang.toZhoushan}辆次、宁向断面流量${flow.jintang.toNingbo}辆次），舟岱大桥断面流量${flow.zhoudai.total}辆次（舟向断面流量${flow.zhoudai.toZhoushan}辆次、宁向断面流量${flow.zhoudai.toNingbo}辆次）。
${patrol}
${otherInfo}

报送：${sendTo}
抄送：${copyTo}`;
  } else {
    // 周报格式可以在这里定义
    return `监控指挥中心工作周报（${date}）
本周监控中心值班组长${leader}，值班员${members}，值班状态${status}。
${trafficStatus}本周事故共${accidents.total}起（其中主线事故${accidents.mainLine}起、收费站事故${accidents.tollStation}起、服务区事故${accidents.serviceArea}起）。
本周金塘大桥断面日均流量${flow.jintang.total}辆次（舟向断面平均流量${flow.jintang.toZhoushan}辆次、宁向断面平均流量${flow.jintang.toNingbo}辆次），舟岱大桥断面平均流量${flow.zhoudai.total}辆次（舟向断面平均流量${flow.zhoudai.toZhoushan}辆次、宁向断面平均流量${flow.zhoudai.toNingbo}辆次）。
${patrol}
${otherInfo}

报送：${sendTo}
抄送：${copyTo}`;
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-select .el-input__wrapper) {
  height: 30px;
}

:deep(.el-badge__content.is-fixed) {
  top: 12px;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  //background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
          margin-top: 10px;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

// 报表相关样式
.report-type-container {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
}

.flow-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.flow-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.flow-label {
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
  text-align: center;
}
</style>
