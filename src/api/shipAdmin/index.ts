import request from '@/utils/request'
//列表
export function shipAdminPage(data) {
    return request({
        url: '/system/shipInfo',
        method: 'get',
        params: data
    })
}

// 验证mmsi
export function existByMMSI(data) {
    return request({
        url: '/system/shipInfo/existByMMSI/'+data,
        method: 'post',
    })
}

// 新增
export function addShipInfo(data) {
    return request({
        url: 'system/shipInfo',
        method: 'post',
        data:data
    })
}
// 修改
export function editShipInfo(data) {
    return request({
        url: 'system/shipInfo',
        method: 'put',
        data:data
    })
}

// 删除
export function delShipInfo(data) {
    return request({
        url: 'system/shipInfo/'+data,
        method: 'delete',
        
    })
}