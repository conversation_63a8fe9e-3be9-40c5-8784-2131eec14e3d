import request from '@/utils/request';


// 获取重点关注船舶列表
export function impShipList(query) {
    return request({
        url: '/system/keyShip',
        method: 'get',
        params: query
    })
}

// 添加重点关注船舶
export function addImpShip(data) {
    return request({
        url: '/system/keyShip/add/' + data,
        method: 'post',
    })
}

// 移出重点船舶（单个）
export function delShip(data) {
    return request({
        url: '/system/keyShip/' + data,
        method: 'delete',
    })
}

// 编辑
export function putShip(data) {
    return request({
        url: '/system/keyShip',
        method: 'put',
        data:data
    })
}