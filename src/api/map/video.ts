import request from '@/utils/request';

export function videoList(query) {
  return request({
    url: '/system/ysy/video/getVideoList',
    method: 'get',
    params: query
  })
}

// 设备列表
export function deviceList(query) {
  return request({
    url: 'system/ysy/video/getDeviceList',
    method: 'get',
    params: query
  })
}

// 通过设备串号获取视频流
export function deviceSerial(deviceSerial,channelNo,protocol) {
  return request({
    url: '/system/ysy/video/getLiveAddress/' + deviceSerial+'/'+channelNo+'/'+protocol,
    method: 'get'
  })
}

// 控制设备
export function controlVideo(data) {
  return request({
    url: 'system/ysy/ptz/ptzStart',
    method: 'post',
    data:data
  })
}

// 停止设备
export function stopControlVideo(data) {
  return request({
    url: 'system/ysy/ptz/ptzStop',
    method: 'post',
    data:data
  })
}

// 获取设备号
export function getPlace(place) {
  return request({
    url: '/system/ysy/video/localCameraList/' + place,
    method: 'get',
  })
}

// 获取设备
export function getDeviceSerial(deviceSerial) {
  return request({
    url: '/system/ysy/video/getDeviceCameraList/' + deviceSerial,
    method: 'get',
  })
}
