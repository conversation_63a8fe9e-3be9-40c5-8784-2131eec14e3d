import request from '@/utils/request';

export function getShipList(data) {
  return request({
    url: '/system/shipTrack/track/list',
    method: 'post',
    data: data
  })
}

// 历史航迹
export function shipHistory(query) {
  return request({
    url: '/system/shipTrack/history',
    method: 'get',
    params: query
  })
}

// 新增通航环境
export function env(data) {
  return request({
    url: '/system/navigationEnv',
    method: 'post',
    data: data
  })
}

// 获取通航环境列表
export function envList(query: any) {
  return request({
    url: '/system/navigationEnv/list',
    method: 'get',
    params: query
  })
}

// 新增通航环境类型
export function envType(data) {
  return request({
    url: '/system/navigationEnvType',
    method: 'post',
    data: data
  })
}

// 获取通航环境类型列表
export function envTypeList(query) {
  return request({
    url: '/system/navigationEnvType/list',
    method: 'get',
    params: query
  })
}

// 删除通航环境
export function delEnv(id) {
  return request({
    url: '/system/navigationEnv/' + id,
    method: 'delete',
  })
}

// 修改通航环境
export function editEnv(data) {
  return request({
    url: '/system/navigationEnv',
    method: 'put',
    data: data,
  })
}


// 新增标绘类型
export function plotType(data) {
  return request({
    url: '/system/plotType',
    method: 'post',
    data: data,
  })
}

// 标绘类型列表
export function plotTypeList(query) {
  return request({
    url: '/system/plotType/list',
    method: 'get',
    params: query
  })
}

// 修改标绘类型
export function editPlotType(data) {
  return request({
    url: '/system/plotType',
    method: 'put',
    data: data,
  })
}

// 删除标绘类型
export function delPlotType(id) {
  return request({
    url: '/system/plotType/' + id,
    method: 'delete',
  })
}


// 新增标绘
export function addplot(data) {
  return request({
    url: '/system/plot',
    method: 'post',
    data: data,
  })
}

// 标绘列表
export function plotList(query) {
  return request({
    url: '/system/plot',
    method: 'get',
    params: query
  })
}

// 删除标绘
export function delPlot(id) {
  return request({
    url: '/system/plot/' + id,
    method: 'delete',
  })
}
// 修改标绘
export function editPlot(data) {
  return request({
    url: '/system/plot',
    method: 'put',
    data:data
  })
}

// 船舶详细信息
export function shipDetails(mmsi) {
  return request({
    url: '/system/shipInfo/byMMSI/' + mmsi,
    method: 'post',
  })
}


//过往船舶数量
export function historyShip(data) {
  return request({
    url: '/system/shipTrack/history?days='+data,
    method: 'post',
  })
}

// 报表
export function statement(data) {
  return request({
    url: '/system/earlyWarning/statement',
    method: 'post',
    data:data
  })
}