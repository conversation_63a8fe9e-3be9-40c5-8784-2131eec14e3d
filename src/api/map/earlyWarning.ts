import request from '@/utils/request';

// 预警列表
export function warnList(query) {
  // return request({
  //   url: '/system/earlyWarning/list',
  //   method: 'get',
  //   params: query
  // })
}

// 预警规则列表
export function warnRuleList(query) {
  return request({
    url: '/system/earlyWarningRule/list',
    method: 'get',
    params: query
  })
}

// 船舶预警规则列表
export function disRuleList(query) {
  return request({
    url: '/system/disposeRule/list',
    method: 'get',
    params: query
  })
}

// 船舶预警规则详情
export function disRuleDetail(id) {
  return request({
    url: '/system/disposeRule/' + id,
    method: 'get'
  })
}

// 新增船舶预警
export function addDisRulet(data) {
  return request({
    url: '/system/disposeRule',
    method: 'post',
    data:data
  })
}

// 修改船舶预警
export function editDisRulet(data) {
  return request({
    url: '/system/disposeRule',
    method: 'put',
    data:data
  })
}

// 删除船舶预警
export function delDisRulet(id) {
  return request({
    url: '/system/disposeRule/' + id,
    method: 'delete',
  })
}

// 历史预警
export function warningList(query) {
  // return request({
  //   url: '/system/earlyWarning/list',
  //   method: 'get',
  //   params: query
  // })
}



// 报警区域
export function warn() {
  return request({
    url: '/system/navigationEnvType/warning/list',
    method: 'get',
    
  })
}
