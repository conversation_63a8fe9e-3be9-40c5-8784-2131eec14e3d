import request from "@/utils/request";

//获取白名单列表
export function getWhiteListList(data: any) {
    return request({
        url: '/system/screen/routine/whitelist',
        method: 'get',
        params: data
    })
}


//获取执法船列表
export function getGaShipList(data: any) {
    return request({
        url: '/system/whiteList/list',
        method: 'get',
        params: data
    })
}

//获取执法船详细信息
export function getGaShipInfo(id: any) {
    return request({
        url: '/system/whiteList/' + id,
        method: 'get',
    })
}

//新增执法船
export function addGaShip(data: any) {
    return request({
        url: '/system/whiteList',
        method: 'post',
        data: data
    })
}

//修改执法船
export function updateGaShip(data: any) {
    return request({
        url: '/system/whiteList',
        method: 'put',
        data: data
    })
}
