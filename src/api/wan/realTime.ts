import request from '@/utils/request'

export function getdispose(data) {
    return request({
        url: '/system/earlyWarningDispose',
        method: 'post',
        data: data,
    })
}
export function getdisposeDetail(id) {
    return request({
        url: '/system/earlyWarning/' + id,
        method: 'get',
    })
}
export function getdisposeDetailList(data) {
    // return request({
    //     url: '/system/earlyWarning/list',
    //     method: 'get',
    //     params: data
    // })
}
export function getEndOrIgnore(id) {
    return request({
        url: '/system/earlyWarning/endOrIgnore/' + id + '/' + 2,
        method: 'put',
    })
}