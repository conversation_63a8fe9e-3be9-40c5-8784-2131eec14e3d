/***mmsi
integer <int64>
船舶唯一识别码
可选
name
string 
船舶名称
可选
time
integer <int64>
时间戳，记录时时间
可选
lon
number <double>
船舶所在位置的经度
可选
lat
number <double>
船舶所在位置的维度
可选
sog
number <double>
船舶的航速
可选
cog
number <double>
船舶的航向
可选
rot
number <double>
可选
船舶的旋转速率，单位为度/分钟

heading
number <double>
船舶的船首向
可选
callsign
string 
船舶的呼号
可选
shipType
string 
可选
船舶的类型，以数字编码表示，使用整数类型存储

toBow
number <double>
可选
从船舶参考点到船首的距离，单位为米

toStern
number <double>
可选
从船舶参考点到船尾的距离，单位为米

toPort
number <double>
可选
从船舶参考点到左舷的宽度，单位为米

toStarboard
number <double>
可选
从船舶参考点到右舷的宽度，单位为米

destination
string 
船舶的目的地
 */

import request from '@/utils/request';

// 船舶AIS数据列表
export function getShipAis(data: any) {
  return request({
    url: '/system/screen/iot/ship/realtime',
    method: 'GET',
    params: data
  });
}

// 船舶AIS数据详情
export function getShipAisDetail(id: any) {
  return request({
    url: '/system/shipData/' + id,
    method: 'GET'
  });
}

// 船舶AIS数据新增
export function addShipAis(data: any) {
  return request({
    url: '/system/shipData',
    method: 'POST',
    data: data
  });
}

// 船舶AIS数据编辑
export function editShipAis(data: any) {
  return request({
    url: '/system/shipData',
    method: 'PUT',
    data: data
  });
}

//判断船舶是否告警
export function getShipAlarm() {
  return request({
    url: '/system/shipData/alarm',
    method: 'GET',
  });
}
