import request from '@/utils/request';

// 人员监控相关接口

// 获取监控设备树-真实后端接口
export function getDeviceTree(data:any) {
  return request({
    url: '/system/dahua/down/getDown',
    method: 'POST',
    data:data
  });
}

//获取人员离岗检测数据列表
export function getPersonnelDetectionList(data: any) {
  return request({
    url: '/system/dahua/video/list',
    method: 'GET',
    params: data
  });
}

//获取人员离岗检测设备树
export function getPersonnelDetectionTree() {
  return request({
    url: '/system/dahua/realplay/getCamerasDetection',
    method: 'POST'
  });
}
// 获取离岗检测实时视频流
export function getLiveVideoUrl(deviceId: string) {
  return request({
    url: `/system/dahua/video/pyUrl/${deviceId}`,
    method: 'get'
  });
}

// 获取实时视频流数据（包含检测框数据）
export function getStreamData(deviceCode: string) {
  const baseUrl = import.meta.env.VITE_APP_PYTHON_API;
  return request({
    url: `${baseUrl}/api/streams/${deviceCode}/mjpeg`,
    method: 'get',
    timeout: 30000, // 30秒超时
    responseType: 'json'
  });
}
// 获取历史视频流地址
export function getHistoryVideoUrl(data: {
  deviceId: string;
  date: string;
  startTime: string;
  endTime: string;
}) {
  return request({
    url: '/system/personnel/video/history',
    method: 'post',
    data
  });
}



// 下载视频文件
export function downloadVideo(data: {
  deviceId: string;
  date: string;
  startTime?: string;
  endTime?: string;
}) {
  return request({
    url: '/system/personnel/video/download',
    method: 'post',
    data,
    responseType: 'blob'
  });
}

// 人员离岗检测相关接口

// 开始检测
export function startDetection(deviceId: string) {
  return request({
    url: '/system/personnel/detection/start',
    method: 'post',
    data: { deviceId }
  });
}



// 获取实时检测结果（包含框选数据）
export function getDetectionResult(deviceId: string) {
  return request({
    url: `/system/personnel/detection/result/${deviceId}`,
    method: 'get'
  });
}




// 删除警告记录
export function deleteWarningRecord(id: string) {
  return request({
    url: `/system/dahua/video/${id}`,
    method: 'delete'
  });
}

