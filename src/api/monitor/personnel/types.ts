// 人员监控相关类型定义

// 设备树节点
export interface DeviceTreeNode {
  id: string;
  name: string;
  type: 'group' | 'device';
  children?: DeviceTreeNode[];
  deviceSerial?: string;
  channelNo?: number;
  status?: 'online' | 'offline';
  location?: string;
}

// 视频播放相关
export interface VideoStreamInfo {
  url: string;
  protocol: 'flv' | 'rtmp' | 'hls';
  quality: 'high' | 'medium' | 'low';
  resolution: string;
  frameRate: number;
}

export interface HistoryVideoQuery {
  deviceId: string;
  date: string;
  startTime: string;
  endTime: string;
}

export interface VideoDownloadQuery {
  deviceId: string;
  date: string;
  startTime?: string;
  endTime?: string;
  quality?: 'high' | 'medium' | 'low';
}

// 人员检测相关
export interface DetectionBox {
  x: number;
  y: number;
  width: number;
  height: number;
  label?: string;
  confidence?: number;
  color?: string;
  personId?: string;
}

export interface DetectedPerson {
  id: string;
  name?: string;
  confidence: number;
  status: 'present' | 'absent';
  x: number;
  y: number;
  width: number;
  height: number;
  lastSeen?: string;
  duration?: number; // 离岗时长（秒）
}

export interface DetectionStatus {
  deviceId: string;
  isActive: boolean;
  connectionStatus: 'connected' | 'disconnected';
  detectionStatus: 'active' | 'idle';
  fps: number;
  processingDelay: number;
  detectedCount: number;
  lastUpdate: string;
}

export interface DetectionResult {
  deviceId: string;
  timestamp: string;
  persons: DetectedPerson[];
  boxes: DetectionBox[];
  frameInfo: {
    width: number;
    height: number;
    fps: number;
  };
}

export interface DetectionSettings {
  deviceId: string;
  sensitivity: number; // 0.1 - 1.0
  minBoxSize: number; // 最小检测框大小
  maxPersons: number; // 最大检测人数
  alertThreshold: number; // 离岗警告阈值（秒）
  enableAlert: boolean;
  alertLevel: 'low' | 'medium' | 'high';
}

// 警告记录相关
export interface WarningRecord {
  id: string;
  personName: string;
  personId: string;
  captureImage: string;
  deviceName: string;
  deviceId: string;
  duration: number; // 离岗时长（分钟）
  warningLevel: 'low' | 'medium' | 'high';
  status: 'pending' | 'handled' | 'ignored';
  warningTime: string;
  handlerName?: string;
  handleTime?: string;
  handleRemark?: string;
  location?: string;
  confidence?: number;
}

export interface WarningQuery {
  pageNum: number;
  pageSize: number;
  personName?: string;
  deviceId?: string;
  status?: 'pending' | 'handled' | 'ignored';
  warningLevel?: 'low' | 'medium' | 'high';
  startTime?: string;
  endTime?: string;
  orderByColumn?: string;
  isAsc?: string;
}

export interface WarningProcessForm {
  action: 'handle' | 'ignore';
  remark: string;
}

export interface BatchProcessForm {
  ids: string[];
  action: 'handle' | 'ignore';
  remark: string;
}

// 统计数据相关
export interface WarningStatistics {
  total: number;
  pending: number;
  handled: number;
  ignored: number;
  todayCount: number;
  weekCount: number;
  monthCount: number;
  levelStats: {
    low: number;
    medium: number;
    high: number;
  };
  deviceStats: Array<{
    deviceId: string;
    deviceName: string;
    count: number;
  }>;
  timeStats: Array<{
    time: string;
    count: number;
  }>;
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface PageResult<T = any> {
  rows: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

// 组件Props类型
export interface VideoPlayerProps {
  src?: string;
  width?: string;
  height?: string;
  showControls?: boolean;
  autoplay?: boolean;
  enableOverlay?: boolean;
  detectionBoxes?: DetectionBox[];
  playerId?: string;
}

export interface TreeSelectProps {
  value: string | number;
  options: DeviceTreeNode[];
  objMap?: {
    value: string;
    label: string;
    children: string;
  };
  placeholder?: string;
  accordion?: boolean;
}

// 事件类型
export interface VideoPlayerEvents {
  play: () => void;
  pause: () => void;
  error: (error: any) => void;
  canvasClick: (position: { x: number; y: number }) => void;
}

export interface DetectionEvents {
  personDetected: (person: DetectedPerson) => void;
  personLeft: (personId: string, duration: number) => void;
  warningGenerated: (warning: WarningRecord) => void;
  statusChanged: (status: DetectionStatus) => void;
}

// WebSocket消息类型
export interface WSMessage {
  type: 'detection_result' | 'status_update' | 'warning_alert' | 'connection_status';
  data: any;
  timestamp: string;
  deviceId?: string;
}

export interface DetectionWSMessage extends WSMessage {
  type: 'detection_result';
  data: DetectionResult;
}

export interface StatusWSMessage extends WSMessage {
  type: 'status_update';
  data: DetectionStatus;
}

export interface WarningWSMessage extends WSMessage {
  type: 'warning_alert';
  data: WarningRecord;
}

export interface ConnectionWSMessage extends WSMessage {
  type: 'connection_status';
  data: {
    deviceId: string;
    status: 'connected' | 'disconnected';
    reason?: string;
  };
}
