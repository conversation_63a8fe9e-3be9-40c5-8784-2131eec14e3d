import request from '@/utils/request';

//新增工作总结
export function getWorkSummary(data: any) {
    return request({
      url: '/system/workSummary/list',
      method: 'GET',
      params: data
    });
  }

  // 添加工作总结
export function addWorkSummary(data: any) {
    return request({
      url: '/system/workSummary',
      method: 'POST',
      data: data
    });
  }
  
  // 修改工作总结
  export function updateEWorkSummary(data: any) {
    return request({
      url: '/system/workSummary',
      method: 'PUT',  
      data: data
    });
  }
  
  // 删除工作总结
  export function deleteWorkSummary(id: any) {
    return request({  
      url: '/system/workSummary/' + id,
      method: 'DELETE'
    });
  }
//工作总结详情
  export function getWorkSummaryInfo(id: any) {
    return request({
      url: '/system/workSummary/' + id,
      method: 'GET'
    });
  }

  