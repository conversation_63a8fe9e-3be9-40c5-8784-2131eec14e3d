import request from '@/utils/request';

//救援队伍列表
export function getRescueTeamList(data: any) {
  return request({
    url: '/system/rescueTeams/list',
    method: 'GET',
    params: data
  });
}

//救援队伍详情
export function getRescueTeamDetail(id: any) {
  return request({
    url: '/system/rescueTeams/' + id,
    method: 'GET'
  });
}

//新增救援队伍
export function addRescueTeam(data: any) {
  return request({
    url: '/system/rescueTeams',
    method: 'POST',
    data: data
  });
}

//修改救援队伍
export function updateRescueTeam(data: any) {
  return request({
    url: '/system/rescueTeams',
    method: 'PUT',
    data: data
  });
}

//删除救援队伍
export function deleteRescueTeam(id: any) {
  return request({
    url: '/system/rescueTeams/' + id,
    method: 'DELETE'
  });
}





