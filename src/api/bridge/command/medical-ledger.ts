import request from '@/utils/request';

// 获取医疗台账列表
export function getMedicalLedgerList(data: any) {
  return request({
    url: '/system/medicalLedger/list',
    method: 'GET',
    params: data
  });
}

// 获取医疗台账详情
export function getMedicalLedgerDetail(id: any) {
  return request({
    url: '/system/medicalLedger/' + id,
    method: 'GET'
  });
}

// 新增医疗台账
export function addMedicalLedger(data: any) {
  return request({
    url: '/system/medicalLedger',
    method: 'POST',
    data: data
  });
}

// 修改医疗台账
export function updateMedicalLedger(data: any) {
  return request({
    url: '/system/medicalLedger',
    method: 'PUT',
    data: data
  });
}

// 删除医疗台账
export function deleteMedicalLedger(id: any) {
  return request({
    url: '/system/medicalLedger/' + id,
    method: 'DELETE'
  });
}
