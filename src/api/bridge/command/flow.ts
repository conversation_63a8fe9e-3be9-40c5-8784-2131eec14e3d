import request from '@/utils/request';

//获取流程列表
export function getFlowList(data: any) {
  return request({
    url: '/system/processFlowLog/list',
    method: 'GET',
    params: data
  });
}

//获取流程详情
export function getFlowDetail(id: any) {
  return request({
    url: '/system/processFlowLog/'+id,
    method: 'GET',
  });
}

//新增流程
export function addFlow(data: any) {
  return request({
    url: '/system/processFlowLog',
    method: 'POST',
    data: data
  });
}

//修改流程
export function updateFlow(data: any) {
  return request({
    url: '/system/processFlowLog',
    method: 'PUT',
    data: data
  });
}

//删除流程
export function deleteFlow(id: any) {
  return request({
    url: '/system/processFlowLog/'+id,
    method: 'DELETE',
  });
}