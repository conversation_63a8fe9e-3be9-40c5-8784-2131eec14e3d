import request from '@/utils/request';

// 获取预警通知列表

export function getNoticeList(data: any) {
  return request({
    url: '/system/warnRuleNotifyUser/list',
    method: 'GET',
    params: data
  });
}

// 获取预警通知详情
export function getNoticeDetail(id: any) {
  return request({
    url: '/system//warnNotifyDetail/' + id,
    method: 'GET'
  });
}

// 添加预警通知
export function addNotice(data: any) {
  return request({
    url: '/system/warnRuleNotifyUser',
    method: 'POST',
    data: data
  });
}

// 修改预警通知
export function updateNotice(data: any) {
  return request({
    url: '/system/warnRuleNotifyUser',
    method: 'PUT',
    data: data
  });
}   

// 删除预警通知
export function deleteNotice(id: any) {
  return request({
    url: '/system/warnRuleNotifyUser/' + id,
    method: 'DELETE'
  });
}

// 发布预警通知
export function publishNotice(id: any) {
  return request({
    url: '/system/warnRuleNotifyUser/publish/' + id,
    method: 'GET',
  });
}

//新增信息模板
export function addInfoTemplate(data: any) {
  return request({
    url: '/system/infoTemplate',
    method: 'POST',
    data: data
  });
}

//修改信息模板
export function updateInfoTemplate(data: any) {
  return request({
    url: '/system/infoTemplate',
    method: 'PUT',
    data: data
  });
}

//删除信息模板
export function deleteInfoTemplate(id: any) {
  return request({
    url: '/system/infoTemplate/' + id,
    method: 'DELETE'
  });
}

//获取信息模板列表
export function getInfoTemplateList(data: any) {
  return request({
    url: '/system/infoTemplate/list',
    method: 'GET',
    params: data
  });
}

//获取信息模板详情
export function getInfoTemplateDetail(id: any) {
  return request({
    url: '/system/infoTemplate/' + id,
    method: 'GET'
  });
}
