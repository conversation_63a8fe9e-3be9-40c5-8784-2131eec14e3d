import request from '@/utils/request';

//获取事件处置列表
export function getEventRecordList(data: any) {
  return request({
    url: '/system/eventRecord/list',
    method: 'GET',
    params: data
  });
}

//获取事件处置详情
export function getEventRecordById(id: any) {
  return request({
    url: '/system/eventRecord/' + id,
    method: 'GET'
  });
}

//新增事件处置
export function addEventRecord(data: any) {
  return request({
    url: '/system/eventRecord',
    method: 'POST',
    data: data
  });
}

//修改事件处置
export function updateEventRecord(data: any) {
  return request({
    url: '/system/eventRecord',
    method: 'PUT',
    data: data
  });
}

//删除事件处置
export function deleteEventRecord(id: any) {
  return request({
    url: '/system/eventRecord/' + id,
    method: 'DELETE'
  });
}

//应急事件详情列表
export function getEmergencyEventList(data: any) {
  return request({
    url: '/system/emergencyEvent/list',
    method: 'GET',
    params: data
  });
}

//获取应急事件详情
export function getEmergencyEventById(id: any) {
  return request({
    url: '/system/emergencyEvent/' + id,
    method: 'GET'
  });
}

//新增应急事件
export function addEmergencyEvent(data: any) {
  return request({
    url: '/system/emergencyEvent',
    method: 'POST',
    data: data
  });
}

//修改应急事件
export function updateEmergencyEvent(data: any) {
  return request({
    url: '/system/emergencyEvent',
    method: 'PUT',
    data: data
  });
}

//删除应急事件
export function deleteEmergencyEvent(id: any) {
  return request({
    url: '/system/emergencyEvent/' + id,
    method: 'DELETE'
  });
}
