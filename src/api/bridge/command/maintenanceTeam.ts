import request from '@/utils/request';

// 获取养护队伍列表
export function getMaintenanceTeamList(data: any) {
  return request({
    url: '/system/maintenanceTeam/list',
    method: 'GET',
    params: data
  });
}

// 获取养护队伍详情
export function getMaintenanceTeamDetail(id: any) {
  return request({
    url: '/system/maintenanceTeam/' + id,
    method: 'GET'
  });
}

// 新增养护队伍
export function addMaintenanceTeam(data: any) {
  return request({
    url: '/system/maintenanceTeam',
    method: 'POST',
    data: data
  });
}

// 修改养护队伍
export function updateMaintenanceTeam(data: any) {
  return request({
    url: '/system/maintenanceTeam',
    method: 'PUT',
    data: data
  });
}

// 删除养护队伍
export function deleteMaintenanceTeam(id: any) {
  return request({
    url: '/system/maintenanceTeam/' + id,
    method: 'DELETE'
  });
}