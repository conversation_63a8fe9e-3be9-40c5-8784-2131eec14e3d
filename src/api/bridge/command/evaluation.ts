import request from '@/utils/request';

//获取评估得分列表
export function getEvaluationScoreList(data: any) {
  return request({
    url: '/system/evaluationScore/list',
    method: 'GET',
    params: data
  });
}

//获取评估得分详情
export function getEvaluationScoreDetail(id: any) {
  return request({
    url: '/system/evaluationScore/' + id,
    method: 'GET',
  });
}

//新增评估得分
export function addEvaluationScore(data: any) {
  return request({
    url: '/system/evaluationScore',
    method: 'POST',
    data: data
  });
}

//修改评估得分
export function updateEvaluationScore(data: any) {
  return request({
    url: '/system/evaluationScore',
    method: 'PUT',
    data: data
  });
}

//删除评估得分
export function deleteEvaluationScore(id: any) {
  return request({
    url: '/system/evaluationScore/' + id,
    method: 'DELETE',
  });
}

//获取评分规则列表
export function getEvaluationRuleList(data: any) {
  return request({
    url: '/system/evaluationRule/list',
    method: 'GET',
    params: data
  });
}

//获取评分规则详情
export function getEvaluationRuleDetail(id: any) {
  return request({
    url: '/system/evaluationRule/' + id,
    method: 'GET',
  });
}

//新增评分规则
export function addEvaluationRule(data: any) {
  return request({
    url: '/system/evaluationRule',
    method: 'POST',
    data: data
  });
}

//修改评分规则
export function updateEvaluationRule(data: any) {
  return request({
    url: '/system/evaluationRule',
    method: 'PUT',
    data: data
  });
}

//删除评分规则
export function deleteEvaluationRule(id: any) {
  return request({
    url: '/system/evaluationRule/' + id,
    method: 'DELETE',
  });
}

//处置效果评估列表
export function getDisposalEvaluationList(data: any) {
  return request({
    url: '/system/disposalEvaluation/list',
    method: 'GET',
    params: data
  });
}

//处置效果评估详情
export function getDisposalEvaluationDetail(id: any) {
  return request({
    url: '/system/disposalEvaluation/' + id,
    method: 'GET',
  });
}

//新增处置效果评估
export function addDisposalEvaluation(data: any) {
  return request({
    url: '/system/disposalEvaluation',
    method: 'POST',
    data: data
  });
}

//修改处置效果评估
export function updateDisposalEvaluation(data: any) {
  return request({
    url: '/system/disposalEvaluation',
    method: 'PUT',
    data: data
  });
}

//删除处置效果评估
export function deleteDisposalEvaluation(id: any) {
  return request({
    url: '/system/disposalEvaluation/' + id,
    method: 'DELETE',
  });
}


