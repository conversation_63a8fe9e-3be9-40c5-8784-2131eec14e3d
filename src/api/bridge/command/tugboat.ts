import request from '@/utils/request';

// 查询拖轮列表
export function getTugboatList(query: any) {
  return request({
    url: '/system/tugboat/list',
    method: 'get',
    params: query
  });
}

// 查询拖轮详细信息
export function getTugboatDetail(tugboatId: number) {
  return request({
    url: `/system/tugboat/${tugboatId}`,
    method: 'get'
  });
}

// 新增拖轮
export function addTugboat(data: any) {
  return request({
    url: '/system/tugboat',
    method: 'post',
    data: data
  });
}

// 更新拖轮
export function updateTugboat(data: any) {
  return request({
    url: '/system/tugboat',
    method: 'put',
    data: data
  });
}

// 删除拖轮
export function deleteTugboat(tugboatId: number) {
  return request({
    url: `/system/tugboat/${tugboatId}`,
    method: 'delete'
  });
}