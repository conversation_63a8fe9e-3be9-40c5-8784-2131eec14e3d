import request from '@/utils/request';

//新增预案通知
export function getPlanNoticeRecord(data: any) {
    return request({
      url: '/system/planNoticeRecord/list',
      method: 'GET',
      params: data
    });
  }

  // 添加预案通知
export function addPlanNoticeRecord(data: any) {
    return request({
      url: '/system/planNoticeRecord',
      method: 'POST',
      data: data
    });
  }
  
  // 修改预案通知
  export function updatePlanNoticeRecord(data: any) {
    return request({
      url: '/system/planNoticeRecord',
      method: 'PUT',  
      data: data
    });
  }
  
  // 删除预案通知
  export function deletePlanNoticeRecord(id: any) {
    return request({  
      url: '/system/planNoticeRecord/' + id,
      method: 'DELETE'
    });
  }
//预案通知详情
  export function getPlanNoticeRecordInfo(id: any) {
    return request({
      url: '/system/planNoticeRecord/' + id,
      method: 'GET'
    });
  }

  