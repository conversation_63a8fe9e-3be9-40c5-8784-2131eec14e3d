import request from '@/utils/request';

// 获取应急预案列表

export function getEmergencyPlanList(data: any) {
  return request({
    url: '/system/emergencyPlan/list',
    method: 'GET',
    params: data
  });
}

// 获取应急预案详情
export function getEmergencyPlanById(id: any) {
  return request({
    url: '/system/emergencyPlan/' + id,
    method: 'GET'
  });
}
//上传应急预案附件
export function uploadEmergencyPlanAttachment(formData: any) {
  return request({
    url: '/system/emergencyPlan/upload',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
//删除应急预案附件
export function deleteEmergencyPlanAttachment(id: any) {
  return request({
    url: '/system/emergencyPlan/delete/' + id,
    method: 'DELETE',
  });
}



// 添加应急预案
export function addEmergencyPlan(data: any) {
  return request({
    url: '/system/emergencyPlan',
    method: 'POST',
    data: data
  });
}

// 修改应急预案
export function updateEmergencyPlan(data: any) {
  return request({
    url: '/system/emergencyPlan',
    method: 'PUT',  
    data: data
  });
}

// 删除应急预案
export function deleteEmergencyPlan(id: any) {
  return request({  
    url: '/system/emergencyPlan/' + id,
    method: 'DELETE'
  });
}

// 获取应急知识条目列表
export function getEmergencyKnowledgeList(data: any) {
  return request({
    url: '/system/emergencyKnowledge/list',
    method: 'GET',
    params: data
  });
}

// 获取应急知识条目详情
export function getEmergencyKnowledgeById(id: any) {
  return request({
    url: '/system/emergencyKnowledge/' + id,
    method: 'GET'
  });
}

// 添加应急知识条目
export function addEmergencyKnowledge(data: any) {
  return request({
    url: '/system/emergencyKnowledge',
    method: 'POST',
    data: data
  });
}

// 修改应急知识条目
export function updateEmergencyKnowledge(data: any) {
  return request({
    url: '/system/emergencyKnowledge',
    method: 'PUT',
    data: data
  });
}

// 删除应急知识条目
export function deleteEmergencyKnowledge(id: any) {
  return request({
    url: '/system/emergencyKnowledge/' + id,
    method: 'DELETE'
  });
}




