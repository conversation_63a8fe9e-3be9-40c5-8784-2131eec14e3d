import request from '@/utils/request';
// 获取路由
export function addPublicHealthWarning(data : any) {
  return request({
    url: '/system/publicHealthWarning',
    method: 'POST',
    data: data
  });
}

export function getPublicHealthWarningList(data : any) {
  return request({
    url: '/system/publicHealthWarning/list',
    method: 'GET',
    params: data
  });
}

export function getPublicHealthWarningById(id : any) {
  return request({
    url: '/system/publicHealthWarning/' + id,
    method: 'GET'
  });
}

export function updatePublicHealthWarning(data : any) {
  return request({
    url: '/system/publicHealthWarning',
    method: 'PUT',
    data: data
  });
}

export function deletePublicHealthWarning(id : any) {
  return request({
    url: '/system/publicHealthWarning/' + id,
    method: 'DELETE'
  });
}

export function deletePublicHealthWarningBatch(ids : string) {
  return request({
    url: '/system/publicHealthWarning/' + ids,
    method: 'DELETE'
  });
}

