import request from '@/utils/request';

//获取预警规则列表
export function getWarningRuleList(data : any) {
  return request({
    url: '/system/warnRule/list',
    method: 'GET',
    params: data
  });
}

//获取预警规则详细信息
export function getWarningRuleById(id : any) {
    return request({
      url: '/system/warnRule/' + id,
      method: 'GET'
    });
  }

//新增预警规则
export function addWarningRule(data : any) {
  return request({
    url: '/system/warnRule',
    method: 'POST',
    data: data
  });
}

//修改预警规则
export function updateWarningRule(data : any) {
  return request({
    url: '/system/warnRule',
    method: 'PUT',
    data: data
  });
}

//删除预警规则
export function deleteWarningRule(id : any) {
  return request({
    url: '/system/warnRule/' + id,
    method: 'DELETE'
  });
}
