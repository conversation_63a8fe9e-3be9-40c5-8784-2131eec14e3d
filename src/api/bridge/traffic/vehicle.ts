import request from '@/utils/request';
/**
 * 创建 checkpointWarning
 * @param data 创建所需的数据
 * @returns Promise 对象
 */
export function addCheckpointWarning(data: any) {
  return request({
    url: '/system/checkpointWarning',
    method: 'post',
    data
  });
}

/**
 * 删除 checkpointWarning
 * @param id 需要删除的 checkpointWarning 的 ID
 * @returns Promise 对象
 */
export function deleteCheckpointWarning(id: number) {
  return request({
    url: `/system/checkpointWarning/${id}`,
    method: 'delete'
  });
}

/**
 * 修改 checkpointWarning
 * @param id 需要修改的 checkpointWarning 的 ID
 * @param data 修改所需的数据
 * @returns Promise 对象
 */
export function updateCheckpointWarning(id: number, data: any) {
  return request({
    url: `/system/checkpointWarning/${id}`,
    method: 'put',
    data
  });
}

/**
 * 分页查询 checkpointWarning
 * @param params 分页查询参数，如页码、每页数量等
 * @returns Promise 对象
 */
export function getCheckpointWarningList(params: any) {
  return request({
    url: '/system/checkpointWarning/list',
    method: 'get',
    params
  });
}

/**
 * 获取 checkpointWarning 详情
 * @param id 需要查询详情的 checkpointWarning 的 ID
 * @returns Promise 对象
 */
export function getCheckpointWarningDetail(id: number) {
  return request({
    url: `/system/checkpointWarning/${id}`,
    method: 'get'
  });
}
