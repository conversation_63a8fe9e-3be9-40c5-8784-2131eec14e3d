import request from '@/utils/request';
// import { AccidentEffect } from '@/api/types';
// 获取路由
export function addTrafficAccidentWarning(data : any) {
  return request({
    url: '/system/trafficAccidentWarning',
    method: 'POST',
    data: data
  });
}

export function getTrafficAccidentWarningList(data : any) {
  return request({
    url: '/system/trafficAccidentWarning/list',
    method: 'GET',
    params: data
  });
}

export function getTrafficAccidentWarningById(id : any) {
  return request({
    url: '/system/trafficAccidentWarning/' + id,
    method: 'GET'
  });
}

export function updateTrafficAccidentWarning(data : any) {
  return request({
    url: '/system/trafficAccidentWarning',
    method: 'PUT',
    data: data
  });
}

export function deleteTrafficAccidentWarning(id : any) {
  return request({
    url: '/system/trafficAccidentWarning/' + id,
    method: 'DELETE'
  });
}

export function deleteTrafficAccidentWarningBatch(ids : string) {
  return request({
    url: '/system/trafficAccidentWarning/' + ids,
    method: 'DELETE'
  });
}

export function uplodadWarningFile(formData : any) {
  return request({
    url: '/system/earlyWarningInfo/upload',
    method: 'POST',
    data: formData
  });
}


