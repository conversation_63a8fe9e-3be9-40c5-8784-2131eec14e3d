import request from '@/utils/request';

export function addShipWarning(data: any) {
  return request({
    url: '/system/shipEarlyWarning',
    method: 'POST',
    data: data
  });
}

export function getShipWarningList(data: any) {
  return request({
    url: '/system/shipEarlyWarning/list',
    method: 'GET',
    params: data
  });
}

export function getShipWarningById(id: any) {
  return request({
    url: '/system/shipEarlyWarning/' + id,
    method: 'GET'
  });
}

export function updateShipWarning(data: any) {
  return request({
    url: '/system/shipEarlyWarning',
    method: 'PUT',
    data: data
  });
}

export function deleteGShipWarning(id: any) {
  return request({
    url: '/system/shipEarlyWarning/' + id,
    method: 'DELETE'
  });
}

export function deleteGShipWarningBatch(ids: string) {
  return request({
    url: '/system/shipEarlyWarning/' + ids,
    method: 'DELETE'
  });
}
