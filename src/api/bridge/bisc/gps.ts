
import request from '@/utils/request';

// 获取运输路线列表
export function getTransportRouteList(data: any) {
    return request({
      url: '/system/transportRoute/list',
      method: 'GET',
      params: data
    });
  }

  // 添加运输路线
export function addTransportRoute(data: any) {
    return request({
      url: '/system/transportRoute',
      method: 'POST',
      data: data
    });
  }

  // 更新运输路线
export function updateTransportRoute(data: any) {
    return request({
      url: '/system/transportRoute',
      method: 'PUT',
      data: data
    });
  }

  // 删除运输路线
export function deleteTransportRoute(id: any) {
    return request({
      url: '/system/transportRoute/' + id,
      method: 'DELETE'
    });
  }

  // 获取运输路线详情
export function getTransportRouteById(id: any) {
    return request({
      url: '/system/transportRoute/' + id,
      method: 'GET'
    });
  }

  //获取车辆GPS定位列表
export function getVehicleGpsList(data: any) {
    return request({
      url: '/system/vehicleGpsTracking/list',
      method: 'GET',
      params: data
    });
  }

  // 添加车辆GPS定位
export function addVehicleGps(data: any) {
    return request({
      url: '/system/vehicleGpsTracking',
      method: 'POST',
      data: data
    });
  }

  // 更新车辆GPS定位
export function updateVehicleGps(data: any) {
    return request({
      url: '/system/vehicleGpsTracking',
      method: 'PUT',
      data: data
    });
  }
  
  // 删除车辆GPS定位
export function deleteVehicleGps(id: any) {
    return request({
      url: '/system/vehicleGpsTracking/' + id,
      method: 'DELETE'
    });
  }

  // 获取车辆GPS定位详情
export function getVehicleGpsById(id: any) {
    return request({
      url: '/system/vehicleGpsTracking/' + id,
      method: 'GET'
    });
  }

