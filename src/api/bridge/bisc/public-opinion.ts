import request from '@/utils/request';

//获取舆情来源列表
export function getPublicOpinionList(data: any) {
  return request({
    url: '/system/opinionSource/list',
    method: 'GET',
    params: data
  });
}

//获取舆情来源详情
export function getPublicOpinionById(id: any) {
  return request({
    url: '/system/opinionSource/' + id,
    method: 'GET'
  });
}

//添加舆情来源
export function addPublicOpinion(data: any) {
  return request({
    url: '/system/opinionSource',
    method: 'POST',
    data: data
  });
}

//更新舆情来源
export function updatePublicOpinion(data: any) {
  return request({
    url: '/system/opinionSource',
    method: 'PUT',
    data: data
  });
}

//删除舆情来源
export function deletePublicOpinion(id: any) {
  return request({
    url: '/system/opinionSource/' + id,
    method: 'DELETE'
  });
}

//获取舆情信息列表
export function getPublicOpinionInfoList(data: any) {
  return request({
    url: '/system/opinionInfo/list',
    method: 'GET',
    params: data
  });
}

//获取舆情信息详情
export function getPublicOpinionInfoById(id: any) {
  return request({
    url: '/system/opinionInfo/' + id,
    method: 'GET'
  });
}

//添加舆情信息
export function addPublicOpinionInfo(data: any) {
  return request({
    url: '/system/opinionInfo',
    method: 'POST',
    data: data
  });
}

//更新舆情信息
export function updatePublicOpinionInfo(data: any) {
  return request({
    url: '/system/opinionInfo',
    method: 'PUT',
    data: data
  });
}

//删除舆情信息
export function deletePublicOpinionInfo(id: any) {
  return request({
    url: '/system/opinionInfo/' + id,
    method: 'DELETE'
  });
}

//获取舆情热词列表
export function getPublicOpinionHotWordList(data: any) {
  return request({
    url: '/system/opinionHotwordTag/list',
    method: 'GET',
    params: data
  });
}

//获取舆情热词详情
export function getPublicOpinionHotWordById(id: any) {
  return request({
    url: '/system/opinionHotwordTag/' + id,
    method: 'GET'
  });
}

//添加舆情热词
export function addPublicOpinionHotWord(data: any) {
  return request({
    url: '/system/opinionHotwordTag',
    method: 'POST',
    data: data
  });
}

//更新舆情热词
export function updatePublicOpinionHotWord(data: any) {
  return request({
    url: '/system/opinionHotwordTag',
    method: 'PUT',
    data: data
  });
}

//删除舆情热词
export function deletePublicOpinionHotWord(id: any) {
  return request({
    url: '/system/opinionHotwordTag/' + id,
    method: 'DELETE'
  });
}
