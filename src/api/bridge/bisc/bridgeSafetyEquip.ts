import request from '@/utils/request';

// 获取桥梁安全设备列表
export function getBridgeSafetyEquipList(data: any) {
  return request({
    url: '/system/bridgeSafetyEquip/list',
    method: 'GET',
    params: data
  });
}

// 添加桥梁安全设备
export function addBridgeSafetyEquip(data: any) {
  return request({
    url: '/system/bridgeSafetyEquip',
    method: 'POST',
    data: data
  });
}

// 更新桥梁安全设备
export function updateBridgeSafetyEquip(data: any) {
  return request({
    url: '/system/bridgeSafetyEquip',
    method: 'PUT',
    data: data
  });
}

// 删除桥梁安全设备
export function deleteBridgeSafetyEquip(id: any) {
  return request({
    url: '/system/bridgeSafetyEquip/' + id,
    method: 'DELETE'
  });
}

// 获取桥梁安全设备详情
export function getBridgeSafetyEquipDetail(id: any) {
  return request({
    url: '/system/bridgeSafetyEquip/' + id,
    method: 'GET'
  });
}

// 获取桥梁养护列表
export function getBridgeMaintainList(data: any) {
  return request({
    url: '/system/bridgeMaintain/list',
    method: 'GET',
    params: data
  });
}

// 添加桥梁养护信息
export function addBridgeMaintain(data: any) {
  return request({
    url: '/system/bridgeMaintain',
    method: 'POST',
    data: data
  });
}

// 更新桥梁养护信息
export function updateBridgeMaintain(data: any) {
  return request({
    url: '/system/bridgeMaintain',
    method: 'PUT',
    data: data
  });
}

// 删除桥梁养护信息
export function deleteBridgeMaintain(id: any) {
  return request({
    url: '/system/bridgeMaintain/' + id,
    method: 'DELETE'
  });
}

// 获取桥梁养护详情
export function getBridgeMaintain(id: any) {
  return request({
    url: '/system/bridgeMaintain/' + id,
    method: 'GET'
  });
}
