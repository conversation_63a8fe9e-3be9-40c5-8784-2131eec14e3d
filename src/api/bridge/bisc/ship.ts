import request from '@/utils/request';

//获取船舶信息列表
export function getShipInfoList(data: any) {
  return request({
    url: '/system/shipInfo/list',
    method: 'GET',
    params: data
  });
}

//获取船舶信息详情
export function getShipInfoById(id: any) {
  return request({
    url: '/system/shipInfo/' + id,
    method: 'GET'
  });
}

//添加船舶信息
export function addShipInfo(data: any) {
  return request({
    url: '/system/shipInfo',
    method: 'POST',
    data: data
  });
}

//更新船舶信息
export function updateShipInfo(data: any) {
  return request({
    url: '/system/shipInfo',
    method: 'PUT',
    data: data
  });
}

//删除船舶信息
export function deleteShipInfo(id: any) {
  return request({
    url: '/system/shipInfo/' + id,
    method: 'DELETE'
  });
}

//获取船舶雷达扫描数据列表
export function getShipRadarScanDataList(data: any) {
  return request({
    url: '/system/shipRadarScan/list',
    method: 'GET',
    params: data
  });
}

//获取船舶雷达扫描数据详情
export function getShipRadarScanDataById(id: any) {
  return request({
    url: '/system/shipRadarScan/' + id,
    method: 'GET'
  });
}

//添加船舶雷达扫描数据
export function addShipRadarScanData(data: any) {
  return request({
    url: '/system/shipRadarScan',
    method: 'POST',
    data: data
  });
}

//更新船舶雷达扫描数据
export function updateShipRadarScanData(data: any) {
  return request({
    url: '/system/shipRadarScan',
    method: 'PUT',
    data: data
  });
}

//删除船舶雷达扫描数据
export function deleteShipRadarScanData(id: any) {
  return request({
    url: '/system/shipRadarScan/' + id,
    method: 'DELETE'
  });
}

//获取船舶AIS数据列表
export function getShipAISDataList(data: any) {
  return request({
    url: '/system/shipAisPosition/list',
    method: 'GET',
    params: data
  });
}

//获取船舶AIS数据详情
export function getShipAISDataById(id: any) {
  return request({
    url: '/system/shipAisPosition/' + id,
    method: 'GET'
  });
}

//添加船舶AIS数据
export function addShipAISData(data: any) {
  return request({
    url: '/system/shipAisPosition',
    method: 'POST',
    data: data
  });
}

//更新船舶AIS数据
export function updateShipAISData(data: any) {
  return request({
    url: '/system/shipAisPosition',
    method: 'PUT',
    data: data
  });
}

//删除船舶AIS数据
export function deleteShipAISData(id: any) {
  return request({
    url: '/system/shipAisPosition/' + id,
    method: 'DELETE'
  });
}
