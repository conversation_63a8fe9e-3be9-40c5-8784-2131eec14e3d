// 危化品车辆信息
import request from '@/utils/request';

// 获取危化品车辆信息
export function getDangerousGoodsCarInfo(data: any) {
  return request({
    url: '/system/hazardousVehicle/list',
    method: 'GET',
    params: data
  });
}

// 添加危化品车辆信息
export function addDangerousGoodsCarInfo(data: any) {
  return request({
    url: '/system/hazardousVehicle',
    method: 'POST',
    data: data
  });
}

// 更新危化品车辆信息
export function updateDangerousGoodsCarInfo(data: any) {
  return request({
    url: '/system/hazardousVehicle',
    method: 'PUT',
    data: data
  });
}

// 删除危化品车辆信息
export function deleteDangerousGoodsCarInfo(id: any) {
  return request({
    url: '/system/hazardousVehicle/' + id,
    method: 'DELETE'
  });
}

// 获取危化品车辆信息详情
export function getDangerousGoodsCarInfoById(id: any) {
  return request({
    url: '/system/hazardousVehicle/' + id,
    method: 'GET'
  });
}

//获取危化品运输信息
export function getDangerousGoodsTransportInfo(data: any) {
  return request({
    url: '/system/hazardousTransport/list',
    method: 'GET',
    params: data
  });
}

// 添加危化品运输信息
export function addDangerousGoodsTransportInfo(data: any) {
  return request({
    url: '/system/hazardousTransport',
    method: 'POST',
    data: data
  });
}

// 更新危化品运输信息
export function updateDangerousGoodsTransportInfo(data: any) {
  return request({
    url: '/system/hazardousTransport',
    method: 'PUT',
    data: data
  });
}

// 删除危化品运输信息
export function deleteDangerousGoodsTransportInfo(id: any) {
  return request({
    url: '/system/hazardousTransport/' + id,
    method: 'DELETE'
  });
}

// 获取危化品运输信息详情
export function getDangerousGoodsTransportInfoById(id: any) {
  return request({
    url: '/system/hazardousTransport/' + id,
    method: 'GET'
  });
}

//获取物资分类信息
export function getSupplyCategoryInfo(data: any) {
  return request({
    url: '/system/supplyCategory/list',
    method: 'GET',
    params: data
  });
}

// 添加物资分类信息
export function addSupplyCategoryInfo(data: any) {
  return request({
    url: '/system/supplyCategory',
    method: 'POST',
    data: data
  });
}

// 更新物资分类信息
export function updateSupplyCategoryInfo(data: any) {
  return request({
    url: '/system/supplyCategory',
    method: 'PUT',
    data: data
  });
}

// 删除物资分类信息
export function deleteSupplyCategoryInfo(id: any) {
  return request({
    url: '/system/supplyCategory/' + id,
    method: 'DELETE'
  });
}

// 获取物资分类信息详情
export function getSupplyCategoryInfoById(id: any) {
  return request({
    url: '/system/supplyCategory/' + id,
    method: 'GET'
  });
}

//获取应急物资列表
export function getEmergencySuppliesList(data: any) {
  return request({
    url: '/system/emergencySupplies/list',
    method: 'GET',
    params: data
  });
}

// 添加应急物资
export function addEmergencySupplies(data: any) {
  return request({
    url: '/system/emergencySupplies',
    method: 'POST',
    data: data
  });
}

// 更新应急物资
export function updateEmergencySupplies(data: any) {
  return request({
    url: '/system/emergencySupplies',
    method: 'PUT',
    data: data
  });
}

// 删除应急物资
export function deleteEmergencySupplies(id: any) {
  return request({
    url: '/system/emergencySupplies/' + id,
    method: 'DELETE'
  });
}

// 获取应急物资详情
export function getEmergencySuppliesById(id: any) {
  return request({
    url: '/system/emergencySupplies/' + id,
    method: 'GET'
  });
}

//   查询所有物资分类数据
export function getSupplyCategoryList() {
  return request({
    url: '/system/supplyCategory/all',
    method: 'GET'
  });
}
