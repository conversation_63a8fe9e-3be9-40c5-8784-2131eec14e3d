import request from '@/utils/request';

// 获取船舶信息列表
export function getShipInfoList(data: any) {
  return request({
    url: '/system/shipInfo/list',
    method: 'GET',
    params: data
  });
}
// 获取船舶信息详情
export function getShipInfoById(id: any) {
  return request({
    url: '/system/shipInfo/' + id,
    method: 'GET'
  });
}

// 新增船舶信息
export function addShipInfo(data: any) {
  return request({
    url: '/system/shipInfo',
    method: 'POST',
    data: data
  }); 
}
// 修改船舶信息
export function updateShipInfo(data: any) {
  return request({
    url: '/system/shipInfo',
    method: 'PUT',
    data: data
  });
}

// 删除船舶信息
export function deleteShipInfo(id: any) {
  return request({
    url: '/system/shipInfo/' + id,
    method: 'DELETE'
  }); 
}

// 获取船舶定位数据列表
export function getShipLocationList(data: any) {
  return request({
    url: '/system/shipAisPosition/list',
    method: 'GET',
    params: data
  }); 
}
// 获取船舶定位数据详情
export function getShipLocationById(id: any) {
  return request({
    url: '/system/shipAisPosition/' + id,
    method: 'GET'
  });
}
// 新增船舶定位数据
export function addShipLocation(data: any) {
  return request({
    url: '/system/shipAisPosition',
    method: 'POST',
    data: data
  });
}
// 修改船舶定位数据
export function updateShipLocation(data: any) {
  return request({
    url: '/system/shipAisPosition',
    method: 'PUT',
    data: data
  });
}

// 删除船舶定位数据
export function deleteShipLocation(id: any) {
  return request({
    url: '/system/shipAisPosition/' + id,
    method: 'DELETE'
  });
}

//获取船舶雷达扫描数据列表
export function getShipRadarList(data: any) {
  return request({
    url: '/system/shipRadarScan/list',
    method: 'GET',
    params: data
  }); 
}
//获取船舶雷达扫描数据详情
export function getShipRadarById(id: any) {
  return request({
    url: '/system/shipRadarScan/' + id,
    method: 'GET'
  });
}

//新增船舶雷达扫描数据
export function addShipRadar(data: any) {
  return request({
    url: '/system/shipRadarScan',
    method: 'POST',
    data: data
  });
}

//修改船舶雷达扫描数据
export function updateShipRadar(data: any) {
  return request({
    url: '/system/shipRadarScan',
    method: 'PUT',
    data: data
  }); 
}
//删除船舶雷达扫描数据
export function deleteShipRadar(id: any) {
  return request({
    url: '/system/shipRadarScan/' + id,
    method: 'DELETE'
  });
}