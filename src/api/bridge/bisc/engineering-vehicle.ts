// 工程车辆信息
import request from '@/utils/request';

// 获取工程车辆信息列表
export function getEngineeringVehicleList(data: any) {
  return request({
    url: '/system/trafficEngineeringVehicle/list',
    method: 'GET',
    params: data
  });
}

// 添加工程车辆信息
export function addEngineeringVehicle(data: any) {
  return request({
    url: '/system/trafficEngineeringVehicle',
    method: 'POST',
    data: data
  });
}

// 更新工程车辆信息
export function updateEngineeringVehicle(data: any) {
  return request({
    url: '/system/trafficEngineeringVehicle',
    method: 'PUT',
    data: data
  });
}

// 删除工程车辆信息
export function deleteEngineeringVehicle(id: any) {
  return request({
    url: '/system/trafficEngineeringVehicle/' + id,
    method: 'DELETE'
  });
}

// 获取工程车辆信息详情
export function getEngineeringVehicleById(id: any) {
  return request({
    url: '/system/trafficEngineeringVehicle/' + id,
    method: 'GET'
  });
}

// 获取执法机构列表
export function getLawEnforcementAgencyList() {
  return request({
    url: '/system/lawEnforcementAgency/all',
    method: 'GET'
  });
}

// 获取桥梁列表
export function getBridgeList() {
  return request({
    url: '/system/bridge/all',
    method: 'GET'
  });
}
