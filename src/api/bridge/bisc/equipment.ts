import request from '@/utils/request';

//新增设备信息
export function getDeviceInfo(data: any) {
  return request({
    url: '/system/deviceInfo/list',
    method: 'GET',
    params: data
  });
}

// 添加设备信息
export function addDeviceInfo(data: any) {
  return request({
    url: '/system/deviceInfo',
    method: 'POST',
    data: data
  });
}

// 修改设备信息
export function updateEDeviceInfo(data: any) {
  return request({
    url: '/system/deviceInfo',
    method: 'PUT',
    data: data
  });
}

// 删除设备信息
export function deleteDeviceInfo(id: any) {
  return request({
    url: '/system/deviceInfo/' + id,
    method: 'DELETE'
  });
}

//设备信息详情
export function getDeviceDetail(id: any) {
  return request({
    url: '/system/deviceInfo/' + id,
    method: 'GET'
  });
}
