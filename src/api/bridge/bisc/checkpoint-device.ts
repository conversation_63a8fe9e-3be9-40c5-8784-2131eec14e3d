import request from '@/utils/request';

// 获取卡口设备列表
export function getCheckpointDeviceList(data: any) {
  return request({
    url: '/system/checkpointDevice/list',
    method: 'GET',
    params: data
  });
}

// 获取卡口设备详情
export function getCheckpointDeviceDetail(id: any) {
  return request({
    url: '/system/checkpointDevice/' + id,
    method: 'GET'
  });
}

// 新增卡口设备
export function addCheckpointDevice(data: any) {
  return request({
    url: '/system/checkpointDevice',
    method: 'POST',
    data: data
  });
}

// 修改卡口设备
export function updateCheckpointDevice(data: any) {
  return request({
    url: '/system/checkpointDevice',
    method: 'PUT',
    data: data
  });
}

// 删除卡口设备
export function deleteCheckpointDevice(id: any) {
  return request({
    url: '/system/checkpointDevice/' + id,
    method: 'DELETE'
  });
}
