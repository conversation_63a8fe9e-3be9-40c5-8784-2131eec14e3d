import request from '@/utils/request'

//修改
export function appEdit(data) {
    return request({
        url: '/system/appVersion',
        method: 'put',
        data: data
    })
}
//新增
export function appAdd(data) {
    return request({
        url: '/system/appVersion',
        method: 'post',
        data: data
    })
}
//详情
export function appDetail(data) {
    return request({
        url: '/system/appVersion/' + data,
        method: 'get',
    })
}
//删除
export function appDelete(data) {
    return request({
        url: '/system/appVersion/' + data,
        method: 'delete',
    })
}
//apk上传
export function appUpload(data) {
    return request({
        url: '/system/appVersion/apkfile',
        method: 'post',
        data: data
    })
}
//apk列表
export function appList(data) {
    return request({
        url: '/system/appVersion',
        method: 'get',
        params: data
    })
}
//批量删除
export function appDeleteMore(data) {
    return request({
        url: '/system/appVersion/removes',
        method: 'delete',
        data: data
    })
}