import request from '@/utils/request'
import { func } from 'vue-types'
//列表
export function getWhiteList(data) {
    return request({
        url: '/system/whiteList/list',
        method: 'get',
        params: data
    })
}

//删除
export function clearWhiteList(id) {
    return request({
        url: '/system/whiteList/' + id,
        method: 'delete'
    })
}
//编辑
export function editWhiteList(data) {
    return request({
        url: '/system/whiteList',
        method: 'put',
        data: data
    })
}
//详情
export function getWhiteListDetail(id) {
    return request({
        url: '/system/whiteList/' + id,
        method: 'get'
    })
}
// 新增
export function addWhiteList(data) {
    return request({
        url: '/system/whiteList',
        method: 'post',
        data: data
    })
}
// mmsi查询详情
export function detailByMMSI(data) {
    return request({
        url: '/system/shipInfo/byMMSI/' + data.mmsi,
        method: 'post',
    })
}

//查询船舶数据

export function searchShipInfo(data) {
    return request({
        url: '/system/shipData/getBySearch' ,
        method: 'get',
        params: data
    })
}