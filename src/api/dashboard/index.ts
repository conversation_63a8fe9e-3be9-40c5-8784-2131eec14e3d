import request from '@/utils/request';
import { 
  mockStats, 
  mockTodos, 
  mockPlatformItems, 
  mockKnowledgeItems, 
  mockHelperItems,
  simulateApiDelay 
} from './mock';

// 是否使用模拟数据
const USE_MOCK = true;

// 获取工作台统计数据
export function getDashboardStats() {
  if (USE_MOCK) {
    return simulateApiDelay(mockStats);
  }
  return request({
    url: '/bridge/dashboard/stats',
    method: 'get'
  });
}

// 获取待办事项
export function getTodoItems() {
  if (USE_MOCK) {
    return simulateApiDelay(mockTodos);
  }
  return request({
    url: '/bridge/dashboard/todos',
    method: 'get'
  });
}

// 获取平台详情
export function getPlatformItems() {
  if (USE_MOCK) {
    return simulateApiDelay(mockPlatformItems);
  }
  return request({
    url: '/bridge/dashboard/platform',
    method: 'get'
  });
}

// 获取知识库数据
export function getKnowledgeItems() {
  if (USE_MOCK) {
    return simulateApiDelay(mockKnowledgeItems);
  }
  return request({
    url: '/bridge/dashboard/knowledge',
    method: 'get'
  });
}

// 获取平台帮手数据
export function getHelperItems() {
  if (USE_MOCK) {
    return simulateApiDelay(mockHelperItems);
  }
  return request({
    url: '/bridge/dashboard/helpers',
    method: 'get'
  });
}

// 处理待办事项
export function handleTodoItem(id: string, action: string) {
  if (USE_MOCK) {
    // 模拟处理待办事项
    const updatedTodos = mockTodos.map(item => {
      if (item.id === id && action === 'complete') {
        return { ...item, status: '已完成' };
      }
      return item;
    });
    return simulateApiDelay({ id, success: true });
  }
  return request({
    url: '/bridge/dashboard/todo/handle',
    method: 'post',
    data: {
      id,
      action
    }
  });
}

// 统计接口数据类型定义
export interface TrendData {
  current: number;
  direction: 'up' | 'down';
  value: number;
}

export interface DashboardStats {
  bridgeHealth: number;
  warningCount: number;
  deviceTotal: number;
  maintenanceCount: number;
  trafficVolume: number;
  warningTrend: TrendData;
  deviceTrend: TrendData;
  maintenanceTrend: TrendData;
  trafficTrend: TrendData;
}

// 待办项数据类型
export interface TodoItem {
  id: string;
  title: string;
  status: string;
  type: string;
  priority: 'high' | 'medium' | 'low';
  createTime: string;
}

// 平台详情数据类型
export interface PlatformItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  imageSrc: string;
}

// 知识库数据类型
export interface KnowledgeItem {
  id: string;
  title: string;
  description: string;
  icon: string;
}

// 平台帮手数据类型
export interface HelperItem {
  id: string;
  title: string;
  description: string;
  imageSrc: string;
}

// 获取桥梁健康状态
export function getBridgeHealthStatus() {
  if (USE_MOCK) {
    return simulateApiDelay({
      health: 92,
      components: [
        { name: '主梁', score: 95 },
        { name: '桥墩', score: 93 },
        { name: '桥面', score: 90 },
        { name: '伸缩缝', score: 85 },
        { name: '支座', score: 89 }
      ],
      lastUpdated: new Date().toISOString()
    });
  }
  return request({
    url: '/bridge/health/status',
    method: 'get'
  });
}

// 获取传感器状态
export function getSensorStatus() {
  if (USE_MOCK) {
    return simulateApiDelay({
      total: 248,
      online: 243,
      offline: 5,
      warning: 3,
      categories: [
        { name: '应变传感器', online: 62, total: 65 },
        { name: '振动传感器', online: 58, total: 60 },
        { name: '位移传感器', online: 55, total: 55 },
        { name: '倾角传感器', online: 38, total: 38 },
        { name: '加速度传感器', online: 30, total: 30 }
      ]
    });
  }
  return request({
    url: '/bridge/sensor/status',
    method: 'get'
  });
}

// 获取天气数据
export function getWeatherData() {
  if (USE_MOCK) {
    return simulateApiDelay({
      location: '舟山大桥区域',
      current: {
        temperature: 28,
        condition: 'sunny',
        humidity: 65,
        windSpeed: 32,
        windDirection: '东南风'
      },
      forecast: [
        { date: '06-01', condition: 'cloudy', tempMax: 32, tempMin: 24, windSpeed: 28 },
        { date: '06-02', condition: 'rainy', tempMax: 26, tempMin: 22, windSpeed: 35 },
        { date: '06-03', condition: 'rainy', tempMax: 25, tempMin: 21, windSpeed: 40 }
      ]
    });
  }
  return request({
    url: '/bridge/weather/current',
    method: 'get'
  });
} 