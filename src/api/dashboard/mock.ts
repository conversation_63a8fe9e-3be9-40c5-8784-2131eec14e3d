// 模拟API响应
import { DashboardStats, TodoItem, PlatformItem, KnowledgeItem, HelperItem } from './index';

// 模拟统计数据
export const mockStats: DashboardStats = {
  bridgeHealth: 92,
  warningCount: 5,
  deviceTotal: 248,
  maintenanceCount: 8,
  trafficVolume: 35612,
  warningTrend: {
    current: 18,
    direction: 'up',
    value: 12
  },
  deviceTrend: {
    current: 15,
    direction: 'up',
    value: 3
  },
  maintenanceTrend: {
    current: 6,
    direction: 'down',
    value: 10
  },
  trafficTrend: {
    current: 34200,
    direction: 'up',
    value: 4
  }
};

// 模拟待办事项
export const mockTodos: TodoItem[] = [
  {
    id: '1',
    title: '振动传感器异常检查',
    status: '待处理',
    type: '设备检查',
    priority: 'high',
    createTime: '2023-06-01'
  },
  {
    id: '2',
    title: '桥墩位移监测',
    status: '进行中',
    type: '安全监测',
    priority: 'medium',
    createTime: '2023-06-02'
  },
  {
    id: '3',
    title: '风速预警处理',
    status: '待处理',
    type: '安全预警',
    priority: 'high',
    createTime: '2023-06-03'
  },
  {
    id: '4',
    title: '设备季度检修',
    status: '已完成',
    type: '设备维护',
    priority: 'low',
    createTime: '2023-06-04'
  }
];

// 模拟平台详情
export const mockPlatformItems: PlatformItem[] = [
  {
    id: '1',
    title: '实时监控',
    description: '实时监控桥梁结构、传感器数据和车流量信息，提供全方位监控视图',
    icon: 'Monitor',
    imageSrc: ''
  },
  {
    id: '2',
    title: '预警管理',
    description: '风力、位移、振动等多维度预警管理，及时发现安全隐患',
    icon: 'Warning',
    imageSrc: ''
  },
  {
    id: '3',
    title: '交通管控',
    description: '提供车流量监测、限速管理和交通事故处理等功能',
    icon: 'Guide',
    imageSrc: ''
  },
  {
    id: '4',
    title: '设备管理',
    description: '全面管理桥梁监测设备，包括维护计划和故障处理',
    icon: 'Setting',
    imageSrc: ''
  }
];

// 模拟知识库数据
export const mockKnowledgeItems: KnowledgeItem[] = [
  {
    id: '1',
    title: '桥梁结构监测指南',
    description: '桥梁结构健康监测的标准和方法',
    icon: 'Document'
  },
  {
    id: '2',
    title: '传感器使用手册',
    description: '桥梁监测传感器的安装和维护指南',
    icon: 'Document'
  },
  {
    id: '3',
    title: '预警处理流程',
    description: '各类预警信息的处理流程和应急预案',
    icon: 'Document'
  },
  {
    id: '4',
    title: '交通管控规范',
    description: '桥梁交通管控的标准操作规程',
    icon: 'Document'
  }
];

// 模拟平台帮手数据
export const mockHelperItems: HelperItem[] = [
  {
    id: '1',
    title: '平台操作指南',
    description: '舟山大桥综管指挥平台功能操作详解',
    imageSrc: ''
  },
  {
    id: '2',
    title: '应急处置手册',
    description: '突发事件处理流程和应对措施',
    imageSrc: ''
  }
];

// 创建模拟响应
export function createMockResponse<T>(data: T) {
  return Promise.resolve({
    code: 200,
    msg: '操作成功',
    data
  });
}

// 处理API请求
export function simulateApiDelay<T>(data: T, delay: number = 500): Promise<any> {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '操作成功',
        data
      });
    }, delay);
  });
} 