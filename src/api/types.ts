/**
 * 注册
 */
export type RegisterForm = {
  tenantId: string;
  username: string;
  password: string;
  confirmPassword?: string;
  code?: string;
  uuid?: string;
  userType?: string;
};

/**
 * 登录请求
 */
export interface LoginData {
  tenantId?: string;
  username?: string;
  password?: string;
  rememberMe?: boolean;
  socialCode?: string;
  socialState?: string;
  source?: string;
  code?: string;
  uuid?: string;
  clientId: string;
  grantType: string;
}

/**
 * 登录响应
 */
export interface LoginResult {
  access_token: string;
}

/**
 * 钉钉用户信息
 */
export interface DingTalkUserInfo {
  lastName?: string;
  accountId: string;
  realmId?: string;
  clientId?: string;
  tenantName?: string;
  realmName?: string;
  namespace?: string;
  tenantId: string;
  nickNameCn?: string;
  tenantUserId?: string;
  account?: string;
  employeeCode?: string;
}

/**
 * 验证码返回
 */
export interface VerifyCodeResult {
  captchaEnabled: boolean;
  uuid?: string;
  img?: string;
}

/**
 * 租户
 */
export interface TenantVO {
  companyName: string;
  domain: any;
  tenantId: string;
}

export interface TenantInfo {
  tenantEnabled: boolean;
  voList: TenantVO[];
}

// export interface AccidentEffect {
//   warningName: string;
//   occurrenceLocation: string;
//   warningLevel: string;
//   accidentType: string;
//   occurTime: string;
// }
