<template>
  <div id="base-map" class="map"></div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Map from 'ol/Map';
import View from 'ol/View';
import { defaults as Defaults } from 'ol/control.js';
import TileLayer from 'ol/layer/Tile';
import XYZ from 'ol/source/XYZ';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Style, Circle as CircleStyle, Fill, Stroke } from 'ol/style';

// 定义地图实例对象
const map = ref<Map | null>(null);
const vectorSource = new VectorSource();

// 创建白色标注样式
const whiteMarkerStyle = new Style({
  image: new CircleStyle({
    radius: 6,
    fill: new Fill({
      color: '#FFFFFF'
    }),
    stroke: new Stroke({
      color: '#FFFFFF',
      width: 2
    })
  })
});

// 初始化地图
const initMap = () => {
  // 天地图
  const source = new XYZ({
    url: 'https://t4.tianditu.com/DataServer?T=vec_w&tk=9dc4d6ee856d047e1a0caf2eec1c17f8&x={x}&y={y}&l={z}'
    // url: 'https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=9dc4d6ee856d047e1a0caf2eec1c17f8'
  });
  const tileLayer = new TileLayer({
    source: source,
    className: 'light-layer'
  });

  // 标注图层
  const sourceMark = new XYZ({
    url: 'https://t4.tianditu.com/DataServer?T=cva_w&tk=9dc4d6ee856d047e1a0caf2eec1c17f8&x={x}&y={y}&l={z}'
    // url: 'https://t0.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=9dc4d6ee856d047e1a0caf2eec1c17f8'
  });
  const tileMark = new TileLayer({
    source: sourceMark,
    className: 'label-layer'
  });

  // 创建地图对象
  map.value = new Map({
    target: 'base-map',
    layers: [tileLayer, tileMark],
    view: new View({
      projection: 'EPSG:4326',
      center: [122.111231, 30.009381],
      zoom: 16,
      maxZoom: 20,
      minZoom: 1
    }),
    controls: Defaults({
      zoom: false,
      rotate: false
    })
  });

  // 添加打点图层
  const vectorLayer = new VectorLayer({
    source: vectorSource,
    style: whiteMarkerStyle
  });
  map.value.addLayer(vectorLayer);
};

// 使用 onMounted 生命周期钩子
onMounted(() => {
  initMap();
});

// 窗口拖拉，更新地图大小
window.addEventListener('resize', () => {
  if (map.value) {
    map.value.updateSize();
  }
});

// 导出地图实例
// export { map };
</script>

<style scoped lang="scss">
.map {
  width: 100vw;
  height: 100vh;
  :deep(.dark-layer) {
    filter: blur(0px) grayscale(0%) sepia(100%) invert(95%) saturate(0%);
  }
  :deep(.label-layer) {
    filter: invert(100%) brightness(250%);
  }
  :deep(.light-layer) {
    filter: brightness(100%) saturate(100%);
  }
}
</style>
