<template>
  <div class="portal-container">
    <div class="portal-background"></div>
    <div class="portal-content">
      <!-- 头部标题和徽标 -->
      <header class="portal-header">
        <div class="logo-container">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" class="bridge-icon" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 12H18M6 12V16C6 17.1046 5.10457 18 4 18H2M6 12V8M18 12V16C18 17.1046 18.8954 18 20 18H22M18 12V8M6 8H18M6 8C6 6.89543 5.10457 6 4 6H2M18 8C18 6.89543 18.8954 6 20 6H22" 
                stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
        </div>
        <h1 class="portal-title">舟山大桥统一门户系统</h1>
        <p class="portal-subtitle">请选择要访问的业务平台</p>
      </header>

      <!-- 平台卡片容器 -->
      <div class="platform-cards">
        <!-- 综合管理平台卡片 -->
        <div 
          class="platform-card integrated"
          :class="{ 'is-loading': loading.integrated }"
          @click="navigateTo('integrated')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon><Platform /></el-icon>
              <div class="icon-glow"></div>
            </div>
            <div class="card-info">
              <h2 class="card-title">大桥综合管理平台</h2>
              <p class="card-description">包含日常运营、设备监控、人员管理等综合功能</p>
            </div>
            <div class="card-action">
              <el-button
                type="primary"
                class="entry-button"
                :loading="loading.integrated"
                @click.stop="navigateTo('integrated')"
              >
                <template #default>
                  <span v-if="!loading.integrated">立即进入</span>
                </template>
                <template #loading>
                  <span>跳转中...</span>
                  <el-icon>
                  <Loading />
                </el-icon>
                </template>
              </el-button>
            </div>
          </div>
          <div class="card-ripple"></div>
        </div>

        <!-- 驾驶舱综合监管平台卡片 -->
        <div 
          class="platform-card cockpit"
          :class="{ 'is-loading': loading.cockpit }"
          @click="navigateTo('cockpit')"
        >
        <!-- <div
          class="platform-card cockpit"
          :class="{ 'is-loading': loading.cockpit }"
        > -->
          <div class="card-content">
            <div class="card-icon">
              <el-icon><DataBoard /></el-icon>
              <div class="icon-glow"></div>
            </div>
            <div class="card-info">
              <h2 class="card-title">驾驶舱综合监控平台</h2>
              <p class="card-description">集成数据可视化、大屏监控、船舶定位等核心功能</p>
            </div>
            <div class="card-action">
              <el-button
                type="info"
                class="entry-button"
                :loading="loading.cockpit"
                @click.stop="navigateTo('cockpit')"
              >
                <template #default>
                  <span v-if="!loading.cockpit">立即进入</span>
                </template>
                <template #loading>
                  <span>跳转中...</span>
                  <el-icon>
                  <Loading />
                </el-icon>
                </template>
              </el-button>
            </div>
          </div>
          <div class="card-ripple"></div>
        </div>

        <!-- 健康管理平台卡片 -->
          <!-- <div 
            class="platform-card health" 
            :class="{ 'is-loading': loading.health }"
          > -->
          <div 
          class="platform-card health" 
          :class="{ 'is-loading': loading.health }"
          @click="navigateTo('health')"
        >
          <div class="card-content">
            <div class="card-icon">
              <el-icon><Monitor /></el-icon>
              <div class="icon-glow"></div>
            </div>
            <div class="card-info">
              <h2 class="card-title">大桥健康管理平台</h2>
              <p class="card-description">实时监测桥梁结构健康状态，提供专业分析报告</p>
            </div>
            <div class="card-action">
              <el-button
                type="success"
                class="entry-button"
                :loading="loading.health"
                @click.stop="navigateTo('health')"
              >
                <template #default>
                  <span v-if="!loading.health">立即进入</span>
                </template>
                <template #loading>
                  <span>跳转中...</span>
                  <el-icon>
                  <Loading />
                </el-icon>
                </template>
              </el-button>
            </div>
          </div>
          <div class="card-ripple"></div>
        </div>
      </div>

      <!-- 动态波浪效果 -->
      <div class="wave-container">
        <div class="wave wave1"></div>
        <div class="wave wave2"></div>
      </div>

      <!-- 底部提示 -->
      <footer class="portal-footer">
        <span>Copyright © 2025 舟山大桥综管指挥平台.</span>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Platform, Monitor, DataBoard } from '@element-plus/icons-vue'
import { useMouseInElement } from '@vueuse/core'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/store/modules/user'

type PlatformType = 'integrated' | 'health' | 'cockpit'

const router = useRouter()
const userStore = useUserStore()
const loading = ref({
  integrated: false,
  health: false,
  cockpit: false
})

// 添加卡片点击时的涟漪效果
const handleCardRipple = (event: MouseEvent, element: HTMLElement) => {
  const x = event.offsetX
  const y = event.offsetY
  
  const rippleElement = element.querySelector('.card-ripple') as HTMLElement
  if (rippleElement) {
    rippleElement.style.left = `${x}px`
    rippleElement.style.top = `${y}px`
    rippleElement.classList.add('active')
    
    setTimeout(() => {
      rippleElement.classList.remove('active')
    }, 600)
  }
}

const navigateTo = async (platform: PlatformType, event?: MouseEvent) => {
  if (loading.value[platform]) return
  
  try {
    // 添加点击涟漪效果
    if (event) {
      const card = (event.currentTarget as HTMLElement)
      handleCardRipple(event, card)
    }
    
    loading.value[platform] = true
    
    // 移除模拟异步跳转，改为直接跳转
    if (platform === 'integrated') {
      router.push('/index')
    } else if (platform === 'cockpit') {
      // 获取token和用户信息
      const token = getToken()
      // 跳转到驾驶舱平台
      const url = new URL(import.meta.env.VITE_APP_REDIRECT_URL)
      url.searchParams.append('token', token)
      window.open(url.toString(), '_blank')
    } else {
      router.push({
        name: 'HealthPlatform'
      })
    }
  } catch (error) {
    // 只有在导航发生错误时才重置loading状态
    loading.value[platform] = false
    console.error('Navigation error:', error)
  }finally{
    loading.value[platform] = false
  }
}

// 添加页面加载动画
onMounted(() => {
  document.body.classList.add('portal-loaded')
})
</script>

<style scoped>
/* 全局样式 */
.portal-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  color: #fff;
  padding: 1rem;
}

/* 背景样式 */
.portal-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/images/login-background.png');
  background-size: cover;
  z-index: -2;
}

.portal-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(9, 32, 63, 0.7), rgba(9, 32, 63, 0.4));
  z-index: -1;
}

/* 内容容器 */
.portal-content {
  /* max-width: 1000px; */
  width: 75%;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 3rem;
  animation: contentFadeIn 1s ease-out forwards;
}

/* 头部样式 */
.portal-header {
  text-align: center;
  position: relative;
  padding-bottom: 2rem;
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #e9b849, #f8c75b);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 25px -5px rgba(248, 199, 91, 0.4);
  position: relative;
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: float 6s ease-in-out infinite;
}

.logo-icon:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px -5px rgba(248, 199, 91, 0.5);
}

.bridge-icon {
  width: 50px;
  height: 50px;
  color: white;
}

.portal-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #f8c75b;
  margin-bottom: 0.5rem;
  letter-spacing: 0.05em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: titleFadeIn 0.8s ease-out forwards;
}

.portal-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  opacity: 0;
  animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
}

/* 平台卡片容器 */
.platform-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  width: 100%;
}

/* 卡片样式 */
.platform-card {
  position: relative;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  transform: translateY(0);
  min-height: 320px;
}

.platform-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(248, 199, 91, 0.4);
}

.card-content {
  padding: 2.5rem 2rem;
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 卡片图标 */
.card-icon {
  position: relative;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  margin-bottom: 1.5rem;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.platform-card:hover .card-icon {
  transform: scale(1.1) rotate(-5deg);
}

.platform-card.integrated .card-icon {
  background: linear-gradient(135deg, #e9b849, #f8c75b);
  box-shadow: 0 10px 20px -5px rgba(248, 199, 91, 0.4);
}

.platform-card.health .card-icon {
  background: linear-gradient(135deg, #65c887, #38a169);
  box-shadow: 0 10px 20px -5px rgba(56, 161, 105, 0.4);
}

.platform-card.cockpit .card-icon {
  background: linear-gradient(135deg, #3182ce, #2b6cb0);
  box-shadow: 0 10px 20px -5px rgba(49, 130, 206, 0.4);
}

.card-icon .el-icon {
  font-size: 2.5rem;
  color: white;
}

.icon-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  filter: blur(15px);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.platform-card.integrated .icon-glow {
  background: rgba(248, 199, 91, 0.6);
}

.platform-card.health .icon-glow {
  background: rgba(56, 161, 105, 0.6);
}

.platform-card.cockpit .icon-glow {
  background: rgba(49, 130, 206, 0.6);
}

.platform-card:hover .icon-glow {
  opacity: 0.6;
}

/* 卡片信息 */
.card-info {
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
}

.card-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* 卡片按钮 */
.entry-button {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: none;
}

.platform-card.integrated .entry-button {
  background: linear-gradient(to right, #e9b849, #f8c75b);
}

.platform-card.integrated .entry-button:hover,
.platform-card.integrated .entry-button:focus {
  background: linear-gradient(to right, #f8c75b, #ffd780);
}

.platform-card.health .entry-button {
  background: linear-gradient(to right, #38a169, #65c887);
}

.platform-card.health .entry-button:hover,
.platform-card.health .entry-button:focus {
  background: linear-gradient(to right, #65c887, #7ddfa2);
}

.platform-card.cockpit .entry-button {
  background: linear-gradient(to right, #2b6cb0, #3182ce);
}

.platform-card.cockpit .entry-button:hover,
.platform-card.cockpit .entry-button:focus {
  background: linear-gradient(to right, #3182ce, #63b3ed);
}

.entry-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transition: left 0.5s ease;
}

.platform-card:hover .entry-button::after {
  left: 100%;
}

.loading-text {
  margin-left: 0.5rem;
}

/* 点击涟漪效果 */
.card-ripple {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  transform: scale(0);
  background: rgba(255, 255, 255, 0.7);
  opacity: 0;
  pointer-events: none;
  z-index: 10;
}

.card-ripple.active {
  animation: rippleEffect 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 波浪效果 */
.wave-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  overflow: hidden;
  z-index: -1;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100px;
  background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1440 120" xmlns="http://www.w3.org/2000/svg"><path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z" fill="%23f8c75b" fill-opacity="0.1"/></svg>') repeat-x;
  background-size: 1440px 120px;
  animation: waveAnimation 20s linear infinite;
}

.wave1 {
  opacity: 0.3;
  animation-duration: 20s;
  animation-delay: 0s;
}

.wave2 {
  opacity: 0.2;
  animation-duration: 15s;
  animation-direction: reverse;
  animation-delay: -5s;
}

/* 底部提示 */
.portal-footer {
  text-align: center;
  margin-top: 2rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.support-link {
  color: #f8c75b;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.support-link:hover {
  color: #ffd780;
  text-decoration: underline;
}

/* 加载状态 */
.platform-card.is-loading {
  pointer-events: none;
}

/* 动画 */
@keyframes contentFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes subtitleFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rippleEffect {
  0% {
    transform: scale(0);
    opacity: 0.8;
  }
  100% {
    transform: scale(100);
    opacity: 0;
  }
}

@keyframes waveAnimation {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .portal-title {
    font-size: 2rem;
  }
  
  .platform-cards {
    grid-template-columns: 1fr;
  }
  
  .platform-card {
    min-height: 280px;
  }
  
  .card-icon {
    width: 60px;
    height: 60px;
  }
  
  .card-title {
    font-size: 1.3rem;
  }
}

/* 页面加载动画 */
:global(body):not(.portal-loaded) .portal-container {
  opacity: 0;
}

:global(body).portal-loaded .portal-container {
  opacity: 1;
  transition: opacity 0.5s ease;
}
</style>
