<template>
  <div class="todo-item-container" :class="getStatusClass(data.status)" @click="$emit('click', data)">
    <div class="todo-icon">
      <el-icon v-if="data.status === '已完成'"><CircleCheck /></el-icon>
      <el-icon v-else-if="data.priority === 'high'"><Warning /></el-icon>
      <el-icon v-else-if="isWarningType(data.type)"><Bell /></el-icon>
      <el-icon v-else><Clock /></el-icon>
    </div>
    
    <div class="todo-content">
      <div class="todo-title">{{ data.title }}</div>
      <div class="todo-info">
        <span class="todo-type">{{ data.type }}</span>
        <span class="todo-date">{{ formatDate(data.createTime) }}</span>
      </div>
    </div>
    
    <div class="todo-status">
      <el-tag 
        size="small" 
        :type="getTagType(data.status)"
      >
        {{ data.status }}
      </el-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CircleCheck, Warning, Clock, Bell } from '@element-plus/icons-vue';

// 定义待办项接口
interface TodoItemData {
  id: string;
  title: string;
  status: string;
  type: string;
  priority: 'high' | 'medium' | 'low';
  createTime: string;
}

// 定义属性
defineProps<{
  data: TodoItemData;
}>();

// 定义事件
defineEmits<{
  (e: 'click', data: TodoItemData): void;
}>();

// 判断是否为预警类型
function isWarningType(type: string): boolean {
  return type.includes('预警') || type.includes('告警');
}

// 获取状态样式类
function getStatusClass(status: string): string {
  switch (status) {
    case '已完成':
      return 'status-completed';
    case '进行中':
      return 'status-in-progress';
    case '待处理':
      return 'status-pending';
    default:
      return '';
  }
}

// 获取标签类型
function getTagType(status: string): 'success' | 'warning' | 'info' | 'danger' | 'primary' {
  switch (status) {
    case '已完成':
      return 'success';
    case '进行中':
      return 'warning';
    case '待处理':
      return 'info';
    default:
      return 'primary';
  }
}

// 格式化日期
function formatDate(date: string): string {
  if (!date) return '';
  
  const dateObj = new Date(date);
  return dateObj.toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
}
</script>

<style scoped lang="scss">
.todo-item-container {
  flex: 1;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  cursor: pointer;
  border-left: 3px solid transparent;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
  
  &.status-completed {
    background-color: #f0f9eb;
    border-left-color: #67c23a;
  }
  
  &.status-in-progress {
    background-color: #fdf6ec;
    border-left-color: #e6a23c;
  }
  
  &.status-pending {
    background-color: #f0f2f5;
    border-left-color: #409eff;
  }
}

.todo-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
  .el-icon {
    font-size: 20px;
  }
}

.status-completed .todo-icon .el-icon {
  color: #67c23a;
}

.status-in-progress .todo-icon .el-icon {
  color: #e6a23c;
}

.status-pending .todo-icon .el-icon {
  color: #409eff;
}

.todo-content {
  flex: 1;
  
  .todo-title {
    font-size: 15px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .todo-info {
    display: flex;
    font-size: 12px;
    color: #909399;
    
    .todo-type {
      margin-right: 12px;
      position: relative;
      background-color: rgba(64, 158, 255, 0.1);
      color: #409eff;
      padding: 0 6px;
      border-radius: 4px;
      
      &::after {
        content: '';
        position: absolute;
        right: -6px;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 10px;
        background-color: #dcdfe6;
      }
    }
    
    .todo-date {
      margin-left: 6px;
    }
  }
}

.todo-status {
  margin-left: 16px;
}
</style> 