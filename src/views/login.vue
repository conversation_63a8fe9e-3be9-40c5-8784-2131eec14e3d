<template>
  <div class="login">
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
      <div class="title-box" style="display: flex; justify-content: center; align-items: center;
      flex-direction: column;">
        <h3 class="title">欢迎登录</h3>
        <h3 class="title">舟山大桥综合管理指挥平台</h3>
      </div>

      <!-- QR Code Toggle in corner -->
      <div class="login-mode-switch" @click="toggleQrLogin">
        <el-tooltip
          :content="isQrLogin ? '账号密码登录' : '浙政钉扫码登录'"
          placement="top"
        >
          <div>
            <el-icon v-if="isQrLogin" class="mode-icon "
            style="width: 40px; height: 40px;"><Monitor /></el-icon>
            <svg v-else t="1748414179850" class="mode-icon qr-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5190" width="24" height="24">
              <path d="M421.639981 0H43.809703A43.943391 43.943391 0 0 0 0 43.809703v377.830278a43.943391 43.943391 0 0 0 43.809703 43.742859h377.830278a43.943391 43.943391 0 0 0 43.809703-43.809703V43.809703A43.943391 43.943391 0 0 0 421.639981 0z m-49.27756 372.362421H93.087263V93.087263h279.275158zM980.176928 0H602.34665a43.930022 43.930022 0 0 0-43.809703 43.809703v377.830278a43.930022 43.930022 0 0 0 43.809703 43.742859h377.830278A43.943391 43.943391 0 0 0 1024.053475 421.639981V43.809703A43.943391 43.943391 0 0 0 980.176928 0z m-49.27756 372.362421H651.637579V93.087263h279.261789zM421.639981 558.536947H43.809703A43.930022 43.930022 0 0 0 0 602.34665v377.843647a43.943391 43.943391 0 0 0 43.809703 43.809703h377.830278a43.943391 43.943391 0 0 0 43.809703-43.809703V602.34665a43.930022 43.930022 0 0 0-43.809703-43.809703z m-49.27756 372.362421H93.087263v-279.275158h279.275158z" fill="#f8c75b" p-id="5191"></path>
              <path d="M930.899368 1023.986631h49.27756A43.943391 43.943391 0 0 0 1024.053475 980.176928v-49.27756h-93.154107zM744.711473 930.899368h93.087263v93.087263h-93.087263z" fill="#f8c75b" p-id="5192" opacity="0.7"></path>
              <path d="M980.176928 558.536947h-49.27756v186.174526h-93.087263V558.536947H602.34665a43.930022 43.930022 0 0 0-43.809703 43.809703v377.843647a43.930022 43.930022 0 0 0 43.809703 43.809703h49.290929V744.711473h93.087263v93.087263H1024.053475V602.34665a43.930022 43.930022 0 0 0-43.809702-43.809703z m0 0" fill="#f8c75b" p-id="5193" opacity="0.7"></path>
              <path d="M175.225443 791.261789a57.486031 57.486031 0 1 0 28.743015-49.798945 57.486031 57.486031 0 0 0-28.743015 49.798945z m0 0M175.225443 232.764949a57.486031 57.486031 0 1 0 28.743015-49.798946 57.486031 57.486031 0 0 0-28.743015 49.798946z m0 0M733.76239 232.764949a57.486031 57.486031 0 1 0 28.743015-49.798946 57.48603 57.48603 0 0 0-28.743015 49.798946z m0 0" fill="#f8c75b" p-id="5194"></path>
            </svg>
          </div>
        </el-tooltip>
      </div>
      <!-- Normal Login Form -->
      <div v-if="!isQrLogin" class="login-panel">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" type="text" size="large" auto-complete="off" :placeholder="t('login.username')">
            <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            :type="passwordVisible ? 'text' : 'password'"
            size="large"
            auto-complete="off"
            :placeholder="t('login.password')"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            <template #suffix>
              <el-icon
                class="password-eye"
                @mousedown="showPassword"
                @mouseup="hidePassword"
                @mouseleave="hidePassword"
              >
                <component :is="passwordVisible ? 'View' : 'Hide'" />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="captchaEnabled" prop="code">
          <el-input
            v-model="loginForm.code"
            size="large"
            auto-complete="off"
            :placeholder="t('login.code')"
            style="width: 63%"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" class="login-code-img" @click="getCode" />
          </div>
        </el-form-item>
        <el-form-item style="width: 100%">
          <el-button :loading="loading" size="large" type="primary" class="login-button" @click.prevent="handleLogin">
            <span v-if="!loading">{{ t('login.login') }}</span>
            <span v-else>{{ t('login.logging') }}</span>
          </el-button>
          <div v-if="register" style="float: right">
            <router-link class="link-type" :to="'/register'">{{ t('login.switchRegisterPage') }}</router-link>
          </div>
        </el-form-item>
      </div>

      <!-- QR Code Login -->
      <div v-else class="login-panel qr-panel">
        <div v-if="qrCodeLoading" class="qr-loading">
          <el-icon class="qr-loading-icon"><Loading /></el-icon>
          <!-- <p>{{ t('login.loadingQrCode') }}</p> -->
        </div>
        <div v-else-if="qrCodeError" class="qr-error">
          <el-icon class="qr-error-icon"><CircleClose /></el-icon>
          <!-- <p>{{ t('login.qrCodeLoadFailed') }}</p> -->
          <el-button type="primary" size="small" @click="initQrCode">{{ t('login.retry') }}</el-button>
        </div>
        <iframe
          v-else
          ref="qrIframe"
          :src="qrLoginUrl"
          class="qr-iframe"
          frameborder="0"
          scrolling="no"
        ></iframe>
        <!-- <iframe src="https://login.on-premises.dingtalk.com/oauth2/auth.htm?response_type=code&client_id=bridge_test_dingoa&redirect_uri=http://**************:8083&scope=get_user_info&authType=QRCODE&embedMode=true"
        style="border: none; background-color: transparent;"></iframe> -->
        <div class="qr-login-footer">
          <!-- <span class="qr-tip">{{ t('login.scanQrToLogin') }}</span> -->
        </div>
      </div>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2025 舟山大桥综管指挥平台.</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCodeImg, getTenantList } from '@/api/login';
import { authBinding } from '@/api/system/social/auth';
import { useUserStore } from '@/store/modules/user';
import { LoginData, TenantVO } from '@/api/types';
import { to } from 'await-to-js';
import { HttpStatus } from '@/enums/RespEnum';
import { useI18n } from 'vue-i18n';
import { Loading, CircleClose, Monitor } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const userStore = useUserStore();
const router = useRouter();
const { t } = useI18n();

const loginForm = ref<LoginData>({
  tenantId: '000000',
  username: '',
  password: '',
  code: '',
  uuid: ''
} as LoginData);

const loginRules: ElFormRules = {
  tenantId: [{ required: true, trigger: 'blur', message: t('login.rule.tenantId.required') }],
  username: [{ required: true, trigger: 'blur', message: t('login.rule.username.required') }],
  password: [{ required: true, trigger: 'blur', message: t('login.rule.password.required') }],
  code: [{ required: true, trigger: 'change', message: t('login.rule.code.required') }]
};

const codeUrl = ref('');
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 租户开关
const tenantEnabled = ref(false);

// 注册开关
const register = ref(false);
const redirect = ref('/');
const loginRef = ref<ElFormInstance>();
// 租户列表
const tenantList = ref<TenantVO[]>([]);

// 密码可见状态控制
const passwordVisible = ref(false);

// QR code login related
const isQrLogin = ref(false);
const qrCodeLoading = ref(false);
const qrCodeError = ref(false);
const qrIframe = ref<HTMLIFrameElement | null>(null);
const qrLoginUrl = ref(import.meta.env.VITE_APP_DINGDING_API);

// 添加一个追踪定时器的数组
const timerIds = ref<number[]>([]);


// 安全的设置定时器，并自动追踪以便清除
const safeSetTimeout = (callback: () => void, delay: number) => {
  const timerId = window.setTimeout(callback, delay);
  timerIds.value.push(timerId);
  return timerId;
};



watch(
  () => router.currentRoute.value,
  (newRoute: any) => {
    redirect.value = newRoute.query && newRoute.query.redirect && decodeURIComponent(newRoute.query.redirect);
  },
  { immediate: true }
);

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;
      // 调用action的登录方法
      const [err] = await to(userStore.login(loginForm.value));
      if (!err) {
        const redirectUrl = redirect.value || '/';
        await router.push(redirectUrl);
        loading.value = false;
      } else {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          await getCode();
        }
      }
    } else {
      console.log('error submit!', fields);
    }
  });
};

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  }
};

/**
 * 获取租户列表
 */
const initTenantList = async () => {
  const { data } = await getTenantList(false);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
    if (tenantList.value != null && tenantList.value.length !== 0) {
      loginForm.value.tenantId = tenantList.value[0].tenantId;
    }
  }
};

/**
 * 第三方登录
 * @param type
 */
const doSocialLogin = (type: string) => {
  authBinding(type, loginForm.value.tenantId).then((res: any) => {
    if (res.code === HttpStatus.SUCCESS) {
      // 获取授权地址跳转
      window.location.href = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

// 显示密码
const showPassword = () => {
  passwordVisible.value = true;
};

// 隐藏密码
const hidePassword = () => {
  passwordVisible.value = false;
};

// 切换到二维码登录
const toggleQrLogin = () => {
  isQrLogin.value = !isQrLogin.value;
  if (isQrLogin.value) {
    initQrCode();
  }
};

// 初始化二维码
const initQrCode = () => {
  qrCodeLoading.value = true;
  qrCodeError.value = false;

  safeSetTimeout(() => {
    try {
      qrCodeLoading.value = false;

      // 不在这里重复添加监听器，监听器已在onMounted中添加
    } catch (error) {
      console.error('Failed to initialize QR code:', error);
      qrCodeLoading.value = false;
      qrCodeError.value = true;
    }
  }, 1500);
};

// 添加处理状态标记，防止重复处理
const isProcessingQrCode = ref(false);

// 处理二维码扫码结果消息
const handleQrCodeMessage = async (event: MessageEvent) => {
  // 验证消息来源 - 确保来自浙政钉iframe
  if (!event.origin || !qrIframe.value) {
    return;
  }

  const data = event.data;
  if(data.code && !isProcessingQrCode.value){
    isProcessingQrCode.value = true;
    loading.value = true;

    try {
      const [err] = await to(userStore.dingTalkLogin(data.code));
      if(!err){
        await router.push("/");
      }else{
        ElMessageBox.alert('该用户未绑定，请先绑定', '登录提示', {
          confirmButtonText: '确定',
          type: 'warning',
          callback: () => {
            // 登录失败时重新获取二维码
            initQrCode();
            isProcessingQrCode.value = false;
          }
        });
        return; // 避免在finally中重复设置
      }
    } catch (error) {
      ElMessageBox.alert('该用户未绑定，请先绑定', '登录提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: () => {
          // 异常时也重新获取二维码
          initQrCode();
          isProcessingQrCode.value = false;
        }
      });
      return; // 避免在finally中重复设置
    } finally {
      loading.value = false;
      isProcessingQrCode.value = false;
    }
  }
};




onMounted(() => {
  getCode();
  // initTenantList();

  // 增强iframe监听 - 只在登录页面添加一次
  if (window.addEventListener) {
    window.addEventListener('message', handleQrCodeMessage, false);
  }
});

// 组件卸载时清除事件监听器
onUnmounted(() => {
  window.removeEventListener('message', handleQrCodeMessage);
});



</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url('@/assets/images/login-background.png');
  background-size: cover;
  background-position: center;
  position: relative;
  overflow-y: auto;
  padding: 20px 0;
  min-height: 100vh;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(9, 32, 63, 0.7), rgba(9, 32, 63, 0.4));
    z-index: 1;
  }
}

.title-box {
  display: flex;
  margin-bottom: 30px;

  .title {
    margin: 0 auto;
    text-align: center;
    color: #2c5282;
    font-size: 24px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 1px;
  }
}

.login-form {
  position: relative;
  margin-left: 50%;
  z-index: 2;
  border-radius: 12px;
  background: rgba(255, 255, 255);
  backdrop-filter: blur(12px);
  width: min(30vw, 400px);
  padding: 25px 25px 5px 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.5);
  display: flex;
  flex-direction: column;

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input {
    height: 45px;
    background: rgba(248, 248, 248, 0.5);
    border-radius: 8px;

    :deep(.el-input__wrapper) {
      background: transparent;
      box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      padding: 0 12px;
      transition: box-shadow 0.3s;

      &:hover, &.is-focus {
        box-shadow: 0 0 0 1px #2c5282;
      }
    }

    :deep(.el-input__inner) {
      height: 48px;
      color: #333333;
      background: transparent;

      &::placeholder {
        color: rgba(0, 0, 0, 0.4);
      }
    }

    input {
      height: 45px;
    }
  }

  .input-icon {
    height: 45px;
    width: 16px;
    margin-left: 0px;
    color: rgba(0, 0, 0, 0.5);
  }

  :deep(.el-checkbox) {
    .el-checkbox__label {
      color: rgba(0, 0, 0, 0.7);
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      background-color: #2c5282;
      border-color: #2c5282;
    }
  }
}

.login-panel {
  width: 100%;
  min-height: 300px;
  transition: all 0.3s ease;
  position: relative;
}

.qr-panel {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Login mode switch styling */
.login-mode-switch {
  position: absolute;
  top: 20px;
  right: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  .mode-icon {
    color: #2c5282;
    font-size: 24px;
    margin-bottom:10px;
    transition: transform 0.3s ease;
  }

  .qr-icon {
    width: 30px;
    height: 30px;

    path {
      fill: #2c5282;
    }
  }

  .mode-text {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.7);
    transition: color 0.3s ease;
  }

  &:hover {
    .mode-icon {
      transform: scale(1.1);
    }

    .mode-text {
      color: #2c5282;
    }
  }
}

.login-button {
  background: linear-gradient(to right, #2c5282, #3a6fa8);
  border: none;
  border-radius: 8px;
  width: 100%;
  height: 45px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;

  &:hover, &:focus {
    background: linear-gradient(to right, #3a6fa8, #4b84bd);
    border: none;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.login-code {
  width: 30px;
  height: 45px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
    height: 100%;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-family: Arial, serif;
  font-size: 12px;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  z-index: 2;
}

.login-code-img {
  height: 48px;
  padding-left: 12px;
}

/* QR Code Login Container */
.qr-iframe {
  width: 100%;
  max-width: 300px;
  height: 300px;
  border-radius: 8px;
  border: none;
  overflow: none;
  margin-top: -20px;
}

.qr-login-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 15px;

  .qr-tip {
    color: rgba(0, 0, 0, 0.7);
    font-size: 14px;
  }
}

/* QR Loading and Error States */
.qr-loading, .qr-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 220px;
  height: 220px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;

  p {
    margin-top: 15px;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
  }
}

.qr-loading-icon {
  font-size: 40px;
  color: #2c5282;
  animation: spin 1.5s linear infinite;
}

.qr-error-icon {
  font-size: 40px;
  color: #e53e3e;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Link styling
.link-type {
  color: #2c5282;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.password-eye {
  cursor: pointer;
  color: rgba(0, 0, 0, 0.5);
  transition: color 0.3s;

  &:hover {
    color: #2c5282;
  }
}

// Add media queries for responsive design
@media screen and (max-width: 576px) {
  .login-form {
    width: 90%;
    padding: 20px 15px 5px 15px;
  }

  .title-box .title {
    font-size: 20px;
  }

  .login-button {
    height: 40px;
    font-size: 15px;
  }

  .qr-iframe {
    height: 250px;
  }

  .qr-loading, .qr-error {
    height: 180px;
  }

  .login-mode-switch {
    top: 15px;
    right: 5px;

    .mode-icon {
      font-size: 20px;
      margin-bottom: 5px;
    }

    .qr-icon {
      width: 24px;
      height: 24px;
    }
  }
}

@media screen and (max-height: 650px) {
  .login-form {
    padding-top: 15px;
    padding-bottom: 0;
  }

  .title-box {
    margin-bottom: 15px;
  }

  .el-form-item {
    margin-bottom: 15px !important;
  }
}
</style>
