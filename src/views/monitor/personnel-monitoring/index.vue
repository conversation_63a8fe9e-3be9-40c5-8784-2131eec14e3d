<template>
  <div class="p-2">
    <el-card shadow="hover">
      <div class="monitoring-container">
        <!-- 左侧控制面板 -->
        <div class="left-panel">
          <el-card shadow="never" class="control-card">
            <template #header>
              <div class="card-header">
                <el-icon>
                  <VideoCamera />
                </el-icon>
                <span>回放查看</span>
              </div>
            </template>

            <!-- 日期选择 -->
            <div class="control-section">
              <div class="section-title">选择日期</div>
              <el-date-picker
                v-model="selectedDate"
                type="date"
                placeholder="选择查看日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                @change="handleDateChange"
              />
            </div>

            <!-- 监控设备选择 -->
            <div class="control-section">
              <div class="section-title">监控设备</div>
              <div class="device-tree-container">
                <el-tree
                  ref="deviceTreeRef"
                  :data="deviceTreeData"
                  :props="{ children: 'children', label: 'name' }"
                  node-key="id"
                  :expand-on-click-node="false"
                  :default-expand-all="true"
                  @node-click="handleDeviceSelect"
                >
                  <template #default="{ node, data }">
                    <div class="tree-node" :class="{ 'tree-node-selected': selectedDevice === data.id }">
                      <el-icon v-if="data.type === 'group'">
                        <Folder />
                      </el-icon>
                      <el-icon v-else>
                        <VideoCamera />
                      </el-icon>
                      <span class="tree-node-label">{{ node.label }}</span>
                    </div>
                  </template>
                </el-tree>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 右侧视频播放区域 -->
        <div class="right-panel">
          <el-card shadow="never" class="video-card">
            <template #header>
              <div class="card-header">
                <el-icon>
                  <Monitor />
                </el-icon>
                <span>视频播放</span>
                <div class="video-info" v-if="currentVideoInfo">
                  <span>{{ currentVideoInfo.deviceName }}</span>
                  <span>{{ currentVideoInfo.date }} {{ currentVideoInfo.timeRange }}</span>
                </div>
              </div>
            </template>

            <div class="video-container">
              <!-- 加载状态 -->
              <div v-if="loading" class="video-loading">
                <el-icon class="is-loading" size="48">
                  <Loading />
                </el-icon>
                <span>正在加载视频...</span>
              </div>

              <!-- 有选择设备时显示视频播放器 -->
              <video-player
                v-else-if="selectedDevice && videoUrl"
                ref="videoPlayerRef"
                :src="videoUrl"
                :width="'100%'"
                :height="'700px'"
                :show-controls="false"
                :show-custom-controls="true"
                :autoplay="true"
                :video-title="currentVideoInfo?.deviceName || '监控回放'"
                @play="handleVideoPlay"
                @pause="handleVideoPause"
                @error="handleVideoError"
              />

              <!-- 无设备选择时的占位 -->
              <div v-else class="video-placeholder">
                <el-icon size="64">
                  <VideoCamera />
                </el-icon>
                <p>请选择日期和监控设备后播放视频</p>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="PersonnelMonitoring" lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { VideoCamera, Monitor, Folder, Loading } from '@element-plus/icons-vue';
import TreeSelect from '@/components/TreeSelect/index.vue';
import VideoPlayer from '@/components/VideoPlayer/index.vue';
import { getDeviceTree, getHistoryVideoUrl, downloadVideo } from '@/api/monitor/personnel';
import type { DeviceTreeNode, HistoryVideoQuery, VideoDownloadQuery } from '@/api/monitor/personnel/types';

// 响应式数据
const selectedDate = ref('');
const selectedDevice = ref('');
const loading = ref(false);
const videoUrl = ref('');
const videoPlayerRef = ref();
const deviceTreeRef = ref();

// 设备树数据
const deviceTreeData = ref<DeviceTreeNode[]>([]);

// 当前视频信息
const currentVideoInfo = ref<{
  deviceName: string;
  date: string;
  timeRange: string;
} | null>(null);

// 计算属性
const canPlay = computed(() => {
  return selectedDate.value && selectedDevice.value;
});

// 获取设备名称
const getDeviceName = (deviceId: string): string => {
  const findDevice = (nodes: DeviceTreeNode[]): string | null => {
    for (const node of nodes) {
      if (node.id === deviceId) return node.name;
      if (node.children) {
        const found = findDevice(node.children);
        if (found) return found;
      }
    }
    return null;
  };

  return findDevice(deviceTreeData.value) || deviceId;
};

// 事件处理
const handleDateChange = (date: string) => {
  // 清空当前选择的设备和视频
  selectedDevice.value = '';
  videoUrl.value = '';
  currentVideoInfo.value = null;

  // 根据新选择的日期重新加载设备列表
  if (date) {
    loadDeviceTree(date);
  }
};

const handleDeviceSelect = (data: DeviceTreeNode & { videoUrl?: string }) => {
  if (data.type === 'device') {
    selectedDevice.value = data.id;

    // 如果设备有直接的视频URL，优先使用
    if (data.videoUrl && selectedDate.value) {
      console.log('使用设备直接视频URL:', data.videoUrl);
      videoUrl.value = data.videoUrl;
      currentVideoInfo.value = {
        deviceName: data.name,
        date: selectedDate.value,
        timeRange: '全天'
      };
      ElMessage.success(`开始播放视频: ${data.name}`);
    } else if (canPlay.value) {
      // 否则尝试通过API获取历史视频
      console.log('通过API获取历史视频');
      playVideo();
    } else {
      ElMessage.warning('请先选择日期');
    }
  } else {
    // 如果选择的是组节点，清空设备选择
    selectedDevice.value = '';
    videoUrl.value = '';
    currentVideoInfo.value = null;
    console.log('选择了组节点，清空视频');
  }
};

// 播放视频
const playVideo = async () => {
  if (!canPlay.value) {
    ElMessage.warning('请先选择日期和设备');
    return;
  }

  loading.value = true;

  try {
    // 调用API获取视频URL
    const videoUrlResult = await getVideoUrl();

    videoUrl.value = videoUrlResult;

    // 设置当前视频信息
    currentVideoInfo.value = {
      deviceName: getDeviceName(selectedDevice.value),
      date: selectedDate.value,
      timeRange: '全天'
    };

    ElMessage.success('开始播放视频');
  } catch (error) {
    ElMessage.error('播放失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 获取历史视频URL
const getVideoUrl = async (): Promise<string> => {
  try {
    const query: HistoryVideoQuery = {
      deviceId: selectedDevice.value,
      date: selectedDate.value,
      startTime: '00:00:00',
      endTime: '23:59:59'
    };

    const response = await getHistoryVideoUrl(query);
    return response.data.url;
  } catch (error) {
    console.error('Get video URL error:', error);
    throw new Error('获取历史视频失败');
  }
};

// 视频播放事件
const handleVideoPlay = () => {
  console.log('视频开始播放:', currentVideoInfo.value?.deviceName);
  ElMessage.success('视频播放中');
};

const handleVideoPause = () => {
  console.log('视频暂停:', currentVideoInfo.value?.deviceName);
};

const handleVideoError = (error: any) => {
  console.error('视频播放错误:', error);
  ElMessage.error(`视频播放出错: ${error.message || '未知错误'}`);

  // 如果是网络错误，可以尝试重新加载
  if (error.message && error.message.includes('网络')) {
    setTimeout(() => {
      if (videoUrl.value) {
        console.log('尝试重新加载视频...');
        // 触发重新加载
        const currentUrl = videoUrl.value;
        videoUrl.value = '';
        setTimeout(() => {
          videoUrl.value = currentUrl;
        }, 100);
      }
    }, 2000);
  }
};

// 后端数据转换为前端格式
interface BackendDeviceNode {
  label: string | null;
  cameraIndexCode: string | null;
  url: string | null;
  mmsi: number | null;
  children: BackendDeviceNode[];
}

const transformDeviceTreeData = (backendData: BackendDeviceNode[]): DeviceTreeNode[] => {
  const result = backendData
    .map((node) => {
      // 生成唯一ID
      let id: string;
      if (node.cameraIndexCode) {
        id = node.cameraIndexCode;
      } else if (node.mmsi) {
        id = `mmsi_${node.mmsi}`;
      } else if (node.url) {
        id = `url_${Math.random().toString(36).substr(2, 9)}`;
      } else {
        id = `node_${Math.random().toString(36).substr(2, 9)}`;
      }

      // 确定节点类型和名称
      let type: 'group' | 'device';
      let name: string;

      if (node.cameraIndexCode && node.label) {
        // 有摄像头编码且有标签，这是设备节点
        type = 'device';
        name = node.label;
      } else if (node.url && !node.label) {
        // 有URL但无标签，这是视频流节点，跳过不显示
        return null;
      } else if (node.label) {
        // 有标签但无摄像头编码，这是组节点
        type = 'group';
        name = node.label;
      } else {
        // 其他情况，跳过
        return null;
      }

      // 递归处理子节点，并过滤掉null值
      const children = node.children && node.children.length > 0 ? transformDeviceTreeData(node.children).filter((child) => child !== null) : [];

      // 如果是设备节点，查找其子节点中的视频URL
      let videoUrl: string | undefined;
      if (type === 'device' && node.children && node.children.length > 0) {
        const urlNode = node.children.find((child) => child.url && !child.label);
        if (urlNode) {
          videoUrl = urlNode.url;
        }
      }

      return {
        id,
        name,
        type,
        children: children.length > 0 ? children : undefined,
        // 添加额外字段用于存储原始数据
        cameraIndexCode: node.cameraIndexCode,
        mmsi: node.mmsi,
        videoUrl
      } as DeviceTreeNode & { cameraIndexCode?: string | null; mmsi?: number | null; videoUrl?: string };
    })
    .filter((node) => node !== null) as DeviceTreeNode[];

  console.log('Transformed device tree data:', result);
  return result;
};

// 加载设备树数据
const loadDeviceTree = async (date?: string) => {
  try {
    // 构建请求参数，如果有日期则传递日期参数
    const requestData = { date: date };

    const response1 = await getDeviceTree(requestData);
    const response = JSON.parse(response1.data);
    console.log('Device tree data:', response);
    if (response && response.length > 0) {
      deviceTreeData.value = transformDeviceTreeData(response);
    }
  } catch (error) {
    console.error('Load device tree error:', error);
    ElMessage.error('加载设备树失败，请检查网络连接');
  }
};

onMounted(() => {
  // 设置默认日期为今天
  selectedDate.value = new Date().toISOString().split('T')[0];
  // 使用默认日期加载设备树数据
  loadDeviceTree(selectedDate.value);
});
</script>

<style lang="scss" scoped>
.monitoring-container {
  display: flex;
  gap: 16px;
  min-height: 600px;
}

.left-panel {
  width: 300px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
}

.control-card,
.video-card {
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.video-info {
  margin-left: auto;
  font-size: 12px;
  color: var(--el-text-color-regular);

  span {
    margin-left: 8px;
  }
}

.control-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--el-text-color-primary);
}

.play-controls {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.video-container {
  position: relative;
  min-height: 700px;
}

.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--el-text-color-primary);
  font-size: 16px;
  z-index: 20;

  .is-loading {
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 700px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  color: var(--el-text-color-placeholder);

  p {
    margin-top: 16px;
    font-size: 14px;
  }
}

.device-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  padding: 8px;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: var(--el-color-primary-light-9);
  }

  &.tree-node-selected {
    background-color: var(--el-color-primary-light-8);
    color: var(--el-color-primary);
  }
}

.tree-node-label {
  font-size: 14px;
}
</style>
