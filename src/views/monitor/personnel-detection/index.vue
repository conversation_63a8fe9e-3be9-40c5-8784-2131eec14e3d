<template>
  <div class="p-2">
    <el-card shadow="hover">
      <div class="detection-container">
        <!-- 左侧控制面板 -->
        <div class="left-panel">
          <el-card shadow="never" class="control-card">
            <template #header>
              <div class="card-header">
                <el-icon>
                  <User />
                </el-icon>
                <span>人员离岗检测</span>
              </div>
            </template>

            <!-- 检测状态 -->
            <div class="control-section">
              <div class="section-title">检测状态</div>
              <div class="status-info">
                <div class="status-item">
                  <span class="status-label">连接状态:</span>
                  <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'">
                    {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}
                  </el-tag>
                </div>
                <div class="status-item">
                  <span class="status-label">检测状态:</span>
                  <el-tag :type="detectionStatus === 'active' ? 'success' : 'info'">
                    {{ detectionStatus === 'active' ? '检测中' : '待机' }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 检测到的人员列表 -->
            <div class="control-section" v-if="detectedPersons.length > 0">
              <div class="section-title">检测到的人员</div>
              <div class="person-list">
                <div
                  v-for="person in detectedPersons"
                  :key="person.id"
                  class="person-item"
                  :class="{ 'person-selected': selectedPerson === person.id }"
                  @click="selectPerson(person.id)"
                >
                  <div class="person-info">
                    <span class="person-name">{{ person.name || `人员${person.id}` }}</span>
                    <span class="person-confidence">{{ (person.confidence * 100).toFixed(1) }}%</span>
                  </div>
                  <div class="person-status">
                    <el-tag size="small" :type="person.status === 'present' ? 'success' : 'warning'">
                      {{ person.status === 'present' ? '在岗' : '离岗' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 监控设备选择 -->
            <div class="control-section">
              <div class="section-title">监控设备</div>
              <div class="device-list-container">
                <el-scrollbar height="400px">
                  <div class="device-list">
                    <div
                      v-for="device in deviceList"
                      :key="device.cameraIndexCode"
                      class="device-item"
                      :class="{ 'device-item-selected': selectedDevice === device.cameraIndexCode }"
                      @click="handleDeviceSelect(device)"
                    >
                      <div class="device-icon">
                        <el-icon>
                          <VideoCamera />
                        </el-icon>
                      </div>
                      <div class="device-info">
                        <div class="device-name">{{ device.name }}</div>
                        <div class="device-mmsi">MMSI: {{ device.mmsi }}</div>
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 右侧视频检测区域 -->
        <div class="right-panel">
          <el-card shadow="never" class="video-card">
            <template #header>
              <div class="card-header">
                <el-icon>
                  <Monitor />
                </el-icon>
                <span>实时监控</span>
                <div class="detection-info" v-if="currentDeviceInfo">
                  <span>{{ currentDeviceInfo.deviceName }}</span>
                  <span>{{ currentTime }}</span>
                </div>
              </div>
            </template>

            <div class="video-container">
              <!-- 有选择设备且有视频URL时显示MJPEG流 -->
              <div v-if="selectedDevice && videoUrl" class="mjpeg-container">
                <!-- 加载动画 -->
                <div v-if="loading" class="video-loading">
                  <el-icon class="is-loading" size="48">
                    <Loading />
                  </el-icon>
                  <span>正在加载视频流...</span>
                </div>

                <img
                  :src="videoUrl"
                  class="mjpeg-stream"
                  alt="实时监控视频"
                  @load="handleVideoLoad"
                  @error="handleVideoError"
                  :style="{ display: loading ? 'none' : 'block' }"
                />

                <!-- 视频信息覆盖层 -->
                <div class="video-info-overlay" v-if="!loading">
                  <div class="video-title">{{ currentDeviceInfo?.deviceName || '人员检测' }}</div>
                  <div class="video-time">{{ currentTime }}</div>
                </div>
              </div>

              <!-- 无设备选择时的占位 -->
              <div v-else class="video-placeholder">
                <el-icon size="64">
                  <User />
                </el-icon>
                <p>请选择监控设备查看实时视频</p>
              </div>
            </div>

            <!-- 连接状态信息 -->
            <div v-if="connectionStatus === 'connected'" class="detection-panel">
              <div class="detection-stats">
                <div class="stat-item">
                  <span class="stat-label">连接状态:</span>
                  <span class="stat-value">正常</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">检测人数:</span>
                  <span class="stat-value">{{ detectedPersons.length }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="PersonnelDetection" lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { User, Monitor, VideoCamera, Folder, Loading } from '@element-plus/icons-vue';
import TreeSelect from '@/components/TreeSelect/index.vue';
import { getPersonnelDetectionTree } from '@/api/monitor/personnel';
import type { DetectedPerson, DetectionBox } from '@/api/monitor/personnel/types';

// 设备信息接口
interface DeviceInfo {
  cameraIndexCode: string;
  name: string;
  mmsi: string;
}



// 响应式数据
const selectedDevice = ref('');
const selectedPerson = ref('');
const loading = ref(false);
const connectionStatus = ref<'connected' | 'disconnected'>('disconnected');
const detectionStatus = ref<'active' | 'idle'>('idle');
const videoUrl = ref('');
const currentTime = ref('');
const detectedPersons = ref<DetectedPerson[]>([]);

// 定时器
let timeUpdateTimer: NodeJS.Timeout | null = null;
let streamDataTimer: NodeJS.Timeout | null = null;

// 设备列表数据
const deviceList = ref<DeviceInfo[]>([]);

// 当前设备信息
const currentDeviceInfo = ref<{
  deviceName: string;
} | null>(null);





// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString();
};

// 事件处理
const handleDeviceSelect = (device: DeviceInfo) => {
  // 先停止之前的流数据获取
  stopStreamData();

  selectedDevice.value = device.cameraIndexCode;

  currentDeviceInfo.value = {
    deviceName: device.name
  };

  // 添加短暂延迟，确保之前的播放器完全停止
  setTimeout(() => {
    startStreamData();
  }, 100);
};

// 开始获取流数据
const startStreamData = async () => {
  if (!selectedDevice.value) {
    return;
  }

  loading.value = true;

  try {
    // 直接构建视频流URL，避免CORS问题
    const baseUrl = import.meta.env.VITE_APP_PYTHON_API;
    const streamUrl = `${baseUrl}/api/streams/${selectedDevice.value}/mjpeg`;

    // 直接设置视频URL
    videoUrl.value = streamUrl;

    // 更新状态为已连接和正常
    connectionStatus.value = 'connected';
    detectionStatus.value = 'active';

    ElMessage.success('连接视频流成功');
  } catch (error) {
    console.error('获取流数据失败:', error);
    ElMessage.error('连接视频流失败，请检查设备连接');
    connectionStatus.value = 'disconnected';
    detectionStatus.value = 'idle';
  } finally {
    loading.value = false;
  }
};



// 停止流数据获取
const stopStreamData = () => {
  detectionStatus.value = 'idle';
  connectionStatus.value = 'disconnected';
  detectedPersons.value = [];

  // 清除定时器
  if (streamDataTimer) {
    clearInterval(streamDataTimer);
    streamDataTimer = null;
  }

  // 清空视频URL
  videoUrl.value = '';
};

// 选择人员
const selectPerson = (personId: string) => {
  selectedPerson.value = selectedPerson.value === personId ? '' : personId;
};

// MJPEG流加载成功事件
const handleVideoLoad = () => {
  console.log('MJPEG流加载成功');
  loading.value = false;
};

const handleVideoError = (error: any) => {
  console.error('视频加载错误:', error);
  ElMessage.error('视频流加载失败，请检查网络连接');
  loading.value = false;
  stopStreamData();
};

// Canvas点击事件
const handleCanvasClick = (position: { x: number; y: number }) => {
  console.log('Canvas clicked at:', position);
  // 可以在这里添加手动标注功能
};

// 数据转换函数
const transformDeviceListData = (data: string): DeviceInfo[] => {
  try {
    const parsedData = JSON.parse(data);
    if (Array.isArray(parsedData)) {
      return parsedData.map((item) => ({
        cameraIndexCode: item.cameraIndexCode,
        name: item.name,
        mmsi: item.mmsi
      }));
    }
    return [];
  } catch (error) {
    console.error('Parse device list data error:', error);
    return [];
  }
};

// 加载设备列表数据
const loadDeviceList = async () => {
  try {
    const response = await getPersonnelDetectionTree();
    console.log('Device list data:', response);
    if (response.data) {
      deviceList.value = transformDeviceListData(response.data);
    }
  } catch (error) {
    console.error('Load device list error:', error);
    ElMessage.error('加载设备列表失败，请检查网络连接');
  }
};

onMounted(() => {
  // 开始更新时间
  updateCurrentTime();
  timeUpdateTimer = setInterval(updateCurrentTime, 1000);
  // 加载设备列表数据
  loadDeviceList();
});

onUnmounted(() => {
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer);
  }
  if (streamDataTimer) {
    clearInterval(streamDataTimer);
  }
});
</script>

<style lang="scss" scoped>
.detection-container {
  display: flex;
  gap: 16px;
  min-height: 600px;
}

.left-panel {
  width: 320px;
  flex-shrink: 0;
}

.right-panel {
  flex: 1;
}

.control-card,
.video-card {
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.detection-info {
  margin-left: auto;
  font-size: 12px;
  color: var(--el-text-color-regular);

  span {
    margin-left: 8px;
  }
}

.control-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--el-text-color-primary);
}

.status-info {
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .status-label {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }

    .person-count {
      font-weight: bold;
      color: var(--el-color-primary);
    }
  }
}

.setting-item {
  margin-bottom: 12px;

  span {
    display: block;
    font-size: 12px;
    margin-bottom: 4px;
    color: var(--el-text-color-regular);
  }
}

.person-list {
  max-height: 200px;
  overflow-y: auto;
}

.person-item {
  padding: 8px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }

  &.person-selected {
    border-color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-8);
  }

  .person-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;

    .person-name {
      font-size: 12px;
      font-weight: 500;
    }

    .person-confidence {
      font-size: 11px;
      color: var(--el-text-color-regular);
    }
  }

  .person-status {
    text-align: right;
  }
}

.video-container {
  position: relative;
  min-height: 700px;
}

.mjpeg-container {
  position: relative;
  width: 100%;
  height: 700px;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
}

.mjpeg-stream {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}

.video-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #fff;
  font-size: 16px;
  z-index: 20;

  .is-loading {
    animation: rotating 2s linear infinite;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.detection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  z-index: 10;
}

.video-info-overlay {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
  padding: 12px;
  border-radius: 4px;
  z-index: 15;

  .video-title {
    font-size: 16px;
    font-weight: 500;
  }

  .video-time {
    font-size: 14px;
    font-family: monospace;
  }
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  color: var(--el-text-color-placeholder);

  p {
    margin-top: 16px;
    font-size: 14px;
  }
}

.detection-panel {
  margin-top: 16px;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
}

.detection-stats {
  display: flex;
  gap: 24px;

  .stat-item {
    .stat-label {
      font-size: 12px;
      color: var(--el-text-color-regular);
      margin-right: 4px;
    }

    .stat-value {
      font-size: 12px;
      font-weight: 500;
      color: var(--el-color-primary);
    }
  }
}

.device-list-container {
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
}

.device-list {
  padding: 8px;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background-color: var(--el-fill-color-light);
    border-color: var(--el-border-color);
  }

  &.device-item-selected {
    background-color: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }
}

.device-icon {
  flex-shrink: 0;
  font-size: 18px;
  color: var(--el-color-primary);
}

.device-info {
  flex: 1;
  min-width: 0;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.device-mmsi {
  font-size: 12px;
  color: var(--el-text-color-regular);
}
</style>
