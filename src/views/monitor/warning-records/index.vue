<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="监控名称" prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入监控名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="MMSI号" prop="mmsi">
              <el-input
                v-model="queryParams.mmsi"
                placeholder="请输入MMSI号"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleBatchDelete"
            >
              批量删除
            </el-button>
          </el-col>
<!--          <right-toolbar v-model:show-search="showSearch" @query-table="getList"></right-toolbar>-->
        </el-row>
      </template>

      <el-table
        ref="detectionTableRef"
        v-loading="loading"
        :data="detectionList"
        :default-sort="defaultSort"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" align="center"  width="100" >
          <template #default="scope">
            <span>
            {{ scope.$index + 1 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="监控名称" align="center" prop="name" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column label="MMSI号" align="center" prop="mmsi" min-width="150" />
        <el-table-column label="抓拍照片" align="center" min-width="180">
          <template #default="scope">
            <el-image
              v-if="scope.row.imageBase64"
              :src="getImageSrc(scope.row.imageBase64)"
              :preview-src-list="[getImageSrc(scope.row.imageBase64)]"
              fit="cover"
              class="capture-image"
            />
            <span v-else class="no-image">无图片</span>
          </template>
        </el-table-column>
        <el-table-column
          label="抓拍时间"
          align="center"
          prop="time"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
          min-width="180"
        >
          <template #default="scope">
            <span>{{ scope.row.time }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button
                link
                type="primary"
                icon="View"
                @click="handleView(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="检测记录详情" width="600px" append-to-body>
      <div v-if="currentRecord" class="detection-detail">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-item">
                <span class="label">记录编号:</span>
                <span class="value">{{ currentRecord.id }}</span>
              </div>
              <div class="detail-item">
                <span class="label">监控名称:</span>
                <span class="value">{{ currentRecord.name }}</span>
              </div>
              <div class="detail-item">
                <span class="label">MMSI号:</span>
                <span class="value">{{ currentRecord.mmsi }}</span>
              </div>
              <div class="detail-item">
                <span class="label">抓拍时间:</span>
                <span class="value">{{ currentRecord.time }}</span>
              </div>
              <div class="detail-item">
                <span class="label">状态:</span>
                <span class="value">{{ currentRecord.status }}</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-section">
              <h4>抓拍照片</h4>
              <el-image
                v-if="currentRecord.imageBase64"
                :src="getImageSrc(currentRecord.imageBase64)"
                :preview-src-list="[getImageSrc(currentRecord.imageBase64)]"
                fit="contain"
                class="detail-image"
              />
              <div v-else class="no-image-placeholder">无图片</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup name="WarningRecords" lang="ts">
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { ComponentInternalInstance, ElFormInstance, ElTableInstance } from 'element-plus';
import type { DateModelType } from 'element-plus';
import {
  getPersonnelDetectionList,
  deleteWarningRecord
} from '@/api/monitor/personnel';

interface PersonnelDetectionRecord {
  id: number;
  name: string;
  mmsi: string;
  time: string;
  imageBase64: string | null;
  createDept: string | null;
  createBy: string | null;
  createTime: string;
  updateBy: string | null;
  updateTime: string;
  tenantId: string;
  streamId: string | null;
  connected: string;
  status: string;
  timestamp: string;
  fps: string;
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  name: string;
  mmsi: string;
  orderByColumn: string;
  isAsc: string;
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 响应式数据
const loading = ref(true);
const showSearch = ref(true);
const detectionList = ref<PersonnelDetectionRecord[]>([]);
const total = ref(0);
const ids = ref<number[]>([]);
const multiple = ref(true);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const defaultSort = ref<any>({ prop: 'time', order: 'descending' });
const detailDialogVisible = ref(false);
const currentRecord = ref<PersonnelDetectionRecord | null>(null);

const queryFormRef = ref<ElFormInstance>();
const detectionTableRef = ref<ElTableInstance>();

// 查询参数
const queryParams = ref<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  name: '',
  mmsi: ''
});



// 工具函数
const getImageSrc = (base64: string) => {
  if (!base64) {
    return '';
  }
  // 如果base64字符串已经包含data:image前缀，直接返回
  if (base64.startsWith('data:image')) {
    return base64;
  }
  // 否则添加data:image/jpeg;base64,前缀
  const result = `data:image/jpeg;base64,${base64}`;
  return result;
};

// 事件处理
const getList = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      ...queryParams.value
    };

    // 调用API获取数据
    const res = await getPersonnelDetectionList(params);

    if (res.code === 200) {
      detectionList.value = res.rows || [];
      total.value = res.total || 0;
    } else {
      ElMessage.error(res.msg || '获取数据失败');
    }
  } catch (error) {
    console.error('Get detection list error:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  detectionTableRef.value?.sort(defaultSort.value.prop, defaultSort.value.order);
  getList();
};



const handleSelectionChange = (selection: PersonnelDetectionRecord[]) => {
  ids.value = selection.map(item => item.id);
  multiple.value = !selection.length;
};

const handleSortChange = (column: any) => {
  queryParams.value.orderByColumn = column.prop;
  queryParams.value.isAsc = column.order;
  getList();
};

const handleView = (row: PersonnelDetectionRecord) => {
  currentRecord.value = row;
  detailDialogVisible.value = true;
};



const handleDelete = async (row: PersonnelDetectionRecord) => {
  await ElMessageBox.confirm(`确定删除检测记录 "${row.id}" 吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  });

  try {
    // 调用删除API
    const res = await deleteWarningRecord(row.id);

    if (res.code === 200) {
      ElMessage.success('删除成功');
      // 重新获取列表数据
      getList();
    } else {
      ElMessage.error(res.msg || '删除失败');
    }
  } catch (error) {
    console.error('Delete record error:', error);
    ElMessage.error('删除失败');
  }
};

const handleBatchDelete = async () => {
  if (!ids.value.length) {
    ElMessage.warning('请选择要删除的记录');
    return;
  }

  await ElMessageBox.confirm(`确定删除选中的 ${ids.value.length} 条记录吗？`, '批量删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  });

  try {
    // 调用删除API，传入多个ID
    const res = await deleteWarningRecord(ids.value);

    if (res.code === 200) {
      ElMessage.success('删除成功');
      // 重新获取列表数据
      getList();
      // 清空选中状态
      ids.value = [];
      multiple.value = true;
    } else {
      ElMessage.error(res.msg || '删除失败');
    }
  } catch (error) {
    console.error('Batch delete error:', error);
    ElMessage.error('删除失败');
  }
};



onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.capture-image {
  width: 60px;
  height: 80px;
  border-radius: 4px;
  cursor: pointer;
}

.no-image {
  color: var(--el-text-color-placeholder);
  font-size: 12px;
}

.detection-detail {
  .detail-section {
    margin-bottom: 20px;

    h4 {
      margin-bottom: 12px;
      color: var(--el-text-color-primary);
      border-bottom: 1px solid var(--el-border-color-light);
      padding-bottom: 8px;
    }
  }

  .detail-item {
    display: flex;
    margin-bottom: 8px;

    .label {
      width: 80px;
      color: var(--el-text-color-regular);
      flex-shrink: 0;
    }

    .value {
      flex: 1;
      color: var(--el-text-color-primary);
    }
  }

  .detail-image {
    width: 100%;
    max-height: 300px;
    border-radius: 4px;
  }

  .no-image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 200px;
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-placeholder);
    border-radius: 4px;
  }
}
</style>
