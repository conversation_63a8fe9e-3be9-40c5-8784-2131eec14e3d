<template>
  <div class="panel-container">
    <div class="panel-header">
      <h3 class="panel-title">传感器状态</h3>
      <div class="panel-actions">
        <el-button type="primary" size="small" plain>查看详情</el-button>
      </div>
    </div>
    
    <div class="panel-content">
      <div class="status-overview">
        <div class="status-card total">
          <div class="status-number">{{ data.total }}</div>
          <div class="status-label">传感器总数</div>
        </div>
        
        <div class="status-card online">
          <div class="status-number">{{ data.online }}</div>
          <div class="status-label">在线</div>
        </div>
        
        <div class="status-card offline">
          <div class="status-number">{{ data.offline }}</div>
          <div class="status-label">离线</div>
        </div>
        
        <div class="status-card warning">
          <div class="status-number">{{ data.warning }}</div>
          <div class="status-label">报警</div>
        </div>
      </div>
      
      <div class="category-list">
        <div class="category-item" v-for="(category, index) in data.categories" :key="index">
          <div class="category-info">
            <div class="category-name">{{ category.name }}</div>
            <div class="category-status">
              <span class="status-online">{{ category.online }}</span>
              <span class="status-separator">/</span>
              <span class="status-total">{{ category.total }}</span>
            </div>
          </div>
          <div class="category-progress">
            <el-progress 
              :percentage="getOnlinePercentage(category)" 
              :stroke-width="6" 
              :color="getProgressColor(getOnlinePercentage(category))" 
              :format="() => ''"
            ></el-progress>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件接口
interface Category {
  name: string;
  total: number;
  online: number;
}

interface SensorData {
  total: number;
  online: number;
  offline: number;
  warning: number;
  categories: Category[];
}

// 定义属性
defineProps<{
  data: SensorData;
}>();

// 计算在线百分比
const getOnlinePercentage = (category: Category) => {
  if (category.total === 0) return 0;
  return Math.round((category.online / category.total) * 100);
};

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67c23a';
  if (percentage >= 70) return '#e6a23c';
  return '#f56c6c';
};
</script>

<style scoped lang="scss">
.panel-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.panel-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-overview {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  
  .status-card {
    flex: 1;
    background: #f9f9f9;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    transition: transform 0.3s;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .status-number {
      font-size: 22px;
      font-weight: 600;
      line-height: 1.2;
    }
    
    .status-label {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
    
    &.total {
      background-color: #ecf5ff;
      
      .status-number {
        color: #409eff;
      }
    }
    
    &.online {
      background-color: #f0f9eb;
      
      .status-number {
        color: #67c23a;
      }
    }
    
    &.offline {
      background-color: #f4f4f5;
      
      .status-number {
        color: #909399;
      }
    }
    
    &.warning {
      background-color: #fef0f0;
      
      .status-number {
        color: #f56c6c;
      }
    }
  }
}

.category-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
  
  .category-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .category-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .category-name {
        font-size: 14px;
        color: #606266;
      }
      
      .category-status {
        font-size: 14px;
        
        .status-online {
          color: #67c23a;
          font-weight: 500;
        }
        
        .status-separator {
          margin: 0 2px;
          color: #dcdfe6;
        }
        
        .status-total {
          color: #909399;
        }
      }
    }
    
    .category-progress {
      margin-bottom: 5px;
    }
  }
}
</style> 