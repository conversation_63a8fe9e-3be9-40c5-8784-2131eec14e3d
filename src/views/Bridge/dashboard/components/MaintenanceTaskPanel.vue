<template>
  <div class="panel-container">
    <div class="panel-header">
      <h3 class="panel-title">维护任务</h3>
      <div class="panel-actions">
        <el-button type="primary" size="small" plain icon="Calendar">计划任务</el-button>
      </div>
    </div>
    
    <div v-if="data.length === 0" class="empty-data">
      <el-empty description="暂无维护任务" :image-size="100"></el-empty>
    </div>
    
    <div v-else class="panel-content">
      <el-scrollbar height="420px">
        <div class="task-list">
          <div 
            v-for="(item, index) in data" 
            :key="index" 
            class="task-item"
            :class="{ 'is-urgent': isUrgentTask(item) }"
          >
            <div class="task-status">
              <el-checkbox 
                v-model="item.completed" 
                disabled 
                :class="{ 'is-completed': item.completed }"
              ></el-checkbox>
            </div>
            
            <div class="task-content">
              <div class="task-header">
                <div class="task-title">{{ item.title || item.taskName || '维护任务' }}</div>
                <div class="task-time">{{ formatDueDate(item.dueDate || item.planTime) }}</div>
              </div>
              
              <div class="task-body">
                <div class="task-device">
                  <el-icon><Monitor /></el-icon>
                  <span>{{ item.deviceName || '设备未指定' }}</span>
                </div>
                <div class="task-desc">{{ item.description || item.taskContent || '暂无详细信息' }}</div>
              </div>
              
              <div class="task-footer">
                <div class="task-tags">
                  <el-tag size="small" :type="getTaskTypeTag(item)">{{ getTaskTypeText(item) }}</el-tag>
                  <el-tag 
                    size="small" 
                    :type="getTaskPriorityTag(item)"
                    v-if="item.priority || item.level"
                  >
                    {{ getPriorityText(item) }}
                  </el-tag>
                </div>
                
                <div class="task-actions">
                  <el-button type="text" size="small" @click="handleViewTask(item)">查看</el-button>
                  <el-button type="text" size="small" @click="handleCompleteTask(item)">完成</el-button>
                </div>
              </div>
              
              <div v-if="isUrgentTask(item)" class="task-reminder">
                <el-icon><Bell /></el-icon>
                <span>此任务已逾期，请尽快处理</span>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
      
      <div class="panel-footer">
        <el-button type="primary" plain>查看全部维护计划</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Monitor, Bell } from '@element-plus/icons-vue';

// 定义接口
interface MaintenanceTask {
  id?: string | number;
  title?: string;
  taskName?: string;
  description?: string;
  taskContent?: string;
  deviceId?: string | number;
  deviceName?: string;
  dueDate?: string;
  planTime?: string;
  completed?: boolean;
  status?: string | number;
  priority?: string | number;
  level?: string | number;
  taskType?: string | number;
  type?: string | number;
  [key: string]: any;
}

// 定义属性
defineProps<{
  data: MaintenanceTask[];
}>();

// 格式化截止日期
const formatDueDate = (dateString?: string) => {
  if (!dateString) return '未设置截止日期';
  
  try {
    const dueDate = new Date(dateString);
    const now = new Date();
    const diffMs = dueDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return `已逾期 ${Math.abs(diffDays)} 天`;
    } else if (diffDays === 0) {
      return '今天截止';
    } else if (diffDays === 1) {
      return '明天截止';
    } else if (diffDays <= 7) {
      return `${diffDays} 天后截止`;
    } else {
      return dueDate.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      }) + ' 截止';
    }
  } catch (e) {
    return dateString;
  }
};

// 获取任务类型标签
const getTaskTypeTag = (item: MaintenanceTask) => {
  const taskType = item.taskType || item.type;
  
  if (taskType === 'regular' || taskType === '1' || taskType === 1) return 'success';
  if (taskType === 'repair' || taskType === '2' || taskType === 2) return 'danger';
  if (taskType === 'inspect' || taskType === '3' || taskType === 3) return 'info';
  
  return 'primary';
};

// 获取任务类型文本
const getTaskTypeText = (item: MaintenanceTask) => {
  const taskType = item.taskType || item.type;
  
  if (taskType === 'regular' || taskType === '1' || taskType === 1) return '定期保养';
  if (taskType === 'repair' || taskType === '2' || taskType === 2) return '维修任务';
  if (taskType === 'inspect' || taskType === '3' || taskType === 3) return '检查任务';
  
  return '维护任务';
};

// 获取优先级标签
const getTaskPriorityTag = (item: MaintenanceTask) => {
  const priority = item.priority || item.level;
  
  if (priority === 'high' || priority === '1' || priority === 1) return 'danger';
  if (priority === 'medium' || priority === '2' || priority === 2) return 'warning';
  if (priority === 'low' || priority === '3' || priority === 3) return 'info';
  
  return 'primary';
};

// 获取优先级文本
const getPriorityText = (item: MaintenanceTask) => {
  const priority = item.priority || item.level;
  
  if (priority === 'high' || priority === '1' || priority === 1) return '高优先级';
  if (priority === 'medium' || priority === '2' || priority === 2) return '中优先级';
  if (priority === 'low' || priority === '3' || priority === 3) return '低优先级';
  
  return '';
};

// 判断是否紧急任务
const isUrgentTask = (item: MaintenanceTask) => {
  try {
    const dateString = item.dueDate || item.planTime;
    if (!dateString) return false;
    
    const dueDate = new Date(dateString);
    const now = new Date();
    const diffMs = dueDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    // 已逾期或者今天截止的高优先级任务
    return diffDays <= 0 || 
      (diffDays <= 1 && (item.priority === 'high' || item.priority === '1' || item.priority === 1 || 
                         item.level === 'high' || item.level === '1' || item.level === 1));
  } catch (e) {
    return false;
  }
};

// 查看任务详情
const handleViewTask = (item: MaintenanceTask) => {
  console.log('查看任务详情:', item);
  // 实际应用中应该跳转到任务详情页或显示弹窗
};

// 完成任务
const handleCompleteTask = (item: MaintenanceTask) => {
  console.log('完成任务:', item);
  // 实际应用中应该调用API更新任务状态
};
</script>

<style scoped lang="scss">
.panel-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.empty-data {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
}

.task-list {
  padding: 0 20px;
  
  .task-item {
    display: flex;
    gap: 12px;
    padding: 15px 0;
    border-bottom: 1px solid #ebeef5;
    transition: background-color 0.3s;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &.is-urgent {
      background-color: rgba(245, 108, 108, 0.05);
      
      &:hover {
        background-color: rgba(245, 108, 108, 0.1);
      }
    }
    
    .task-status {
      padding-top: 3px;
      
      .is-completed {
        &::after {
          text-decoration: line-through;
        }
      }
    }
    
    .task-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow: hidden;
      
      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .task-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
        }
        
        .task-time {
          font-size: 12px;
          color: #909399;
          
          &:not(:empty)::before {
            content: '⏱ ';
          }
        }
      }
      
      .task-body {
        display: flex;
        flex-direction: column;
        gap: 5px;
        
        .task-device {
          display: flex;
          align-items: center;
          gap: 5px;
          font-size: 13px;
          color: #606266;
          
          .el-icon {
            color: #909399;
            font-size: 14px;
          }
        }
        
        .task-desc {
          font-size: 13px;
          color: #606266;
          line-height: 1.5;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
      
      .task-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 5px;
        
        .task-tags {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
        
        .task-actions {
          display: flex;
          gap: 10px;
        }
      }
      
      .task-reminder {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 12px;
        color: #f56c6c;
        margin-top: 5px;
        padding: 5px 8px;
        background-color: rgba(245, 108, 108, 0.1);
        border-radius: 4px;
        
        .el-icon {
          font-size: 14px;
        }
      }
    }
  }
}

.panel-footer {
  padding: 10px 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}
</style> 