<template>
  <div class="panel-container">
    <div class="panel-header">
      <h3 class="panel-title">桥梁健康状态</h3>
      <span class="panel-update-time" v-if="data.lastCheckTime">最后检测: {{ formatDate(data.lastCheckTime) }}</span>
    </div>
    
    <div class="panel-content">
      <div class="health-score-section">
        <div class="score-circle" :class="getScoreClass(data.score)">
          <div class="score-value">{{ data.score }}</div>
          <div class="score-label">健康评分</div>
        </div>
        
        <div class="score-status">
          <div class="status-icon" :class="getScoreClass(data.score)">
            <el-icon v-if="data.score >= 90"><Check /></el-icon>
            <el-icon v-else-if="data.score >= 70"><Warning /></el-icon>
            <el-icon v-else><CircleClose /></el-icon>
          </div>
          <div class="status-text">{{ getScoreText(data.score) }}</div>
        </div>
      </div>
      
      <div class="component-list">
        <h4 class="component-list-title">部件健康状态</h4>
        <div class="component-item" v-for="(component, index) in data.components" :key="index">
          <div class="component-info">
            <div class="component-name">{{ component.name }}</div>
            <div class="component-score" :class="getScoreClass(component.score)">{{ component.score }}</div>
          </div>
          <div class="component-progress">
            <el-progress 
              :percentage="component.score" 
              :stroke-width="6" 
              :color="getProgressColor(component.score)" 
              :format="() => ''"
            ></el-progress>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Check, Warning, CircleClose } from '@element-plus/icons-vue';

// 定义组件接口
interface Component {
  name: string;
  score: number;
  status?: string;
}

interface HealthData {
  score: number;
  status?: string;
  lastCheckTime: string;
  components: Component[];
}

// 定义属性
defineProps<{
  data: HealthData;
}>();

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
};

// 根据评分获取状态类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-good';
  if (score >= 70) return 'score-warn';
  return 'score-danger';
};

// 根据评分获取状态文本
const getScoreText = (score: number) => {
  if (score >= 90) return '状态良好';
  if (score >= 70) return '需要关注';
  return '需要检修';
};

// 获取进度条颜色
const getProgressColor = (score: number) => {
  if (score >= 90) return '#67c23a';
  if (score >= 70) return '#e6a23c';
  return '#f56c6c';
};
</script>

<style scoped lang="scss">
.panel-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
  
  .panel-update-time {
    font-size: 12px;
    color: #909399;
  }
}

.panel-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.health-score-section {
  display: flex;
  align-items: center;
  gap: 30px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #ebeef5;
}

.score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3px solid;
    box-sizing: border-box;
  }
  
  &.score-good::before {
    border-color: #67c23a;
  }
  
  &.score-warn::before {
    border-color: #e6a23c;
  }
  
  &.score-danger::before {
    border-color: #f56c6c;
  }
  
  .score-value {
    font-size: 32px;
    font-weight: 700;
    line-height: 1.1;
  }
  
  .score-label {
    font-size: 12px;
    color: #909399;
  }
}

.score-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  
  .status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    
    &.score-good {
      background-color: #67c23a;
    }
    
    &.score-warn {
      background-color: #e6a23c;
    }
    
    &.score-danger {
      background-color: #f56c6c;
    }
    
    .el-icon {
      font-size: 24px;
    }
  }
  
  .status-text {
    font-size: 14px;
    font-weight: 500;
  }
}

.component-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  
  .component-list-title {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
    margin: 0 0 5px 0;
  }
  
  .component-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .component-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .component-name {
        font-size: 14px;
        color: #606266;
      }
      
      .component-score {
        font-size: 14px;
        font-weight: 500;
        
        &.score-good {
          color: #67c23a;
        }
        
        &.score-warn {
          color: #e6a23c;
        }
        
        &.score-danger {
          color: #f56c6c;
        }
      }
    }
    
    .component-progress {
      margin-bottom: 5px;
    }
  }
}
</style> 