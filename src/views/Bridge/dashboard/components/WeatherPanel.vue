<template>
  <div class="panel-container">
    <div class="panel-header">
      <h3 class="panel-title">气象信息</h3>
      <div class="panel-actions">
        <el-tag size="small" type="info">{{ data.location }}</el-tag>
      </div>
    </div>
    
    <div class="panel-content">
      <div class="current-weather">
        <div class="weather-icon">
          <el-icon v-if="data.current.condition === 'sunny'" size="48"><Sunny /></el-icon>
          <el-icon v-else-if="data.current.condition === 'cloudy'" size="48"><Cloudy /></el-icon>
          <el-icon v-else-if="data.current.condition === 'rainy'" size="48"><Umbrella /></el-icon>
          <el-icon v-else-if="data.current.condition === 'thunder'" size="48"><Lightning /></el-icon>
          <el-icon v-else size="48"><Cloudy /></el-icon>
        </div>
        
        <div class="weather-info">
          <div class="weather-main">
            <div class="temperature">{{ data.current.temperature }}°C</div>
            <div class="weather-desc">{{ data.current.condition }}</div>
          </div>
          
          <div class="weather-details">
            <div class="detail-item">
              <div class="detail-label">湿度</div>
              <div class="detail-value">{{ data.current.humidity }}%</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">风速</div>
              <div class="detail-value">{{ data.current.windSpeed }} km/h</div>
            </div>
            
            <div class="detail-item">
              <div class="detail-label">风向</div>
              <div class="detail-value">{{ data.current.windDirection }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="weather-divider"></div>
      
      <div class="forecast-container">
        <h4 class="forecast-title">未来天气预报</h4>
        
        <div class="forecast-list">
          <div class="forecast-item" v-for="(item, index) in data.forecast" :key="index">
            <div class="forecast-day">{{ item.date }}</div>
            <div class="forecast-icon">
              <el-icon v-if="item.condition === 'sunny'" size="24"><Sunny /></el-icon>
              <el-icon v-else-if="item.condition === 'cloudy'" size="24"><Cloudy /></el-icon>
              <el-icon v-else-if="item.condition === 'rainy'" size="24"><Umbrella /></el-icon>
              <el-icon v-else-if="item.condition === 'thunder'" size="24"><Lightning /></el-icon>
              <el-icon v-else size="24"><Cloudy /></el-icon>
            </div>
            <div class="forecast-weather">{{ item.condition }}</div>
            <div class="forecast-temp">
              <span class="max-temp">{{ item.tempMax }}°</span>
              <span class="temp-separator">/</span>
              <span class="min-temp">{{ item.tempMin }}°</span>
            </div>
            <div class="forecast-wind">{{ item.windSpeed }} km/h</div>
          </div>
        </div>
      </div>
      
      <div class="weather-warning" v-if="hasWarning">
        <div class="warning-icon">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="warning-content">
          <div class="warning-title">气象预警</div>
          <div class="warning-message">当前有强风预警，风速可能达到8-10级，请注意安全。</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Sunny, Cloudy, Warning, Lightning, Umbrella } from '@element-plus/icons-vue';

// 定义接口
interface ForecastItem {
  date: string;
  condition: string;
  tempMax: number;
  tempMin: number;
  windSpeed: number;
}

interface WeatherData {
  location: string;
  current: {
    temperature: number;
    condition: string;
    humidity: number;
    windSpeed: number;
    windDirection: string;
  };
  forecast: ForecastItem[];
}

// 定义属性
const props = defineProps<{
  data: WeatherData;
}>();

// 计算是否有气象预警
const hasWarning = computed(() => {
  // 这里可以根据实际情况判断是否有预警，例如风速超过某个阈值
  return props.data.current.windSpeed > 25 || props.data.forecast.some(item => item.windSpeed > 30);
});

// 你可以在这里添加更多的计算属性或方法
</script>

<style scoped lang="scss">
.panel-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.panel-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow: auto;
}

.current-weather {
  display: flex;
  align-items: center;
  gap: 20px;
  
  .weather-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
    color: #0288d1;
  }
  
  .weather-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    
    .weather-main {
      display: flex;
      align-items: center;
      gap: 10px;
      
      .temperature {
        font-size: 28px;
        font-weight: 700;
        line-height: 1;
        color: #303133;
      }
      
      .weather-desc {
        font-size: 16px;
        color: #606266;
      }
    }
    
    .weather-details {
      display: flex;
      gap: 20px;
      
      .detail-item {
        .detail-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }
        
        .detail-value {
          font-size: 14px;
          font-weight: 500;
          color: #606266;
        }
      }
    }
  }
}

.weather-divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 10px 0;
}

.forecast-container {
  .forecast-title {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
    margin: 0 0 15px 0;
  }
  
  .forecast-list {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    
    .forecast-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 6px;
      transition: transform 0.3s;
      
      &:hover {
        transform: translateY(-5px);
        background-color: #ecf5ff;
      }
      
      .forecast-day {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 10px;
      }
      
      .forecast-icon {
        margin-bottom: 10px;
        color: #409eff;
      }
      
      .forecast-weather {
        font-size: 12px;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .forecast-temp {
        font-size: 14px;
        margin-bottom: 8px;
        
        .max-temp {
          color: #f56c6c;
          font-weight: 500;
        }
        
        .temp-separator {
          margin: 0 2px;
          color: #dcdfe6;
        }
        
        .min-temp {
          color: #409eff;
          font-weight: 500;
        }
      }
      
      .forecast-wind {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.weather-warning {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: #fef0f0;
  border-radius: 6px;
  margin-top: 10px;
  
  .warning-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f56c6c;
    color: white;
    
    .el-icon {
      font-size: 24px;
    }
  }
  
  .warning-content {
    flex: 1;
    
    .warning-title {
      font-size: 14px;
      font-weight: 600;
      color: #f56c6c;
      margin-bottom: 5px;
    }
    
    .warning-message {
      font-size: 12px;
      color: #606266;
      line-height: 1.5;
    }
  }
}

@media (max-width: 768px) {
  .current-weather {
    flex-direction: column;
    text-align: center;
    
    .weather-info {
      .weather-main {
        justify-content: center;
      }
      
      .weather-details {
        justify-content: center;
      }
    }
  }
  
  .forecast-list {
    flex-wrap: wrap;
    
    .forecast-item {
      flex: 1 1 calc(50% - 5px);
      min-width: calc(50% - 5px);
    }
  }
}
</style> 