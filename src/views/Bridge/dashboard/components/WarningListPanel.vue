<template>
  <div class="panel-container">
    <div class="panel-header">
      <h3 class="panel-title">预警列表</h3>
      <div class="panel-actions">
        <el-button type="primary" size="small" plain icon="Plus">处理预警</el-button>
      </div>
    </div>
    
    <div v-if="data.length === 0" class="empty-data">
      <el-empty description="暂无预警信息" :image-size="100"></el-empty>
    </div>
    
    <div v-else class="panel-content">
      <el-scrollbar height="420px">
        <div class="warning-list">
          <div 
            v-for="(item, index) in data" 
            :key="index" 
            class="warning-item"
            :class="{ 'is-critical': isHighPriority(item) }"
          >
            <div class="warning-icon">
              <el-icon v-if="item.typeIcon === 'Ship'"><Ship /></el-icon>
              <el-icon v-else-if="item.typeIcon === 'Cloudy'"><Cloudy /></el-icon>
              <el-icon v-else-if="item.typeIcon === 'Warning'"><Warning /></el-icon>
              <el-icon v-else><WarningFilled /></el-icon>
            </div>
            
            <div class="warning-content">
              <div class="warning-header">
                <div class="warning-title">{{ item.title || '预警信息' }}</div>
                <div class="warning-time">{{ formatTime(item.createTime) }}</div>
              </div>
              
              <div class="warning-body">
                {{ item.content || '暂无详细信息' }}
              </div>
              
              <div class="warning-footer">
                <el-tag size="small" :type="getWarningTagType(item)">{{ item.type }}</el-tag>
                <div class="warning-actions">
                  <el-button type="text" size="small" @click="handleWarningDetail(item)">详情</el-button>
                  <el-button type="text" size="small" @click="handleProcessWarning(item)">处理</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
      
      <div class="panel-footer" v-if="totalWarnings > data.length">
        <el-link type="primary">查看更多预警（{{ totalWarnings }}条）</el-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Ship, Cloudy, Warning, WarningFilled } from '@element-plus/icons-vue';

// 定义接口
interface WarningItem {
  id?: string | number;
  title?: string;
  content?: string;
  type: string;
  typeIcon?: string;
  level?: string | number;
  createTime: string;
  status?: string | number;
  [key: string]: any;
}

// 定义属性
const props = defineProps<{
  data: WarningItem[];
}>();

// 模拟总预警数量
const totalWarnings = computed(() => {
  return props.data.length + 5; // 假设还有5条未显示
});

// 格式化时间
const formatTime = (timeString: string) => {
  if (!timeString) return '';
  
  try {
    const date = new Date(timeString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffMins < 24 * 60) {
      const hours = Math.floor(diffMins / 60);
      return `${hours}小时前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  } catch (e) {
    return timeString;
  }
};

// 获取预警标签类型
const getWarningTagType = (item: WarningItem) => {
  const type = item.type?.toLowerCase() || '';
  
  if (type.includes('船舶')) return 'info';
  if (type.includes('气象')) return 'warning';
  if (type.includes('事故')) return 'danger';
  if (type.includes('污染')) return 'success';
  if (type.includes('灾害')) return 'danger';
  
  // 根据级别判断
  if (item.level === 1 || item.level === '1' || item.level === 'high') return 'danger';
  if (item.level === 2 || item.level === '2' || item.level === 'medium') return 'warning';
  
  return 'info';
};

// 判断是否高优先级预警
const isHighPriority = (item: WarningItem) => {
  return item.level === 1 || item.level === '1' || item.level === 'high' || 
    (item.type && (item.type.includes('事故') || item.type.includes('灾害')));
};

// 处理查看预警详情
const handleWarningDetail = (item: WarningItem) => {
  console.log('查看预警详情:', item);
  // 实际应用中应该跳转到预警详情页或显示弹窗
};

// 处理预警
const handleProcessWarning = (item: WarningItem) => {
  console.log('处理预警:', item);
  // 实际应用中应该打开处理预警的表单或弹窗
};
</script>

<style scoped lang="scss">
.panel-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.empty-data {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px;
}

.warning-list {
  padding: 0 20px;
  
  .warning-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #ebeef5;
    transition: background-color 0.3s;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &.is-critical {
      background-color: rgba(245, 108, 108, 0.05);
      border-left: 3px solid #f56c6c;
      padding-left: 12px;
      
      &:hover {
        background-color: rgba(245, 108, 108, 0.1);
      }
    }
    
    .warning-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #ebeef5;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #909399;
      
      .el-icon {
        font-size: 20px;
      }
    }
    
    .warning-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      overflow: hidden;
      
      .warning-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .warning-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
        }
        
        .warning-time {
          font-size: 12px;
          color: #909399;
        }
      }
      
      .warning-body {
        font-size: 13px;
        color: #606266;
        line-height: 1.5;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .warning-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 5px;
        
        .warning-actions {
          display: flex;
          gap: 10px;
        }
      }
    }
  }
}

.panel-footer {
  padding: 10px 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}
</style> 