<template>
  <div class="card-wrapper">
    <el-main class="form-card container-card">
      <!-- 优化标题样式 -->
      <div class="card-header">
        <span class="header-title">新增交通事故预警</span>
        <div class="header-line"></div>
      </div>

      <el-form 
        label-width="180px" 
        :model="form"
        :rules="rules" 
        ref="formRef"
        class="form-container"
      >
        <el-row :gutter="40">
          <el-col :xs="24" :sm="12" :md="10" :lg="11">
            <div class="left-form">
              <!-- 统一表单项样式 -->
              <el-form-item label="预警名称：" class="form-item" prop="earlyWarningInfo.title">
                <el-input v-model="form.earlyWarningInfo.title" placeholder="请输入预警名称" disabled />
              </el-form-item>

              <!-- 添加桥梁选择 -->
              <el-form-item label="桥梁：" class="form-item" prop="earlyWarningInfo.bridgeId">
                <el-select v-model="form.earlyWarningInfo.bridgeId" placeholder="请选择桥梁" filterable style="width: 100%">
                  <el-option
                    v-for="item in bridgeList"
                    :key="item.id"
                    :label="item.bridgeName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <!-- 调整单选组样式 -->
              <el-form-item label="发生地点：" class="form-item" prop="earlyWarningInfo.location">
                <el-input 
                  v-model="form.earlyWarningInfo.location" 
                  placeholder="请输入地理坐标或道路桩号" 
                />
              </el-form-item>

              <!-- 新增经纬度表单项 -->
              <el-form-item label="地理坐标：" class="form-item" prop="coordinates">
                <div class="coordinate-input-group">
                  <el-input 
                    v-model="form.coordinates" 
                    placeholder="点击选择地理坐标"
                    readonly
                    @click="openMapSelector" 
                  >
                    <template #suffix>
                      <el-icon class="coordinate-icon" @click.stop="openMapSelector">
                        <Location />
                      </el-icon>
                    </template>
                  </el-input>
                </div>
              </el-form-item>

              <!-- 统一选择器样式 -->
              <el-form-item label="预警级别：" class="form-item" prop="earlyWarningInfo.warnLevel">
                <el-select
                  v-model="form.earlyWarningInfo.warnLevel"
                  placeholder="请选择预警级别"
                  style="width: 100%"
                >
                  <el-option 
                    v-for="item in warnLevelList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :class="`option-level-${item.value}`"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="事故类型：" class="form-item" prop="accidentType">
                <el-select
                  v-model="form.accidentType"
                  placeholder="请选择事故类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in typeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="发生时间：" class="form-item" prop="occurrenceTime">
                <el-date-picker
                  v-model="form.occurrenceTime"
                  type="datetime"
                  placeholder="选择日期和时间"
                  style="width: 100%"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>

              <el-form-item label="交通中断原因：" class="form-item" prop="blockageCause">
                <el-select
                  v-model="form.blockageCause"
                  placeholder="请选择中断原因"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in blockageReasonList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="起止位置及桩号：" class="form-item" prop="blockageRange">
                <el-input 
                  v-model="form.blockageRange" 
                  placeholder="例：K12+300至K14+500" 
                />
              </el-form-item>

              <el-form-item label="预计恢复时间（小时）：" class="form-item" prop="recoveryTime">
                <el-input-number 
                  v-model="form.recoveryTime"
                  :min="1" 
                  controls-position="right" 
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="基础设施损失（万元）：" class="form-item">
                  <el-input-number
                    v-model="form.directLoss"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="14" :lg="12">
            <div class="right-section">
              <div class="right-form" >
                

                <el-form-item label="滞留车辆数量：" class="form-item" prop="strandedVehicles">
                  <el-input-number
                    v-model="form.strandedVehicles"
                    :min="0"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="排队长度（公里）：" class="form-item" prop="queueLength">
                  <el-input-number
                    v-model="form.queueLength"
                    :min="0"
                    :precision="1"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="大桥损毁描述：" class="form-item" prop="bridgeDamage">
                  <el-input 
                    v-model="form.bridgeDamage" 
                    type="textarea"
                    :rows="4"
                    placeholder="请描述大桥受损情况" 
                  />
                </el-form-item>

                <el-form-item label="应急管理措施：" class="form-item" prop="emergencyMeasures">
                  <el-input
                    type="textarea"
                    v-model="form.emergencyMeasures"
                    :rows="4"
                    placeholder="请输入应急管理措施"
                  />
                </el-form-item>

                <el-form-item label="预警状态：" class="form-item" prop="earlyWarningInfo.status">
                  <el-radio-group v-model="form.earlyWarningInfo.status">
                    <el-radio :label="1">未处理</el-radio>
                    <el-radio :label="2">处理中</el-radio>
                    <el-radio :label="3">已处理</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="禁航需求：" class="form-item" prop="earlyWarningInfo.navigationClosure">
                  <el-radio-group v-model="form.earlyWarningInfo.navigationClosure">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>

                <!-- 新增图片上传组件 -->
                <el-form-item label="上传图片：" class="form-item">
                  <div class="upload-container">
                    <el-upload
                      :action="uploadFileUrl"
                      :headers="headers"
                      list-type="picture-card"
                      :on-success="handleUploadSuccess"
                      :on-remove="handleUploadRemove"
                      :before-upload="beforeUpload"
                      :on-error="handleUploadError"
                      multiple
                      name="file"
                    >
                      <el-icon><Plus /></el-icon>
                    </el-upload>
                  </div>
                </el-form-item>

                <!-- 添加操作按钮 -->
                <el-form-item>
                <div class="form-buttons-container">
                  <div class="form-actions">
                    <el-button @click="resetForm" class="cancel-btn">重置</el-button>
                    <el-button type="primary" @click="submitForm" class="submit-btn">保存</el-button>
                  </div>
                </div>
                </el-form-item>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-main>

    <!-- 新增地图选择器弹窗 -->
    <el-dialog
      v-model="mapSelectorVisible"
      title="选择地理坐标"
      width="65%"
      :destroy-on-close="true"
      :lock-scroll="false"
    >
      <div class="map-selector-container">
        <map-coordinate-selector
          :initial-lat="initialCoordinates.lat"
          :initial-lng="initialCoordinates.lng"
          @confirm="handleCoordinateConfirm"
          @cancel="mapSelectorVisible = false"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits } from "vue";
import { listData } from "@/api/system/dict/data";
import { DictDataQuery } from "@/api/system/dict/data/types";
import { addTrafficAccidentWarning } from "@/api/bridge/traffic/traffic";
import dayjs from 'dayjs';
import router from "@/router";
import { ElMessage } from "element-plus";
import { Location, Plus } from '@element-plus/icons-vue';
import MapCoordinateSelector from "@/components/MapCoordinateSelector/index.vue";
import { getBridgeList } from '@/api/bridge/bisc/bridge';
import { globalHeaders } from '@/utils/request';

// 定义事件
const emit = defineEmits(['success']);

const queryParams = ref<DictDataQuery>({
  dictType: "",
  pageNum: 1,
  pageSize: 1000,
  dictName: '',
  dictLabel: ''
});

const formRef = ref();

const warnLevelList = ref([]);
const typeList = ref([]);
const blockageReasonList = ref([]);
const bridgeList = ref([]);

function getWarnLevelList() {
  queryParams.value.dictType = "warn_level";
  listData(queryParams.value).then((res) => {
    warnLevelList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

function getTypeList() {
  queryParams.value.dictType = "accident_type";
  listData(queryParams.value).then((res) => {
    typeList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

function getBlockageReasonList() {
  queryParams.value.dictType = "blockage_range";
  listData(queryParams.value).then((res) => {
    blockageReasonList.value = res.rows.map(item => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

// 获取桥梁列表
function getBridgeData() {
  // 不传递分页参数，获取所有数据
  getBridgeList({ isWarn: 1 }).then((res) => {
    bridgeList.value = res.rows;
  });
}

// 新增地图选择器相关数据
const mapSelectorVisible = ref(false);
const initialCoordinates = ref({
  lat: 30.5,
  lng: 121.9
});

// 表单数据
const form = ref({
  // earlyWarningInfo 部分
  earlyWarningInfo: {
    title: "交通事故预警",  // 原warningName改为title
    warnLevel: "" ,         // 原warningLevel改为warnLevel
    navigationClosure: 0,   // 新增禁航需求字段
    status: 1, // 新增预警状态字段
    bridgeId: null, // 新增桥梁ID字段
    lon: null, // 新增经度字段
    lat: null,  // 新增纬度字段
    fileId: "", // 修改为fileId
    location: "", // 发生地点
  },
  // trafficAccidentWarning 部分
  location: "",
  coordinates: "", // 新增坐标字段
  accidentType: "",
  occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  bridgeDamage: "",
  blockageCause: "",
  blockageRange: "",
  recoveryTime: null,
  directLoss: null,
  strandedVehicles: null,
  queueLength: null,
  emergencyMeasures: "",
  detourRoute: "",
  estimatedRecoveryTime: null,

});

// 在script部分更新校验规则
const rules = ref({
  'earlyWarningInfo.title': [{ required: true, message: '预警标题不能为空', trigger: 'blur' }],
  'earlyWarningInfo.warnLevel': [{ required: true, message: '请选择预警级别', trigger: 'blur' }],
  'earlyWarningInfo.bridgeId': [{ required: true, message: '请选择桥梁', trigger: 'change' }],
  'earlyWarningInfo.location': [{ required: true, message: '发生地点不能为空', trigger:'blur' }],
  coordinates: [{ required: true, message: '请选择地理坐标', trigger: 'blur' }],
  accidentType: [{ required: true, message: '请选择事故类型', trigger:'blur' }],
  occurrenceTime: [{ required: true, message: '请选择发生时间', trigger: 'blur' }],
  bridgeDamage: [{ required: true, message: '大桥损毁描述不能为空', trigger: 'blur' }],
  blockageCause: [{ required: true, message: '请选择中断原因', trigger:'blur' }],
  blockageRange: [{ required: true, message: '起止位置不能为空', trigger: 'blur' }],
  recoveryTime: [{ required: true, message: '请输入预计恢复时间', trigger: 'blur' }],
  strandedVehicles: [{ required: true, message: '请输入滞留车辆数', trigger: 'blur' }],
  queueLength: [{ required: true, message: '请输入排队长度', trigger:'blur' }],
  emergencyMeasures: [{ required: true, message: '应急措施不能为空', trigger: 'blur' }],
  'earlyWarningInfo.navigationClosure': [{ required: true, message: '请选择禁航需求', trigger: 'blur' }],
  'earlyWarningInfo.status': [{ required: true, message: '请选择预警状态', trigger: 'blur' }]
});

// 打开地图选择器
const openMapSelector = () => {
  // 如果已有坐标，解析并设置为初始值
  if (form.value.coordinates) {
    try {
      const [lat, lng] = form.value.coordinates.split(',');
      initialCoordinates.value = {
        lat: parseFloat(lat),
        lng: parseFloat(lng)
      };
    } catch (e) {
      console.error('Invalid coordinates format', e);
    }
  }
  mapSelectorVisible.value = true;
};

// 处理坐标确认
const handleCoordinateConfirm = (coords) => {
  form.value.coordinates = `${coords.lat},${coords.lng}`;
  // 同时更新主表中的经纬度
  form.value.earlyWarningInfo.lat = coords.lat.toString();
  form.value.earlyWarningInfo.lon = coords.lng.toString();
  mapSelectorVisible.value = false;
};

// 新增上传相关配置
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + '/resource/oss/upload');
const headers = ref(globalHeaders());

// 处理上传成功
const handleUploadSuccess = (response, uploadFile) => {
  if (response.code === 200) {
    const currentIds = form.value.earlyWarningInfo.fileId ? form.value.earlyWarningInfo.fileId.split(',') : [];
    currentIds.push(response.data.ossId);
    form.value.earlyWarningInfo.fileId = currentIds.join(',');
    ElMessage.success('上传成功');
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
};

// 处理上传错误
const handleUploadError = (error) => {
  ElMessage.error('上传文件失败');
};

// 处理移除图片
const handleUploadRemove = (uploadFile) => {
  // 从fileId中移除对应的ID
  if (form.value.earlyWarningInfo.fileId) {
    const currentIds = form.value.earlyWarningInfo.fileId.split(',');
    // 找到要删除的ID的索引
    const index = currentIds.findIndex(id => id === uploadFile.response?.data?.ossId);
    if (index > -1) {
      currentIds.splice(index, 1);
      form.value.earlyWarningInfo.fileId = currentIds.join(',');
    }
  }
};

// 上传前校验
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
};

// 更新提交表单函数，将坐标包含到提交数据中
const submitForm = async () => {
    await formRef.value.validate();
    // 确保主表包含经纬度
    if (form.value.coordinates) {
      const [lat, lng] = form.value.coordinates.split(',');
      form.value.earlyWarningInfo.lat = lat;
      form.value.earlyWarningInfo.lon = lng;
    }
    
    const submitData = {
      earlyWarningInfo: {
        title: form.value.earlyWarningInfo.title,
        warnLevel: form.value.earlyWarningInfo.warnLevel,
        navigationClosure: form.value.earlyWarningInfo.navigationClosure,
        status: form.value.earlyWarningInfo.status,
        bridgeId: form.value.earlyWarningInfo.bridgeId,
        lon: form.value.earlyWarningInfo.lon,
        lat: form.value.earlyWarningInfo.lat,
        location: form.value.earlyWarningInfo.location, // 添加发生地点
        fileId: form.value.earlyWarningInfo.fileId // 添加文件ID
      },
      trafficAccidentWarning: {
        location: form.value.location,
        coordinates: form.value.coordinates, // 添加坐标信息
        accidentType: typeList.value.find(item => item.value === form.value.accidentType) 
          ? form.value.accidentType 
          : 'custom:' + form.value.accidentType,
        occurrenceTime: form.value.occurrenceTime,
        bridgeDamage: form.value.bridgeDamage,
        blockageCause: blockageReasonList.value.find(item => item.value === form.value.blockageCause)
          ? form.value.blockageCause
          : 'custom:' + form.value.blockageCause,
        blockageRange: form.value.blockageRange,
        recoveryTime: form.value.recoveryTime,
        directLoss: form.value.directLoss,
        strandedVehicles: form.value.strandedVehicles,
        queueLength: form.value.queueLength,
        emergencyMeasures: form.value.emergencyMeasures,
      }
    };
    console.log("提交数据", submitData);
    const res = await addTrafficAccidentWarning(submitData);
    if (res.code === 200) {
      ElMessage.success("提交成功");
      // 通知父组件关闭对话框并刷新列表
      emit('success');
    }
};

// 重置表单
const resetForm = () => {
  form.value = {
    earlyWarningInfo: {
      title: "交通事故预警",
      warnLevel: "",
      navigationClosure: 0,
      status: 0,
      bridgeId: null,
      lon: null,
      lat: null,
      location: "", // 发生地点
      fileId: "", // 修改为fileId
    },
    location: "",
    coordinates: "", // 重置坐标字段
    accidentType: "",
    occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    bridgeDamage: "",
    blockageCause: "",
    blockageRange: "",
    recoveryTime: null,
    directLoss: null,
    strandedVehicles: null,
    queueLength: null,
    emergencyMeasures: "",
    detourRoute: "",
    estimatedRecoveryTime: null,
  };
};
getWarnLevelList();
getTypeList();
getBlockageReasonList();
getBridgeData();
</script>

<style scoped lang="scss">
.card-wrapper {
  position: relative;
  height: 100%;
  background: var(--el-bg-color-page);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    pointer-events: none;
  }
}

.form-card {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--el-bg-color-overlay);
  // border: 1px solid var(--el-border-color-light);
  padding: 20px 30px;
  border-radius: 8px;

  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding: 16px 20px;
  }

  .card-header {
    margin-bottom: 25px;
    .header-title {
      font-size: 20px;
      color: var(--el-text-color-primary);
      font-weight: 600;
    }
    .header-line {
      height: 3px;
      background: linear-gradient(90deg, #409eff 30%, transparent 100%);
      margin-top: 12px;
    }
  }

  .form-container {
    flex: 1;
    min-height: 600px;
    padding: 0 15px;

    .el-col {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .left-form {
      border-right: 1px dashed #e4e7ed;
      padding-right: 40px;
      height: 100%;
    }

    .right-section {
      padding-left: 40px;
      height: 100%;
      display: flex;
      flex-direction: column;

      .sub-title {
        font-size: 16px;
        margin-bottom: 25px;
        padding-left: 12px;
        border-left-width: 4px;
      }
    }
  }

  .form-item {
    margin-bottom: 28px;
    :deep(.el-form-item__label) {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }

    .el-input, .el-select, .el-date-editor {
      :deep(.el-input__inner) {
        line-height: 1.5;
        background: var(--el-input-bg-color);
        border-color: var(--el-input-border-color);
        color: var(--el-text-color-primary);
      }
    }
  }

  .checkbox-group {
    width: 50%;
    display: flex;
    justify-content: space-between;
    gap: 20px;
    .el-checkbox {
      margin: 4px 0;
    }
  }

  .form-divider {
    margin: 28px 0;
    background-color: #e4e7ed;
  }

  .form-actions {
    margin-top: auto;
    padding-top: 20px;
    display: flex;
    gap: 20px;
    justify-content: flex-end;

    .el-button {
      padding: 12px 24px;
    }
  }
  
  .form-buttons-container {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    
    .form-actions {
      margin-top: 0;
      padding-top: 0;
    }
  }
}

@media (max-width: 768px) {
  .form-card {
    height: auto;
    padding: 15px;
    
    .form-container {
      .left-form {
        border-right: none;
        padding-right: 0;
      }
      
      .right-section {
        padding-left: 0;
        margin-top: 20px;
      }
    }
    
    .form-buttons-container {
      flex-direction: column;
      gap: 12px;
    }
    
    .form-actions {
      flex-direction: column;
      gap: 12px;
    }
  }
}

// 预警级别颜色标识
.option-level-1 {
  color: #f56c6c;
  font-weight: 500;
}
.option-level-2 {
  color: #e6a23c;
  font-weight: 500;
}
.option-level-3 {
  color: #409eff;
  font-weight: 500;
}
.option-level-4 {
  color: #67c23a;
  font-weight: 500;
}

// 新增的样式
.coordinate-input-group {
  display: flex;
  align-items: center;
  width: 100%;
  
  .el-input {
    cursor: pointer;
    
    :deep(.el-input__inner) {
      cursor: pointer;
    }
  }
  
  .coordinate-icon {
    color: #409EFF;
    cursor: pointer;
    font-size: 16px;
    
    &:hover {
      color: #66b1ff;
    }
  }
}

.map-selector-container {
  width: 100%;
  height: 500px;
}

// 新增上传组件样式
.upload-container {
  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 100px;
    height: 100px;
  }
}
</style>
