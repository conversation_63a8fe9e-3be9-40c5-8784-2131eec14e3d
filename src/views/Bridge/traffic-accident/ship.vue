<template>
  <div class="card-wrapper">
    <el-main class="form-card container-card">
      <div class="card-header">
        <span class="header-title">新增船舶预警</span>
        <div class="header-line"></div>
      </div>

      <el-form label-width="180px" :model="form" :rules="rules" ref="formRef" class="form-container">
        <el-row :gutter="40">
          <el-col :xs="24" :sm="12" :md="10" :lg="11">
            <div class="left-form">
              <el-form-item label="预警名称：" class="form-item" prop="earlyWarningInfo.title">
                <el-input v-model="form.earlyWarningInfo.title" placeholder="请输入预警名称" disabled />
              </el-form-item>

              <el-form-item label="船名：" class="form-item" prop="shipEarlyWarning.shipName">
                <el-input v-model="form.shipEarlyWarning.shipName" placeholder="请输入船名" />
              </el-form-item>

              <el-form-item label="MMSI：" class="form-item" prop="shipEarlyWarning.mmsi">
                <el-input v-model="form.shipEarlyWarning.mmsi" placeholder="请输入MMSI编号" />
              </el-form-item>

              <el-form-item label="船舶类型：" class="form-item" prop="shipEarlyWarning.shipType">
                <el-select v-model="form.shipEarlyWarning.shipType" placeholder="请选择船舶类型" style="width: 100%">
                  <el-option
                    v-for="item in shipTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="预警级别：" class="form-item" prop="earlyWarningInfo.warnLevel">
                <el-select v-model="form.earlyWarningInfo.warnLevel" placeholder="请选择预警级别" style="width: 100%">
                  <el-option
                    v-for="item in warnLevelList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :class="`option-level-${item.value}`"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="发生时间：" class="form-item" prop="shipEarlyWarning.createTime">
                <el-date-picker
                  v-model="form.shipEarlyWarning.createTime"
                  type="datetime"
                  placeholder="选择日期和时间"
                  style="width: 100%"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>



              <el-form-item label="上传图片：" class="form-item">
                <div class="upload-container">
                  <el-upload
                    :action="uploadFileUrl"
                    :headers="headers"
                    list-type="picture-card"
                    :on-success="handleUploadSuccess"
                    :on-remove="handleUploadRemove"
                    :before-upload="beforeUpload"
                    :on-error="handleUploadError"
                    multiple
                    name="file"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </div>
              </el-form-item>
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="14" :lg="12">
            <div class="right-section">
              <div class="right-form">
                <el-form-item label="定位信息：" class="form-item" prop="coordinates">
                  <div class="coordinate-input-group" style="width: 100%;">
                    <el-input 
                      v-model="coordinates" 
                      placeholder="点击选择地理坐标"
                      readonly
                      @click="openMapSelector"
                    >
                      <template #suffix>
                        <el-icon class="coordinate-icon" @click.stop="openMapSelector">
                          <Location />
                        </el-icon>
                      </template>
                    </el-input>
                  </div>
                </el-form-item>

                <el-form-item v-show="false">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item label="经度：" prop="shipEarlyWarning.lon" label-width="100px">
                        <el-input v-model="form.shipEarlyWarning.lon" placeholder="经度" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="纬度：" prop="shipEarlyWarning.lat" label-width="100px">
                        <el-input v-model="form.shipEarlyWarning.lat" placeholder="纬度" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form-item>
                <el-form-item label="事件内容：" class="form-item" prop="shipEarlyWarning.eventContent">
                <el-input v-model="form.shipEarlyWarning.eventContent" placeholder="请输入事件内容" />
              </el-form-item>

              <el-form-item label="桥梁：" class="form-item" prop="earlyWarningInfo.bridgeId">
                <el-select v-model="form.earlyWarningInfo.bridgeId" placeholder="请选择桥梁" filterable style="width: 100%">
                  <el-option
                    v-for="item in bridgeList"
                    :key="item.id"
                    :label="item.bridgeName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
                <el-form-item label="发生地点：" class="form-item" prop="earlyWarningInfo.location">
                <el-input v-model="form.earlyWarningInfo.location" placeholder="请输入发生地点" />
              </el-form-item>

                <el-form-item label="禁航需求：" class="form-item" prop="earlyWarningInfo.navigationClosure">
                  <el-radio-group v-model="form.earlyWarningInfo.navigationClosure">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="预警状态：" class="form-item" prop="earlyWarningInfo.status">
                  <el-radio-group v-model="form.earlyWarningInfo.status">
                    <el-radio :label="1">未处理</el-radio>
                    <el-radio :label="2">处理中</el-radio>
                    <el-radio :label="3">已处理</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item>
                  <div style="width: 100%; display: flex; justify-content: flex-end">
                    <div class="form-actions">
                      <el-button @click="resetForm" class="cancel-btn">重置</el-button>
                      <el-button type="primary" @click="submitForm" class="submit-btn">保存</el-button>
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-main>

    <!-- 新增地图选择器弹窗 -->
    <el-dialog
      v-model="mapSelectorVisible"
      title="选择地理坐标"
      width="65%"
      :destroy-on-close="true"
      :lock-scroll="false"
    >
      <div class="map-selector-container">
        <map-coordinate-selector
          :initial-lat="initialCoordinates.lat"
          :initial-lng="initialCoordinates.lng"
          @confirm="handleCoordinateConfirm"
          @cancel="mapSelectorVisible = false"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineEmits } from 'vue';
import { listData } from '@/api/system/dict/data';
import { DictDataQuery } from '@/api/system/dict/data/types';
import dayjs from 'dayjs';
import router from '@/router';
import { addShipWarning } from '@/api/bridge/traffic/ship';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Location, Plus } from '@element-plus/icons-vue';
import MapCoordinateSelector from "@/components/MapCoordinateSelector/index.vue";
import { getBridgeList } from '@/api/bridge/bisc/bridge';
import { globalHeaders } from '@/utils/request';

const emit = defineEmits(['success']);

const queryParams = ref<DictDataQuery>({
  dictType: '',
  pageNum: 1,
  pageSize: 10,
  dictName: '',
  dictLabel: ''
});

// 新增地图选择器相关数据
const mapSelectorVisible = ref(false);
const initialCoordinates = ref({
  lat: 30.5,
  lng: 121.9
});
const coordinates = computed({
  get: () => {
    if (form.value.shipEarlyWarning.lat && form.value.shipEarlyWarning.lon) {
      return `${form.value.shipEarlyWarning.lat},${form.value.shipEarlyWarning.lon}`;
    }
    return '';
  },
  set: (val) => {
    if (val) {
      const [lat, lon] = val.split(',');
      form.value.shipEarlyWarning.lat = lat;
      form.value.shipEarlyWarning.lon = lon;
    } else {
      form.value.shipEarlyWarning.lat = null;
      form.value.shipEarlyWarning.lon = null;
    }
  }
});

const formRef = ref();

const warnLevelList = ref([]);
const shipTypeList = ref([]);
const bridgeList = ref([]);

// 新增上传相关配置
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = ref(baseUrl + '/resource/oss/upload');
const headers = ref(globalHeaders());

function getWarnLevelList() {
  queryParams.value.dictType = 'warn_level';
  listData(queryParams.value).then((res) => {
    warnLevelList.value = res.rows.map((item) => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

function getShipTypeList() {
  queryParams.value.dictType = 'ship_type';
  listData(queryParams.value).then((res) => {
    shipTypeList.value = res.rows.map((item) => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  });
}

// 获取桥梁列表
function getBridgeData() {
  // 不传递分页参数，获取所有数据
  getBridgeList({ isWarn: 1 }).then((res) => {
    bridgeList.value = res.rows;
  });
}

// 打开地图选择器
const openMapSelector = () => {
  // 如果已有坐标，解析并设置为初始值
  if (form.value.shipEarlyWarning.lat && form.value.shipEarlyWarning.lon) {
    initialCoordinates.value = {
      lat: parseFloat(form.value.shipEarlyWarning.lat),
      lng: parseFloat(form.value.shipEarlyWarning.lon)
    };
  }
  mapSelectorVisible.value = true;
};

// 处理坐标确认
const handleCoordinateConfirm = (coords) => {
  form.value.shipEarlyWarning.lat = coords.lat.toString();
  form.value.shipEarlyWarning.lon = coords.lng.toString();
  // 同时更新主表中的经纬度
  form.value.earlyWarningInfo.lat = coords.lat.toString();
  form.value.earlyWarningInfo.lon = coords.lng.toString();
  mapSelectorVisible.value = false;
};

// 表单数据
const form = ref({
  // earlyWarningInfo 部分 - 主表
  earlyWarningInfo: {
    title: "船舶预警",
    warnLevel: null,
    navigationClosure: 0,
    status: 1,
    bridgeId: null,
    lon: null, // 经度添加到主表
    lat: null,  // 纬度添加到主表
    location: "", // 发生地点
    fileId: "", // 文件ID字段
  },
  // shipEarlyWarning 部分 - 副表
  shipEarlyWarning: {
    shipName: null,
    mmsi: null,
    shipType: null,
    eventContent: null,
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    lon: null,
    lat: null,
    place: "",
    type: 0
  }
});

// 在script部分更新校验规则
const rules = ref({
  'earlyWarningInfo.title': [{ required: true, message: '预警标题不能为空', trigger: 'blur' }],
  'earlyWarningInfo.warnLevel': [{ required: true, message: '请选择预警级别', trigger: 'blur' }],
  'shipEarlyWarning.shipName': [{ required: true, message: '请输入船名', trigger: 'blur' }],
  'shipEarlyWarning.mmsi': [{ required: true, message: '请输入MMSI编号', trigger: 'blur' }],
  'shipEarlyWarning.shipType': [{ required: true, message: '请选择船舶类型', trigger: 'blur' }],
  'shipEarlyWarning.eventContent': [{ required: true, message: '请输入事件内容', trigger: 'blur' }],
  'shipEarlyWarning.createTime': [{ required: true, message: '请选择发生时间', trigger: 'blur' }],
  'shipEarlyWarning.lon': [{ required: true, message: '请输入经度', trigger: 'blur' }],
  'shipEarlyWarning.lat': [{ required: true, message: '请输入纬度', trigger: 'blur' }],
  'earlyWarningInfo.location': [{ required: true, message: '请输入发生地点', trigger: 'blur' }],
  'earlyWarningInfo.navigationClosure': [{ required: true, message: '请选择禁航需求', trigger: 'blur' }],
  'earlyWarningInfo.status': [{ required: true, message: '请选择预警状态', trigger: 'blur' }]
});

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
};

// 更新提交表单函数，取消注释验证代码
const submitForm = async () => {
  if (!formRef.value) return;
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 确保主表包含经纬度信息
        if (form.value.shipEarlyWarning.lon && form.value.shipEarlyWarning.lat) {
          form.value.earlyWarningInfo.lon = form.value.shipEarlyWarning.lon;
          form.value.earlyWarningInfo.lat = form.value.shipEarlyWarning.lat;
        }
        
        // 如果place还有值，则同步到location
        if (form.value.shipEarlyWarning.place) {
          form.value.earlyWarningInfo.location = form.value.shipEarlyWarning.place;
        }
        
        // 执行表单提交
        await addShipWarning(form.value);
        ElMessage.success('预警信息添加成功');
        // 触发成功事件，通知父组件关闭对话框并刷新列表
        emit('success');
      } catch (error) {
        console.error('提交表单失败:', error);
        ElMessage.error('提交失败，请重试');
      }
    } else {
      ElMessage.warning('请填写完整的表单信息');
    }
  });
};

// 处理上传成功
const handleUploadSuccess = (response, uploadFile) => {
  if (response.code === 200) {
    const currentIds = form.value.earlyWarningInfo.fileId ? form.value.earlyWarningInfo.fileId.split(',') : [];
    currentIds.push(response.data.ossId);
    form.value.earlyWarningInfo.fileId = currentIds.join(',');
    ElMessage.success('上传成功');
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
};

// 处理上传错误
const handleUploadError = (error) => {
  ElMessage.error('上传文件失败');
};

// 处理移除图片
const handleUploadRemove = (uploadFile) => {
  // 从fileId中移除对应的ID
  if (form.value.earlyWarningInfo.fileId) {
    const currentIds = form.value.earlyWarningInfo.fileId.split(',');
    // 找到要删除的ID的索引
    const index = currentIds.findIndex(id => id === uploadFile.response?.data?.ossId);
    if (index > -1) {
      currentIds.splice(index, 1);
      form.value.earlyWarningInfo.fileId = currentIds.join(',');
    }
  }
};

// 上传前校验
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
};

// 在组件加载时获取所有需要的数据
onMounted(() => {
  getWarnLevelList();
  getShipTypeList();
  getBridgeData();
});
</script>

<style scoped lang="scss">
.card-wrapper {
  position: relative;
  height: 100%;
  background: var(--el-bg-color-page);
  border-radius: 8px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 8px;
    pointer-events: none;
  }
}

.form-card {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--el-bg-color-overlay);
  padding: 20px 30px;
  border-radius: 8px;

  :deep(.el-card__body) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    padding: 16px 20px;
  }

  .card-header {
    margin-bottom: 25px;

    .header-title {
      font-size: 20px;
      color: var(--el-text-color-primary);
      font-weight: 600;
    }

    .header-line {
      height: 3px;
      background: linear-gradient(90deg, #409eff 30%, transparent);
      margin-top: 12px;
    }
  }

  .form-container {
    flex: 1;
    min-height: 600px;
    padding: 0 15px;
    display: flex;
    flex-direction: column;

    .el-col {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .left-form {
      border-right: 1px dashed #e4e7ed;
      padding-right: 40px;
      height: 100%;
    }

    .right-section {
      padding-left: 40px;
      height: 100%;
      display: flex;
      flex-direction: column;

      .sub-title {
        font-size: 16px;
        margin-bottom: 25px;
        padding-left: 12px;
        border-left: 4px solid transparent;
      }
    }
  }

  .form-item {
    margin-bottom: 28px;

    :deep(.el-form-item__label) {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }

    .el-input,
    .el-select,
    .el-date-editor {
      :deep(.el-input__inner) {
        line-height: 1.5;
        background: var(--el-input-bg-color);
        border-color: var(--el-input-border-color);
        color: var(--el-text-color-primary);
      }
    }
  }

  .checkbox-group {
    width: 50%;
    display: flex;
    justify-content: space-between;
    gap: 20px;

    .el-checkbox {
      margin: 4px 0;
    }
  }

  .form-divider {
    margin: 28px 0;
    background-color: #e4e7ed;
  }

  .form-actions {
    margin-top: auto;
    padding-top: 20px;
    display: flex;
    gap: 20px;
    justify-content: flex-end;

    .el-button {
      padding: 12px 24px;
    }
  }
}

@media (max-width: 768px) {
  .form-card {
    height: auto;
    padding: 15px;

    .form-container {
      .left-form {
        border-right: none;
        padding-right: 0;
      }

      .right-section {
        padding-left: 0;
        margin-top: 20px;
      }
    }

    .form-actions {
      flex-direction: column;
      gap: 12px;
    }
  }
}

// 预警级别颜色标识
.option-level-1 {
  color: #f56c6c;
  font-weight: 500;
}

.option-level-2 {
  color: #e6a23c;
  font-weight: 500;
}

.option-level-3 {
  color: #409eff;
  font-weight: 500;
}

.option-level-4 {
  color: #67c23a;
  font-weight: 500;
}

// 新增的样式
.coordinate-input-group {
  display: flex;
  align-items: center;
  width: 100%;
  
  .el-input {
    cursor: pointer;
    
    :deep(.el-input__inner) {
      cursor: pointer;
    }
  }
  
  .coordinate-icon {
    color: #409EFF;
    cursor: pointer;
    font-size: 16px;
    
    &:hover {
      color: #66b1ff;
    }
  }
}

.map-selector-container {
  width: 100%;
  height: 500px;
}

// 新增上传组件样式
.upload-container {
  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 100px;
    height: 100px;
  }
}
</style>
