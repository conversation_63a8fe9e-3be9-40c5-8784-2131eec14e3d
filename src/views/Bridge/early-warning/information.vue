<template>
  <div class="classification-container">
    <!-- 标题区域 -->
    <div class="header-section">
      <h1 class="main-title">预警信息分类门户</h1>
      <div class="title-divider"></div>
    </div>

    <!-- 分类导航网格 - 使用更好的居中布局 -->
    <div class="grid-wrapper">
      <el-row :gutter="30" class="grid-container">
        <el-col v-for="(item, index) in categoryList.slice(0, 3)" :key="index" :span="8" class="grid-item">
          <template v-if="item.title !== '其他类型预警'">
            <router-link :to="item.path" class="category-card" @mouseenter.native="hoverIndex = index" @mouseleave.native="hoverIndex = -1">
              <div v-if="item.key && pendingCounts[item.key] > 0" class="pending-badge">
                {{ pendingCounts[item.key] }}
              </div>
              <div class="card-content">
                <div class="icon-container" :class="`bg-${item.key}`">
                  <el-icon :size="46" class="card-icon">
                    <component :is="item.icon" />
                  </el-icon>
                </div>
                <h3 class="category-title">{{ item.title }}</h3>
                <div class="card-status">
                  <span v-if="pendingCounts[item.key] > 0" class="status-text warning">{{ pendingCounts[item.key] }}个未处理</span>
                  <span v-else class="status-text normal">状态正常</span>
                </div>
              </div>
            </router-link>
          </template>
          <div v-else class="category-card disabled-card">
            <div class="card-content">
              <div class="icon-container disabled">
                <el-icon :size="46" class="card-icon disabled-icon">
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <h3 class="category-title disabled-title">{{ item.title }}</h3>
              <div class="card-status">
                <span class="status-text disabled">暂未开放</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="30" class="grid-container second-row">
        <el-col v-for="(item, index) in categoryList.slice(3)" :key="index + 3" :span="8" class="grid-item">
          <template v-if="item.title !== '其他类型预警'">
            <router-link :to="item.path" class="category-card" @mouseenter.native="hoverIndex = index + 3" @mouseleave.native="hoverIndex = -1">
              <div v-if="item.key && pendingCounts[item.key] > 0" class="pending-badge">
                {{ pendingCounts[item.key] }}
              </div>
              <div class="card-content">
                <div class="icon-container" :class="`bg-${item.key}`">
                  <el-icon :size="46" class="card-icon">
                    <component :is="item.icon" />
                  </el-icon>
                </div>
                <h3 class="category-title">{{ item.title }}</h3>
                <div class="card-status">
                  <span v-if="pendingCounts[item.key] > 0" class="status-text warning">{{ pendingCounts[item.key] }}个未处理</span>
                  <span v-else class="status-text normal">状态正常</span>
                </div>
              </div>
            </router-link>
          </template>
          <div v-else class="category-card disabled-card">
            <div class="card-content">
              <div class="icon-container disabled">
                <el-icon :size="46" class="card-icon disabled-icon">
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <h3 class="category-title disabled-title">{{ item.title }}</h3>
              <div class="card-status">
                <span class="status-text disabled">暂未开放</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Cloudy, Promotion, Picture, Monitor, WindPower, Warning, Ship,Location,Van } from '@element-plus/icons-vue';

// 导入所有预警类型的API
import { getTrafficAccidentWarningList } from '@/api/bridge/traffic/traffic';
import { getEnvironmentalPollutionWarningList } from '@/api/bridge/traffic/environmental';
import { getGeologyWarningList } from '@/api/bridge/traffic/geology';
import { getPublicHealthWarningList } from '@/api/bridge/traffic/publicHealth';
import { getMeteorologicalWarningList } from '@/api/bridge/traffic/weather';
import { getShipWarningList } from '@/api/bridge/traffic/ship';
import { getCheckpointWarningList } from '@/api/bridge/traffic/vehicle';

const hoverIndex = ref(-1);

// 添加未处理数量统计对象
const pendingCounts = ref({
  traffic: 0,
  environmental: 0,
  geology: 0,
  health: 0,
  weather: 0,
  ship: 0,
  vehicle: 0
});

// 更新分类列表，添加key属性关联到未处理计数
const categoryList = [
  {
    title: '交通事故预警',
    icon: Warning,
    path: '/early-warning/group/accident-effct',
    key: 'traffic'
  },
  {
    title: '环境污染预警',
    icon: WindPower,
    path: '/early-warning/group/environmental',
    key: 'environmental'
  },
  {
    title: '地质灾害预警',
    icon: Location,
    path: '/early-warning/group/geology',
    key: 'geology'
  },
  {
    title: '公共卫生灾害预警',
    icon: Promotion,
    path: '/early-warning/group/health',
    key: 'health'
  },
  {
    title: '气象灾害预警',
    icon: Cloudy,
    path: '/early-warning/group/weather',
    key: 'weather'
  },
  {
    title: '船舶预警',
    icon: Ship,
    path: '/early-warning/group/ship',
    key: 'ship'
  },
  {
    title: '车辆卡口预警',
    icon: Van,
    path: '/early-warning/group/vehicle-checkpoint-warning',
    key: 'vehicle'
  }
  // {
  //   title: '其他类型预警',
  //   icon: Promotion,
  //   path: '/early-warning/group/otners'
  // }
];

// 获取交通事故预警未处理数量
const fetchTrafficPendingCount = async () => {
  try {
    const params = {
      status: 1 // 未处理状态
    };
    const res = await getTrafficAccidentWarningList(params);
    pendingCounts.value.traffic = res.total || 0;
  } catch (error) {
    console.error('获取交通事故预警未处理数量失败:', error);
  }
};

// 获取环境污染预警未处理数量
const fetchEnvironmentalPendingCount = async () => {
  try {
    const params = {
      status: 1 // 未处理状态
    };
    const res = await getEnvironmentalPollutionWarningList(params);
    pendingCounts.value.environmental = res.total || 0;
  } catch (error) {
    console.error('获取环境污染预警未处理数量失败:', error);
  }
};

// 获取地质灾害预警未处理数量
const fetchGeologyPendingCount = async () => {
  try {
    const params = {
      status: 1 // 未处理状态
    };
    const res = await getGeologyWarningList(params);
    pendingCounts.value.geology = res.total || 0;
  } catch (error) {
    console.error('获取地质灾害预警未处理数量失败:', error);
  }
};

// 获取公共卫生预警未处理数量
const fetchHealthPendingCount = async () => {
  try {
    const params = {
      status: 1 // 未处理状态
    };
    const res = await getPublicHealthWarningList(params);
    pendingCounts.value.health = res.total || 0;
  } catch (error) {
    console.error('获取公共卫生预警未处理数量失败:', error);
  }
};

// 获取气象灾害预警未处理数量
const fetchWeatherPendingCount = async () => {
  try {
    const params = {
      status: 1 // 未处理状态
    };
    const res = await getMeteorologicalWarningList(params);
    pendingCounts.value.weather = res.total || 0;
  } catch (error) {
    console.error('获取气象灾害预警未处理数量失败:', error);
  }
};

// 获取船舶预警未处理数量
const fetchShipPendingCount = async () => {
  try {
    const params = {
      status: 1 // 未处理状态
    };
    const res = await getShipWarningList(params);
    pendingCounts.value.ship = res.total || 0;
  } catch (error) {
    console.error('获取船舶预警未处理数量失败:', error);
  }
};

// 获取车辆卡口预警未处理数量
const fetchVehiclePendingCount = async () => {
  try {
    const params = { status: 1 };
    const res = await getCheckpointWarningList(params);
    pendingCounts.value.vehicle = res.total || 0;
  } catch (error) {
    console.error('获取车辆卡口预警未处理数量失败:', error);
  }
};

// 组件挂载时获取所有未处理预警数量
onMounted(async () => {
  // 并行请求所有未处理预警数量
  await Promise.all([
    fetchTrafficPendingCount(),
    fetchEnvironmentalPendingCount(),
    fetchGeologyPendingCount(),
    fetchHealthPendingCount(),
    fetchWeatherPendingCount(),
    fetchShipPendingCount(),
    fetchVehiclePendingCount()
  ]);
});
</script>

<style scoped lang="scss">
.classification-container {
  padding: 40px 20px 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-section {
  text-align: center;
  margin-bottom: 3rem;

  .main-title {
    font-size: 32px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 12px 0;
  }

  .title-divider {
    width: 80px;
    height: 4px;
    background: var(--el-color-primary);
    margin: 0 auto;
    border-radius: 2px;
  }
}

.grid-wrapper {
  width: 100%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.grid-container {
  width: 100%;
  margin: 0 auto;
  padding: 0;
  display: flex;
  justify-content: center;
}

.second-row {
  margin-top: 10px;
}

.grid-item {
  padding: 15px;
  margin-bottom: 30px;
}

.category-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 260px;
  border-radius: 10px;
  background: var(--el-bg-color);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  text-decoration: none;
  border: 1px solid var(--el-border-color-light);
  padding: 24px;
  overflow: hidden;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--el-color-primary-light-5);
  }
}

.pending-badge {
  position: absolute;
  top: 14px;
  right: 14px;
  min-width: 26px;
  height: 26px;
  border-radius: 13px;
  background-color: var(--el-color-danger);
  color: white;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 86px;
  height: 86px;
  border-radius: 50%;
  margin-bottom: 20px;

  .card-icon {
    color: white;
    font-size: 50px;
  }

  &.bg-traffic {
    background-color: #f56c6c;
    background-image: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%);
  }

  &.bg-environmental {
    background-color: #67c23a;
    background-image: linear-gradient(135deg, #67c23a 0%, #95d475 100%);
  }

  &.bg-geology {
    background-color: #e6a23c;
    background-image: linear-gradient(135deg, #e6a23c 0%, #f56c6c 100%);
  }

  &.bg-health {
    background-color: #909399;
    background-image: linear-gradient(135deg, #909399 0%, #c0c4cc 100%);
  }

  &.bg-weather {
    background-color: #409eff;
    background-image: linear-gradient(135deg, #409eff 0%, #53a8ff 100%);
  }

  &.bg-ship {
    background-color: #9254de;
    background-image: linear-gradient(135deg, #9254de 0%, #b37feb 100%);
  }

  &.bg-vehicle {
    background-color: #722ed1;
    background-image: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  }

  &.disabled {
    background-color: var(--el-disabled-bg-color);
    background-image: none;
  }
}

.category-title {
  font-size: 19px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin: 16px 0;
  text-align: center;
}

.card-status {
  margin-top: 14px;
  font-size: 15px;

  .status-text {
    padding: 6px 12px;
    border-radius: 14px;

    &.warning {
      color: var(--el-color-danger);
      background-color: var(--el-color-danger-light-9);
    }

    &.normal {
      color: var(--el-color-success);
      background-color: var(--el-color-success-light-9);
    }

    &.disabled {
      color: var(--el-text-color-disabled);
      background-color: var(--el-disabled-bg-color);
    }
  }
}

.disabled-card {
  cursor: not-allowed !important;
  opacity: 0.7;
  background: var(--el-disabled-bg-color) !important;

  &:hover {
    transform: none !important;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08) !important;
  }
}

.disabled-icon {
  color: var(--el-text-color-disabled) !important;
}

.disabled-title {
  color: var(--el-text-color-disabled) !important;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .grid-container {
    flex-wrap: wrap;
  }

  .grid-item {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  .classification-container {
    padding: 30px 10px 15px;
  }

  .header-section {
    margin-bottom: 2rem;

    .main-title {
      font-size: 28px;
    }
  }

  .grid-item {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 10px;
  }

  .category-card {
    height: 240px;
    padding: 16px;
  }

  .icon-container {
    width: 76px;
    height: 76px;

    .card-icon {
      font-size: 40px;
    }
  }

  .category-title {
    font-size: 16px;
  }

  .card-status {
    font-size: 14px;

    .status-text {
      padding: 4px 8px;
    }
  }
}
</style>
