<template>
  <div class="list-page">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" style="display: flex;justify-content: space-between;width: 100%;">
            <el-form-item label="发生地点" prop="location">
              <el-input v-model="queryParams.location" placeholder="请输入发生地点" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="处理状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="处理状态" clearable>
                <el-option :value="1" label="未处理" />
                <el-option :value="2" label="处理中" />
                <el-option :value="3" label="已处理" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-form-item>
            <div style="display: flex;justify-content: space-between;width: 100%;">
              <br />
              <div>
                <el-button icon="RefreshRight" @click="resetQuery">刷新</el-button>
                <el-button type="primary" icon="Plus" class="add-button" @click="handleAdd">新增</el-button>
                <el-button type="danger" icon="Delete" :disabled="multipleSelection.length === 0" @click="batchDelete">删除</el-button>
              </div>
            </div>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover" class="w-full">
      <el-table :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="序号" prop="index" width="80" align="center">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="灾害类型" prop="geologicalWarning.disasterType" min-width="180">
          <template #default="scope">
            {{ getDictLabel('disaster_type', scope.row.geologicalWarning?.disasterType) }}
          </template>
        </el-table-column>
        <el-table-column label="强度等级" prop="geologicalWarning.intensity" width="180" align="center">
          <template #default="scope">
            {{ getDictLabel('disaster_intensity', scope.row.geologicalWarning?.intensity) }}
          </template>
        </el-table-column>
        <el-table-column label="发生地点" prop="earlyWarningInfo.location" width="250"></el-table-column>
        <el-table-column label="地理坐标" prop="coordinates" width="180" align="center">
          <template #default="scope">
            {{ scope.row.earlyWarningInfo?.lat && scope.row.earlyWarningInfo?.lon ? 
               `${scope.row.earlyWarningInfo.lat},${scope.row.earlyWarningInfo.lon}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="影响范围" prop="geologicalWarning.affectedArea" width="250"></el-table-column>
        <el-table-column label="预计持续时间" prop="geologicalWarning.expectedDuration" width="180" align="center">
          <template #default="scope">
            {{ scope.row.geologicalWarning?.expectedDuration + '小时' }}
          </template>
        </el-table-column>
        <el-table-column label="附件" prop="attachmentPath" width="100" align="center">
          <template #default="{ row }">
            <el-link v-if="row.earlyWarningInfo?.fileId" type="primary" :underline="false" @click.prevent="handleViewAttachment(row)">
              <el-icon>
                <Document />
              </el-icon>
              查看附件
            </el-link>
            <el-button v-else link type="primary" @click="handleUploadAttachment(row)">
              <el-icon>
                <Upload />
              </el-icon>
              上传
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="预警级别" prop="earlyWarningInfo.warnLevel" width="200" align="center">
          <template #default="scope">
            <el-tooltip :content="`${scope.row.earlyWarningInfo?.warnLevel}级预警`" placement="top">
              <img :src="getAlertImage(scope.row.earlyWarningInfo?.warnLevel)" style="width: 32px; height: 32px" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="处理状态" prop="earlyWarningInfo.status" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.earlyWarningInfo?.status)">
              {{ getStatusLabel(scope.row.earlyWarningInfo?.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="viewDetails(scope.row)">详情</el-button>
            <el-button v-if="scope.row.geologicalWarning?.status" size="small" type="info" link @click="viewDetails(scope.row)"> 结果 </el-button>
            <el-button size="small" type="success" link @click="handlePushNotice(scope.row)"> 同步</el-button>
            <el-button size="small" type="warning" link @click="handleJudgment(scope.row)"> 研判</el-button>
            <el-button size="small" type="danger" link @click="handleDelete(scope.row)" v-perms="['bridge:traffic:remove']"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-if="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

      <!-- 地图选择器弹窗 -->
      <el-dialog
        v-model="mapSelectorVisible"
        title="选择地理坐标"
        width="65%"
        :destroy-on-close="true"
        :lock-scroll="false"
      >
        <div class="map-selector-container">
          <map-coordinate-selector
            :initial-lat="initialCoordinates.lat"
            :initial-lng="initialCoordinates.lng"
            @confirm="handleCoordinateConfirm"
            @cancel="mapSelectorVisible = false"
          />
        </div>
      </el-dialog>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailVisible" width="1400px" center destroy-on-close class="responsive-dialog">
      <div class="card-header">
        <span class="header-title">地质灾害预警详情</span>
        <div class="header-line"></div>
      </div>
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="120px" class="form-container">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="灾害类型：" prop="geologicalWarning.disasterType">
              <el-select v-model="editForm.geologicalWarning.disasterType" placeholder="请选择灾害类型" style="width: 100%" :disabled="!isEditing">
                <el-option v-for="item in disasterTypeDict" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
              </el-select>
            </el-form-item>

            <el-form-item label="发生地点：" prop="earlyWarningInfo.location">
              <el-input v-model="editForm.earlyWarningInfo.location" placeholder="请输入发生地点" :disabled="!isEditing" />
            </el-form-item>

            <el-form-item label="地理坐标：" prop="earlyWarningInfo.lat" class="form-item">
              <div class="coordinate-input-group">
                <el-input 
                  v-model="formattedCoordinates" 
                  placeholder="点击选择地理坐标"
                  readonly
                  :disabled="!isEditing"
                  @click="isEditing && openMapSelector()" 
                >
                  <template #suffix>
                    <el-icon class="coordinate-icon" @click.stop="isEditing && openMapSelector()">
                      <Location />
                    </el-icon>
                  </template>
                </el-input>
              </div>
            </el-form-item>

            <el-form-item label="所属桥梁：" prop="earlyWarningInfo.bridgeId">
              <el-select v-model="editForm.earlyWarningInfo.bridgeId" placeholder="请选择桥梁" filterable style="width: 100%" :disabled="!isEditing">
                <el-option
                  v-for="item in bridgeList"
                  :key="item.id"
                  :label="item.bridgeName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="强度等级：" prop="geologicalWarning.intensity">
              <el-select v-model="editForm.geologicalWarning.intensity" style="width: 100%" :disabled="!isEditing">
                <el-option v-for="item in intensityDict" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
              </el-select>
            </el-form-item>

            <el-form-item label="影响范围：" prop="geologicalWarning.affectedArea">
              <el-input v-model="editForm.geologicalWarning.affectedArea" :disabled="!isEditing" />
            </el-form-item>

            <el-form-item label="预警级别：" prop="earlyWarningInfo.warnLevel">
              <el-select v-model="editForm.earlyWarningInfo.warnLevel" style="width: 100%" :disabled="!isEditing">
                <el-option v-for="item in warnLevelList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
              </el-select>
            </el-form-item>



            <el-form-item label="附件：" class="form-item">
              <div class="upload-container">

                <!-- 编辑状态下的上传组件 -->
                <div class="edit-upload-container">
                  <!-- 显示已上传的图片 -->
                  <div v-if="attachmentList.length > 0" class="existing-images">
                  <el-scrollbar style="width: 600px;height: 100%;">
                    <div class="scrollbar-wrapper">
                      <div class="image-list">
                        <div v-for="item in attachmentList" :key="item.ossId" class="image-item">
                          <ImagePreview
                            :width="148"
                            :height="148"
                            :src="item.url"
                            :preview-src-list="attachmentList.map(img => img.url)"
                            :initial-index="attachmentList.findIndex(img => img.ossId === item.ossId)"
                          />
                          <div class="image-actions">
                            <el-button :disabled="!isEditing" type="danger" size="small" @click="handleRemoveExistingImage(item)">
                              <el-icon><Delete /></el-icon>
                            </el-button>
                          </div>
                        </div>
                        <!-- 上传按钮 -->
                        <el-upload
                          :disabled="!isEditing"
                          class="upload-button"
                          :action="uploadFileUrl"
                          :headers="headers"
                          list-type="picture-card"
                          :on-success="handleUploadSuccess"
                          :on-remove="handleUploadRemove"
                          :before-upload="beforeUpload"
                          :on-error="handleUploadError"
                          multiple
                          name="file"
                          :show-file-list="false"
                        >
                          <el-icon><Plus /></el-icon>
                        </el-upload>
                      </div>
                   
                    </div>
                  </el-scrollbar>
                  </div>
                  <!-- 没有图片时显示上传组件 -->
                  <el-upload
                    :disabled="!isEditing"
                    v-else
                    :action="uploadFileUrl"
                    :headers="headers"
                    list-type="picture-card"
                    :on-success="handleUploadSuccess"
                    :on-remove="handleUploadRemove"
                    :before-upload="beforeUpload"
                    :on-error="handleUploadError"
                    multiple
                    name="file"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="预计持续时间：" prop="geologicalWarning.expectedDuration">
              <el-input
                v-model="editForm.geologicalWarning.expectedDuration"
                :min="1"
                controls-position="right"
                style="width: 100%"
                :disabled="!isEditing"
              />
              <span class="unit">小时</span>
            </el-form-item>

            <el-form-item label="预警状态：" prop="earlyWarningInfo.status">
              <el-select v-model="editForm.earlyWarningInfo.status" style="width: 100%" :disabled="!isEditing">
                <el-option :value="1" label="未处理" />
                <el-option :value="2" label="处理中" />
                <el-option :value="3" label="已处理" />
              </el-select>
            </el-form-item>

            <el-form-item label="禁航需求：">
              <el-radio-group v-model="editForm.earlyWarningInfo.navigationClosure" :disabled="!isEditing">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="记录：" prop="earlyWarningInfo.result">
              <el-input
                v-model="editForm.earlyWarningInfo.result"
                type="textarea"
                :rows="3"
                placeholder="请输入记录信息"
                :disabled="!isEditing"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="form-actions">
          <el-button v-if="!isEditing" type="primary" @click="enableEditing" class="edit-btn"><i class="el-icon-edit" /> 编辑 </el-button>
          <el-button v-if="isEditing" type="primary" @click="handleSubmit" class="save-btn"><i class="el-icon-check" /> 保存</el-button>
          <el-button @click="detailVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增弹窗 -->
    <el-dialog v-model="addDialogVisible" width="85%" top="5vh" destroy-on-close class="add-dialog responsive-dialog" :show-header="false">
      <AddComponent @success="handleAddSuccess" />
    </el-dialog>

    <!-- 推送通知弹窗 -->
    <el-dialog v-model="noticeDialog.visible" title="预警信息同步" width="1400px" destroy-on-close class="responsive-dialog">
      <el-form ref="noticeFormRef" :model="noticeForm" :rules="noticeRules" label-width="100px">
        <el-form-item label="通知标题" prop="title">
          <el-input v-model="noticeForm.title" placeholder="请输入通知标题" />
        </el-form-item>
        <el-form-item label="通知内容" prop="content">
          <el-input v-model="noticeForm.content" type="textarea" :rows="8" placeholder="请输入通知内容" />
        </el-form-item>
        <el-form-item label="通知方式" prop="notifyMethod">
          <el-select v-model="noticeForm.notifyMethod" placeholder="请选择通知方式" style="width: 100%" multiple>
            <el-option v-for="dict in notifyMethodOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <!-- 通知附加内容 -->
        <el-form-item label="通知附加内容">
          <el-input
            v-model="noticeForm.postContent"
            type="textarea"
            :rows="3"
            placeholder="请输入通知附加内容"
            maxlength="200"
            show-word-limit
          />
          <el-text size="small" type="info" style="margin-top: 5px;">
            {{ smsPreviewText }}
          </el-text>
        </el-form-item>
        <el-form-item label="推送对象" prop="notifyTargets">
          <DeptTreeSelector
            ref="deptTreeSelectorRef"
            :dept-data="deptData"
            :user-data="userData"
            :selected-user-ids="selectedUserIds"
            :height="500"
            @selection-change="handleDeptTreeSelectionChange"
            @user-select="handleDeptTreeUserSelect"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitNoticeForm">确 定</el-button>
          <el-button @click="noticeDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加研判弹窗 -->
    <el-dialog v-model="judgmentDialog.visible" title="预警研判" width="1400px" destroy-on-close class="judgment-dialog responsive-dialog">
      <el-form ref="judgmentFormRef" :model="judgmentForm" :rules="judgmentRules" label-width="120px">
        <!-- 第一个板块：预警信息 -->
        <div class="judgment-section info-section">
          <h3 class="section-title">
            <el-icon>
              <InfoFilled />
            </el-icon>
            预警信息
          </h3>
          <el-divider></el-divider>
          <div class="info-display">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-row" v-if="currentWarning.geologicalWarning">
                  <span class="info-label">灾害类型：</span>
                  <span class="info-value">{{ getDictLabel('disaster_type', currentWarning.geologicalWarning.disasterType) }}</span>
                </div>
                <div class="info-row" v-if="currentWarning.earlyWarningInfo">
                  <span class="info-label">发生地点：</span>
                  <span class="info-value">{{ currentWarning.earlyWarningInfo.location }}</span>
                </div>
                <div class="info-row" v-if="currentWarning.geologicalWarning">
                  <span class="info-label">发生时间：</span>
                  <span class="info-value">{{ dayjs(currentWarning.geologicalWarning.occurrenceTime).format('YYYY-MM-DD HH:mm') }}</span>
                </div>
                <div class="info-row" v-if="currentWarning.earlyWarningInfo">
                  <span class="info-label">处理状态：</span>
                  <span class="info-value">
                    <el-tag :type="getStatusType(currentWarning.earlyWarningInfo.status)" size="small">
                      {{ getStatusLabel(currentWarning.earlyWarningInfo.status) }}
                    </el-tag>
                  </span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-row" v-if="currentWarning.geologicalWarning">
                  <span class="info-label">灾害原因：</span>
                  <span class="info-value">{{ currentWarning.geologicalWarning.disasterCause }}</span>
                </div>
                <div class="info-row" v-if="currentWarning.geologicalWarning">
                  <span class="info-label">影响范围：</span>
                  <span class="info-value">{{ currentWarning.geologicalWarning.affectedArea }}平方米</span>
                </div>
                <div class="info-row" v-if="currentWarning.earlyWarningInfo">
                  <span class="info-label">预警级别：</span>
                  <span class="info-value">
                    <el-tag :type="getWarnLevelType(currentWarning.earlyWarningInfo.warnLevel)" size="small">
                      {{ currentWarning.earlyWarningInfo.warnLevel }}级预警
                    </el-tag>
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 第二个板块：预警研判 -->
        <div class="judgment-section">
          <h3 class="section-title">
            <el-icon>
              <Warning />
            </el-icon>
            预警研判
          </h3>
          <el-divider></el-divider>

          <el-form-item label="预警级别" prop="eventLevel">
            <el-select v-model="judgmentForm.eventLevel" placeholder="请选择预警级别" style="width: 100%">
              <el-option v-for="item in warnLevelList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
            </el-select>
          </el-form-item>

          <el-form-item label="研判结果" prop="judgmentResult">
            <el-radio-group v-model="judgmentForm.judgmentResult">
              <el-radio :label="1">转为应急事件</el-radio>
              <el-radio :label="0">无需转为应急事件</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- 第三个板块：预警转事件（仅当选择转为应急事件时显示） -->
        <div class="judgment-section" v-if="judgmentForm.judgmentResult === 1">
          <h3 class="section-title">
            <el-icon>
              <Bell />
            </el-icon>
            预警转事件
          </h3>
          <el-divider></el-divider>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="事件标题" prop="eventName">
                <el-input v-model="judgmentForm.eventName" placeholder="请输入事件标题" />
              </el-form-item>

              <el-form-item label="发生时间" prop="happenTime">
                <el-date-picker
                  v-model="judgmentForm.happenTime"
                  type="datetime"
                  placeholder="选择发生时间"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>

              
              <el-form-item label="桥梁" prop="bridgeId">
                <el-select v-model="judgmentForm.bridgeId" placeholder="请选择桥梁" filterable style="width: 100%">
                  <el-option
                    v-for="item in bridgeList"
                    :key="item.id"
                    :label="item.bridgeName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发生地点" prop="location">
                <el-input v-model="judgmentForm.location" placeholder="请输入发生地点" />
              </el-form-item>

              <el-form-item label="首次报告时间" prop="firstReportTime">
                <el-date-picker
                  v-model="judgmentForm.firstReportTime"
                  type="datetime"
                  placeholder="选择首次报告时间"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="处置状态" prop="status">
                <el-select v-model="judgmentForm.status" placeholder="请选择处置状态" style="width: 100%">
                  <el-option :value="1" label="未启动" />
                  <el-option :value="2" label="进行中" />
                  <el-option :value="3" label="已控制" />
                  <el-option :value="4" label="已关闭" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="事件描述" prop="description">
            <el-input type="textarea" v-model="judgmentForm.description" :rows="3" placeholder="请输入事件描述" />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="judgmentDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="submitJudgmentForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件上传对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传附件" width="600px" center destroy-on-close class="custom-dialog responsive-dialog">
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">上传附件</span>
        </div>
      </template>
      <div class="upload-container">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="10"
          :on-exceed="handleExceed"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          multiple
          action="#"
          :file-list="uploadFiles"
        >
          <template #trigger>
            <el-button type="primary">
              <el-icon>
                <Plus />
              </el-icon>
              选择文件
            </el-button>
          </template>
          <template #tip>
            <div class="el-upload__tip">支持任意格式文件，单个文件不超过5MB</div>
          </template>
        </el-upload>

        <div v-if="uploadProgress.total > 0" class="upload-progress">
          <div class="progress-text">正在上传: {{ uploadProgress.current }}/{{ uploadProgress.total }}</div>
          <el-progress
            :percentage="(uploadProgress.current / uploadProgress.total) * 100"
            :format="progressFormat"
            :status="uploadProgress.status"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploading" :disabled="!uploadFiles.length"> 开始上传 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 附件查看对话框 -->
    <el-dialog v-model="attachmentDialogVisible" title="附件列表" width="1000px" center destroy-on-close class="custom-dialog responsive-dialog">
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">附件列表</span>
        </div>
      </template>
      <div class="attachment-container">
        <el-table :data="attachmentList" style="width: 100%">
          <el-table-column prop="originalName" label="文件名称" />
          <el-table-column label="文件预览" width="120" align="center">
            <template #default="{ row }">
              <ImagePreview
                v-if="checkFileSuffix(row.fileSuffix)"
                :width="100"
                :height="100"
                :src="row.url"
                :preview-src-list="[row.url]"
              />
              <span v-else>不支持预览</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="handleDownload(row)">
                <el-icon>
                  <Download />
                </el-icon>
                下载
              </el-button>
              <el-button size="small" type="danger" link @click="handleDeleteAttachment(row)">
                <el-icon>
                  <Delete />
                </el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <el-button @click="attachmentDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineAsyncComponent, reactive, onMounted, getCurrentInstance, type ComponentInternalInstance, toRefs, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listData } from '@/api/system/dict/data';
import { DictDataQuery } from '@/api/system/dict/data/types';
import { getGeologyWarningList as getListAPI, getGeologyWarningById, updateGeologyWarning, deleteGeologyWarning, deleteGeologyWarningBatch } from '@/api/bridge/traffic/geology';
import dayjs from 'dayjs';
import { useRouter, useRoute } from 'vue-router';
import { addNotice, publishNotice } from '@/api/bridge/command/notice';
import { addEmergencyEvent } from '@/api/bridge/command/event';
import useUserStore from '@/store/modules/user';
import { InfoFilled, Warning, Bell, RefreshRight, Document, Upload, Download, Delete, Plus, Location } from '@element-plus/icons-vue';
import { listByIds, delOss } from '@/api/system/oss';
import ImagePreview from '@/components/ImagePreview/index.vue';
import { uplodadWarningFile } from '@/api/bridge/traffic/traffic';
import { globalHeaders } from '@/utils/request';
import { listDept } from '@/api/system/dept';
import { getUserInfoList } from '@/api/bridge/user/user';
import { getBridgeList } from '@/api/bridge/bisc/bridge'; // 添加桥梁列表API导入
import MapCoordinateSelector from '@/components/MapCoordinateSelector/index.vue';
import DeptTreeSelector from '@/components/DeptTreeSelector/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { yjcl } = toRefs<any>(proxy?.useDict('yjcl'));

interface JudgmentFormType {
  judgmentResult: number;
  eventLevel: string;
  eventName: string;
  happenTime: string;
  location: string;
  firstReportTime: string;
  status: string | number;
  description: string;
  warningId: string;
  eventType: string;
  bridgeId?: string | number;
  lat?: string; // 添加纬度字段
  lon?: string; // 添加经度字段
}

const DictQueryParams = ref<DictDataQuery>({
  dictType: '',
  pageNum: 1,
  pageSize: 10,
  dictName: '',
  dictLabel: ''
});

const isEditing = ref(false);
const originalData = ref({});
const showSearch = ref(true);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  type: null,
  location: null,
  status: null,
  title: null
});

const tableData = ref([]);
const getList = async () => {
  try {
    const res = await getListAPI(queryParams);
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// 分类选择
const filterType = ref('全部');
// 搜索值
const searchValue = ref('');
// 当前页码
const currentPage = ref(1);
// 每页数量
const pageSize = ref(10);
// 总数量
const total = ref(0);

const detailVisible = ref(false);
const currentDetail = ref<any>({});

// 在script部分新增表单数据和校验规则
const editForm = ref({
  earlyWarningInfo: {
    title: currentDetail.value.earlyWarningInfo?.title,
    warnLevel: currentDetail.value.earlyWarningInfo?.warnLevel,
    navigationClosure: currentDetail.value.earlyWarningInfo?.navigationClosure,
    status: currentDetail.value.earlyWarningInfo?.status,
    result: currentDetail.value.earlyWarningInfo?.result || '',
    bridgeId: currentDetail.value.earlyWarningInfo?.bridgeId || '', // 添加桥梁ID字段
    location: currentDetail.value.earlyWarningInfo?.location || '' // 添加发生地点字段
  },
  geologicalWarning: {
    disasterType: '',
    location: '',
    intensity: '',
    expectedDuration: null,
    affectedArea: ''
  }
});

const editRules = ref<any>({
  'geologicalWarning.disasterType': [{ required: true, message: '请选择灾害类型', trigger: 'change' }],
  'earlyWarningInfo.location': [{ required: true, message: '请输入发生地点', trigger: 'blur' }],
  'geologicalWarning.intensity': [{ required: true, message: '请选择强度等级', trigger: 'change' }],
  'geologicalWarning.affectedArea': [{ required: true, message: '请输入影响范围', trigger: 'blur' }],
  'geologicalWarning.expectedDuration': [
    {
      required: true,
      type: 'number',
      message: '请输入预计持续时间',
      trigger: 'blur'
    }
  ],
  'earlyWarningInfo.result': [{ required: true, message: '请输入记录信息', trigger: 'blur' }],
  'earlyWarningInfo.warnLevel': [{ required: true, message: '请选择预警级别', trigger: 'change' }],
  'earlyWarningInfo.navigationClosure': [{ required: true, message: '请选择禁航需求', trigger: 'change' }],
  'earlyWarningInfo.status': [{ required: true, message: '请选择预警状态', trigger: 'change' }]
});

const queryFormRef = ref<any>(null);

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

const resetQuery = () => {
  // 在这里添加重置逻辑
  queryParams.location = null;
  queryFormRef.value?.resetFields();
  // 调用查询接口
  getList();
};

// 查看详情方法
const viewDetails = async (row: any) => {
  isEditing.value = false;
  try {
    const res = await getGeologyWarningById(row.geologicalWarning.id);
    currentDetail.value = res.data;
    if (res.data.earlyWarningInfo?.fileId) {
      const attachmentRes = await listByIds(res.data.earlyWarningInfo.fileId);
      attachmentList.value = (attachmentRes.data || []).map(item => ({
        ...item,
        url: item.url || item.fileUrl,
        originalName: item.originalName || item.fileName,
        fileSuffix: item.fileSuffix || item.fileName?.substring(item.fileName.lastIndexOf('.'))
      }));
    } else {
      attachmentList.value = [];
    }
    editForm.value = {
      earlyWarningInfo: res.data.earlyWarningInfo,
      geologicalWarning: res.data.geologicalWarning
    };
    detailVisible.value = true;
  } catch (error) {
    ElMessage.error('获取详情失败');
  }
};

// 处理事件方法
const handleEvent = (row: any) => {
  console.log('处理事件', row);
};

// 查看结果方法
const viewResult = (row: any) => {
  console.log('查看结果', row);
};

// 修改分页处理方法
const handlePageChange = (val: number) => {
  currentPage.value = val;
  DictQueryParams.value.pageNum = val; // 同步到查询参数
  getList();
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  DictQueryParams.value.pageSize = val; // 同步到查询参数
  getList();
};

// 添加搜索和筛选的监听
watch([filterType, searchValue], () => {
  currentPage.value = 1; // 重置为第一页
  DictQueryParams.value.pageNum = 1;
  getList();
});

// 删除处理
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除该条预警吗？', '警告', {
    type: 'warning'
  })
    .then(async () => {
      await deleteGeologyWarning(row.geologicalWarning.id);
      ElMessage.success('删除成功');
      getList();
    })
    .catch(() => {});
};

// 添加新增处理方法
const router = useRouter();

const AddComponent = defineAsyncComponent(() => import('@/views/Bridge/traffic-accident/geology.vue'));
const addDialogVisible = ref(false);

const handleAdd = () => {
  addDialogVisible.value = true;
};

const handleAddSuccess = () => {
  addDialogVisible.value = false;
  getList();
  ElMessage.success('新增成功');
};

const disasterTypeDict = ref<any[]>([]); // 灾害类型字典
const intensityDict = ref<any[]>([]); // 强度等级字典
const warnLevelList = ref<any[]>([]);

// 获取灾害类型字典
const getDisasterTypeDict = async () => {
  DictQueryParams.value.dictType = 'disaster_type';
  const res = await listData(DictQueryParams.value);
  disasterTypeDict.value = res.rows;
};

// 获取强度等级字典
const getIntensityDict = async () => {
  DictQueryParams.value.dictType = 'disaster_intensity';
  const res = await listData(DictQueryParams.value);
  intensityDict.value = res.rows;
};

// 获取预警级别字典
const getWarnLevelDict = async () => {
  DictQueryParams.value.dictType = 'warn_level';
  const res = await listData(DictQueryParams.value);
  warnLevelList.value = res.rows;
};

// 添加字典转换方法
const getDictLabel = (dictType: string, value: string) => {
  const dict = dictType === 'disaster_type' ? disasterTypeDict.value : intensityDict.value;
  const item = dict.find((d: any) => d.dictValue === value);
  return item?.dictLabel || 'N/A';
};

// 添加预警图片获取方法
const getAlertImage = (level: string) => {
  return new URL(`/src/assets/svg/alert${level}.svg`, import.meta.url).href;
};

// 新增提交方法
const handleSubmit = async () => {
  try {
    const submitData = {
      earlyWarningInfo: editForm.value.earlyWarningInfo,
      geologicalWarning: editForm.value.geologicalWarning
    };

    const originalStatus = originalData.value.earlyWarningInfo?.status;
    const newStatus = editForm.value.earlyWarningInfo.status;

    await updateGeologyWarning(submitData);

    // 如果状态发生改变，询问是否发送通知
    if (originalStatus !== newStatus) {
      ElMessageBox.confirm('您修改了处理状态，是否发送通知？', '提示', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      })
        .then(() => {
          handlePushNotice(currentDetail.value);
        })
        .catch(() => {});
    }

    ElMessage.success('更新成功');
    getList();
    detailVisible.value = false;
  } catch (error) {
    ElMessage.error('更新失败');
  }
};

// 添加字典查询方法调用
getDisasterTypeDict();
getIntensityDict();
getWarnLevelDict();
getList();

// 在script部分添加状态控制

const enableEditing = () => {
  isEditing.value = true;
  if (!originalData.value.earlyWarningInfo) {
    originalData.value = {
      earlyWarningInfo: {},
      geologicalWarning: {}
    };
  }
  originalData.value = JSON.parse(JSON.stringify(editForm.value));
};

const cancelEditing = () => {
  isEditing.value = false;
  editForm.value = JSON.parse(JSON.stringify(originalData.value));
};

const route = useRoute();
const userStore = useUserStore();

// 添加当前行数据的响应式引用
const currentRow = ref<any>(null);

// 通知相关数据
const noticeDialog = reactive({
  visible: false
});

const noticeForm = ref({
  title: '',
  content: '',
  warnType: '3', // 地质灾害预警固定为3
  status: 1,
  notifyMethod: [],
  notifyTargets: [], // 新增推送对象字段
  sendTime: '',
  happenTime:'',
  userId: userStore.userId,
  postContent: '' // 短信通知附加内容
});

const noticeRules = {
  title: [{ required: true, message: '请输入通知标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入通知内容', trigger: 'blur' }],
  notifyMethod: [{ required: true, message: '请选择通知方式', trigger: 'change' }],
};

const notifyMethodOptions = ref<any[]>([]);

// 移除短信附加内容显示条件，始终显示通知附加内容
// const showSmsPostContent = computed(() => {
//   return noticeForm.value.notifyMethod.includes('sms');
// });

// 生成短信预览文本
const smsPreviewText = computed(() => {
  if (!currentRow.value) {
    return '【预警通知】[人员姓名]：[发生时间] 在 [发生地点] 发生"[预警类型]"预警。请速登录系统"预警/事件"管理，了解详情。[用户自己输入内容]';
  }

  // 获取发生时间
  const happenTime = currentRow.value.geologicalWarning?.occurrenceTime
    ? dayjs(currentRow.value.geologicalWarning.occurrenceTime).format('YYYY-MM-DD HH:mm:ss')
    : '[发生时间]';

  // 获取发生地点
  const location = currentRow.value.earlyWarningInfo?.location || '[发生地点]';

  // 获取预警类型
  const warningType = getDictLabel('disaster_type', currentRow.value.geologicalWarning?.disasterType) || '地质灾害';

  // 获取用户输入的附加内容
  const userContent = noticeForm.value.postContent || '[用户自己输入内容]';

  return `【预警通知】[人员姓名]：${happenTime} 在 ${location} 发生"${warningType}"预警。请速登录系统"预警/事件"管理，了解详情。${userContent}`;
});

// 获取通知方式字典
const getNotifyMethodDict = async () => {
  DictQueryParams.value.dictType = 'notification_method';
  const res = await listData(DictQueryParams.value);
  notifyMethodOptions.value = res.rows;
};

// 格式化通知内容
const formatNoticeContent = (row: any) => {
  const content = [
    `灾害类型：${getDictLabel('disaster_type', row.geologicalWarning?.disasterType)}`,
    `强度等级：${getDictLabel('disaster_intensity', row.geologicalWarning?.intensity)}`,
    `发生地点：${row.earlyWarningInfo?.location}`,
    `影响范围：${row.geologicalWarning?.affectedArea}`,
    `预计持续时间：${row.geologicalWarning?.expectedDuration}小时`,
    `预警级别：${row.earlyWarningInfo?.warnLevel}级预警`
  ].join('\n');
  return content;
};

// 处理推送通知
const handlePushNotice = (row: any) => {
  currentRow.value = row; // 存储当前行数据
  noticeForm.value = {
    title: (route.meta?.title as string) || '地质灾害预警通知',
    content: formatNoticeContent(row),
    warnType: '3',
    status: 1,
    notifyMethod: [],
    notifyTargets: [], // 确保包含notifyTargets字段
    happenTime: row.geologicalWarning.createTime,
    warnLevel: row.earlyWarningInfo.warnLevel,
    location: row.earlyWarningInfo.location,
    sendTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    userId: userStore.userId,
    postContent: '' // 短信通知附加内容
  };
  noticeDialog.visible = true;
};

// 提交通知表单
const noticeFormRef = ref();
const submitNoticeForm = async () => {
  if (!noticeFormRef.value) return;
  try {
    await noticeFormRef.value.validate();

    // 1. 用户级别
    const notifyList = [];
    const selectedUserKeys = new Set();
    for (const userKey of notifyTargets.value) {
      const [deptId, userId] = userKey.split('-');
      const userInfo = findUserInfo(deptId, userId);
      if (userInfo) {
        notifyList.push({
          departmentId: deptId,
          departmentName: userInfo.deptName,
          personnelId: userId,
          personnelName: userInfo.nickName || userInfo.userName,
          phonenumber: userInfo.phonenumber
        });
        selectedUserKeys.add(`${deptId}-${userId}`);
      }
    }
    console.log(noticeForm.value)
    const submitData = {
      ...noticeForm.value,
      notifyMethod: Array.isArray(noticeForm.value.notifyMethod) ? noticeForm.value.notifyMethod.join(',') : noticeForm.value.notifyMethod,
      warnLevel: currentRow.value.earlyWarningInfo.warnLevel,
      notifyList
    };
    
    const res = await addNotice(submitData);
    if (res.code === 200) {
      await publishNotice(res.data);
      ElMessage.success('通知已同步');
      noticeDialog.visible = false;
    }
  } catch (error) {
    console.error('提交通知失败:', error);
    ElMessage.error('提交通知失败');
  }
};

// 添加字典查询方法调用
getNotifyMethodDict();
getList();

// 添加推送对象选项
const notifyTargetOptions = ref([
  { label: '大桥管理局', value: 'bridge_admin' },
  { label: '卫生管理局', value: 'health_admin' },
  { label: '中交公规院', value: 'traffic_institute' },
  { label: '海事局', value: 'maritime_admin' }
]);

// 添加研判相关变量和方法
const judgmentDialog = reactive({
  visible: false
});

const judgmentFormRef = ref();
const judgmentForm = ref<JudgmentFormType>({
  judgmentResult: 0,
  eventLevel: '',
  eventName: '',
  happenTime: '',
  location: '',
  firstReportTime: '',
  status: 1,
  description: '',
  warningId: '',
  eventType: '地质灾害事件',
  bridgeId: '',
  lat: '',
  lon: ''
});

// 当前预警信息
const currentWarning = ref({
  geologicalWarning: {
    disasterType: '',
    location: '',
    occurrenceTime: '',
    intensity: '',
    affectedArea: '',
    expectedDuration: 0,
    disasterCause: ''
  },
  earlyWarningInfo: {
    location: '',
    title: '',
    warnLevel: '',
    navigationClosure: 0,
    status: 0
  }
});

// 预警级别类型获取
const getWarnLevelType = (level: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const levelMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '1': 'danger', // 一级预警 - 红色
    '2': 'warning', // 二级预警 - 橙色
    '3': 'success', // 三级预警 - 黄色
    '4': 'info' // 四级预警 - 蓝色
  };
  return levelMap[level] || 'info';
};

// 研判表单校验规则
const judgmentRules = {
  eventLevel: [{ required: true, message: '请选择预警级别', trigger: 'change' }],
  judgmentResult: [{ required: true, message: '请选择研判结果', trigger: 'change' }],
  eventName: [{ required: true, message: '请输入事件标题', trigger: 'blur' }],
  happenTime: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
  location: [{ required: true, message: '请输入发生地点', trigger: 'blur' }],
  firstReportTime: [{ required: true, message: '请选择首次报告时间', trigger: 'change' }],
  status: [{ required: true, message: '请选择处置状态', trigger: 'change' }],
  description: [{ required: true, message: '请输入事件描述', trigger: 'blur' }]
};

// 处理研判方法
async function handleJudgment(row: any) {
  try {
    // 先获取详情数据
    const detailRes = await getGeologyWarningById(row.geologicalWarning.id);
    if (!detailRes.data) {
      ElMessage.error('获取预警详情失败');
      return;
    }
    
    // 保存当前行详情数据
    currentWarning.value = detailRes.data;

    // 使用详情数据初始化表单
    judgmentForm.value = {
      judgmentResult: 0,
      eventLevel: detailRes.data.earlyWarningInfo?.warnLevel || '',
      eventName: `地质灾害事件-${getDictLabel('disaster_type', detailRes.data.geologicalWarning?.disasterType)}`,
      happenTime: detailRes.data.geologicalWarning?.occurrenceTime || '',
      location: detailRes.data.earlyWarningInfo?.location || '',
      firstReportTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      status: 1,
      description: `${getDictLabel('disaster_type', detailRes.data.geologicalWarning?.disasterType)}，原因：${detailRes.data.geologicalWarning?.disasterCause || '未知'}，影响范围：${detailRes.data.geologicalWarning?.affectedArea || 0}平方米`,
      warningId: detailRes.data.geologicalWarning?.id,
      eventType: '地质灾害事件',
      occurrenceTime: detailRes.data.geologicalWarning?.occurrenceTime || '',
      reportTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      deathCount: 0,
      bridgeId: detailRes.data.earlyWarningInfo?.bridgeId || '' // 添加桥梁ID
    };

    judgmentDialog.visible = true;
  } catch (error) {
    console.error('获取预警详情失败:', error);
    ElMessage.error('获取预警详情失败');
  }
}

// 提交研判表单
async function submitJudgmentForm() {
  if (!judgmentFormRef.value) return;

  try {
    await judgmentFormRef.value.validate();

    // 如果选择转为应急事件，则调用添加事件API
    if (judgmentForm.value.judgmentResult === 1) {
      // 准备事件数据
      const eventData = {
        happenTime: judgmentForm.value.happenTime,
        location: judgmentForm.value.location,
        description: judgmentForm.value.description,
        firstReportTime: judgmentForm.value.firstReportTime,
        status: judgmentForm.value.status,
        eventName: judgmentForm.value.eventName,
        warningId: judgmentForm.value.warningId,
        bridgeId: judgmentForm.value.bridgeId, // 添加桥梁ID
        eventType: '地质灾害事件', // 添加事件类型，与预警类型一致
        eventLevel: judgmentForm.value.eventLevel // 添加事件级别，与预警级别一致
      };

      // 调用添加事件API
      const res = await addEmergencyEvent(eventData);

      if (res.code === 200) {
        ElMessage.success('已成功将预警转为应急事件');

        // 更新预警状态为已处理，使用currentWarning中的完整数据
        if (currentWarning.value && currentWarning.value.earlyWarningInfo) {
          const updateData = {
            earlyWarningInfo: {
              ...currentWarning.value.earlyWarningInfo,
              status: 1 // 设置为已处理
            },
            geologicalWarning: currentWarning.value.geologicalWarning
          };

          await updateGeologyWarning(updateData);
        }

        // 刷新列表
        getList();
      } else {
        ElMessage.error(res.msg || '转为事件失败');
      }
    } else {
      // 更新预警研判结果但不转为事件
      if (currentWarning.value && currentWarning.value.earlyWarningInfo) {
        const updateData = {
          earlyWarningInfo: {
            ...currentWarning.value.earlyWarningInfo,
            status: currentWarning.value.earlyWarningInfo.status // 保持原状态
          },
          geologicalWarning: currentWarning.value.geologicalWarning
        };

        await updateGeologyWarning(updateData);
        ElMessage.success('研判完成');
      }
    }

    judgmentDialog.visible = false;
  } catch (error) {
    console.error('研判提交失败:', error);
    ElMessage.error('研判提交失败');
  }
}

// 多选相关
const multipleSelection = ref<any[]>([]);

// 表格多选框选中事件处理
const handleSelectionChange = (selection: any[]) => {
  multipleSelection.value = selection;
};

// 删除
const batchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }
  
  // 获取选中行的ID列表
  const ids = multipleSelection.value.map(item => {
    return item.geologicalWarning?.id || item.earlyWarningInfo?.id || item.id;
  }).join(',');
  
  ElMessageBox.confirm('确定要删除选中的数据吗？', '警告', {
    type: 'warning'
  })
    .then(async () => {
      await deleteGeologyWarningBatch(ids);
      ElMessage.success('删除成功');
      // 清空选中项
      multipleSelection.value = [];
      // 刷新列表数据
      await getList();
    })
    .catch(() => {});
};

const getStatusType = (status: number): 'warning' | 'primary' | 'success' | 'info' => {
  const statusMap: Record<number, 'warning' | 'primary' | 'success' | 'info'> = {
    1: 'warning',
    2: 'primary',
    3: 'success'
  };
  return statusMap[status] || 'warning';
};

const getStatusLabel = (status: number): string => {
  const statusMap: Record<number, string> = {
    1: '未处理',
    2: '处理中',
    3: '已处理'
  };
  return statusMap[status] || '未处理';
};

// 文件相关方法
function checkFileSuffix(fileSuffix: string | string[]) {
  const arr = ['.png', '.jpg', '.jpeg'];
  const suffixArray = Array.isArray(fileSuffix) ? fileSuffix : [fileSuffix];
  return suffixArray.some((suffix) => arr.includes(suffix.toLowerCase()));
}

// 附件弹窗相关
const attachmentDialogVisible = ref(false);
const attachmentList = ref<any[]>([]);



// 处理附件查看
const handleViewAttachment = async (row) => {
  const res = await getGeologyWarningById(row.geologicalWarning.id);
  currentRow.value = res.data;
  if (row.earlyWarningInfo?.fileId) {
    try {
      const res = await listByIds(row.earlyWarningInfo.fileId);
      attachmentList.value = res.data;
      attachmentDialogVisible.value = true;
    } catch (error) {
      console.error('获取附件列表失败:', error);
      ElMessage.error('获取附件列表失败');
    }
  } else {
    ElMessage.warning('暂无附件');
  }
};

// 处理文件下载
const handleDownload = (file) => {
  proxy?.$download.oss(file.ossId);
};

// 修改删除附件函数
const handleDeleteAttachment = (file) => {
  ElMessageBox.confirm(`确定要删除文件 "${file.originalName}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        // 1. 物理删除文件
        await delOss(file.ossId);
        
        // 2. 更新预警数据中的fileId
        if (currentRow.value?.earlyWarningInfo) {
          // 从fileId中移除被删除的文件ID
          const currentFileIds = currentRow.value.earlyWarningInfo.fileId?.split(',') || [];
          const updatedFileIds = currentFileIds.filter(id => id !== file.ossId).join(',');
          
          // 准备更新数据
          const updateData = {
            earlyWarningInfo: {
              ...currentRow.value.earlyWarningInfo,
              fileId: updatedFileIds || null // 如果没有文件了，设置为null
            },
            geologicalWarning: currentRow.value.geologicalWarning
          };

          // 调用更新接口
          await updateGeologyWarning(updateData);
          
          // 更新当前行数据
          currentRow.value.earlyWarningInfo.fileId = updatedFileIds || null;
          
          // 重新获取附件列表
          if (currentRow.value.earlyWarningInfo.fileId) {
            const res = await listByIds(currentRow.value.earlyWarningInfo.fileId);
            attachmentList.value = res.data;
          } else {
            attachmentList.value = [];
          }
          
          ElMessage.success('删除成功');
          // 删除附件后刷新页面数据
          getList();
        }
      } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 取消删除操作
    });
};

// 文件上传相关
const uploadDialogVisible = ref(false);
const uploadRef = ref();
const uploadFiles = ref([]);
const uploading = ref(false);
const uploadProgress = ref({
  current: 0,
  total: 0,
  status: '' as '' | 'success' | 'warning' | 'exception'
});

// 处理文件选择
const handleFileChange = (file, fileList) => {
  uploadFiles.value = fileList;
};

// 处理文件移除
const handleFileRemove = (file, fileList) => {
  uploadFiles.value = fileList;
};

// 处理超出文件数限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传10个文件');
};

// 格式化进度条文字
const progressFormat = (percentage) => {
  return `${uploadProgress.value.current}/${uploadProgress.value.total}`;
};

// 上传文件前的钩子
const beforeUpload = (file) => {
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('上传文件大小不能超过 5MB!');
    return false;
  }
  return true;
};

// 处理附件上传点击
const handleUploadAttachment = async (row) => {
  try {
    // 先获取详情数据
    const detailRes = await getGeologyWarningById(row.geologicalWarning.id);
    if (!detailRes.data) {
      ElMessage.error('获取预警详情失败');
      return;
    }
    
    // 保存当前行详情数据
    currentRow.value = detailRes.data;
    uploadDialogVisible.value = true;
    uploadFiles.value = [];
    uploadProgress.value = {
      current: 0,
      total: 0,
      status: ''
    };
  } catch (error) {
    console.error('获取预警详情失败:', error);
    ElMessage.error('获取预警详情失败');
  }
};

// 提交文件上传
const submitUpload = async () => {
  if (!uploadFiles.value.length) {
    ElMessage.warning('请先选择要上传的文件');
    return;
  }

  if (!currentRow.value?.earlyWarningInfo?.id) {
    ElMessage.error('未获取到主表ID');
    return;
  }

  uploading.value = true;
  uploadProgress.value.total = uploadFiles.value.length;
  uploadProgress.value.current = 0;
  uploadProgress.value.status = '';

  try {
    for (const file of uploadFiles.value) {
      const formData = new FormData();
      formData.append('file', file.raw);
      formData.append('id', currentRow.value.earlyWarningInfo.id);
      await uplodadWarningFile(formData);
      uploadProgress.value.current++;
    }

    uploadProgress.value.status = 'success';
    ElMessage.success('文件上传成功');
    await getList(); // 刷新列表
    setTimeout(() => {
      uploadDialogVisible.value = false;
      uploading.value = false;
      uploadFiles.value = [];
    }, 1000);
  } catch (error) {
    uploadProgress.value.status = 'exception';
    ElMessage.error('文件上传失败');
    uploading.value = false;
  }
};

// 添加 watch 监听器
watch(attachmentDialogVisible, (newVal) => {
  if (!newVal) {
    // 关闭附件悬浮窗时刷新数据
    getList();
  }
});

watch(detailVisible, (newVal) => {
  if (!newVal) {
    // 关闭详情悬浮窗时刷新数据
    getList();
  }
});
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadFileUrl = baseUrl + '/resource/oss/upload';
const headers = globalHeaders();

const handleUploadSuccess = (response, uploadFile) => {
  if (response.code === 200) {
    const currentIds = editForm.value.earlyWarningInfo.fileId ? editForm.value.earlyWarningInfo.fileId.split(',') : [];
    currentIds.push(response.data.ossId);
    editForm.value.earlyWarningInfo.fileId = currentIds.join(',');
    attachmentList.value.push({
      ossId: response.data.ossId,
      url: response.data.url,
      originalName: uploadFile.name,
      fileSuffix: uploadFile.name.substring(uploadFile.name.lastIndexOf('.'))
    });
    ElMessage.success('上传成功');
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
};

// 处理上传错误
const handleUploadError = (error) => {
  ElMessage.error('上传文件失败');
};

// 处理移除图片
const handleUploadRemove = (uploadFile) => {
  // 从fileId中移除对应的ID
  if (editForm.value.earlyWarningInfo.fileId) {
    const currentIds = editForm.value.earlyWarningInfo.fileId.split(',');
    // 找到要删除的ID的索引
    const index = currentIds.findIndex(id => id === uploadFile.response?.data?.ossId);
    if (index > -1) {
      currentIds.splice(index, 1);
      editForm.value.earlyWarningInfo.fileId = currentIds.join(',');
    }
  }
};

const handleRemoveExistingImage = async (file: { ossId: string }) => {
  try {
    await delOss(file.ossId);
    if (editForm.value.earlyWarningInfo.fileId) {
      const currentIds = editForm.value.earlyWarningInfo.fileId.split(',');
      const index = currentIds.findIndex(id => id === file.ossId);
      if (index > -1) {
        currentIds.splice(index, 1);
        editForm.value.earlyWarningInfo.fileId = currentIds.join(',');
      }
    }
    const idx = attachmentList.value.findIndex(item => item.ossId === file.ossId);
    if (idx > -1) {
      attachmentList.value.splice(idx, 1);
    }
    ElMessage.success('删除成功');
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

// DeptTreeSelector 相关数据
const deptTreeSelectorRef = ref();
const deptData = ref([]);
const userData = ref([]);
const selectedUserIds = ref<string[]>([]);

// DeptTreeSelector 事件处理
const handleDeptTreeSelectionChange = (event) => {
  // 更新选中的用户ID列表
  selectedUserIds.value = event.selectedUsers.map(user => user.userId);

  // 更新 notifyTargets 以保持兼容性
  notifyTargets.value = event.selectedUsers.map(user => `${user.deptId}-${user.userId}`);
};

const handleDeptTreeUserSelect = (user, selected) => {
  console.log('用户选择变化:', user, selected);
};

// 部门树相关（保持兼容性）
const deptOptions = ref([]);
const deptTreeRef = ref();
const notifyTargets = ref([]); // 选中的用户key

// 用户树节点渲染
const renderContent = (h, { node, data }) => {
  return h('div', { class: 'custom-tree-node' }, [
    h('div', { class: 'node-header' }, [
      h('span', { class: 'node-label' }, [
        h('span', null, node.label),
        h('span', { class: 'user-count' }, ` (${data.users?.length || 0}人)`)
      ]),
      // 将用户列表放在同一行，使用内联显示
      data.users?.length > 0 ? h('div', { class: 'user-list-inline' },
        data.users.map(user =>
          h('span', {
            class: 'user-item-inline',
            key: user.userId
          }, [
            h('el-checkbox', {
              modelValue: isUserSelected(user),
              'onUpdate:modelValue': (val) => handleUserCheck(user, val),
              onClick: (e) => e.stopPropagation(),
              size: 'small'
            }, (user.nickName || user.userName) + (user.phonenumber ? `（${user.phonenumber}）` : ''))
          ])
        )
      ) : null
    ])
  ]);
};

const handleCheck = () => updateSelectedUsers();
const handleCheckChange = () => updateSelectedUsers();

const handleUserCheck = (user, checked) => {
  const userKey = `${user.deptId}-${user.userId}`;
  if (checked) {
    if (!notifyTargets.value.includes(userKey)) {
      notifyTargets.value.push(userKey);
    }
  } else {
    notifyTargets.value = notifyTargets.value.filter(key => key !== userKey);
  }
  updateSelectedUsers();
};

const isUserSelected = (user) => {
  const userKey = `${user.deptId}-${user.userId}`;
  return notifyTargets.value.includes(userKey);
};

const updateSelectedUsers = () => {
  const tree = deptTreeRef.value;
  if (!tree) return;

  const checkedNodes = tree.getCheckedNodes();
  const halfCheckedNodes = tree.getHalfCheckedNodes();

  const selectedUsers = [];

  // Collect selected users from fully checked nodes
  checkedNodes.forEach(node => {
    if (node.users) {
      node.users.forEach(user => {
        const userKey = `${user.deptId}-${user.userId}`;
        if (!selectedUsers.includes(userKey)) {
          selectedUsers.push(userKey);
        }
      });
    }
  });

  // Collect selected users from half-checked (部门) nodes based on individual user checks
  halfCheckedNodes.forEach(node => {
     if (node.users) {
      node.users.forEach(user => {
        const userKey = `${user.deptId}-${user.userId}`;
        // Only add if this specific user is marked as selected (via handleUserCheck)
        if (isUserSelected(user) && !selectedUsers.includes(userKey)) {
          selectedUsers.push(userKey);
        }
      });
    }
  });

  notifyTargets.value = selectedUsers;
};

// 根据部门ID和用户ID查找用户信息
const findUserInfo = (deptId, userId) => {
  const findInDept = (depts) => {
    for (const dept of depts) {
      if (String(dept.deptId) === String(deptId)) {
        const user = dept.users?.find(u => String(u.userId) === String(userId));
        if (user) return user;
      }
      if (dept.children) {
        const found = findInDept(dept.children);
        if (found) return found;
      }
    }
    return null;
  };
  return findInDept(deptOptions.value);
};

onMounted(async () => {
  // 获取部门和用户数据，构建树结构
  const deptRes = await listDept({ pageNum: 1, pageSize: 10000 });
  const userRes = await getUserInfoList({ pageNum: 1, pageSize: 10000 });

  // 为 DeptTreeSelector 准备数据
  if (deptRes.code === 200 && deptRes.data) {
    deptData.value = deptRes.data;
  }

  if (userRes.code === 200 && userRes.rows) {
    userData.value = userRes.rows;
  }

  // 保持原有的部门树逻辑以兼容其他功能
  const userMap = {};
  if (userRes.code === 200 && userRes.rows) {
    userRes.rows.forEach(user => {
      const deptId = String(user.deptId);
      if (!userMap[deptId]) userMap[deptId] = [];
      userMap[deptId].push({
        userId: user.userId,
        userName: user.userName,
        nickName: user.nickName,
        phonenumber: user.phonenumber,
        deptId: deptId,
        deptName: user.deptName
      });
    });
  }

  const buildDeptTree = (data) => {
    const normalize = data.map(item => ({
      ...item,
      deptId: String(item.deptId),
      parentId: String(item.parentId),
      users: userMap[String(item.deptId)] || []
    }));

    const map = {};
    normalize.forEach(item => {
      map[item.deptId] = { ...item, children: [] };
    });

    const tree = [];
    normalize.forEach(item => {
      const parentId = item.parentId;
      const id = item.deptId;
      if (map[parentId]) {
        map[parentId].children.push(map[id]);
      } else {
        if (parentId === '0') tree.push(map[id]);
      }
    });
    return tree;
  };

  if (deptRes.code === 200 && deptRes.data) {
    deptOptions.value = buildDeptTree(deptRes.data);
  }

  // 执行其他初始化
  getDisasterTypeDict();
  getIntensityDict();
  getWarnLevelDict();
  getNotifyMethodDict();
  getList();

  // 获取桥梁列表
  const bridgeList = ref([]);

  function fetchBridgeData() {
    // 传递isWarn参数，获取相关桥梁数据
    getBridgeList({ isWarn: 1 }).then((res) => {
      bridgeList.value = res.rows;
    });
  }

  // 将fetchBridgeData函数添加到onMounted中
  fetchBridgeData();
});

// 获取桥梁列表
const bridgeList = ref([]);
const fetchBridgeList = async () => {
  try {
    const res = await getBridgeList({ isWarn: 1 });
    bridgeList.value = res.rows;
  } catch (error) {
    console.error('获取桥梁列表失败:', error);
    ElMessage.error('获取桥梁列表失败');
  }
};

// 在mounted钩子中调用获取桥梁列表的函数
fetchBridgeList();

// 地图选择器相关
const mapSelectorVisible = ref(false);
const initialCoordinates = ref({ lat: 30.5, lng: 121.9 });

// 打开地图选择器
const openMapSelector = () => {
  // 如果已有坐标，解析并设置为初始值
  if (editForm.value.earlyWarningInfo.lat && editForm.value.earlyWarningInfo.lon) {
    initialCoordinates.value = {
      lat: parseFloat(editForm.value.earlyWarningInfo.lat),
      lng: parseFloat(editForm.value.earlyWarningInfo.lon)
    };
  }
  mapSelectorVisible.value = true;
};

// 处理坐标确认
const handleCoordinateConfirm = (coords) => {
  editForm.value.earlyWarningInfo.lat = coords.lat.toString();
  editForm.value.earlyWarningInfo.lon = coords.lng.toString();
  mapSelectorVisible.value = false;
};

// 格式化坐标显示
const formattedCoordinates = computed(() => {
  if (editForm.value.earlyWarningInfo.lat && editForm.value.earlyWarningInfo.lon) {
    return `${editForm.value.earlyWarningInfo.lat},${editForm.value.earlyWarningInfo.lon}`;
  }
  return '';
});
</script>

<style scoped lang="scss">
.list-page {
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

.filter-area {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  background: var(--el-bg-color-overlay);
  padding: 16px;
  border-radius: 4px;

  .search-input {
    width: 300px;
    margin-left: auto;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: var(--el-bg-color-overlay);

  :deep(th) {
    background: var(--el-bg-color-page) !important;
  }

  :deep(.warning-row),
  :deep(.success-row) {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
  }

  :deep(.success-row) {
    --el-table-tr-bg-color: var(--el-color-success-light-9);
  }
}

.detail-container {
  display: flex;
  gap: 30px;
  padding: 20px 0;

  .detail-column {
    flex: 1;
    min-width: 300px;

    .detail-title,
    .detail-label {
      color: #409eff;
      font-weight: 600;
    }

    .detail-title {
      font-size: 16px;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }

    .detail-row {
      margin-bottom: 12px;
      display: flex;
      line-height: 1.6;

      .detail-label {
        width: 90px;
        flex-shrink: 0;
      }

      .detail-value {
        flex: 1;
        color: #606266;
        word-break: break-word;
      }
    }
  }

  .unit {
    margin-left: 10px;
    color: #909399;
    font-size: 14px;
    font-style: italic;
  }

  .el-input-number,
  .el-date-editor {
    width: 100%;
  }

  .el-input-number {
    :deep(.el-input__inner) {
      text-align: left;
    }
  }

  .el-radio-group {
    margin-top: 8px;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: var(--el-bg-color-page);
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid var(--el-border-color-light);

  .dialog-title {
    font-size: 18px;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }
}

.edit-btn,
.cancel-btn,
.save-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 10px 20px;
  border-radius: 6px;
}

.edit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cancel-btn:hover {
  background-color: #f4f4f5;
  color: #909399;
}

.save-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.disabled-field {
  :deep(.el-input__inner) {
    background-color: #f8f9fa;
    border-color: #e4e7ed;
    color: #909399;
    cursor: not-allowed;
  }
}

.el-dialog {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

  &__body {
    padding: 24px;
    background: #fff;
  }
}

.el-form-item {
  margin-bottom: 24px;

  :deep(.el-form-item__label) {
    color: #606266;
    font-weight: 500;
  }
}

.refresh-button {
  transition: all 0.3s ease;

  &:hover {
    transform: rotate(180deg);
  }
}

.judgment-section {
  margin-bottom: 30px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;

  .section-title {
    font-size: 18px;
    color: #409eff;
    font-weight: 600;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }

  .info-display {
    margin-top: 15px;

    .info-row {
      margin-bottom: 12px;
      display: flex;
      line-height: 1.6;

      .info-label {
        width: 120px;
        color: #606266;
        font-weight: 500;
        flex-shrink: 0;
      }

      .info-value {
        flex: 1;
        color: #303133;
        word-break: break-word;
      }
    }
  }
}

.info-section {
  background-color: rgba(64, 158, 255, 0.05);
  border-left: 4px solid #409eff;
}

.judgment-dialog {
  :deep(.el-dialog) {
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .el-dialog__header {
      background: linear-gradient(to right, #409eff, #67c23a);
      padding: 15px 20px;

      .el-dialog__title {
        color: white;
        font-size: 18px;
        font-weight: 600;
      }

      .el-dialog__headerbtn {
        top: 15px;

        .el-dialog__close {
          color: white;
        }
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 10px 20px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.upload-container {
  padding: 20px;
  .image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    .static-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
  }
  .edit-upload-container {
    .existing-images {
      width: 100%;
      .scrollbar-wrapper {
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        padding-bottom: 10px;
        &::-webkit-scrollbar { height: 6px; }
        &::-webkit-scrollbar-thumb { background: #c0c4cc; border-radius: 3px; }
        &::-webkit-scrollbar-track { background: #f5f7fa; border-radius: 3px; }
        .image-list {
          display: inline-flex;
          gap: 10px;
          padding: 10px 0;
          min-width: min-content;
          .image-item {
            position: relative;
            flex-shrink: 0;
            width: 148px;
            height: 148px;
            border: 1px solid #d9d9d9;
  border-radius: 4px;
            overflow: hidden;
            .image-actions {
              position: absolute;
              top: 0;
              right: 0;
              padding: 4px;
              background: rgba(0, 0, 0, 0.5);
              border-radius: 0 0 0 4px;
              opacity: 0;
              transition: opacity 0.3s;
              .el-button { padding: 4px; color: white; }
            }
            &:hover .image-actions { opacity: 1; }
          }
          .upload-button {
            flex-shrink: 0;
            width: 148px;
            height: 148px;
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: border-color 0.3s;
            &:hover { border-color: var(--el-color-primary); }
            .el-icon { font-size: 28px; color: #8c939d; }
          }
        }
      }
    }
  }
}

.upload-progress {
  margin-top: 20px;
  
  .progress-text {
    margin-bottom: 10px;
    color: #606266;
  }
}

.attachment-container {
  padding: 20px;
}

:deep(.el-upload--picture-card) {
  width: 148px;
  height: 148px;
  line-height: 146px;
}

// 新增卡片标题样式
.card-header {
  margin-bottom: 25px;
  
  .header-title {
    font-size: 20px;
    color: var(--el-text-color-primary);
    font-weight: 600;
  }
  
  .header-line {
    height: 3px;
    background: linear-gradient(90deg, #409eff 30%, transparent 100%);
    margin-top: 12px;
  }
}

// 新增表单容器样式
.form-container {
  flex: 1;
  padding: 0 15px;
  
  .form-item {
    margin-bottom: 28px;
    
    :deep(.el-form-item__label) {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
    
    .el-input, .el-select, .el-date-editor {
      :deep(.el-input__inner) {
        line-height: 1.5;
        background: var(--el-input-bg-color);
        border-color: var(--el-input-border-color);
        color: var(--el-text-color-primary);
      }
    }
  }
}

// 新增表单按钮样式
.form-actions {
  margin-top: auto;
  padding-top: 20px;
  display: flex;
  gap: 20px;
  justify-content: flex-end;
  
  .el-button {
    padding: 12px 24px;
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-right: 8px;
  width: 100%;
}

.node-header {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  flex-wrap: wrap;
}

.node-label {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  font-weight: bold;
}

.user-count {
  color: #909399;
  font-size: 12px;
}

.user-list-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-left: 8px;
}

.user-item-inline {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  margin-right: 8px;
}

.user-item-inline:hover {
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

:deep(.el-tree) {
  overflow-y: auto;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  padding: 8px;
  
  .el-tree-node__content {
    height: auto;
    min-height: 32px;
  }

  .el-tree-node__children {
    padding-left: 0;
  }
}

.w-full {
  width: 100%;
}

.map-selector-container {
  height: 500px;
  width: 100%;
}

.coordinate-input-group {
  display: flex;
  align-items: center;
  width: 100%;
  
  .el-input {
    cursor: pointer;
    
    :deep(.el-input__inner) {
      cursor: pointer;
    }
  }
  
  .coordinate-icon {
    color: #409EFF;
    cursor: pointer;
    font-size: 16px;
    
    &:hover {
      color: #66b1ff;
    }
  }
}

/* 响应式弹窗样式 */
.responsive-dialog {
  :deep(.el-dialog) {
    margin: 0 auto;

    @media (max-width: 1600px) {
      width: 90% !important;
    }

    @media (max-width: 1200px) {
      width: 95% !important;
    }

    @media (max-width: 768px) {
      width: 98% !important;
      margin: 5vh auto;
    }

    .el-dialog__body {
      padding: 20px;

      @media (max-width: 768px) {
        padding: 15px;
      }
    }
  }
}
</style>
