<template>
  <div class="list-page">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" style="display: flex;justify-content: space-between;width: 100%;">
            <el-form-item label="卡口ID" prop="checkpointId">
              <el-input v-model="queryParams.checkpointId" placeholder="请输入卡口ID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="拥堵级别" prop="congestionLevel">
              <el-select v-model="queryParams.congestionLevel" placeholder="请选择拥堵级别" clearable>
                <el-option :value="1" label="畅通" />
                <el-option :value="2" label="基本畅通" />
                <el-option :value="3" label="轻度拥堵" />
                <el-option :value="4" label="中度拥堵" />
                <el-option :value="5" label="严重拥堵" />
              </el-select>
            </el-form-item>
            <el-form-item label="处理状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="处理状态" clearable>
                <el-option :value="1" label="未处理" />
                <el-option :value="2" label="处理中" />
                <el-option :value="3" label="已处理" />
              </el-select>
            </el-form-item>
            <div class="flex items-center space-x-2">
              <el-button type="primary" :icon="Search" @click="handleQuery">搜索</el-button>
              <el-button :icon="RefreshRight" @click="resetQuery">重置</el-button>
              <el-button type="primary" :icon="Plus" @click="handleAdd">新增</el-button>
              <el-button type="danger" :icon="Delete" @click="batchDelete" :disabled="selectedRows.length === 0">批量删除</el-button>
              <el-button type="info" :icon="RefreshRight" @click="() => showSearch = !showSearch">
                {{ showSearch ? '隐藏搜索' : '显示搜索' }}
              </el-button>
            </div>
          </el-form>
        </el-card>
      </div>
    </transition>

    <!-- 列表区域 -->
    <el-card shadow="hover" class="w-full">
      <el-table :data="tableData" border style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column label="序号" prop="index" width="80" align="center">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="卡口ID" prop="vehicleCheckpointWarning.checkpointId" min-width="120">
          <template #default="scope">
            {{ scope.row.vehicleCheckpointWarning?.checkpointId || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="拥堵级别" prop="vehicleCheckpointWarning.congestionLevel" width="150" align="center">
          <template #default="scope">
            {{ getCongestionLevelLabel(scope.row.vehicleCheckpointWarning?.congestionLevel) }}
          </template>
        </el-table-column>
        <el-table-column label="平均速度(km/h)" prop="vehicleCheckpointWarning.avgSpeed" width="160" align="center">
          <template #default="scope">
            {{ scope.row.vehicleCheckpointWarning?.avgSpeed || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="限速(km/h)" prop="vehicleCheckpointWarning.speedLimit" width="120" align="center">
          <template #default="scope">
            {{ scope.row.vehicleCheckpointWarning?.speedLimit || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="不规范通行类型" prop="vehicleCheckpointWarning.violationType" min-width="180">
          <template #default="scope">
            {{ scope.row.vehicleCheckpointWarning?.violationType || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="不规范通行次数" prop="vehicleCheckpointWarning.violationCount" width="150" align="center">
          <template #default="scope">
            {{ scope.row.vehicleCheckpointWarning?.violationCount || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="发生时间" prop="vehicleCheckpointWarning.occurrenceTime" width="200" align="center">
          <template #default="scope">
            {{ scope.row.vehicleCheckpointWarning?.occurrenceTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="attachmentPath" label="附件" width="100" align="center">
          <template #default="{ row }">
            <el-link v-if="row.earlyWarningInfo?.fileId" type="primary" :underline="false" @click.prevent="handleViewAttachment(row)">
              <el-icon>
                <Document />
              </el-icon>
              查看附件
            </el-link>
            <el-button v-else link type="primary" @click="handleUploadAttachment(row)">
              <el-icon>
                <Upload />
              </el-icon>
              上传
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="预警级别" prop="earlyWarningInfo.warnLevel" width="200" align="center">
          <template #default="scope">
            <el-tooltip :content="`${scope.row.earlyWarningInfo?.warnLevel}级预警`" placement="top">
              <img :src="getAlertImage(scope.row.earlyWarningInfo?.warnLevel)" style="width: 32px; height: 32px" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="处理状态" prop="earlyWarningInfo.status" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.earlyWarningInfo?.status)">
              {{ getStatusLabel(scope.row.earlyWarningInfo?.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="scope">
            <el-button size="small" type="primary" link @click="viewDetails(scope.row)">详情</el-button>
            <el-button size="small" type="success" link @click="handlePushNotice(scope.row)"> 同步</el-button>
            <el-button size="small" type="warning" link @click="handleJudgment(scope.row)"> 研判</el-button>
            <el-button size="small" type="danger" link @click="handleDelete(scope.row)" v-perms="['bridge:vehicle:remove']"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区域 -->
      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailVisible" width="1400px" center destroy-on-close class="responsive-dialog">
      <div class="card-header">
        <span class="header-title">车辆卡口预警详情</span>
        <div class="header-line"></div>
      </div>
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="180px" class="form-container">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="预警标题：" prop="earlyWarningInfo.title" class="form-item">
                  <el-input v-model="editForm.earlyWarningInfo.title" :disabled="!isEditing" placeholder="请输入预警标题" />
                </el-form-item>
                <el-form-item label="预警级别：" prop="earlyWarningInfo.warnLevel" class="form-item">
                  <el-select v-model="editForm.earlyWarningInfo.warnLevel" placeholder="请选择预警级别" style="width: 100%" :disabled="!isEditing">
                    <el-option v-for="item in warnLevelList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
                <el-form-item label="关联桥梁：" prop="earlyWarningInfo.bridgeId" class="form-item">
                  <el-select v-model="editForm.earlyWarningInfo.bridgeId" placeholder="请选择关联桥梁" style="width: 100%" :disabled="!isEditing">
                    <el-option v-for="bridge in bridgeList" :key="bridge.id" :label="bridge.bridgeName" :value="bridge.id" />
                  </el-select>
                </el-form-item>
                <el-form-item label="发生地点：" prop="earlyWarningInfo.location" class="form-item">
                  <el-input v-model="editForm.earlyWarningInfo.location" :disabled="!isEditing" placeholder="请输入发生地点" />
                </el-form-item>
                <el-form-item label="地理坐标：" prop="earlyWarningInfo.lat" class="form-item">
                  <div class="coordinate-input-group">
                    <el-input
                      v-model="formattedCoordinates"
                      placeholder="点击选择地理坐标"
                      readonly
                      :disabled="!isEditing"
                      @click="isEditing && openMapSelector()"
                    >
                      <template #suffix>
                        <el-icon class="coordinate-icon" @click.stop="isEditing && openMapSelector()">
                          <Location />
                        </el-icon>
                      </template>
                    </el-input>
                  </div>
                </el-form-item>
                <el-form-item label="通航封闭：" prop="earlyWarningInfo.navigationClosure" class="form-item">
                  <el-radio-group v-model="editForm.earlyWarningInfo.navigationClosure" :disabled="!isEditing">
                    <el-radio :value="0">否</el-radio>
                    <el-radio :value="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="处理状态：" prop="earlyWarningInfo.status" class="form-item">
                  <el-select v-model="editForm.earlyWarningInfo.status" placeholder="请选择处理状态" style="width: 100%" :disabled="!isEditing">
                    <el-option :value="1" label="未处理" />
                    <el-option :value="2" label="处理中" />
                    <el-option :value="3" label="已处理" />
                  </el-select>
                </el-form-item>
                <el-form-item label="处理结果：" prop="earlyWarningInfo.result" class="form-item">
                  <el-input v-model="editForm.earlyWarningInfo.result" type="textarea" :rows="3" :disabled="!isEditing" placeholder="请输入处理结果" />
                </el-form-item>
                <el-form-item label="附件上传：" class="form-item">
                  <el-upload
                    ref="uploadRef"
                    :action="uploadAction"
                    :headers="uploadHeaders"
                    :file-list="fileList"
                    :on-success="handleUploadSuccess"
                    :on-remove="handleRemove"
                    :before-upload="beforeUpload"
                    :disabled="!isEditing"
                    multiple
                  >
                    <el-button type="primary" :disabled="!isEditing">
                      <el-icon><Upload /></el-icon>
                      选择文件
                    </el-button>
                    <template #tip>
                      <div class="el-upload__tip">支持jpg/png/pdf文件，且不超过10MB</div>
                    </template>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 车辆卡口信息 -->
          <el-tab-pane label="车辆卡口信息" name="vehicle">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="卡口ID：" prop="vehicleCheckpointWarning.checkpointId" class="form-item">
                  <el-input v-model="editForm.vehicleCheckpointWarning.checkpointId" :disabled="!isEditing" placeholder="请输入卡口ID" />
                </el-form-item>
                <el-form-item label="拥堵级别：" prop="vehicleCheckpointWarning.congestionLevel" class="form-item">
                  <el-select v-model="editForm.vehicleCheckpointWarning.congestionLevel" placeholder="请选择拥堵级别" style="width: 100%" :disabled="!isEditing">
                    <el-option :value="1" label="畅通" />
                    <el-option :value="2" label="基本畅通" />
                    <el-option :value="3" label="轻度拥堵" />
                    <el-option :value="4" label="中度拥堵" />
                    <el-option :value="5" label="严重拥堵" />
                  </el-select>
                </el-form-item>
                <el-form-item label="平均速度(km/h)：" prop="vehicleCheckpointWarning.avgSpeed" class="form-item">
                  <el-input v-model="editForm.vehicleCheckpointWarning.avgSpeed" type="number" :disabled="!isEditing" placeholder="请输入平均速度" />
                </el-form-item>
                <el-form-item label="限速(km/h)：" prop="vehicleCheckpointWarning.speedLimit" class="form-item">
                  <el-input v-model="editForm.vehicleCheckpointWarning.speedLimit" type="number" :disabled="!isEditing" placeholder="请输入限速" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="不规范通行类型：" prop="vehicleCheckpointWarning.violationType" class="form-item">
                  <el-input v-model="editForm.vehicleCheckpointWarning.violationType" :disabled="!isEditing" placeholder="请输入不规范通行类型" />
                </el-form-item>
                <el-form-item label="不规范通行次数：" prop="vehicleCheckpointWarning.violationCount" class="form-item">
                  <el-input v-model="editForm.vehicleCheckpointWarning.violationCount" type="number" :disabled="!isEditing" placeholder="请输入不规范通行次数" />
                </el-form-item>
                <el-form-item label="发生时间：" prop="vehicleCheckpointWarning.occurrenceTime" class="form-item">
                  <el-date-picker
                    v-model="editForm.vehicleCheckpointWarning.occurrenceTime"
                    type="datetime"
                    placeholder="选择发生时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :disabled="!isEditing"
                  />
                </el-form-item>
                <el-form-item label="备注：" prop="vehicleCheckpointWarning.remarks" class="form-item">
                  <el-input v-model="editForm.vehicleCheckpointWarning.remarks" type="textarea" :rows="3" :disabled="!isEditing" placeholder="请输入备注信息" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
          <el-button v-if="!isEditing" type="primary" @click="enableEditing">编辑</el-button>
          <el-button v-if="isEditing" @click="cancelEditing">取消</el-button>
          <el-button v-if="isEditing" type="primary" @click="handleSave">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 地图选择器弹窗 -->
    <el-dialog
      v-model="mapSelectorVisible"
      title="选择地理坐标"
      width="65%"
      :destroy-on-close="true"
      :lock-scroll="false"
    >
      <div class="map-selector-container">
        <map-coordinate-selector
          :initial-lat="initialCoordinates.lat"
          :initial-lng="initialCoordinates.lng"
          @confirm="handleCoordinateConfirm"
          @cancel="mapSelectorVisible = false"
        />
      </div>
    </el-dialog>

    <!-- 附件查看弹窗 -->
    <el-dialog v-model="attachmentDialogVisible" title="查看附件" width="60%" destroy-on-close>
      <div v-if="attachmentList.length > 0">
        <div v-for="(file, index) in attachmentList" :key="index" class="attachment-item">
          <div class="file-info">
            <span class="file-name">{{ file.originalName }}</span>
            <span class="file-size">({{ (file.size / 1024 / 1024).toFixed(2) }}MB)</span>
          </div>
          <div class="file-actions">
            <el-button type="primary" size="small" @click="downloadFile(file)">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button v-if="checkFileSuffix(file.fileSuffix)" type="success" size="small" @click="previewImage(file)">
              预览
            </el-button>
            <el-button v-if="isEditing" type="danger" size="small" @click="deleteFile(file)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </div>
      <div v-else class="no-attachment">
        <el-empty description="暂无附件" />
      </div>
    </el-dialog>

    <!-- 图片预览组件 -->
    <ImagePreview v-if="previewVisible" :images="previewImages" @close="previewVisible = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineAsyncComponent, reactive, getCurrentInstance, type ComponentInternalInstance, toRefs, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listData } from '@/api/system/dict/data';
import { DictDataQuery } from '@/api/system/dict/data/types';
import {
  getCheckpointWarningList,
  getCheckpointWarningDetail,
  updateCheckpointWarning,
  addCheckpointWarning,
  deleteCheckpointWarning,
  uplodadWarningFile
} from '@/api/bridge/traffic/vehicle';
import dayjs from 'dayjs';
import { useRouter, useRoute } from 'vue-router';
import { addNotice, publishNotice } from '@/api/bridge/command/notice';
import useUserStore from '@/store/modules/user';
import useDictStore from '@/store/modules/dict';
import { addEmergencyEvent } from '@/api/bridge/command/event';
import { InfoFilled, Warning, Bell, RefreshRight, Document, Upload, Download, Delete, Plus, Location, Edit, Search } from '@element-plus/icons-vue';
import { listByIds, delOss } from '@/api/system/oss';
import ImagePreview from '@/components/ImagePreview/index.vue';
import { globalHeaders } from '@/utils/request';
import { listDept } from '@/api/system/dept';
import { getUserInfoList } from '@/api/bridge/user/user';
import { getBridgeList } from '@/api/bridge/bisc/bridge';
import MapCoordinateSelector from '@/components/MapCoordinateSelector/index.vue';
import DeptTreeSelector from '@/components/DeptTreeSelector/index.vue';
import Pagination from '@/components/Pagination/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { yjcl } = toRefs<any>(proxy?.useDict('yjcl'));

const router = useRouter();
const route = useRoute();

// 表格数据
const tableData = ref([]);
const total = ref(0);
const showSearch = ref(true);
const selectedRows = ref([]);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  checkpointId: '',
  congestionLevel: null,
  status: null
});

// 详情弹窗
const detailVisible = ref(false);
const isEditing = ref(false);
const activeTab = ref('basic');
const editFormRef = ref();
const currentRow = ref<any>({});

// 表单数据结构
const editForm = ref({
  earlyWarningInfo: {
    title: '',
    warnLevel: '',
    navigationClosure: 0,
    status: 1,
    result: '',
    fileId: '',
    bridgeId: null,
    lat: '30.563858',
    lon: '121.573157',
    location: ''
  },
  vehicleCheckpointWarning: {
    checkpointId: '',
    congestionLevel: 1,
    avgSpeed: null,
    speedLimit: null,
    violationType: '',
    violationCount: null,
    occurrenceTime: '',
    remarks: ''
  }
});

const editRules = ref({
  'earlyWarningInfo.title': [{ required: true, message: '请输入预警标题', trigger: 'blur' }],
  'earlyWarningInfo.warnLevel': [{ required: true, message: '请选择预警级别', trigger: 'change' }],
  'earlyWarningInfo.location': [{ required: true, message: '请输入发生地点', trigger: 'blur' }],
  'vehicleCheckpointWarning.checkpointId': [{ required: true, message: '请输入卡口ID', trigger: 'blur' }],
  'vehicleCheckpointWarning.congestionLevel': [{ required: true, message: '请选择拥堵级别', trigger: 'change' }],
  'vehicleCheckpointWarning.occurrenceTime': [{ required: true, message: '请选择发生时间', trigger: 'change' }]
});

// 附件相关
const attachmentList = ref<any[]>([]);
const attachmentDialogVisible = ref(false);
const fileList = ref<any[]>([]);
const uploadRef = ref();
const uploadAction = ref(`${import.meta.env.VITE_APP_BASE_API}/system/oss/upload`);
const uploadHeaders = ref(globalHeaders());
const previewVisible = ref(false);
const previewImages = ref<string[]>([]);

// 字典查询参数
const DictQueryParams = ref<DictDataQuery>({
  dictType: '',
  pageNum: 1,
  pageSize: 10,
  dictName: '',
  dictLabel: ''
});

// 字典数据
const warnLevelList = ref<any[]>([]);
const bridgeList = ref<any[]>([]);

// 地图选择器相关
const mapSelectorVisible = ref(false);
const initialCoordinates = ref({
  lat: 30.5,
  lng: 121.9
});

// 获取预警级别字典
const getWarnLevelDict = async () => {
  DictQueryParams.value.dictType = 'warn_level';
  const res = await listData(DictQueryParams.value);
  warnLevelList.value = res.rows;
};

// 获取桥梁列表
const fetchBridgeList = async () => {
  try {
    const bridgeRes = await getBridgeList({ isWarn: 1 });
    bridgeList.value = bridgeRes.rows;
  } catch (error) {
    console.error('获取桥梁列表失败:', error);
    ElMessage.error('获取桥梁列表失败');
  }
};

onMounted(() => {
  getWarnLevelDict();
  fetchBridgeList();
  getList();
});

// 获取列表数据
const getList = async () => {
  try {
    const res = await getCheckpointWarningList(queryParams);
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    ElMessage.error('获取数据失败');
  }
};

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  Object.assign(queryParams, {
    checkpointId: '',
    congestionLevel: null,
    violationType: ''
  });
  getList();
};

// 查看详情
const viewDetails = async (row) => {
  detailVisible.value = true;
  isEditing.value = false;
  activeTab.value = 'basic';

  try {
    const res = await getCheckpointWarningDetail(row.vehicleCheckpointWarning?.id || row.id);
    const detailData = res.data || res;

    // 设置表单数据
    editForm.value = {
      earlyWarningInfo: {
        id: detailData.earlyWarningInfo?.id || '',
        title: detailData.earlyWarningInfo?.title || '',
        warnLevel: detailData.earlyWarningInfo?.warnLevel || '',
        navigationClosure: detailData.earlyWarningInfo?.navigationClosure || 0,
        status: detailData.earlyWarningInfo?.status || 1,
        result: detailData.earlyWarningInfo?.result || '',
        fileId: detailData.earlyWarningInfo?.fileId || '',
        bridgeId: detailData.earlyWarningInfo?.bridgeId || null,
        lat: detailData.earlyWarningInfo?.lat || '30.563858',
        lon: detailData.earlyWarningInfo?.lon || '121.573157',
        location: detailData.earlyWarningInfo?.location || ''
      },
      vehicleCheckpointWarning: {
        id: detailData.vehicleCheckpointWarning?.id || '',
        checkpointId: detailData.vehicleCheckpointWarning?.checkpointId || '',
        congestionLevel: detailData.vehicleCheckpointWarning?.congestionLevel || 1,
        avgSpeed: detailData.vehicleCheckpointWarning?.avgSpeed || null,
        speedLimit: detailData.vehicleCheckpointWarning?.speedLimit || null,
        violationType: detailData.vehicleCheckpointWarning?.violationType || '',
        violationCount: detailData.vehicleCheckpointWarning?.violationCount || null,
        occurrenceTime: detailData.vehicleCheckpointWarning?.occurrenceTime || '',
        remarks: detailData.vehicleCheckpointWarning?.remarks || ''
      }
    };

    currentRow.value = detailData;

    // 处理附件列表
    if (detailData.earlyWarningInfo?.fileId) {
      try {
        const fileRes = await listByIds(detailData.earlyWarningInfo.fileId);
        fileList.value = fileRes.data.map((file: any) => ({
          name: file.originalName,
          url: file.url,
          uid: file.ossId
        }));
      } catch (error) {
        console.error('获取附件列表失败:', error);
      }
    } else {
      fileList.value = [];
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error('获取详情失败');
  }
};

// 新增
const handleAdd = () => {
  detailVisible.value = true;
  isEditing.value = true;
  activeTab.value = 'basic';
  editForm.value = {
    earlyWarningInfo: {
      title: '',
      warnLevel: '',
      navigationClosure: 0,
      status: 1,
      result: '',
      fileId: '',
      bridgeId: null,
      lat: '30.563858',
      lon: '121.573157',
      location: ''
    },
    vehicleCheckpointWarning: {
      checkpointId: '',
      congestionLevel: 1,
      avgSpeed: null,
      speedLimit: null,
      violationType: '',
      violationCount: null,
      occurrenceTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      remarks: ''
    }
  };
  fileList.value = [];
};

// 保存
const handleSave = async () => {
  if (!editFormRef.value) return;

  try {
    await editFormRef.value.validate();

    const submitData = {
      earlyWarningInfo: {
        ...editForm.value.earlyWarningInfo,
        fileId: editForm.value.earlyWarningInfo.fileId
      },
      vehicleCheckpointWarning: editForm.value.vehicleCheckpointWarning
    };

    if (editForm.value.earlyWarningInfo.id) {
      await updateCheckpointWarning(submitData);
      ElMessage.success('更新成功');
    } else {
      await addCheckpointWarning(submitData);
      ElMessage.success('新增成功');
    }

    detailVisible.value = false;
    isEditing.value = false;
    getList();
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  }
};

// 删除
const handleDelete = async (row) => {
  ElMessageBox.confirm('确定要删除该记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteCheckpointWarning(row.vehicleCheckpointWarning?.id || row.id);
      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      ElMessage.error('删除失败');
    }
  });
};

// 批量删除
const batchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录');
    return;
  }
  const ids = selectedRows.value.map(item => item.vehicleCheckpointWarning?.id || item.id).join(',');
  ElMessageBox.confirm(`确定要删除选中的${selectedRows.value.length}条记录吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteCheckpointWarning(ids);
      ElMessage.success('删除成功');
      getList();
      selectedRows.value = [];
    } catch (error) {
      ElMessage.error('删除失败');
    }
  });
};

// 同步
const handlePushNotice = async (row) => {
  try {
    // await syncVehicleCheckpointWarning(row.id);
    ElMessage.success('同步成功');
    getList();
  } catch (error) {
    ElMessage.error('同步失败');
  }
};

// 选择变化
const handleSelectionChange = (val) => {
  selectedRows.value = val;
};

// 获取拥堵级别标签
const getCongestionLevelLabel = (level) => {
  const levelMap = {
    1: '畅通',
    2: '基本畅通',
    3: '轻度拥堵',
    4: '中度拥堵',
    5: '严重拥堵'
  };
  return levelMap[level] || '-';
};

// 获取状态类型
const getStatusType = (status) => {
  const typeMap = {
    1: 'info',
    2: 'warning',
    3: 'success'
  };
  return typeMap[status] || 'default';
};

// 获取状态标签
const getStatusLabel = (status) => {
  const labelMap = {
    1: '未处理',
    2: '处理中',
    3: '已处理'
  };
  return labelMap[status] || '-';
};

// 获取预警图片
const getAlertImage = (level: string) => {
  return new URL(`/src/assets/svg/alert${level}.svg`, import.meta.url).href;
};

// 处理附件查看
const handleViewAttachment = async (row) => {
  const res = await getCheckpointWarningDetail(row.vehicleCheckpointWarning.id);
  currentRow.value = res.data;
  if (row.earlyWarningInfo?.fileId) {
    try {
      const res = await listByIds(row.earlyWarningInfo.fileId);
      attachmentList.value = res.data;
      attachmentDialogVisible.value = true;
    } catch (error) {
      console.error('获取附件列表失败:', error);
      ElMessage.error('获取附件列表失败');
    }
  } else {
    ElMessage.warning('暂无附件');
  }
};

// 处理附件上传
const handleUploadAttachment = (row) => {
  currentRow.value = row;
  // 这里可以添加上传附件的逻辑
  ElMessage.info('上传附件功能待实现');
};

// 研判功能
const handleJudgment = (row) => {
  ElMessage.info('研判功能待实现');
};

// 计算属性：格式化坐标显示
const formattedCoordinates = computed(() => {
  if (editForm.value.earlyWarningInfo?.lat && editForm.value.earlyWarningInfo?.lon) {
    return `${editForm.value.earlyWarningInfo.lat},${editForm.value.earlyWarningInfo.lon}`;
  }
  return '';
});

// 打开地图选择器
const openMapSelector = () => {
  if (editForm.value.earlyWarningInfo?.lat && editForm.value.earlyWarningInfo?.lon) {
    initialCoordinates.value = {
      lat: parseFloat(editForm.value.earlyWarningInfo.lat),
      lng: parseFloat(editForm.value.earlyWarningInfo.lon)
    };
  }
  mapSelectorVisible.value = true;
};

// 处理坐标确认
const handleCoordinateConfirm = (coordinates) => {
  editForm.value.earlyWarningInfo.lat = coordinates.lat.toString();
  editForm.value.earlyWarningInfo.lon = coordinates.lng.toString();
  mapSelectorVisible.value = false;
};

// 状态控制
const originalData = ref<any>({});

const enableEditing = () => {
  isEditing.value = true;
  originalData.value = JSON.parse(JSON.stringify(editForm.value));
};

const cancelEditing = () => {
  isEditing.value = false;
  editForm.value = JSON.parse(JSON.stringify(originalData.value));
};

// 文件上传相关
const beforeUpload = (file: any) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isValidType) {
    ElMessage.error('只能上传 JPG/PNG/PDF 格式的文件!');
    return false;
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

const handleUploadSuccess = (response: any, file: any) => {
  if (response.code === 200) {
    const fileIds = editForm.value.earlyWarningInfo.fileId ? editForm.value.earlyWarningInfo.fileId.split(',') : [];
    fileIds.push(response.data.ossId);
    editForm.value.earlyWarningInfo.fileId = fileIds.join(',');
    ElMessage.success('文件上传成功');
  } else {
    ElMessage.error('文件上传失败');
  }
};

const handleRemove = (file: any) => {
  if (file.response && file.response.data) {
    const fileIds = editForm.value.earlyWarningInfo.fileId ? editForm.value.earlyWarningInfo.fileId.split(',') : [];
    const index = fileIds.indexOf(file.response.data.ossId);
    if (index > -1) {
      fileIds.splice(index, 1);
      editForm.value.earlyWarningInfo.fileId = fileIds.join(',');
    }
  }
};

// 文件相关方法
const checkFileSuffix = (fileSuffix: string | string[]) => {
  const arr = ['.png', '.jpg', '.jpeg'];
  const suffixArray = Array.isArray(fileSuffix) ? fileSuffix : [fileSuffix];
  return suffixArray.some((suffix) => arr.includes(suffix.toLowerCase()));
};

const downloadFile = async (file: any) => {
  try {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.originalName;
    link.click();
  } catch (error) {
    ElMessage.error('下载失败');
  }
};

const previewImage = (file: any) => {
  previewImages.value = [file.url];
  previewVisible.value = true;
};

const deleteFile = async (file: any) => {
  try {
    await delOss(file.ossId);
    const index = attachmentList.value.findIndex(item => item.ossId === file.ossId);
    if (index > -1) {
      attachmentList.value.splice(index, 1);
    }
    ElMessage.success('删除成功');
  } catch (error) {
    ElMessage.error('删除失败');
  }
};
</script>

<style scoped lang="scss">
$bg-color-page: var(--el-bg-color-page);
$bg-color-overlay: var(--el-bg-color-overlay);
$box-shadow-light: var(--el-box-shadow-light);
$box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
$border-radius: 4px;
$border-radius-dialog: 8px;
$border-color-light: var(--el-border-color-light);
$color-primary: var(--el-color-primary);
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$deep-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
$header-padding: 15px 20px;
$general-padding: 20px;
$footer-padding: 10px 20px;
$divider-color: #f0f0f0;

.list-page {
  padding: 20px;
  background: $bg-color-page;
  border-radius: $border-radius;
  box-shadow: $box-shadow-light;
}

.filter-area {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  background: $bg-color-overlay;
  padding: 16px;
  border-radius: $border-radius;
}

.search-input {
  width: 300px;
  margin-left: auto;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: $bg-color-overlay;

  :deep(th) {
    background: $bg-color-page !important;
  }
}

// 新增卡片标题样式
.card-header {
  margin-bottom: 25px;

  .header-title {
    font-size: 20px;
    color: var(--el-text-color-primary);
    font-weight: 600;
  }

  .header-line {
    height: 3px;
    background: linear-gradient(90deg, #409eff 30%, transparent 100%);
    margin-top: 12px;
  }
}

.el-dialog {
  border-radius: $border-radius-dialog;
  box-shadow: $box-shadow;

  &__body {
    padding: 24px;
    background: $bg-color-overlay;
  }

  &__header {
    .el-dialog__title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 24px;
        z-index: 1000;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

        &:hover {
          color: $color-primary;
        }
      }
    }
  }

  &__footer {
    padding: 10px 20px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
