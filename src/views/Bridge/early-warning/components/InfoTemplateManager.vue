<template>
  <div class="info-template-manager">
    <div class="top-actions">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>新增模板
      </el-button>
      <el-input
        v-model="queryParams.templateName"
        placeholder="请输入模板名称"
        clearable
        class="search-input"
        @keyup.enter="handleQuery"
      >
        <template #append>
          <el-button @click="handleQuery">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 表格区域 -->
    <el-table :data="templateList" border style="width: 100%; margin-top: 15px">
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="templateName" label="模板名称" min-width="150" show-overflow-tooltip />
      <el-table-column prop="templateContent" label="模板内容" min-width="250" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" width="180" align="center" />
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template #default="scope">
          <el-button size="small" type="primary" link @click="handleEdit(scope.row)">
            编辑
          </el-button>
          <el-button size="small" type="danger" link @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getTemplateList"
    />

    <!-- 新增/编辑模板对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增信息模板' : '编辑信息模板'"
      width="600px"
      destroy-on-close
      append-to-body
    >
      <el-form ref="templateFormRef" :model="templateForm" :rules="templateRules" label-width="100px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="templateForm.templateName" placeholder="请输入模板名称" />
        </el-form-item>
        
        <el-form-item label="模板内容" prop="templateContent">
          <el-input
            v-model="templateForm.templateContent"
            type="textarea"
            :rows="6"
            placeholder="请输入模板内容，可使用变量如${location}、${accidentType}等"
          />
          <div class="template-help">
            <div class="help-title">支持的变量:</div>
            <el-tag
              v-for="item in templateVariables"
              :key="item.name"
              class="variable-tag"
              @click="insertVariable(item.name)"
            >
              ${{'{'}}{{item.name}}{{'}'}} - {{item.desc}}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="预览效果">
          <div class="template-preview">
            <div class="preview-title">模板预览</div>
            <div class="preview-content">{{previewContent}}</div>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Search } from '@element-plus/icons-vue';
import { 
  addInfoTemplate, 
  updateInfoTemplate, 
  deleteInfoTemplate, 
  getInfoTemplateList, 
  getInfoTemplateDetail 
} from '@/api/bridge/command/notice';

// 定义属性
const props = defineProps({
  onSelect: {
    type: Function,
    default: null
  },
  mode: {
    type: String,
    default: 'manage' // 'manage' or 'select'
  }
});

// 定义事件
const emit = defineEmits(['select', 'close']);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  templateName: ''
});

// 模板列表数据
const templateList = ref([]);
const total = ref(0);
const loading = ref(false);

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' or 'edit'
const templateFormRef = ref();

// 表单对象
const templateForm = ref({
  id: undefined,
  templateName: '',
  templateContent: ''
});

// 表单验证规则
const templateRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到50个字符之间', trigger: 'blur' }
  ],
  templateContent: [
    { required: true, message: '请输入模板内容', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在5到500个字符之间', trigger: 'blur' }
  ]
};

// 模板变量定义
const templateVariables = [
  { name: 'location', desc: '发生地点' },
  { name: 'accidentType', desc: '事故类型' },
  { name: 'blockageCause', desc: '发生原因' },
  { name: 'occurrenceTime', desc: '发生时间' },
  { name: 'recoveryTime', desc: '预计恢复时间' },
  { name: 'warnLevel', desc: '预警级别' },
  { name: 'bridgeName', desc: '桥梁名称' }
];

// 模板预览内容
const previewContent = computed(() => {
  let content = templateForm.value.templateContent;
  if (!content) return '无预览内容';
  
  // 替换模板变量为示例值
  content = content.replace(/\${location}/g, '杭州湾跨海大桥南端');
  content = content.replace(/\${accidentType}/g, '追尾事故');
  content = content.replace(/\${blockageCause}/g, '车辆碰撞');
  content = content.replace(/\${occurrenceTime}/g, '2023-10-15 08:30');
  content = content.replace(/\${recoveryTime}/g, '2小时');
  content = content.replace(/\${warnLevel}/g, '3级');
  content = content.replace(/\${bridgeName}/g, '杭州湾跨海大桥');
  
  return content;
});

// 获取模板列表
const getTemplateList = async () => {
  loading.value = true;
  try {
    const res = await getInfoTemplateList(queryParams);
    if (res.code === 200) {
      templateList.value = res.rows;
      total.value = res.total;
    } else {
      ElMessage.error(res.msg || '获取模板列表失败');
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
    ElMessage.error('获取模板列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getTemplateList();
};

// 重置查询
const resetQuery = () => {
  queryParams.templateName = '';
  queryParams.pageNum = 1;
  getTemplateList();
};

// 新增按钮点击
const handleAdd = () => {
  dialogType.value = 'add';
  templateForm.value = {
    id: undefined,
    templateName: '',
    templateContent: ''
  };
  dialogVisible.value = true;
};

// 编辑按钮点击
const handleEdit = async (row) => {
  dialogType.value = 'edit';
  try {
    const res = await getInfoTemplateDetail(row.id);
    if (res.code === 200) {
      templateForm.value = res.data;
    } else {
      ElMessage.error(res.msg || '获取模板详情失败');
    }
  } catch (error) {
    console.error('获取模板详情失败:', error);
    ElMessage.error('获取模板详情失败');
  }
  dialogVisible.value = true;
};

// 删除按钮点击
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除模板 "${row.templateName}" 吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteInfoTemplate(row.id);
      if (res.code === 200) {
        ElMessage.success('删除成功');
        getTemplateList();
      } else {
        ElMessage.error(res.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
};

// 提交表单
const submitForm = async () => {
  if (!templateFormRef.value) return;
  
  await templateFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const apiFunc = dialogType.value === 'add' ? addInfoTemplate : updateInfoTemplate;
        const res = await apiFunc(templateForm.value);
        
        if (res.code === 200) {
          ElMessage.success(dialogType.value === 'add' ? '新增成功' : '修改成功');
          dialogVisible.value = false;
          getTemplateList();
        } else {
          ElMessage.error(res.msg || (dialogType.value === 'add' ? '新增失败' : '修改失败'));
        }
      } catch (error) {
        console.error(dialogType.value === 'add' ? '新增失败:' : '修改失败:', error);
        ElMessage.error(dialogType.value === 'add' ? '新增失败' : '修改失败');
      }
    }
  });
};

// 插入变量到模板内容
const insertVariable = (varName) => {
  const textarea = document.querySelector('.el-textarea__inner');
  const cursorPos = textarea?.selectionStart || 0;
  const textBefore = templateForm.value.templateContent.substring(0, cursorPos);
  const textAfter = templateForm.value.templateContent.substring(cursorPos);
  templateForm.value.templateContent = `${textBefore}\${${varName}}${textAfter}`;
};

// 模板选择方法（仅在select模式下可用）
const handleSelect = (row) => {
  if (props.mode === 'select') {
    emit('select', row);
  }
};

// 生命周期钩子
onMounted(() => {
  getTemplateList();
});
</script>

<style scoped lang="scss">
.info-template-manager {
  padding: 16px;
}

.top-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  
  .search-input {
    width: 300px;
  }
}

.template-help {
  margin-top: 12px;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  
  .help-title {
    margin-bottom: 8px;
    font-weight: 500;
    color: #606266;
  }
  
  .variable-tag {
    margin-right: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    
    &:hover {
      color: #409EFF;
      border-color: #409EFF;
    }
  }
}

.template-preview {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
  
  .preview-title {
    font-weight: 500;
    color: #606266;
    margin-bottom: 10px;
  }
  
  .preview-content {
    color: #303133;
    line-height: 1.6;
    white-space: pre-wrap;
    word-break: break-word;
  }
}
</style> 