<template>
  <div class="classification-container">
    <!-- 标题区域 -->
    <div class="header-section">
      <h1 class="title-font">预警信息分类门户</h1>
      <div class="title-divider"></div>
    </div>

    <!-- 分类导航网格 -->
    <el-row :gutter="30" class="grid-container">
      <el-col v-for="(item, index) in categoryList" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="grid-item">
        <template v-if="item.title !== '其他类型预警'">
          <router-link :to="item.path" class="category-card" @mouseenter.native="hoverIndex = index" @mouseleave.native="hoverIndex = -1">
            <div v-if="item.key && pendingCounts[item.key] > 0" class="pending-badge">
              {{ pendingCounts[item.key] }}
            </div>

            <div class="card-content">
              <el-icon :size="48" :class="['card-icon', { 'hover-effect': hoverIndex === index }]">
                <component :is="item.icon" />
              </el-icon>
              <h3 class="category-title">{{ item.title }}</h3>
            </div>
          </router-link>
        </template>
        <div v-else class="category-card disabled-card">
          <div class="card-content">
            <el-icon :size="48" class="card-icon disabled-icon">
              <component :is="item.icon" />
            </el-icon>
            <h3 class="category-title disabled-title">{{ item.title }}</h3>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Cloudy, Promotion, Picture, Monitor, MagicStick, Warning, Ship } from '@element-plus/icons-vue';

// 导入所有预警类型的API
import { getTrafficAccidentWarningList } from '@/api/bridge/traffic/traffic';
import { getEnvironmentalPollutionWarningList } from '@/api/bridge/traffic/environmental';
import { getGeologyWarningList } from '@/api/bridge/traffic/geology';
import { getPublicHealthWarningList } from '@/api/bridge/traffic/publicHealth';
import { getMeteorologicalWarningList } from '@/api/bridge/traffic/weather';
import { getShipWarningList } from '@/api/bridge/traffic/ship';

const hoverIndex = ref(-1);

// 添加未处理数量统计对象
const pendingCounts = ref({
  traffic: 0,
  environmental: 0,
  geology: 0,
  health: 0,
  weather: 0,
  ship: 0
});

// 更新分类列表，添加key属性关联到未处理计数
const categoryList = [
  {
    title: '交通事故预警',
    icon: Warning,
    path: '/early-warning/group/accident-effct',
    key: 'traffic'
  },
  {
    title: '环境污染预警',
    icon: MagicStick,
    path: '/early-warning/group/environmental',
    key: 'environmental'
  },
  {
    title: '地质灾害预警',
    icon: Picture,
    path: '/early-warning/group/geology',
    key: 'geology'
  },
  {
    title: '公共卫生灾害预警',
    icon: Monitor,
    path: '/early-warning/group/health',
    key: 'health'
  },
  {
    title: '气象灾害预警',
    icon: Cloudy,
    path: '/early-warning/group/weather',
    key: 'weather'
  },
  {
    title: '船舶预警',
    icon: Ship,
    path: '/early-warning/group/ship',
    key: 'ship'
  }
  // {
  //   title: '其他类型预警',
  //   icon: Promotion,
  //   path: '/early-warning/group/otners'
  // }
];

// 获取交通事故预警未处理数量
const fetchTrafficPendingCount = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100000,
      status: 0 // 未处理状态
    };
    const res = await getTrafficAccidentWarningList(params);
    pendingCounts.value.traffic = res.total || 0;
  } catch (error) {
    console.error('获取交通事故预警未处理数量失败:', error);
  }
};

// 获取环境污染预警未处理数量
const fetchEnvironmentalPendingCount = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100000,
      status: 0 // 未处理状态
    };
    const res = await getEnvironmentalPollutionWarningList(params);
    pendingCounts.value.environmental = res.total || 0;
  } catch (error) {
    console.error('获取环境污染预警未处理数量失败:', error);
  }
};

// 获取地质灾害预警未处理数量
const fetchGeologyPendingCount = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100000,
      status: 0 // 未处理状态
    };
    const res = await getGeologyWarningList(params);
    pendingCounts.value.geology = res.total || 0;
  } catch (error) {
    console.error('获取地质灾害预警未处理数量失败:', error);
  }
};

// 获取公共卫生预警未处理数量
const fetchHealthPendingCount = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100000,
      status: 0 // 未处理状态
    };
    const res = await getPublicHealthWarningList(params);
    pendingCounts.value.health = res.total || 0;
  } catch (error) {
    console.error('获取公共卫生预警未处理数量失败:', error);
  }
};

// 获取气象灾害预警未处理数量
const fetchWeatherPendingCount = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100000,
      status: 0 // 未处理状态
    };
    const res = await getMeteorologicalWarningList(params);
    pendingCounts.value.weather = res.total || 0;
  } catch (error) {
    console.error('获取气象灾害预警未处理数量失败:', error);
  }
};

// 获取船舶预警未处理数量
const fetchShipPendingCount = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100000,
      status: 0 // 未处理状态
    };
    const res = await getShipWarningList(params);
    pendingCounts.value.ship = res.total || 0;
  } catch (error) {
    console.error('获取船舶预警未处理数量失败:', error);
  }
};

// 组件挂载时获取所有未处理预警数量
onMounted(async () => {
  // 并行请求所有未处理预警数量
  await Promise.all([
    fetchTrafficPendingCount(),
    fetchEnvironmentalPendingCount(),
    fetchGeologyPendingCount(),
    fetchHealthPendingCount(),
    fetchWeatherPendingCount(),
    fetchShipPendingCount()
  ]);
});
</script>

<style scoped lang="scss">
.classification-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  padding: 2rem;
  background: var(--el-bg-color-page);
}

.header-section {
  text-align: center;
  margin-bottom: 3rem;
}

.title-font {
  font-size: 2.5rem;
  color: var(--el-text-color-primary);
  font-weight: 600;
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.title-divider {
  width: 80px;
  height: 3px;
  background: #409eff;
  margin: 0 auto;
  border-radius: 2px;
}

.grid-container {
  width: 100%;
  margin: 0 auto;
}

.grid-item {
  padding: 1rem;
  transition: transform 0.3s ease;
}

.category-card {
  margin: 0 auto;
  display: block;
  max-width: 80%;
  background: var(--el-bg-color-overlay);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  text-decoration: none;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--el-border-color-light);
  position: relative; /* 添加相对定位用于徽章定位 */

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    background: var(--el-color-primary-light-9);
  }
}

/* 添加未处理计数徽章样式 */
.pending-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  min-width: 22px;
  height: 22px;
  border-radius: 11px;
  background-color: #f56c6c;
  color: white;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.card-content {
  text-align: center;
}

.card-icon {
  color: #409eff;
  transition: all 0.3s ease;
}

.category-card:hover .card-icon {
  color: white;
  transform: scale(1.1);
}

.category-title {
  margin-top: 1.5rem;
  color: var(--el-text-color-primary);
  font-size: 1.2rem;
  transition: color 0.3s ease;
}

.category-card:hover .category-title {
  color: white;
}

.hover-effect {
  animation: icon-float 1.5s ease-in-out infinite;
}

@keyframes icon-float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.disabled-card {
  cursor: not-allowed !important;
  opacity: 0.6;
  background: var(--el-disabled-bg-color) !important;

  &:hover {
    transform: none !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    background: var(--el-disabled-bg-color) !important;
  }
}

.disabled-icon {
  color: var(--el-text-color-disabled) !important;
  animation: none !important;
}

.disabled-title {
  color: var(--el-text-color-disabled) !important;
}
</style>
