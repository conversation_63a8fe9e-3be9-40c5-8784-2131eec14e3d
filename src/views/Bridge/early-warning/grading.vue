<template>
  <div class="classification-container">
    <!-- 标题区域 -->
    <div class="header-section">
      <h1 class="title-font">预警分类管理</h1>
      <div class="title-divider"></div>
    </div>

    <!-- 分类导航网格 -->
    <el-row :gutter="30" class="grid-container">
      <el-col v-for="(item, index) in categoryList" :key="index" :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="grid-item">
        <template v-if="item.title !== '其他类型预警'">
          <router-link :to="item.path" class="category-card" @mouseenter.native="hoverIndex = index" @mouseleave.native="hoverIndex = -1">
            <div class="card-content">
              <el-icon :size="48" :class="['card-icon', { 'hover-effect': hoverIndex === index }]">
                <component :is="item.icon" />
              </el-icon>
              <h3 class="category-title">{{ item.title }}</h3>
            </div>
          </router-link>
        </template>
        <div v-else class="category-card disabled-card">
          <div class="card-content">
            <el-icon :size="48" class="card-icon disabled-icon">
              <component :is="item.icon" />
            </el-icon>
            <h3 class="category-title disabled-title">{{ item.title }}</h3>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Cloudy, Promotion, Picture, Monitor, MagicStick, Warning, Ship } from '@element-plus/icons-vue';

const hoverIndex = ref(-1);

const categoryList = [
  {
    title: '交通事故预警',
    icon: Warning,
    path: '/early-warning/group/accident-effct'
  },
  {
    title: '环境污染预警',
    icon: MagicStick,
    path: '/early-warning/group/environmental'
  },
  {
    title: '地质灾害预警',
    icon: Picture,
    path: '/early-warning/group/geology'
  },
  {
    title: '公共卫生灾害预警',
    icon: Monitor,
    path: '/early-warning/group/health'
  },
  {
    title: '气象灾害预警',
    icon: Cloudy,
    path: '/early-warning/group/weather'
  },
  {
    title: '船舶预警',
    icon: Ship,
    path: '/early-warning/group/ship'
  }
  // {
  //   title: '其他类型预警',
  //   icon: Promotion,
  //   path: '/early-warning/group/otners'
  // }
];
</script>

<style scoped lang="scss">
.classification-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  padding: 2rem;
  background: var(--el-bg-color-page);
}

.header-section {
  text-align: center;
  margin-bottom: 3rem;
}

.title-font {
  font-size: 2.5rem;
  color: var(--el-text-color-primary);
  font-weight: 600;
  letter-spacing: 2px;
  margin-bottom: 1rem;
}

.title-divider {
  width: 80px;
  height: 3px;
  background: #409eff;
  margin: 0 auto;
  border-radius: 2px;
}

.grid-container {
  width: 100%;
  margin: 0 auto;
}

.grid-item {
  padding: 1rem;
  transition: transform 0.3s ease;
}

.category-card {
  margin: 0 auto;
  display: block;
  max-width: 80%;
  background: var(--el-bg-color-overlay);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  text-decoration: none;
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--el-border-color-light);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    background: var(--el-color-primary-light-9);
  }
}

.card-content {
  text-align: center;
}

.card-icon {
  color: #409eff;
  transition: all 0.3s ease;
}

.category-card:hover .card-icon {
  color: white;
  transform: scale(1.1);
}

.category-title {
  margin-top: 1.5rem;
  color: var(--el-text-color-primary);
  font-size: 1.2rem;
  transition: color 0.3s ease;
}

.category-card:hover .category-title {
  color: white;
}

.hover-effect {
  animation: icon-float 1.5s ease-in-out infinite;
}

@keyframes icon-float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.disabled-card {
  cursor: not-allowed !important;
  opacity: 0.6;
  background: var(--el-disabled-bg-color) !important;

  &:hover {
    transform: none !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    background: var(--el-disabled-bg-color) !important;
  }
}

.disabled-icon {
  color: var(--el-text-color-disabled) !important;
  animation: none !important;
}

.disabled-title {
  color: var(--el-text-color-disabled) !important;
}
</style>
