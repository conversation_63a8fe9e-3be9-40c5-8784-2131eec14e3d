<template>
  <div class="rule-container">
    <!-- 通用预警规则 -->
    <div v-if="ruleType === 'generic'">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
          <el-card shadow="hover">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form">
              <div class="left-items">
                <!-- 规则类型选择 -->
                  <el-radio-group v-model="ruleType" size="default" @change="handleRuleTypeChange" class="rule-type-toggle">
                    <el-radio-button label="generic">通用预警规则</el-radio-button>
                    <el-radio-button label="ship">船舶预警规则</el-radio-button>
                  </el-radio-group>
                <!-- <el-form-item label="规则名称" prop="title">
                  <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                </el-form-item> -->
              </div>
              <!-- 操作按钮区域 -->
              <div class="action-buttons">
                <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button>
                <el-button icon="Refresh" @click="resetQuery">刷新</el-button>
                <el-button type="danger" icon="Delete" @click="handleBatchDelete" :disabled="selectedRows.length === 0">删除</el-button>
              </div>
            </el-form>
          </el-card>
        </div>
      </transition>

      <!-- 规则列表 -->
      <el-card shadow="hover" class="table-card">
        <el-table v-loading="loading" :data="ruleList" border stripe @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="规则名称" prop="ruleName" :show-overflow-tooltip="true" min-width="150" />
          <el-table-column label="预警类型" prop="warningType" width="150" align="center">
            <template #default="scope">
              {{ getWarningTypeName(scope.row.warningType) }}
            </template>
          </el-table-column>
          <el-table-column label="触发条件" prop="triggerCondition" min-width="180" :show-overflow-tooltip="true" />
          <el-table-column label="预警级别" prop="warningLevel" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getWarningLevelType(scope.row.warningLevel)" effect="dark">
                {{ getWarningLevelName(scope.row.warningLevel) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="通知方式" prop="notificationMethod" width="120" align="center">
            <template #default="scope">
              <el-tooltip :content="getNotificationMethodText(scope.row.notificationMethod)" placement="top">
                <div class="notification-icons">
                  <template v-for="method in scope.row.notificationMethod" :key="method">
                    <el-icon v-if="method === 'email'">
                      <Message />
                    </el-icon>
                    <el-icon v-if="method === 'sms'">
                      <ChatLineRound />
                    </el-icon>
                    <el-icon v-if="method === 'system'">
                      <Bell />
                    </el-icon>
                  </template>
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="100" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.status" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="160" align="center">
            <template #default="scope">
              <span>{{ formatDate(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="scope">
              <el-button size="small" type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
              <el-button size="small" type="success" link @click="handleTest(scope.row)">测试</el-button>
              <el-button size="small" type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </el-card>

      <!-- 新增/编辑规则弹窗 -->
      <el-dialog v-model="dialogVisible" :title="dialogTitle" width="60%" destroy-on-close append-to-body>
        <el-form ref="ruleFormRef" :model="form" :rules="rules" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="规则名称" prop="ruleName">
                <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预警类型" prop="warningType">
                <el-select v-model="form.warningType" placeholder="请选择预警类型" style="width: 100%">
                  <el-option v-for="dict in warningTypeOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="触发条件" prop="triggerCondition">
            <el-input v-model="form.triggerCondition" placeholder="请输入触发条件描述" />
          </el-form-item>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="预警级别" prop="warningLevel">
                <el-select v-model="form.warningLevel" placeholder="请选择预警级别" style="width: 100%">
                  <el-option v-for="item in warningLevelOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规则状态" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio :label="0">启用</el-radio>
                  <el-radio :label="1">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="通知方式" prop="notificationMethod">
            <el-checkbox-group v-model="form.notificationMethod">
              <el-checkbox v-for="item in notificationMethodOptions" :key="item.dictValue" :label="item.dictValue">
                {{ item.dictLabel }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="阈值设置" prop="warnRuleThresholdList">
            <el-card shadow="never" class="threshold-card">
              <el-row v-for="(item, index) in form.warnRuleThresholdList" :key="index" :gutter="10" class="threshold-item">
                <el-col :span="8">
                  <el-form-item
                    :prop="`warnRuleThresholdList.${index}.parameterName`"
                    :rules="{ required: true, message: '请选择参数', trigger: 'change' }"
                  >
                    <el-select v-model="item.parameterName" placeholder="选择参数" style="width: 100%">
                      <el-option v-for="param in parameterOptions" :key="param.value" :label="param.label" :value="param.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :prop="`warnRuleThresholdList.${index}.minValue`">
                    <el-input v-model="item.minValue" placeholder="最小阈值" clearable>
                      <template #prepend>最小值</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item :prop="`warnRuleThresholdList.${index}.maxValue`">
                    <el-input v-model="item.maxValue" placeholder="最大阈值" clearable>
                      <template #prepend>最大值</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" class="threshold-actions">
                  <el-button type="danger" icon="Delete" circle @click="removeThreshold(index)" />
                </el-col>
              </el-row>
              <div class="threshold-help">
                <p>提示：</p>
                <ul>
                  <li>设置最小值表示参数应大于等于此值</li>
                  <li>设置最大值表示参数应小于等于此值</li>
                  <li>同时设置最小值和最大值表示参数应在此范围内</li>
                  <li>可以只设置最小值或只设置最大值</li>
                </ul>
              </div>
              <el-button type="primary" plain icon="Plus" @click="addThreshold">添加阈值条件</el-button>
            </el-card>
          </el-form-item>

          <el-form-item label="规则描述" prop="remark">
            <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入规则描述" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="dialogVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 测试规则弹窗 -->
      <el-dialog v-model="testDialogVisible" title="测试规则" width="600px" destroy-on-close>
        <el-form :model="testForm" label-width="150px">
          <el-form-item v-for="(item, index) in currentRule.warnRuleThresholdList" :key="index" :label="getParameterLabel(item.parameterName)">
            <el-input v-model="testForm[item.parameterName]" :placeholder="`请输入${getParameterLabel(item.parameterName)}测试值`" />
          </el-form-item>
        </el-form>
        <div class="test-result" v-if="testResult !== null">
          <el-alert
            :title="testResult ? '预警规则将触发' : '预警规则不会触发'"
            :type="testResult ? 'warning' : 'info'"
            :description="testResultDescription"
            show-icon
          />
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="runTest">运行测试</el-button>
            <el-button @click="testDialogVisible = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
    </div>

    <!-- 船舶预警规则 -->
    <div v-else-if="ruleType === 'ship'">
      <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
        <div v-show="showSearch" class="mb-[10px]">
          <el-card shadow="hover">
            <el-form ref="shipQueryFormRef" :model="shipQueryParams" :inline="true" class="search-form">
              <div class="left-items">
                <!-- 规则类型选择 -->
                <el-form-item>
                  <el-radio-group v-model="ruleType" size="default" @change="handleRuleTypeChange" class="rule-type-toggle">
                    <el-radio-button label="generic">通用预警规则</el-radio-button>
                    <el-radio-button label="ship">船舶预警规则</el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="通航环境" prop="envTypeName">
                  <el-select v-model="shipQueryParams.envType" placeholder="请选择通航环境" clearable>
                    <el-option v-for="item in envTypeOptions" :key="item.id" :label="item.name" :value="item.id" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleShipQuery">搜索</el-button>
                </el-form-item>
              </div>
              <!-- 操作按钮区域 -->
              <div class="action-buttons">
                <el-button type="primary" icon="Plus" @click="handleShipAdd">新增</el-button>
                <el-button icon="Refresh" @click="resetShipQuery">刷新</el-button>
                <el-button type="danger" icon="Delete" @click="handleShipBatchDelete" :disabled="selectedShipRows.length === 0">删除</el-button>
              </div>
            </el-form>
          </el-card>
        </div>
      </transition>

      <!-- 船舶规则列表 -->
      <el-card shadow="hover" class="table-card">
        <el-table v-loading="shipLoading" :data="shipRuleList" border stripe @selection-change="handleShipSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="序号" width="60" align="center">
            <template #default="{ $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="通航环境" prop="envTypeName" width="120" align="center" />
          <el-table-column label="预警" prop="warning" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.warning === 1 ? 'warning' : 'info'">{{ scope.row.warning === 1 ? '开启' : '关闭' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="AIS报文" prop="aisContent" min-width="120" align="center">
            <template #default="scope">
              <span>{{ scope.row.ais === 1 && scope.row.aisContent ? scope.row.aisContent : '未启用' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="喇叭喊话" prop="loudspeakerContent" min-width="120" align="center">
            <template #default="scope">
              <span>{{ scope.row.loudspeaker === 1 && scope.row.loudspeakerContent ? scope.row.loudspeakerContent : '未启用' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="VHF喊话" prop="vhfContent" min-width="120" align="center">
            <template #default="scope">
              <span>{{ scope.row.vhf === 1 && scope.row.vhfContent ? scope.row.vhfContent : '未启用' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="短信模板" prop="smsTemplate" min-width="120" align="center">
            <template #default="scope">
              <span>{{ scope.row.sms === 1 && scope.row.smsTemplate ? scope.row.smsTemplate : '未启用' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="记录方式" min-width="160" align="center">
            <template #default="scope">
              <div class="ship-record-methods">
                <el-tag v-if="scope.row.eventRecord === 1" size="small" type="success" class="m-1">事件记录</el-tag>
                <el-tag v-if="scope.row.trackRecord === 1" size="small" type="warning" class="m-1">航迹记录</el-tag>
                <el-tag v-if="scope.row.videoRecord === 1" size="small" type="info" class="m-1">视频记录</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="80" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="handleShipStatusChange(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="160" align="center">
            <template #default="scope">
              <span>{{ formatDate(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="scope">
              <el-button size="small" type="primary" link @click="handleShipEdit(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" link @click="handleShipDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="shipQueryParams.pageNum"
            v-model:page-size="shipQueryParams.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="shipTotal"
            @current-change="handleShipPageChange"
            @size-change="handleShipSizeChange"
          />
        </div>
      </el-card>

      <!-- 船舶预警规则弹窗 -->
      <el-dialog v-model="shipDialogVisible" :title="shipDialogTitle" width="60%" destroy-on-close append-to-body>
        <el-form ref="shipFormRef" :model="shipForm" :rules="shipRules" label-width="120px">
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="通航环境" prop="envType">
                <el-select v-model="shipForm.envType" placeholder="请选择通航环境" style="width: 100%">
                  <el-option v-for="item in envTypeOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="生效时间" prop="startDate">
                <el-date-picker
                  v-model="shipForm.startDate"
                  type="datetime"
                  placeholder="选择开始时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="失效时间" prop="endDate">
                <el-date-picker
                  v-model="shipForm.endDate"
                  type="datetime"
                  placeholder="选择结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="每日开始时间" prop="startTimeDay">
                <el-time-picker
                  v-model="shipForm.startTimeDay"
                  placeholder="选择每日开始时间"
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="每日结束时间" prop="endTimeDay">
                <el-time-picker
                  v-model="shipForm.endTimeDay"
                  placeholder="选择每日结束时间"
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="是否开启预警" prop="warning">
            <el-radio-group v-model="shipForm.warning">
              <el-radio :label="1">开启</el-radio>
              <el-radio :label="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-divider content-position="center">通知方式</el-divider>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="VHF喊话" prop="vhf">
                <el-radio-group v-model="shipForm.vhf">
                  <el-radio :label="1">开启</el-radio>
                  <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="shipForm.vhf === 1">
              <el-form-item label="VHF喊话内容" prop="vhfContent">
                <el-input v-model="shipForm.vhfContent" placeholder="请输入VHF喊话内容" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="喇叭喊话" prop="loudspeaker">
                <el-radio-group v-model="shipForm.loudspeaker">
                  <el-radio :label="1">开启</el-radio>
                  <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="shipForm.loudspeaker === 1">
              <el-form-item label="喇叭喊话内容" prop="loudspeakerContent">
                <el-input v-model="shipForm.loudspeakerContent" placeholder="请输入喇叭喊话内容" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="AIS报文" prop="ais">
                <el-radio-group v-model="shipForm.ais">
                  <el-radio :label="1">开启</el-radio>
                  <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="shipForm.ais === 1">
              <el-form-item label="AIS报文内容" prop="aisContent">
                <el-input v-model="shipForm.aisContent" placeholder="请输入AIS报文内容" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="短信通知" prop="sms">
                <el-radio-group v-model="shipForm.sms">
                  <el-radio :label="1">开启</el-radio>
                  <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="shipForm.sms === 1">
              <el-form-item label="短信模板" prop="smsTemplate">
                <el-input v-model="shipForm.smsTemplate" placeholder="请输入短信模板" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="探照灯" prop="searchlight">
            <el-radio-group v-model="shipForm.searchlight">
              <el-radio :label="1">开启</el-radio>
              <el-radio :label="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>

<!--          <el-divider content-position="center">记录设置</el-divider>-->

<!--          <el-form-item label="记录方式">-->
<!--            <el-row :gutter="24">-->
<!--              <el-col :span="8">-->
<!--                <el-form-item label="事件记录" prop="eventRecord" label-width="80px">-->
<!--                  <el-radio-group v-model="shipForm.eventRecord">-->
<!--                    <el-radio :label="1">开启</el-radio>-->
<!--                    <el-radio :label="0">关闭</el-radio>-->
<!--                  </el-radio-group>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="8">-->
<!--                <el-form-item label="航迹记录" prop="trackRecord" label-width="80px">-->
<!--                  <el-radio-group v-model="shipForm.trackRecord">-->
<!--                    <el-radio :label="1">开启</el-radio>-->
<!--                    <el-radio :label="0">关闭</el-radio>-->
<!--                  </el-radio-group>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="8">-->
<!--                <el-form-item label="视频记录" prop="videoRecord" label-width="80px">-->
<!--                  <el-radio-group v-model="shipForm.videoRecord">-->
<!--                    <el-radio :label="1">开启</el-radio>-->
<!--                    <el-radio :label="0">关闭</el-radio>-->
<!--                  </el-radio-group>-->
<!--                </el-form-item>-->
<!--              </el-col>-->
<!--            </el-row>-->
<!--          </el-form-item>-->

<!--          <el-form-item label="状态" prop="status">-->
<!--            <el-radio-group v-model="shipForm.status">-->
<!--              <el-radio :label="1">启用</el-radio>-->
<!--              <el-radio :label="0">禁用</el-radio>-->
<!--            </el-radio-group>-->
<!--          </el-form-item>-->
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitShipForm">确 定</el-button>
            <el-button @click="shipDialogVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch, nextTick, getCurrentInstance, type ComponentInternalInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Message, ChatLineRound, Bell } from '@element-plus/icons-vue';
import { listData } from '@/api/system/dict/data';
import type { DictDataVO } from '@/api/system/dict/data/types';
import { getWarningRuleList, getWarningRuleById, addWarningRule, updateWarningRule, deleteWarningRule } from '@/api/bridge/traffic/rule';
import { disRuleList, disRuleDetail, addDisRulet, editDisRulet, delDisRulet } from '@/api/map/earlyWarning';
import { envList } from '@/api/map/map';
import dayjs from 'dayjs';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 规则类型 - 通用预警还是船舶预警
const ruleType = ref('generic');

interface DictOption {
  dictValue: string;
  dictLabel: string;
  dictType?: string;
  unit?: string;
}

interface ParameterOption {
  value: string;
  label: string;
}

interface EnvTypeOption {
  id: number;
  name: string;
  envTypeId: number;
  envType: string;
}

// 选中的行
const selectedRows = ref<any[]>([]);
const selectedShipRows = ref<any[]>([]);

// 测试相关的状态
const testDialogVisible = ref(false);
const testForm = ref<Record<string, any>>({});
const testResult = ref<boolean | null>(null);
const testResultDescription = ref('');
const currentRule = ref<any>({});
const showSearch = ref(true);

// 查询参数 - 通用预警
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  ruleName: ''
});

// 查询参数 - 船舶预警
const shipQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  envType: ''
});

// 表单参数 - 通用预警
const form = reactive({
  ruleId: undefined,
  ruleName: '',
  warningType: '',
  triggerCondition: '',
  warningLevel: '',
  notificationMethod: [] as string[],
  status: 0,
  remark: '',
  warnRuleThresholdList: [{ parameterName: '', minValue: '', maxValue: '' }]
});

// 表单参数 - 船舶预警
const shipForm = reactive({
  id: undefined,
  envType: '',
  warning: 1,
  vhf: 0,
  vhfContent: '',
  loudspeaker: 0,
  loudspeakerContent: '',
  searchlight: 0,
  ais: 0,
  aisContent: '',
  sms: 0,
  smsTemplate: '',
  eventRecord: 0,
  trackRecord: 0,
  videoRecord: 0,
  status: 1,
  startDate: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 默认为当前时间
  endDate: dayjs().add(2, 'year').format('YYYY-MM-DD HH:mm:ss'), // 默认为两年后
  startTimeDay: '08:00:00', // 默认为每天早上八点
  endTimeDay: '00:00:00'  // 默认为每天晚上零点
});

// 添加编辑模式标志
const isEditMode = ref(false);
const isShipEditMode = ref(false);

// 字典数据
const warningTypeOptions = ref<DictOption[]>([]);
const warningLevelOptions = ref<DictOption[]>([]);
const notificationMethodOptions = ref<DictOption[]>([]);
const envTypeOptions = ref<EnvTypeOption[]>([]); // 通航环境类型选项

// 获取预警类型字典
const getWarningTypeDict = async () => {
  const res = await listData({
    dictType: 'warning_type',
    pageNum: 1,
    pageSize: 100,
    dictName: '',
    dictLabel: ''
  });
  warningTypeOptions.value = res.rows;
};

// 获取预警级别字典
const getWarningLevelDict = async () => {
  const res = await listData({
    dictType: 'warn_level',
    pageNum: 1,
    pageSize: 100,
    dictName: '',
    dictLabel: ''
  });
  warningLevelOptions.value = res.rows;
};

// 获取通知方式字典
const getNotificationMethodDict = async () => {
  const res = await listData({
    dictType: 'notification_method',
    pageNum: 1,
    pageSize: 100,
    dictName: '',
    dictLabel: ''
  });
  notificationMethodOptions.value = res.rows;
};

// 获取通航环境类型
const getEnvTypeOptions = async () => {
  try {
    const res = await envList({});
    // 确保数据已正确排序和映射
    envTypeOptions.value = res.rows || [];
    console.log('通航环境数据:', envTypeOptions.value);
  } catch (error) {
    console.error('获取通航环境类型失败:', error);
  }
};

// 获取通知方式文本
const getNotificationMethodText = (methods: string[]) => {
  return methods
    .map((method) => {
      const found = notificationMethodOptions.value.find((item) => item.dictValue === method);
      return found ? found.dictLabel : method;
    })
    .join('、');
};

// 判断是否包含特定通知方式
const hasNotificationMethod = (methods: string[], method: string) => {
  return methods.includes(method);
};

// 获取预警参数选项
const getParameterOptions = async (warningType: string): Promise<DictOption[]> => {
  // 获取通用参数
  const commonParamsRes = await listData({
    dictType: 'common_params',
    pageNum: 1,
    pageSize: 100,
    dictName: '',
    dictLabel: ''
  });
  let commonParams = commonParamsRes.rows || [];

  // 根据预警类型获取对应的参数字典类型
  const paramDictType: Record<string, string> = {
    '1': 'accident_params', // 交通拥堵
    '2': 'environmental_params', // 环境污染
    '3': 'geology_params', // 地质灾害
    '4': 'health_params', // 公共卫生
    '5': 'weather_params' // 气象灾害
  };

  // 获取特定类型的参数
  const specificType = paramDictType[warningType];
  const specificParamsRes = await listData({
    dictType: specificType,
    pageNum: 1,
    pageSize: 100,
    dictName: '',
    dictLabel: ''
  });
  let specificParams = specificParamsRes.rows || [];

  // 合并通用参数和特定参数
  return [...commonParams, ...specificParams];
};

// 状态选项
const statusOptions = [
  { value: 0, label: '启用' },
  { value: 1, label: '禁用' }
];

// 参数选项
const parameterOptions = ref<ParameterOption[]>([]);

// 获取参数选项
const loadParameterOptions = async (type: string) => {
  try {
    const options = await getParameterOptions(type);
    return options.map((option) => ({
      value: option.dictValue,
      label: `${option.dictLabel}${option.unit ? `(${option.unit})` : ''}`
    }));
  } catch (error) {
    console.error('获取参数选项失败:', error);
    return [];
  }
};

// 监听预警类型变化，获取对应的参数选项
watch(
  () => form.warningType,
  async (newType) => {
    if (newType) {
      parameterOptions.value = await loadParameterOptions(newType);
      // 只有在非编辑模式下才重置阈值设置
      if (!isEditMode.value) {
        form.warnRuleThresholdList = [{ parameterName: '', minValue: '', maxValue: '' }];
      }
    } else {
      parameterOptions.value = [];
    }
  }
);

// 获取预警级别类型
const getWarningLevelType = (level: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  switch (level) {
    case '1': // I级（特别重大）
      return 'danger'; // 红色
    case '2': // II级（重大）
      return 'warning'; // 橙色
    case '3': // III级（较大）
      return 'warning'; // 黄色
    case '4': // IV级（一般）
      return 'info'; // 蓝色
    default:
      return 'info';
  }
};

// 获取预警级别名称
const getWarningLevelName = (level: string) => {
  const found = warningLevelOptions.value.find((item) => item.dictValue === level);
  return found ? found.dictLabel : '';
};

// 获取预警类型名称
const getWarningTypeName = (type: string) => {
  const found = warningTypeOptions.value.find((item) => item.dictValue === type);
  return found ? found.dictLabel : '';
};

// 获取参数标签
const getParameterLabel = (paramValue: string): string => {
  const option = parameterOptions.value.find((item) => item.value === paramValue);
  return option ? option.label : paramValue;
};

// 获取通航环境名称
const getEnvTypeName = (envTypeId: string | number): string => {
  if (!envTypeId) return '';

  // 将环境ID转为数值类型进行比较
  const numEnvTypeId = Number(envTypeId);
  const found = envTypeOptions.value.find((item) => item.id === numEnvTypeId);

  if (found) {
    return found.name;
  }

  // 如果找不到匹配的环境类型，记录日志并返回ID值
  console.warn(`未找到通航环境类型: ${envTypeId}`);
  return String(envTypeId);
};

// 通用预警列表数据
const ruleList = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);

// 船舶预警列表数据
const shipRuleList = ref<any[]>([]);
const shipLoading = ref(false);
const shipTotal = ref(0);

// 表单校验规则 - 通用预警
const rules = {
  ruleName: [{ required: true, message: '规则名称不能为空', trigger: 'blur' }],
  warningType: [{ required: true, message: '预警类型不能为空', trigger: 'change' }],
  triggerCondition: [{ required: true, message: '触发条件不能为空', trigger: 'blur' }],
  warningLevel: [{ required: true, message: '预警级别不能为空', trigger: 'change' }],
  notificationMethod: [
    {
      required: true,
      message: '请至少选择一种通知方式',
      trigger: 'change',
      type: 'array' as const
    }
  ],
  warnRuleThresholdList: [
    {
      required: true,
      message: '请至少设置一个阈值条件',
      trigger: 'change',
      type: 'array' as const
    },
    {
      validator: (rule: any, value: any[], callback: Function) => {
        if (!value || value.length === 0) {
          callback(new Error('请至少设置一个阈值条件'));
          return;
        }

        // 检查每个阈值设置
        for (let i = 0; i < value.length; i++) {
          const item = value[i];

          // 检查参数名称
          if (!item.parameterName) {
            callback(new Error(`第${i + 1}个阈值条件的参数名称不能为空`));
            return;
          }

          // 转换最小值和最大值为数字
          const minValue = Number(item.minValue);
          const maxValue = Number(item.maxValue);

          // 检查是否至少设置了一个阈值
          if (isNaN(minValue) && isNaN(maxValue)) {
            callback(new Error(`第${i + 1}个阈值条件至少需要设置最小值或最大值`));
            return;
          }

          // 如果两个值都设置了，检查大小关系
          if (!isNaN(minValue) && !isNaN(maxValue)) {
            if (minValue >= maxValue) {
              callback(new Error(`第${i + 1}个阈值条件的最小值必须小于最大值`));
              return;
            }
          }
        }

        callback();
      },
      trigger: 'change'
    }
  ]
};

// 表单校验规则 - 船舶预警
const shipRules = {
  envType: [{ required: true, message: '通航环境不能为空', trigger: 'change' }],
  vhfContent: [{ required: true, message: 'VHF喊话内容不能为空', trigger: 'blur', validator: (rule: any, value: string, callback: Function) => {
    if (shipForm.vhf === 1 && !value) {
      callback(new Error('VHF喊话内容不能为空'));
    } else {
      callback();
    }
  }}],
  loudspeakerContent: [{ required: true, message: '喇叭喊话内容不能为空', trigger: 'blur', validator: (rule: any, value: string, callback: Function) => {
    if (shipForm.loudspeaker === 1 && !value) {
      callback(new Error('喇叭喊话内容不能为空'));
    } else {
      callback();
    }
  }}],
  aisContent: [{ required: true, message: 'AIS报文内容不能为空', trigger: 'blur', validator: (rule: any, value: string, callback: Function) => {
    if (shipForm.ais === 1 && !value) {
      callback(new Error('AIS报文内容不能为空'));
    } else {
      callback();
    }
  }}],
  smsTemplate: [{ required: true, message: '短信模板不能为空', trigger: 'blur', validator: (rule: any, value: string, callback: Function) => {
    if (shipForm.sms === 1 && !value) {
      callback(new Error('短信模板不能为空'));
    } else {
      callback();
    }
  }}],
  startDate: [{ required: true, message: '生效时间不能为空', trigger: 'change' }],
  endDate: [{
    required: true,
    message: '失效时间不能为空',
    trigger: 'change'
  }, {
    validator: (rule: any, value: string, callback: Function) => {
      if (value && shipForm.startDate && dayjs(value).isBefore(dayjs(shipForm.startDate))) {
        callback(new Error('失效时间必须晚于生效时间'));
      } else {
        callback();
      }
    },
    trigger: 'change'
  }],
  startTimeDay: [{ required: true, message: '每日开始时间不能为空', trigger: 'change' }],
  endTimeDay: [{ required: true, message: '每日结束时间不能为空', trigger: 'change' }]
};

// 对话框参数 - 通用预警
const dialogVisible = ref(false);
const dialogTitle = ref('');
const ruleFormRef = ref<any>(null);
const queryFormRef = ref<any>(null);

// 对话框参数 - 船舶预警
const shipDialogVisible = ref(false);
const shipDialogTitle = ref('');
const shipFormRef = ref<any>(null);
const shipQueryFormRef = ref<any>(null);

// 页面初始化
onMounted(() => {
  getWarningTypeDict();
  getWarningLevelDict();
  getNotificationMethodDict();
  getEnvTypeOptions();
  getList();
  getShipList();
});

// 切换规则类型
const handleRuleTypeChange = async (type: string) => {
  if (type === 'generic') {
    getList();
  } else if (type === 'ship') {
    // 确保在切换到船舶规则时加载通航环境数据
    if (envTypeOptions.value.length === 0) {
      await getEnvTypeOptions();
    }
    getShipList();
  }
};

// 获取规则列表 - 通用预警
const getList = async () => {
  loading.value = true;
  try {
    const res = await getWarningRuleList(queryParams);
    // 处理返回的数据，将通知方式字符串转换为数组，并将 id 映射为 ruleId
    ruleList.value = res.rows.map((item) => ({
      ...item,
      ruleId: item.id,
      notificationMethod: item.notificationMethod ? item.notificationMethod.split(',') : []
    }));
    total.value = res.total;
  } catch (error) {
    console.error('获取预警规则列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取规则列表 - 船舶预警
const getShipList = async () => {
  shipLoading.value = true;
  try {
    // 确保在获取列表前已加载通航环境数据
    if (envTypeOptions.value.length === 0) {
      await getEnvTypeOptions();
    }
    const res = await disRuleList(shipQueryParams);
    // 处理返回的数据，添加通航环境名称
    shipRuleList.value = res.rows.map((item) => ({
      ...item,
      // 确保envType转为数值进行比较
      envTypeName: getEnvTypeName(item.envType)
    }));
    shipTotal.value = res.total;
  } catch (error) {
    console.error('获取船舶预警规则列表失败:', error);
  } finally {
    shipLoading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm');
};

// 搜索按钮操作 - 通用预警
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 搜索按钮操作 - 船舶预警
const handleShipQuery = () => {
  shipQueryParams.pageNum = 1;
  // 确保envType是字符串格式
  if (shipQueryParams.envType) {
    shipQueryParams.envType = shipQueryParams.envType.toString();
  }
  getShipList();
};

// 重置按钮操作 - 通用预警
const resetQuery = () => {
  queryParams.ruleName = '';
  queryFormRef.value?.resetFields();
  getList();
};

// 重置按钮操作 - 船舶预警
const resetShipQuery = () => {
  shipQueryParams.envType = '';
  shipQueryFormRef.value?.resetFields();
  getShipList();
};

// 新增按钮操作 - 通用预警
const handleAdd = () => {
  resetForm();
  isEditMode.value = false;
  dialogTitle.value = '新增预警规则';
  dialogVisible.value = true;
};

// 新增按钮操作 - 船舶预警
const handleShipAdd = async () => {
  resetShipForm();
  isShipEditMode.value = false;
  shipDialogTitle.value = '新增船舶预警规则';
  // 确保在打开新增对话框前已加载通航环境数据
  if (envTypeOptions.value.length === 0) {
    await getEnvTypeOptions();
  }
  shipDialogVisible.value = true;
};

// 编辑按钮操作 - 通用预警
const handleEdit = async (row: any) => {
  resetForm();
  isEditMode.value = true;
  dialogTitle.value = '编辑预警规则';

  try {
    const { data } = await getWarningRuleById(row.id);
    // 先设置预警类型，触发参数选项加载
    form.warningType = data.warningType;
    // 等待参数选项加载完成
    await nextTick();

    // 处理返回的数据，将通知方式字符串转换为数组
    const formData = {
      ...data,
      ruleId: data.id,
      notificationMethod: data.notificationMethod ? data.notificationMethod.split(',') : []
    };

    Object.assign(form, formData);
    dialogVisible.value = true;
  } catch (error) {
    console.error('获取规则详情失败:', error);
    ElMessage.error('获取规则详情失败');
  }
};

// 编辑按钮操作 - 船舶预警
const handleShipEdit = async (row: any) => {
  resetShipForm();
  isShipEditMode.value = true;
  shipDialogTitle.value = '编辑船舶预警规则';

  try {
    // 确保在编辑前已加载通航环境数据
    if (envTypeOptions.value.length === 0) {
      await getEnvTypeOptions();
    }
    const { data } = await disRuleDetail(row.id);

    // 处理数据以确保类型正确
    const processedData = {
      ...data,
      // 确保envType为数值类型
      envType: Number(data.envType) || data.envType,
      id: data.id
    };

    // 将后端数据映射到表单，忽略不需要的字段
    Object.keys(shipForm).forEach(key => {
      if (key in processedData) {
        shipForm[key] = processedData[key];
      }
    });

    shipDialogVisible.value = true;
  } catch (error) {
    console.error('获取船舶规则详情失败:', error);
    ElMessage.error('获取船舶规则详情失败');
  }
};

// 删除按钮操作 - 通用预警
const handleDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除预警规则"${row.ruleName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await deleteWarningRule(row.id);
      ElMessage.success('删除成功');
      getList();
    })
    .catch(() => {});
};

// 删除按钮操作 - 船舶预警
const handleShipDelete = (row: any) => {
  ElMessageBox.confirm(`确定要删除船舶预警规则"${row.ruleName}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await delDisRulet(row.id);
      ElMessage.success('删除成功');
      getShipList();
    })
    .catch(() => {});
};

// 状态变更操作 - 通用预警
const handleStatusChange = async (row: any) => {
  // 处理提交的数据，将通知方式数组转换为逗号分隔的字符串
  const submitData = {
    ...row,
    notificationMethod: Array.isArray(row.notificationMethod) ? row.notificationMethod.join(',') : row.notificationMethod
  };
  await updateWarningRule(submitData);
  ElMessage.success(`规则已${row.status === 0 ? '启用' : '禁用'}`);
};

// 状态变更操作 - 船舶预警
const handleShipStatusChange = async (row: any) => {
  await editDisRulet(row);
  ElMessage.success(`规则已${row.status === 1 ? '启用' : '禁用'}`);
};

// 测试按钮操作
const handleTest = (row: any) => {
  currentRule.value = row;
  testForm.value = {};
  testResult.value = null;
  testResultDescription.value = '';

  // 确保warnRuleThresholdList存在且不为空
  if (row.warnRuleThresholdList && row.warnRuleThresholdList.length > 0) {
    // 为每个阈值参数创建输入字段
    row.warnRuleThresholdList.forEach((item: any) => {
      testForm.value[item.parameterName] = '';
    });
  } else {
    console.log(row);
    ElMessage.warning('测试不通过');
    return;
  }

  testDialogVisible.value = true;
};

// 运行测试
const runTest = () => {
  // 确保有阈值设置才进行测试
  if (!currentRule.value.warnRuleThresholdList || currentRule.value.warnRuleThresholdList.length === 0) {
    ElMessage.warning('测试不通过');
    return;
  }

  // 判断测试结果
  const results = currentRule.value.warnRuleThresholdList.map((setting: any) => {
    const testValue = Number(testForm.value[setting.parameterName]);
    const minValue = Number(setting.minValue);
    const maxValue = Number(setting.maxValue);
    let result = true;

    if (!isNaN(minValue) && testValue < minValue) {
      result = false;
    }

    if (!isNaN(maxValue) && testValue > maxValue) {
      result = false;
    }

    return {
      parameterName: setting.parameterName,
      parameterLabel: getParameterLabel(setting.parameterName),
      result,
      testValue,
      minValue,
      maxValue
    };
  });

  // 所有条件都满足才触发预警
  testResult.value = results.every((r) => r.result);

  // 生成结果描述
  testResultDescription.value = results
    .map((r) => {
      let conditionText = '';
      if (!isNaN(r.minValue) && !isNaN(r.maxValue)) {
        conditionText = `${r.minValue} <= ${r.testValue} <= ${r.maxValue}`;
      } else if (!isNaN(r.minValue)) {
        conditionText = `${r.testValue} >= ${r.minValue}`;
      } else if (!isNaN(r.maxValue)) {
        conditionText = `${r.testValue} <= ${r.maxValue}`;
      }

      return `${r.parameterLabel} ${conditionText}：${r.result ? '满足' : '不满足'}`;
    })
    .join('\n');
};

// 重置表单 - 通用预警
const resetForm = () => {
  isEditMode.value = false;
  form.ruleId = undefined;
  form.ruleName = '';
  form.warningType = '';
  form.triggerCondition = '';
  form.warningLevel = '';
  form.notificationMethod = [];
  form.status = 0; // 默认启用
  form.remark = '';
  form.warnRuleThresholdList = [{ parameterName: '', minValue: '', maxValue: '' }];
  ruleFormRef.value?.resetFields();
};

// 重置表单 - 船舶预警
const resetShipForm = () => {
  isShipEditMode.value = false;
  shipForm.id = undefined;
  shipForm.envType = '';
  shipForm.warning = 1;
  shipForm.vhf = 0;
  shipForm.vhfContent = '';
  shipForm.loudspeaker = 0;
  shipForm.loudspeakerContent = '';
  shipForm.searchlight = 0;
  shipForm.ais = 0;
  shipForm.aisContent = '';
  shipForm.sms = 0;
  shipForm.smsTemplate = '';
  shipForm.eventRecord = 0;
  shipForm.trackRecord = 0;
  shipForm.videoRecord = 0;
  shipForm.status = 1;
  // 重置时间
  shipForm.startDate = dayjs().format('YYYY-MM-DD HH:mm:ss');
  shipForm.endDate = dayjs().add(2, 'year').format('YYYY-MM-DD HH:mm:ss');
  shipForm.startTimeDay = '08:00:00'; // 每天早上八点
  shipForm.endTimeDay = '00:00:00';  // 每天晚上零点
  shipFormRef.value?.resetFields();
};

// 添加阈值条件
const addThreshold = () => {
  form.warnRuleThresholdList.push({ parameterName: '', minValue: '', maxValue: '' });
};

// 移除阈值条件
const removeThreshold = (index: number) => {
  form.warnRuleThresholdList.splice(index, 1);
  if (form.warnRuleThresholdList.length === 0) {
    addThreshold();
  }
};

// 提交表单 - 通用预警
const submitForm = () => {
  ruleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 处理提交的数据，将通知方式数组转换为逗号分隔的字符串
      const submitData = {
        ...form,
        id: form.ruleId,
        notificationMethod: form.notificationMethod.join(',')
      };

      if (form.ruleId) {
        await updateWarningRule(submitData);
        ElMessage.success('修改成功');
      } else {
        await addWarningRule(submitData);
        ElMessage.success('新增成功');
      }
      dialogVisible.value = false;
      getList();
    }
  });
};

// 提交表单 - 船舶预警
const submitShipForm = () => {
  shipFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 确保提交到后端的数据格式正确
      const submitData = {
        ...shipForm,
        // 确保环境类型使用字符串格式 (后端存储为字符串)
        envType: shipForm.envType.toString()
      };

      try {
        if (shipForm.id) {
          await editDisRulet(submitData);
          ElMessage.success('修改成功');
        } else {
          await addDisRulet(submitData);
          ElMessage.success('新增成功');
        }
        shipDialogVisible.value = false;
        getShipList();
      } catch (error) {
        console.error('保存船舶预警规则失败:', error);
        ElMessage.error('保存失败，请重试');
      }
    }
  });
};

// 分页处理 - 通用预警
const handlePageChange = (val: number) => {
  queryParams.pageNum = val;
  getList();
};

// 分页处理 - 船舶预警
const handleShipPageChange = (val: number) => {
  shipQueryParams.pageNum = val;
  getShipList();
};

// 每页条数变更 - 通用预警
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val;
  getList();
};

// 每页条数变更 - 船舶预警
const handleShipSizeChange = (val: number) => {
  shipQueryParams.pageSize = val;
  getShipList();
};

// 处理选择变化 - 通用预警
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

// 处理选择变化 - 船舶预警
const handleShipSelectionChange = (selection: any[]) => {
  selectedShipRows.value = selection;
};

// 处理批量删除 - 通用预警
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  ElMessageBox.confirm(`确认要删除选中的 ${selectedRows.value.length} 条数据吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 执行批量删除，这里假设 API 支持批量删除
      // 如果 API 不支持批量删除，可以使用 Promise.all 进行多个单条删除
      const ids = selectedRows.value.map(row => row.id);
      await Promise.all(ids.map(id => deleteWarningRule(id)));

      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败，请重试');
    }
  }).catch(() => {
    // 用户取消删除操作
  });
};

// 处理批量删除 - 船舶预警
const handleShipBatchDelete = () => {
  if (selectedShipRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  ElMessageBox.confirm(`确认要删除选中的 ${selectedShipRows.value.length} 条数据吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const ids = selectedShipRows.value.map(row => row.id);
      await Promise.all(ids.map(id => delDisRulet(id)));

      ElMessage.success('删除成功');
      getShipList();
    } catch (error) {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败，请重试');
    }
  }).catch(() => {
    // 用户取消删除操作
  });
};
</script>

<style scoped lang="scss">
.rule-container {
  padding: 20px;
}

.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.left-items {
  display: flex;
  justify-content: center;
  align-items: center;
}

.rule-type-toggle {
  margin-right: 15px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.notification-icons {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.ship-notification-methods,
.ship-record-methods {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 4px;
}

.notify-users {
  display: flex;
  align-items: center;
  justify-content: center;

  .more-users {
    font-size: 12px;
    background-color: #f2f6fc;
    color: #909399;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -8px;
  }
}

.threshold-card {
  width: 100%;
  border: 1px solid var(--el-border-color-light);
  margin-top: 10px;
}

.threshold-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.threshold-actions {
  display: flex;
  justify-content: center;
  align-items: center;
}

.threshold-help {
  background-color: #f2f6fc;
  padding: 10px 15px;
  border-radius: 4px;
  margin: 10px 0 15px;

  p {
    font-weight: bold;
    margin-bottom: 5px;
    margin-top: 0;
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      margin-bottom: 3px;
      font-size: 12px;
      color: #606266;
    }
  }
}

.test-result {
  margin-top: 20px;
}

.m-1 {
  margin: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-area {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    margin-left: 0;
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }

  .left-items {
    flex-wrap: wrap;

    .rule-type-toggle {
      margin-bottom: 10px;
      width: 100%;
    }
  }
}

@media (max-width: 576px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .action-buttons {
    margin-top: 15px;
    justify-content: flex-start;
  }
}
</style>
