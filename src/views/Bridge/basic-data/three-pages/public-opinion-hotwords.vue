<template>
  <div class="page-container">
    <OpinionVerticalTabNav />
    
    <div class="opinion-hotwords-container">
      <!-- 标题栏与返回按钮 -->
      <div class="page-header">
        <div class="left-area">
          <h2 class="page-title">舆情热词管理</h2>
        </div>
        <div class="right-area">
          <el-button type="primary" @click="handleAdd" class="custom-button">
            <el-icon><Plus /></el-icon>新增热词
          </el-button>
          <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="batchDelete">删除</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        height="calc(100vh - 250px)"
        :header-cell-style="{ background: 'var(--el-bg-color-page)' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="80" align="center" />
        <el-table-column label="关键词" prop="keyword" min-width="150">
          <template #default="scope">
            <span class="keyword-tag">{{ scope.row.keyword }}</span>
          </template>
        </el-table-column>
        <el-table-column label="出现频次" prop="frequency" width="120" align="center">
          <template #default="scope">
            <span class="frequency-badge">{{ scope.row.frequency }}</span>
          </template>
        </el-table-column>
        <el-table-column label="热词类型" prop="keywordType" width="150" align="center">
          <template #default="scope">
            <el-tag :type="getKeywordTypeTag(scope.row.keywordType)">
              {{ scope.row.keywordType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 热词词云展示 -->
      <div class="word-cloud-container">
        <div class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          <span>热词词云</span>
        </div>
        <div class="word-cloud">
          <div
            v-for="(item, index) in wordCloudData"
            :key="index"
            class="word-item"
            :style="getWordStyle(item)"
          >
            {{ item.name }}
          </div>
        </div>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 编辑/新增弹窗 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'add' ? '新增热词' : '编辑热词'"
        width="50%"
        destroy-on-close
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="dialog-form"
        >
          <el-form-item label="关键词" prop="keyword">
            <el-input v-model="form.keyword" placeholder="请输入关键词" />
          </el-form-item>
          <el-form-item label="出现频次" prop="frequency">
            <el-input-number
              v-model="form.frequency"
              :min="1"
              style="width: 100%"
              placeholder="请输入出现频次"
            />
          </el-form-item>
          <el-form-item label="热词类型" prop="keywordType">
            <el-select v-model="form.keywordType" placeholder="请选择热词类型" style="width: 100%">
              <el-option label="事件主体" value="事件主体" />
              <el-option label="情感倾向" value="情感倾向" />
              <el-option label="区域地点" value="区域地点" />
              <el-option label="时间节点" value="时间节点" />
              <el-option label="行业术语" value="行业术语" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowLeft, Plus, DataAnalysis } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { 
  getPublicOpinionHotWordList, 
  getPublicOpinionHotWordById, 
  addPublicOpinionHotWord, 
  updatePublicOpinionHotWord, 
  deletePublicOpinionHotWord 
} from "@/api/bridge/bisc/public-opinion";
import OpinionVerticalTabNav from '../components/OpinionVerticalTabNav.vue';

// 路由
const router = useRouter();

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 词云数据
const wordCloudData = computed(() => {
  return tableData.value.map(item => ({
    name: item.keyword,
    value: item.frequency,
    type: item.keywordType
  }));
});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

// 弹窗控制
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");

// 删除
const selectedRows = ref<any[]>([]);
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }
  
  ElMessageBox.confirm(`确认删除选中的 ${selectedRows.value.length} 条舆情热词数据吗？`, "警告", {
    type: "warning",
  })
    .then(async () => {
      try {
        for (const row of selectedRows.value) {
          await deletePublicOpinionHotWord(row.id);
        }
        ElMessage.success("删除成功");
        getList();
        selectedRows.value = [];
      } catch (error) {
        console.error("删除舆情热词失败:", error);
        ElMessage.error("删除舆情热词失败");
      }
    })
    .catch(() => {});
};

// 表单数据
const formRef = ref();
const form = ref({
  id: "",
  keyword: "",
  frequency: 1,
  keywordType: "",
});

// 表单校验规则
const rules = {
  keyword: [{ required: true, message: "请输入关键词", trigger: "blur" }],
  frequency: [{ required: true, message: "请输入出现频次", trigger: "blur" }],
  keywordType: [{ required: true, message: "请选择热词类型", trigger: "change" }],
};

// 获取热词类型对应的标签类型
const getKeywordTypeTag = (type: string) => {
  switch (type) {
    case "事件主体":
      return "primary";
    case "情感倾向":
      return "danger";
    case "区域地点":
      return "success";
    case "时间节点":
      return "warning";
    case "行业术语":
      return "info";
    default:
      return "";
  }
};

// 获取词云中单词的样式
const getWordStyle = (item: { name: string, value: number, type: string }) => {
  // 根据频率计算字体大小，最小12px，最大30px
  const fontSize = Math.max(12, Math.min(30, 12 + item.value * 0.7));
  // 获取类型对应的颜色
  let color;
  switch (item.type) {
    case "事件主体":
      color = "var(--el-color-primary)";
      break;
    case "情感倾向":
      color = "var(--el-color-danger)";
      break;
    case "区域地点":
      color = "var(--el-color-success)";
      break;
    case "时间节点":
      color = "var(--el-color-warning)";
      break;
    case "行业术语":
      color = "var(--el-color-info)";
      break;
    default:
      color = "#909399";
  }
  
  // 随机旋转 -20 到 20 度
  const rotate = Math.floor(Math.random() * 41) - 20;
  
  return {
    fontSize: `${fontSize}px`,
    color,
    fontWeight: item.value > 5 ? 'bold' : 'normal',
    transform: `rotate(${rotate}deg)`,
  };
};

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    queryParams.pageNum = currentPage.value;
    queryParams.pageSize = pageSize.value;

    const res = await getPublicOpinionHotWordList(queryParams);
    tableData.value = res.rows || [];
    total.value = res.total || 0;
  } catch (error) {
    console.error("获取舆情热词列表失败:", error);
    ElMessage.error("获取舆情热词列表失败");
  } finally {
    loading.value = false;
  }
};

// 返回舆情列表
const goBack = () => {
  router.push('/basic-data/public-opinion');
};

// 处理分页
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getList();
};

// 新增
const handleAdd = () => {
  dialogType.value = "add";
  form.value = {
    id: "",
    keyword: "",
    frequency: 1,
    keywordType: "",
  };
  dialogVisible.value = true;
};

// 编辑
const handleEdit = async (row: any) => {
  try {
    loading.value = true;
    const res = await getPublicOpinionHotWordById(row.id);
    dialogType.value = "edit";
    form.value = res.data;
    dialogVisible.value = true;
  } catch (error) {
    console.error("获取舆情热词详情失败:", error);
    ElMessage.error("获取舆情热词详情失败");
  } finally {
    loading.value = false;
  }
};

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm("确认删除该舆情热词吗？", "警告", {
    type: "warning",
  })
    .then(async () => {
      try {
        await deletePublicOpinionHotWord(row.id);
        ElMessage.success("删除成功");
        getList();
      } catch (error) {
        console.error("删除舆情热词失败:", error);
        ElMessage.error("删除舆情热词失败");
      }
    })
    .catch(() => {});
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (dialogType.value === "add") {
          await addPublicOpinionHotWord(form.value);
          ElMessage.success("新增成功");
        } else {
          await updatePublicOpinionHotWord(form.value);
          ElMessage.success("修改成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        console.error("操作失败:", error);
        ElMessage.error("操作失败");
      }
    }
  });
};

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  height: 100%;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.opinion-hotwords-container {
  flex: 1;
  padding: 20px;
  overflow: auto;
  height: 100%;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: var(--el-bg-color-overlay);
  padding: 16px;
  border-radius: 4px;

  .left-area {
    display: flex;
    align-items: center;
    gap: 12px;

    .back-button {
      display: flex;
      align-items: center;
      gap: 4px;
      transition: all 0.3s;

      &:hover {
        transform: translateX(-2px);
      }
    }

    .page-title {
      margin: 0;
      font-size: 18px;
      color: var(--el-text-color-primary);
      position: relative;
      padding-left: 15px;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: var(--el-color-primary);
        border-radius: 2px;
      }
    }
  }

  .right-area {
    .custom-button {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s ease, height 0.6s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        &::before {
          width: 300px;
          height: 300px;
        }
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.keyword-tag {
  display: inline-block;
  padding: 2px 8px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border-radius: 4px;
  font-weight: 500;
}

.frequency-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  font-weight: bold;
}

.word-cloud-container {
  margin-top: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color-overlay);
  box-shadow: var(--el-box-shadow-light);

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
    color: var(--el-text-color-primary);
    padding-bottom: 12px;
    border-bottom: 1px solid var(--el-border-color-light);
    
    .el-icon {
      color: var(--el-color-primary);
    }
  }

  .word-cloud {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    padding: 30px;
    min-height: 200px;

    .word-item {
      display: inline-block;
      padding: 5px 10px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
}
</style> 