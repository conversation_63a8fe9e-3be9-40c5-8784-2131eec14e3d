<template>
  <div class="page-container">
    <ShipVerticalTabNav :ship-id="shipId" :ship-name="shipName" />
    
    <div class="ship-ais-container">
      <!-- 标题栏与返回按钮 -->
      <div class="page-header">
        <div class="left-area">
          <!-- <el-button @click="goBack" class="back-button">
            <el-icon><ArrowLeft /></el-icon>
            返回船舶列表
          </el-button> -->
          <h2 class="page-title">AIS定位数据</h2>
        </div>
        <div class="right-area">
          <el-button type="primary" @click="handleAdd" class="custom-button">
            <el-icon><Plus /></el-icon>新增AIS数据
          </el-button>
          <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="batchDelete">删除</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        height="calc(100vh - 250px)"
        :header-cell-style="{ background: 'var(--el-bg-color-page)' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="80" align="center" />
        <el-table-column label="船舶" width="150" align="center">
          <template #default="{ row }">
            {{ getShipName(row.shipId || row.shipCode) }}
          </template>
        </el-table-column>
        <el-table-column label="中文船名" prop="nameCn" min-width="120" />
        <el-table-column label="英文船名" prop="nameEn" min-width="120" />
        <!-- 注意fkTypeId 是船舶类型 -->
        <el-table-column label="船舶类型" prop="fkTypeId" width="120" align="center" />
        <el-table-column label="MMSI号" prop="mmsi" width="120" align="center" />
        <el-table-column label="IMO号" prop="imo" width="120" align="center" />
        <el-table-column label="呼号" prop="callSign" width="100" align="center" />
        <el-table-column label="经度" prop="lon" width="150" align="center">
          <template #default="scope">
            {{ formatCoordinate(scope.row.lon) }}
          </template>
        </el-table-column>
        <el-table-column label="纬度" prop="lat" width="150" align="center">
          <template #default="scope">
            {{ formatCoordinate(scope.row.lat) }}
          </template>
        </el-table-column>
        <el-table-column label="航向(度)" prop="cog" width="120" align="center">
          <template #default="scope">
            {{ Number(scope.row.cog).toFixed(2) }}°
          </template>
        </el-table-column>
        <el-table-column label="航速(节)" prop="sog" width="120" align="center">
          <template #default="scope">
            {{ Number(scope.row.sog).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="预抵港" prop="destinationPort" width="120" align="center" />
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 编辑/新增弹窗 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'add' ? '新增AIS数据' : '编辑AIS数据'"
        width="60%"
        destroy-on-close
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="dialog-form"
        >
          <el-form-item label="选择船舶" prop="shipId">
            <el-select v-model="form.shipId" placeholder="请选择船舶" style="width: 100%" @change="handleShipChange">
              <el-option
                v-for="item in shipOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="中文船名" prop="nameCn">
                <el-input v-model="form.nameCn" placeholder="请输入中文船名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="英文船名" prop="nameEn">
                <el-input v-model="form.nameEn" placeholder="请输入英文船名" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="MMSI号" prop="mmsi">
                <el-input v-model="form.mmsi" maxlength="9" placeholder="请输入9位MMSI号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="IMO号" prop="imo">
                <el-input v-model="form.imo" maxlength="7" placeholder="请输入7位IMO号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="呼号" prop="callSign">
                <el-input v-model="form.callSign" placeholder="请输入呼号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="国籍" prop="nationality">
                <el-input v-model="form.nationality" placeholder="请输入国籍" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="船舶类型" prop="fkTypeId">
                <el-select v-model="form.fkTypeId" placeholder="请选择船舶类型" style="width: 100%">
                  <el-option label="货船" value="货船" />
                  <el-option label="客船" value="客船" />
                  <el-option label="渔船" value="渔船" />
                  <el-option label="油轮" value="油轮" />
                  <el-option label="集装箱船" value="集装箱船" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="船长(米)" prop="length">
                <el-input-number v-model="form.length" :precision="2" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="船宽(米)" prop="beam">
                <el-input-number v-model="form.beam" :precision="2" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="吃水(米)" prop="draft">
                <el-input-number v-model="form.draft" :precision="2" :min="0" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度" prop="lon">
                <el-input-number
                  v-model="form.lon"
                  :precision="8"
                  :step="0.00000001"
                  :controls="false"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="lat">
                <el-input-number
                  v-model="form.lat"
                  :precision="8"
                  :step="0.00000001"
                  :controls="false"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="航向(度)" prop="cog">
                <el-input-number
                  v-model="form.cog"
                  :precision="2"
                  :min="0"
                  :max="360"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="航速(节)" prop="sog">
                <el-input-number
                  v-model="form.sog"
                  :precision="2"
                  :min="0"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="预抵港" prop="destinationPort">
                <el-input v-model="form.destinationPort" placeholder="请输入预抵港" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="预抵时间" prop="eta">
                <el-date-picker
                  v-model="form.eta"
                  type="datetime"
                  placeholder="请选择预抵时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="停泊" :value="0" />
                  <el-option label="航行" :value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="contactInfo">
                <el-input v-model="form.contactInfo" placeholder="请输入联系方式" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowLeft, Plus } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { useRouter, useRoute } from "vue-router";
import { 
  getShipAISDataList, 
  getShipAISDataById, 
  addShipAISData, 
  updateShipAISData, 
  deleteShipAISData,
  getShipInfoList,
  getShipInfoById
} from "@/api/bridge/bisc/ship";
import ShipVerticalTabNav from '@/views/Bridge/basic-data/components/ShipVerticalTabNav.vue';

// 路由相关
const router = useRouter();
const route = useRoute();
const shipId = ref<string | null>(null);
const shipName = ref<string | null>(null);

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 删除
const selectedRows = ref<any[]>([]);
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }
  
  ElMessageBox.confirm(`确认删除选中的 ${selectedRows.value.length} 条AIS数据吗？`, "警告", {
    type: "warning",
  })
    .then(async () => {
      try {
        for (const row of selectedRows.value) {
          await deleteShipAISData(row.id);
        }
        ElMessage.success("删除成功");
        getList();
        selectedRows.value = [];
      } catch (error) {
        console.error("删除AIS数据失败:", error);
        ElMessage.error("删除AIS数据失败");
      }
    })
    .catch(() => {});
};

// 船舶选项列表
const shipOptions = ref<Array<{value: string, label: string}>>([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

// 船舶查询参数
const shipQueryParams = reactive({
  pageNum: 1,
  pageSize: 1000000, // 获取大量数据以确保能获取所有船舶
});

// 弹窗控制
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit">("add");

// 表单数据
const formRef = ref();


// 表单校验规则
const rules = {
  shipId: [{ required: true, message: "请选择船舶", trigger: "change" }],
  nameCn: [{ required: true, message: "请输入中文船名", trigger: "blur" }],
  mmsi: [
    { required: true, message: "请输入MMSI号", trigger: "blur" },
    { pattern: /^\d{9}$/, message: "MMSI号必须为9位数字", trigger: "blur" }
  ],
  lat: [{ required: true, message: "请输入纬度", trigger: "blur" }],
  lon: [{ required: true, message: "请输入经度", trigger: "blur" }],
};

// 根据shipId获取船舶名称
const getShipNameById = computed(() => {
  if (!shipId.value || shipOptions.value.length === 0) return '未知船舶';
  
  const ship = shipOptions.value.find(item => item.value === shipId.value);
  return ship ? ship.label : '未知船舶';
});

// 获取所有船舶数据
const getAllShips = async () => {
  try {
    const res = await getShipInfoList(shipQueryParams);
    shipOptions.value = res.rows.map((item: any) => ({
      value: item.id,
      label: item.nameCn || item.nameEn || '未命名船舶',
    }));
    
    // 如果路由中传递了船舶名称，优先使用；否则根据shipId查找
    if (route.query.shipName) {
      shipName.value = route.query.shipName as string;
    } else if (shipId.value) {
      shipName.value = getShipNameById.value;
    }
  } catch (error) {
    console.error("获取船舶列表失败:", error);
    ElMessage.error("获取船舶列表失败");
  }
};

// 格式化坐标，保留8位小数
const formatCoordinate = (value: number | string) => {
  if (value === undefined || value === null) return "-";
  
  return Number(value).toFixed(8);
};

// 获取状态对应的标签类型
const getStatusType = (status: number | string) => {
  // 将字符串转为数字
  const statusNum = typeof status === 'string' ? parseInt(status) : status;
  
  switch (statusNum) {
    case 0:
      return "info";
    case 1:
      return "success";
    default:
      return "info";
  }
};

// 获取状态文本
const getStatusText = (status: number | string) => {
  // 将字符串转为数字
  const statusNum = typeof status === 'string' ? parseInt(status) : status;
  
  switch (statusNum) {
    case 0:
      return "停泊";
    case 1:
      return "航行";
    default:
      return "未知";
  }
};

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    queryParams.pageNum = currentPage.value;
    queryParams.pageSize = pageSize.value;
    
    // 只有当shipId有值时才将其添加到查询参数中
    const params: any = { ...queryParams };
    if (shipId.value) {
      params.shipId = shipId.value;
    }

    const res = await getShipAISDataList(params);
    tableData.value = res.rows || [];
    total.value = res.total || 0;
  } catch (error) {
    console.error("获取AIS数据列表失败:", error);
    ElMessage.error("获取AIS数据列表失败");
  } finally {
    loading.value = false;
  }
};

// 返回船舶列表
const goBack = () => {
  router.push('/basic-data/boat');
};

// 处理分页
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getList();
};

// 新增
const handleAdd = () => {
  dialogType.value = "add";
  form.value = {
    id: "",
    shipId: shipId.value || "", // 即使为空，也提供一个空字符串
    nameCn: "",
    nameEn: "",
    mmsi: "",
    imo: "",
    callSign: "",
    nationality: "",
    fkTypeId: "",
    lat: 0,
    lon: 0,
    cog: 0,
    sog: 0,
    length: 0,
    beam: 0,
    draft: 0,
    destinationPort: "",
    eta: null,
    status: 0,
    contactInfo: "",
  };
  
  // 如果有shipId，自动填充船舶信息
  if (shipId.value) {
    getShipInfoById(shipId.value).then(res => {
      if (res.data) {
        form.value.nameCn = res.data.nameCn || "";
        form.value.nameEn = res.data.nameEn || "";
        form.value.mmsi = res.data.mmsi || "";
        form.value.imo = res.data.imo || "";
        form.value.callSign = res.data.callSign || "";
        form.value.nationality = res.data.nationality || "";
        form.value.fkTypeId = res.data.shipType || "";
        // 其他船舶属性填充
      }
    }).catch(error => {
      console.error("获取船舶详情失败:", error);
    });
  }
  
  dialogVisible.value = true;
};

// 编辑
const handleEdit = async (row: any) => {
  try {
    loading.value = true;
    const res = await getShipAISDataById(row.id);
    dialogType.value = "edit";
    
    // 处理时间字段，确保在表单中正确显示
    const data = { ...res.data };
    
    // 如果eta是时间戳字符串，将其转换为日期对象
    if (data.eta && typeof data.eta === 'string' && /^\d+$/.test(data.eta)) {
      data.eta = new Date(parseInt(data.eta));
    }
    
    form.value = data;
    dialogVisible.value = true;
  } catch (error) {
    console.error("获取AIS数据详情失败:", error);
    ElMessage.error("获取AIS数据详情失败");
  } finally {
    loading.value = false;
  }
};

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm("确认删除该AIS数据吗？", "警告", {
    type: "warning",
  })
    .then(async () => {
      try {
        await deleteShipAISData(row.id);
        ElMessage.success("删除成功");
        getList();
      } catch (error) {
        console.error("删除AIS数据失败:", error);
        ElMessage.error("删除AIS数据失败");
      }
    })
    .catch(() => {});
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 处理表单数据
        if(shipId.value && typeof form.value.shipId === 'string'){
          form.value.shipId = shipId.value;
        }
        let submitData: any = { ...form.value };
        
        // 使用dayjs格式化日期时间，不再使用时间戳
        if (submitData.eta instanceof Date) {
          submitData.eta = dayjs(submitData.eta).format('YYYY-MM-DD HH:mm:ss');
        } else if (submitData.eta && typeof submitData.eta === 'string' && !(/^\d+$/.test(submitData.eta))) {
          // 如果是日期字符串但不是时间戳格式，也进行格式化
          submitData.eta = dayjs(submitData.eta).format('YYYY-MM-DD HH:mm:ss');
        }
        
        if (dialogType.value === "add") {
          await addShipAISData(submitData);
          ElMessage.success("新增成功");
        } else {
          await updateShipAISData(submitData);
          ElMessage.success("修改成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        console.error("操作失败:", error);
        ElMessage.error("操作失败");
      }
    }
  });
};

// 根据船舶ID获取船舶名称
const getShipName = (id: string) => {
  if (!id || shipOptions.value.length === 0) return '未知船舶';
  
  const ship = shipOptions.value.find(item => item.value === id);
  return ship ? ship.label : '未知船舶';
};

const form = ref({
  id: "",
  shipId: getShipName(shipId.value || ""),
  nameCn: "",
  nameEn: "",
  mmsi: "",
  imo: "",
  callSign: "",
  nationality: "",
  fkTypeId: "",
  lat: 0,
  lon: 0,
  cog: 0,
  sog: 0,
  length: 0,
  beam: 0,
  draft: 0,
  destinationPort: "",
  eta: null as null | Date | string,
  status: 0,
  contactInfo: "",
});

// 船舶选择变化处理函数
const handleShipChange = (shipId: string) => {
  if (!shipId || shipOptions.value.length === 0) return;
  
  const selectedShip = shipOptions.value.find(ship => ship.value === shipId);
  if (!selectedShip) return;
  
  // 根据选择的船舶ID查询完整船舶信息
  getShipInfoById(shipId).then(res => {
    if (res.data) {
      // 自动填充船舶相关信息
      form.value.nameCn = res.data.nameCn || "";
      form.value.nameEn = res.data.nameEn || "";
      form.value.mmsi = res.data.mmsi || "";
      form.value.imo = res.data.imo || "";
      form.value.callSign = res.data.callSign || "";
      form.value.nationality = res.data.nationality || "";
      // 可能还有其他字段需要填充
    }
  }).catch(error => {
    console.error("获取船舶详情失败:", error);
  });
};

onMounted(() => {
  // 从路由参数获取船舶ID
  shipId.value = route.query.shipId as string || null;
  
  // 先获取所有船舶信息
  getAllShips().then(() => {
    // 获取AIS数据
    getList();
  });
});
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  height: 100%;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.ship-ais-container {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: var(--el-bg-color-overlay);
  padding: 16px;
  border-radius: 4px;

  .left-area {
    display: flex;
    align-items: center;
    gap: 12px;

    .back-button {
      display: flex;
      align-items: center;
      gap: 4px;
      transition: all 0.3s;

      &:hover {
        transform: translateX(-2px);
      }
    }

    .page-title {
      margin: 0;
      font-size: 18px;
      color: var(--el-text-color-primary);
      position: relative;
      padding-left: 15px;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: var(--el-color-primary);
        border-radius: 2px;
      }
    }
  }

  .right-area {
    .custom-button {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s ease, height 0.6s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        &::before {
          width: 300px;
          height: 300px;
        }
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-form {
  :deep(.el-input-number) {
    width: 100%;
  }
}
</style> 