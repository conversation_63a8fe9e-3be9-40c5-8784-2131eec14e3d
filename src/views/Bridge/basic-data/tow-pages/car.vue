<template>
  <div class="page-container">
    <VerticalTabNav :vehicleId="vehicleIdFromRoute" />
    
    <div class="bridge-health-container">
      <!-- 顶部操作栏 -->
      <div class="operation-bar" style="display: flex; justify-content: space-between;">
        <div class="left">
          <span class="title">危化品运输信息</span>
          <!-- <el-select
            v-model="currentPageValue"
            class="page-selector"
            @change="handlePageChange"
          >
            <el-option
              v-for="item in pageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :class="{ 'is-active': item.value === currentPageValue }"
            />
          </el-select> -->
        </div>
        <div class="right" >
          <el-button type="primary" @click="handleAdd" class="custom-button">
            <i class="el-icon-plus"></i> 新增数据
          </el-button>
          <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="handleBatchDelete" class="custom-button">
            批量删除
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        class="custom-table"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="vehicleId" label="车辆名称" min-width="120">
          <template #default="{ row }">
            <span class="bridge-name">
              {{ carOptions.find((item) => String(item.value) === String(row.vehicleId))?.label || row.vehicleId }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="hazardousMaterial" label="危化品名称" width="160" />
        <el-table-column prop="hazardousCategory" label="危化品类别" width="120" />
        <el-table-column prop="unNumber" label="UN编号" width="100" />
        <el-table-column prop="packagingType" label="包装类型" width="120" />
        <el-table-column prop="startLocation" label="起始地" min-width="120" />
        <el-table-column prop="destinationLocation" label="目的地" min-width="120" />
        <el-table-column prop="startTime" label="开始时间" width="160" />
        <el-table-column prop="endTime" label="结束时间" width="160">
          <template #default="{ row }">
            {{ row.endTime ? formatDateTime(row.endTime) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="transportStatus" label="运输状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.transportStatus)">
              {{ getStatusText(row.transportStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="remarks"
          label="备注"
          min-width="120"
          align="center"
        >
          <template #default="scope">
            <span class="ellipsis-text" @click="showRemarks(scope.row)">
              {{ (scope.row.remarks || "暂无备注").slice(0, 20) }}
              {{ scope.row.remarks?.length > 20 ? "..." : "" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)" class="operation-button">
              详情
            </el-button>
            <el-button link @click="handleDelete(row)" class="operation-button danger">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          background
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 新增/编辑弹窗 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="50%"
        class="custom-dialog"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="dialog-form"
        >
          <el-form-item label="选择车辆" prop="vehicleId">
            <el-select v-model="form.vehicleId" placeholder="请选择车辆" style="width: 100%">
              <el-option
                v-for="item in carOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="危化品名称" prop="hazardousMaterial">
            <el-input v-model="form.hazardousMaterial" placeholder="请输入危化品名称" />
          </el-form-item>
          <el-form-item label="危化品类别" prop="hazardousCategory">
            <el-input v-model="form.hazardousCategory" placeholder="请输入危化品类别" />
          </el-form-item>
          <el-form-item label="UN编号" prop="unNumber">
            <el-input v-model="form.unNumber" placeholder="请输入UN编号" />
          </el-form-item>
          <el-form-item label="包装类型" prop="packagingType">
            <el-input v-model="form.packagingType" placeholder="请输入包装类型" />
          </el-form-item>
          <el-form-item label="起始地" prop="startLocation">
            <el-input v-model="form.startLocation" placeholder="请输入起始地" />
          </el-form-item>
          <el-form-item label="目的地" prop="destinationLocation">
            <el-input v-model="form.destinationLocation" placeholder="请输入目的地" />
          </el-form-item>
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              placeholder="选择开始时间"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              placeholder="选择结束时间"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="运输状态" prop="transportStatus">
            <el-select v-model="form.transportStatus" placeholder="请选择运输状态" style="width: 100%">
              <el-option label="进行中" :value="0" />
              <el-option label="已完成" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注信息" prop="remarks">
            <el-input
              type="textarea"
              v-model="form.remarks"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </template>
      </el-dialog>

      <!-- 备注信息弹窗 -->
      <el-dialog v-model="remarksDialogVisible" title="备注信息" width="40%">
        <div style="white-space: pre-wrap">{{ currentRemarks }}</div>
        <template #footer>
          <span>
            <el-button @click="remarksDialogVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import VerticalTabNav from '@/views/Bridge/basic-data/components/VerticalTabNav.vue';
import {
  getDangerousGoodsTransportInfo,
  getDangerousGoodsTransportInfoById,
  addDangerousGoodsTransportInfo,
  updateDangerousGoodsTransportInfo,
  deleteDangerousGoodsTransportInfo,
} from "@/api/bridge/bisc/car";
import { getDangerousGoodsCarInfo } from "@/api/bridge/bisc/car";
import dayjs from "dayjs";
import { useRouter, useRoute } from "vue-router";

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const searchQuery = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const selectedRows = ref([]);

// 获取路由参数
const router = useRouter();
const route = useRoute();
const vehicleIdFromRoute = ref<string | null>(null);


// 页面选择相关
const currentPageValue = ref('transport');
const pageOptions = ref([
  { label: '运输信息', value: 'transport' },
  { label: '车辆定位', value: 'location' },
  { label: '运输路线', value: 'route' }
]);

// 页面跳转处理
const handlePageChange = (value: string) => {
  // 保持当前的vehicleId参数
  const query = vehicleIdFromRoute.value ? { vehicleId: vehicleIdFromRoute.value } : {};
  
  if (value === 'transport') {
    router.push({ path: '/basic-data/two-pages/car' });
  } else if (value === 'location') {
    router.push({ path: '/basic-data/three-pages/car-location' });
  } else if (value === 'route') {
    router.push({ path: '/basic-data/four-pages/car-route' });
  }
};

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref();
const form = ref({
  id: "",
  vehicleId: "",
  hazardousMaterial: "",
  hazardousCategory: "",
  unNumber: "",
  packagingType: "",
  startLocation: "",
  destinationLocation: "",
  startTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
  endTime: "",
  transportStatus: 0,
  remarks: "",
});

// 备注弹窗相关
const remarksDialogVisible = ref(false);
const currentRemarks = ref("");

// 危化品车辆列表相关
const carOptions = ref([]);

// 表单校验规则
const rules = {
  vehicleId: [{ required: true, message: "请选择车辆", trigger: "change" }],
  hazardousMaterial: [{ required: true, message: "请输入危化品名称", trigger: "blur" }],
  hazardousCategory: [{ required: true, message: "请输入危化品类别", trigger: "blur" }],
  unNumber: [{ required: true, message: "请输入UN编号", trigger: "blur" }],
  packagingType: [{ required: true, message: "请输入包装类型", trigger: "blur" }],
  startLocation: [{ required: true, message: "请输入起始地", trigger: "blur" }],
  destinationLocation: [{ required: true, message: "请输入目的地", trigger: "blur" }],
  startTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  transportStatus: [{ required: true, message: "请选择运输状态", trigger: "change" }],
};

// 格式化日期时间
const formatDateTime = (date: string | number | Date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 获取运输状态对应的标签类型
const getStatusType = (status: number) => {
  return status === 0 ? "warning" : "success";
};

// 获取运输状态文本
const getStatusText = (status: number) => {
  return status === 0 ? "进行中" : "已完成";
};

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    const params: any = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchQuery.value,
    };
    
    // 如果有车辆ID参数，添加到查询条件
    if (vehicleIdFromRoute.value) {
      params.vehicleId = vehicleIdFromRoute.value;
    }
    
    const res = await getDangerousGoodsTransportInfo(params);
    // 格式化返回数据中的时间
    tableData.value = res.rows.map(item => ({
      ...item,
      startTime: item.startTime ? formatDateTime(item.startTime) : '',
      endTime: item.endTime ? formatDateTime(item.endTime) : ''
    }));
    total.value = res.total;
    console.log('获取到的运输信息列表', tableData.value);
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  getList();
};

// 处理新增
const handleAdd = () => {
  dialogTitle.value = "新增运输信息";
  form.value = {
    id: "",
    vehicleId: vehicleIdFromRoute.value || "",
    hazardousMaterial: "",
    hazardousCategory: "",
    unNumber: "",
    packagingType: "",
    startLocation: "",
    destinationLocation: "",
    startTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    endTime: "",
    transportStatus: 0,
    remarks: "",
  };
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row: any) => {
  try {
    const res = await getDangerousGoodsTransportInfoById(row.id);
    form.value = {
      ...res.data,
      startTime: res.data.startTime ? dayjs(res.data.startTime).format('YYYY-MM-DD HH:mm:ss') : dayjs().format('YYYY-MM-DD HH:mm:ss'),
      endTime: res.data.endTime ? dayjs(res.data.endTime).format('YYYY-MM-DD HH:mm:ss') : ''
    };
    dialogTitle.value = "编辑运输信息";
    dialogVisible.value = true;
  } catch (error) {
    ElMessage.error("获取详情失败");
  }
};

// 处理查看
const handleView = async (row: any) => {
  try {
    const res = await getDangerousGoodsTransportInfoById(row.id);
    form.value = {
      ...res.data,
      startTime: res.data.startTime ? dayjs(res.data.startTime).format('YYYY-MM-DD HH:mm:ss') : '',
      endTime: res.data.endTime ? dayjs(res.data.endTime).format('YYYY-MM-DD HH:mm:ss') : ''
    };
    dialogTitle.value = "查看详情";
    dialogVisible.value = true;
  } catch (error) {
    ElMessage.error("获取详情失败");
  }
};

// 处理删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm("确认删除该条运输信息吗？", "提示", {
    type: "warning",
  }).then(async () => {
    try {
      await deleteDangerousGoodsTransportInfo(row.id);
      ElMessage.success("删除成功");
      getList();
    } catch (error) {
      ElMessage.error("删除失败");
    }
  });
};

// 表格选择改变
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows;
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const submitData = {
          ...form.value,
          startTime: form.value.startTime ? dayjs(form.value.startTime).format('YYYY-MM-DD HH:mm:ss') : '',
          endTime: form.value.endTime ? dayjs(form.value.endTime).format('YYYY-MM-DD HH:mm:ss') : ''
        };
        
        if (form.value.id) {
          await updateDangerousGoodsTransportInfo(submitData);
          ElMessage.success("修改成功");
        } else {
          await addDangerousGoodsTransportInfo(submitData);
          ElMessage.success("新增成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        ElMessage.error("操作失败");
      }
    }
  });
};

// 显示备注信息
const showRemarks = (row: any) => {
  currentRemarks.value = row.remarks || "暂无备注";
  remarksDialogVisible.value = true;
};

// 分页相关方法
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getList();
};

const queryParams = ref<any>({
  pageNum: 1,
  pageSize: 100000000,
})

// 获取车辆列表数据
const getCarOptions = async () => {
  try {
    const res = await getDangerousGoodsCarInfo(queryParams.value);
    carOptions.value = res.rows.map((item) => ({
      value: item.id,
      label: item.licensePlate || '未知车辆',
    }));
    console.log('已获取车辆列表', carOptions.value);
  } catch (error) {
    console.error("获取车辆列表失败:", error);
    ElMessage.error("获取车辆列表失败");
  }
};

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }
  
  ElMessageBox.confirm(`确定要删除选中的${selectedRows.value.length}条数据吗？`, '警告', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await Promise.all(
        selectedRows.value.map((row) => deleteDangerousGoodsTransportInfo(row.id))
      );
      ElMessage.success('批量删除成功');
      // 清空选中项
      selectedRows.value = [];
      // 刷新列表数据
      getList();
    } catch (error) {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

onMounted(async () => {
  // 初始化时获取路由参数
  if (route.query.vehicleId) {
    vehicleIdFromRoute.value = route.query.vehicleId as string;
  }
  
  await getCarOptions();
  getList();
});
// 监听路由参数变化
watch(
  () => route.query.vehicleId,
  (newVal) => {
    if (newVal) {
      vehicleIdFromRoute.value = newVal as string;
    } else {
      vehicleIdFromRoute.value = null;
    }
    getList();
  },
  { immediate: true }
);

</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  height: 100%;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.bridge-health-container {
  flex: 1;
  padding: 20px;
  overflow: auto;

  .operation-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    background: var(--el-bg-color-overlay);
    padding: 16px;
    border-radius: 4px;

    .left {
      .page-selector {
        width: 180px;
        
        :deep(.el-input__wrapper) {
          background-color: transparent;
          box-shadow: none !important;
          
          &:hover {
            background-color: var(--el-fill-color-light);
          }
        }

        :deep(.el-input__inner) {
          font-size: 16px;
          font-weight: 500;
          color: var(--el-color-primary);
        }

        :deep(.el-select-dropdown__item) {
          &.is-active {
            color: var(--el-color-primary);
            font-weight: bold;
            background-color: var(--el-color-primary-light-9);
          }

          &:hover {
            background-color: var(--el-color-primary-light-9);
          }
        }
      }
    }

    .custom-button {
      margin-left: 10px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .custom-table {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--el-bg-color-overlay);

    :deep(th) {
      background: var(--el-bg-color-page) !important;
    }

    :deep(.el-table__row) {
      background-color: var(--el-bg-color-overlay);
    }

    .bridge-name {
      color: var(--el-color-primary);
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .operation-button {
      padding: 0 10px;
      font-size: 14px;

      &.danger {
        color: var(--el-color-danger);
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

.custom-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    background: var(--el-bg-color-overlay);

    .el-dialog__header {
      background: var(--el-bg-color-page);
      margin: 0;
      padding: 20px;
      border-bottom: 1px solid var(--el-border-color-light);
    }

    .el-dialog__body {
      padding: 30px 20px;
    }

    .el-dialog__footer {
      border-top: 1px solid var(--el-border-color-light);
      padding: 15px 20px;
    }
  }

  .dialog-form {
    :deep(.el-form-item__label) {
      color: var(--el-text-color-regular);
    }

    :deep(.el-input__inner) {
      background-color: var(--el-bg-color-overlay);
      border-color: var(--el-border-color);
      color: var(--el-text-color-primary);
    }
  }
}

// 自定义 Element Plus 组件样式
:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-light);
  --el-table-header-bg-color: var(--el-bg-color-page);
  --el-table-row-hover-bg-color: var(--el-fill-color-light);
}

:deep(.el-button--primary) {
  &:hover {
    background: var(--el-color-primary-light-3);
    border-color: var(--el-color-primary-light-3);
  }
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 10px;
  background-color: var(--el-fill-color);
  border-color: var(--el-border-color-light);
  color: var(--el-text-color-regular);
}

.ellipsis {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-text {
  cursor: pointer;
  color: var(--el-color-primary);
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    text-decoration: underline;
    opacity: 0.8;
  }
}
</style> 