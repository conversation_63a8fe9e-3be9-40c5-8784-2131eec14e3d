<template>
  <div class="page-container">
    <BridgeVerticalTabNav :bridge-id="bridgeIdFromRoute" />
    
    <div class="bridge-maintain-container">
      <!-- 顶部操作栏 -->
      <div class="operation-bar">
        <div class="left">
          <span>桥梁运维记录</span>
        </div>
        <div class="right">
          <el-button type="primary" @click="handleAdd" class="custom-button">
            <i class="el-icon-plus"></i> 新增数据
          </el-button>
          <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="handleBatchDelete" class="custom-button">
            删除
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        class="custom-table"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="bridgeName" label="桥梁名称" />
        <el-table-column prop="taskName" label="运维任务名称" />
        <el-table-column prop="taskType" label="任务类型" />
        <el-table-column prop="executePerson" label="执行人" />
        <el-table-column prop="executeTime" label="执行时间" width="180" />
        <el-table-column prop="taskStatus" label="任务状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getTaskStatusType(row.taskStatus)">
              {{ getTaskStatusLabel(row.taskStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="mainContent" label="主要内容" min-width="200">
          <template #default="scope">
            <span class="ellipsis-text" @click="showContent(scope.row)">
              {{ (scope.row.mainContent || "暂无内容").slice(0, 20) }}
              {{ scope.row.mainContent?.length > 20 ? "..." : "" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click="handleView(row)"
              class="operation-button"
            >
              详情
            </el-button>
            <el-button link @click="handleEdit(row)" class="operation-button">
              编辑
            </el-button>
            <el-button link @click="handleDelete(row)" class="operation-button danger">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          background
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 新增/编辑弹窗 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="50%"
        class="custom-dialog"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="dialog-form"
        >
          <el-form-item label="选择桥梁" prop="bridgeId">
            <el-select
              v-model="form.bridgeId"
              placeholder="请选择桥梁"
              style="width: 100%"
            >
              <el-option
                v-for="item in bridgeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="运维任务名称" prop="taskName">
            <el-input v-model="form.taskName" placeholder="请输入运维任务名称" />
          </el-form-item>
          <el-form-item label="任务类型" prop="taskType">
            <el-select v-model="form.taskType" placeholder="请选择任务类型" style="width: 100%">
              <el-option label="巡检" value="巡检" />
              <el-option label="维修" value="维修" />
              <el-option label="保养" value="保养" />
            </el-select>
          </el-form-item>
          <el-form-item label="执行人" prop="executePerson">
            <el-input v-model="form.executePerson" placeholder="请输入执行人" />
          </el-form-item>
          <el-form-item label="执行时间" prop="executeTime">
            <el-date-picker
              v-model="form.executeTime"
              type="datetime"
              placeholder="选择执行时间"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item label="任务状态" prop="taskStatus">
            <el-select v-model="form.taskStatus" placeholder="请选择任务状态" style="width: 100%">
              <el-option label="已完成" :value="1" />
              <el-option label="进行中" :value="2" />
              <el-option label="计划中" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="主要内容" prop="mainContent">
            <el-input
              type="textarea"
              v-model="form.mainContent"
              :rows="4"
              placeholder="请输入主要内容"
            />
          </el-form-item>
          <el-form-item label="关联设备ID" prop="relatedEquipId">
            <el-input v-model="form.relatedEquipId" placeholder="请输入关联设备ID" type="number" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </template>
      </el-dialog>

      <!-- 内容信息弹窗 -->
      <el-dialog v-model="contentDialogVisible" title="主要内容" width="40%">
        <div style="white-space: pre-wrap">{{ currentContent }}</div>
        <template #footer>
          <span>
            <el-button @click="contentDialogVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getBridgeMaintainList,
  getBridgeMaintain,
  addBridgeMaintain,
  updateBridgeMaintain,
  deleteBridgeMaintain,
} from "@/api/bridge/bisc/bridgeSafetyEquip"; 
import { getBridgeList } from "@/api/bridge/bisc/bridge";
import dayjs from "dayjs";
import { useRouter, useRoute } from "vue-router";
import BridgeVerticalTabNav from './components/BridgeVerticalTabNav.vue';

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const selectedRows = ref([]);

// 获取路由参数
const router = useRouter();
const route = useRoute();
const bridgeIdFromRoute = ref<string | null>(null);

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref();
const form = ref({
  maintainId: "",
  bridgeId: "",
  bridgeName: "",
  taskName: "",
  taskType: "",
  executePerson: "",
  executeTime: "",
  taskStatus: 1,
  mainContent: "",
  relatedEquipId: "",
});

// 内容弹窗相关
const contentDialogVisible = ref(false);
const currentContent = ref("");

// 桥梁列表
const bridgeOptions = ref([]);

// 表单校验规则
const rules = {
  bridgeId: [{ required: true, message: "请选择桥梁", trigger: "change" }],
  taskName: [{ required: true, message: "请输入运维任务名称", trigger: "blur" }],
  taskType: [{ required: true, message: "请选择任务类型", trigger: "change" }],
  executePerson: [{ required: true, message: "请输入执行人", trigger: "blur" }],
  executeTime: [{ required: true, message: "请选择执行时间", trigger: "change" }],
  taskStatus: [{ required: true, message: "请选择任务状态", trigger: "change" }],
  mainContent: [{ required: true, message: "请输入主要内容", trigger: "blur" }],
};

// 获取任务状态标签类型
const getTaskStatusType = (status: number) => {
  switch (status) {
    case 1: return "success";
    case 2: return "warning";
    case 3: return "info";
    default: return "info";
  }
};

// 获取任务状态标签文本
const getTaskStatusLabel = (status: number) => {
  switch (status) {
    case 1: return "已完成";
    case 2: return "进行中";
    case 3: return "计划中";
    default: return "未知";
  }
};

// 显示内容信息
const showContent = (row: any) => {
  currentContent.value = row.mainContent || "暂无内容";
  contentDialogVisible.value = true;
};

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    const params: any = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };
    
    // 如果有桥梁ID参数，添加到查询条件
    if (bridgeIdFromRoute.value) {
      params.bridgeId = bridgeIdFromRoute.value;
    }
    
    const res = await getBridgeMaintainList(params);
    // 处理时间格式化
    tableData.value = res.rows.map(item => ({
      ...item,
      executeTime: item.executeTime ? dayjs(item.executeTime).format('YYYY-MM-DD HH:mm:ss') : '',
    }));
    total.value = res.total;
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

// 获取桥梁列表数据
const getBridgeOptions = async () => {
  try {
    const params = {
      pageNum: 1,
      pageSize: 100000000,
    };
    const res = await getBridgeList(params);
    bridgeOptions.value = res.rows.map((item) => ({
      value: item.id,
      label: item.bridgeName,
    }));
  } catch (error) {
    console.error("获取桥梁列表失败:", error);
    ElMessage.error("获取桥梁列表失败");
  }
};

// 处理新增
const handleAdd = () => {
  dialogTitle.value = "新增数据";
  form.value = {
    maintainId: "",
    bridgeId: bridgeIdFromRoute.value || "",
    bridgeName: "",
    taskName: "",
    taskType: "",
    executePerson: "",
    executeTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    taskStatus: 1,
    mainContent: "",
    relatedEquipId: "",
  };
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row: any) => {
  try {
    const res = await updateBridgeMaintain(row.maintainId);
    form.value = {
      ...res.data,
      executeTime: res.data.executeTime ? dayjs(res.data.executeTime).format('YYYY-MM-DD HH:mm:ss') : '',
    };
    dialogTitle.value = "编辑数据";
    dialogVisible.value = true;
  } catch (error) {
    ElMessage.error("获取详情失败");
  }
};

// 处理查看
const handleView = async (row: any) => {
  try {
    const res = await getBridgeMaintain(row.maintainId);
    form.value = {
      ...res.data,
      executeTime: res.data.executeTime ? dayjs(res.data.executeTime).format('YYYY-MM-DD HH:mm:ss') : '',
    };
    dialogTitle.value = "查看详情";
    dialogVisible.value = true;
  } catch (error) {
    ElMessage.error("获取详情失败");
  }
};

// 处理删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm("确认删除该条数据吗？", "提示", {
    type: "warning",
  }).then(async () => {
    try {
      await deleteBridgeMaintain(row.maintainId);
      ElMessage.success("删除成功");
      getList();
    } catch (error) {
      ElMessage.error("删除失败");
    }
  });
};

// 处理批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }
  
  ElMessageBox.confirm(`确定要删除选中的${selectedRows.value.length}条数据吗？`, '警告', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await Promise.all(
        selectedRows.value.map((row) => deleteBridgeMaintain(row.maintainId))
      );
      ElMessage.success('删除成功');
      // 清空选中项
      selectedRows.value = [];
      // 刷新列表数据
      getList();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const submitForm = {
          ...form.value,
          executeTime: form.value.executeTime ? dayjs(form.value.executeTime).format('YYYY-MM-DD HH:mm:ss') : '',
        };
        
        if (form.value.maintainId) {
          await updateBridgeMaintenanceRecord(submitForm);
          ElMessage.success("修改成功");
        } else {
          await addBridgeMaintenanceRecord(submitForm);
          ElMessage.success("新增成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        ElMessage.error("操作失败");
      }
    }
  });
};

// 表格选择改变
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows;
};

// 分页相关方法
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getList();
};

onMounted(() => {
  // 初始化时获取路由参数
  if (route.query.bridgeId) {
    bridgeIdFromRoute.value = route.query.bridgeId as string;
  }
  
  // 先获取桥梁列表，再获取主数据
  getBridgeOptions().then(() => {
    getList();
  });
});

// 监听路由参数变化
watch(
  () => route.query.bridgeId,
  (newVal) => {
    if (newVal) {
      bridgeIdFromRoute.value = newVal as string;
    } else {
      bridgeIdFromRoute.value = null;
    }
    getList();
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  height: 100%;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.bridge-maintain-container {
  padding: 20px;
  flex: 1;
  overflow: auto;
  height: 100%;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);

  .operation-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    background: var(--el-bg-color-overlay);
    padding: 16px;
    border-radius: 4px;

    .left {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .custom-button {
      margin-left: 10px;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .custom-table {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--el-bg-color-overlay);

    :deep(th) {
      background: var(--el-bg-color-page) !important;
    }

    :deep(.el-table__row) {
      background-color: var(--el-bg-color-overlay);
    }

    .operation-button {
      padding: 0 10px;
      font-size: 14px;

      &.danger {
        color: var(--el-color-danger);
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

.custom-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    background: var(--el-bg-color-overlay);

    .el-dialog__header {
      background: var(--el-bg-color-page);
      margin: 0;
      padding: 20px;
      border-bottom: 1px solid var(--el-border-color-light);
    }

    .el-dialog__body {
      padding: 30px 20px;
    }

    .el-dialog__footer {
      border-top: 1px solid var(--el-border-color-light);
      padding: 15px 20px;
    }
  }

  .dialog-form {
    :deep(.el-form-item__label) {
      color: var(--el-text-color-regular);
    }

    :deep(.el-input__inner) {
      background-color: var(--el-bg-color-overlay);
      border-color: var(--el-border-color);
      color: var(--el-text-color-primary);
    }
  }
}

// 自定义 Element Plus 组件样式
:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-light);
  --el-table-header-bg-color: var(--el-bg-color-page);
  --el-table-row-hover-bg-color: var(--el-fill-color-light);
}

:deep(.el-button--primary) {
  &:hover {
    background: var(--el-color-primary-light-3);
    border-color: var(--el-color-primary-light-3);
  }
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 10px;
  background-color: var(--el-fill-color);
  border-color: var(--el-border-color-light);
  color: var(--el-text-color-regular);
}

.ellipsis-text {
  cursor: pointer;
  color: var(--el-color-primary);
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    text-decoration: underline;
    opacity: 0.8;
  }
}
</style>