<template>
  <div class="page-container">
    <div class="list-page">
      <!-- 返回按钮和标题 -->
      <div class="header-area">
        <el-button type="primary" icon="ArrowLeft" @click="goBack">返回设备管理</el-button>
        <h2 style="margin: 0; color: var(--el-text-color-primary);">大桥车辆卡口设备管理</h2>
      </div>

      <!-- 搜索和操作区域 -->
      <div class="filter-area">
        <div style="display: flex;">
          <div class="search-area" style="display: flex;">
            <el-input v-model="searchValue" placeholder="请输入设备名称搜索" clearable class="search-input" @keyup.enter="handleQuery"></el-input>
            <el-select v-model="statusFilter" placeholder="设备状态" clearable style="width: 120px; margin-left: 10px;">
              <el-option label="正常" :value="1" />
              <el-option label="故障" :value="2" />
              <el-option label="维护中" :value="3" />
            </el-select>
            <el-input v-model="positionFilter" placeholder="安装位置" clearable style="width: 150px; margin-left: 10px;" @keyup.enter="handleQuery"></el-input>
          </div>
        </div>

        <div class="operation-area">
          <el-button class="custom-button primary-button" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon>
            新增设备
          </el-button>
          <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="batchDelete">删除</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        height="calc(100vh - 300px)"
        :header-cell-style="{ background: 'var(--el-bg-color-page)' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="80" align="center" />
        <el-table-column label="设备编码" prop="deviceCode" min-width="120" />
        <el-table-column label="设备名称" prop="deviceName" min-width="150" />
        <el-table-column label="安装位置" prop="installPosition" min-width="150" />
        <el-table-column label="车道编号" prop="laneNumber" width="100" align="center" />
        <el-table-column label="设备类型" prop="deviceType" width="120" align="center" />
        <el-table-column label="设备型号" prop="model" width="120" align="center" />
        <el-table-column label="生产厂商" prop="manufacturer" width="120" align="center" />
        <el-table-column label="IP地址" prop="ipAddress" width="130" align="center" />
        <el-table-column label="设备状态" prop="status" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)">
              {{ getStatusName(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="负责人" prop="responsiblePerson" width="100" align="center" />
        <el-table-column label="联系电话" prop="contactPhone" width="130" align="center" />
        <el-table-column label="安装日期" prop="installDate" width="120" align="center" :formatter="formatDate" />
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 编辑/新增弹窗 -->
      <el-dialog v-model="dialogVisible" width="60%" top="5vh" destroy-on-close :show-header="false">
        <div style="height: 600px; display: flex; justify-content: center; align-items: center">
          <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="device-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备编码" prop="deviceCode">
                  <el-input v-model="form.deviceCode" placeholder="请输入设备编码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备名称" prop="deviceName">
                  <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="安装位置" prop="installPosition">
                  <el-input v-model="form.installPosition" placeholder="如：桥北入口/桥南出口" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车道编号" prop="laneNumber">
                  <el-input v-model="form.laneNumber" placeholder="请输入车道编号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="生产厂商" prop="manufacturer">
                  <el-input v-model="form.manufacturer" placeholder="请输入生产厂商" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="IP地址" prop="ipAddress">
                  <el-input v-model="form.ipAddress" placeholder="请输入设备IP地址" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备状态" prop="status">
                  <el-select v-model="form.status" placeholder="请选择设备状态" style="width: 100%">
                    <el-option label="正常" :value="1" />
                    <el-option label="故障" :value="2" />
                    <el-option label="维护中" :value="3" />
                  </el-select>
                </el-form-item>
                <el-form-item label="设备型号" prop="model">
                  <el-input v-model="form.model" placeholder="请输入设备型号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="安装日期" prop="installDate">
                  <el-date-picker
                    v-model="form.installDate"
                    type="date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    placeholder="选择安装日期"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="负责人" prop="responsiblePerson">
                  <el-input v-model="form.responsiblePerson" placeholder="请输入负责人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="上次维护日期" prop="lastMaintainDate">
              <el-date-picker
                v-model="form.lastMaintainDate"
                type="date"
                format="YYYY-MM-DD"
                placeholder="选择上次维护日期"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSubmit">确定</el-button>
              <el-button @click="dialogVisible = false">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { ArrowLeft, Plus } from '@element-plus/icons-vue';
import {
  getCheckpointDeviceList,
  addCheckpointDevice,
  updateCheckpointDevice,
  deleteCheckpointDevice,
  getCheckpointDeviceDetail
} from '@/api/bridge/bisc/checkpoint-device';

// 路由
const router = useRouter();

// 查询参数
const queryParams = ref<any>({
  pageNum: 1,
  pageSize: 10,
  deviceName: '',
  installPosition: '',
  status: null
});

// 表格数据
const tableData = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchValue = ref('');
const statusFilter = ref(null);
const positionFilter = ref('');
const selectedRows = ref([]);

// 弹窗控制
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');

// 表单数据
const formRef = ref();
const form = ref({
  id: '',
  deviceCode: '',
  deviceName: '',
  installPosition: '',
  laneNumber: '',
  deviceType: '',
  model: '',
  manufacturer: '',
  installDate: '',
  ipAddress: '',
  status: 1,
  lastMaintainDate: '',
  responsiblePerson: '',
  contactPhone: ''
});

// 表单校验规则
const rules = {
  deviceCode: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  installPosition: [{ required: true, message: '请输入安装位置', trigger: 'blur' }],
  deviceType: [{ required: true, message: '请输入设备类型', trigger: 'blur' }],
  manufacturer: [{ required: true, message: '请输入生产厂商', trigger: 'blur' }],
  status: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
  responsiblePerson: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
};

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    1: "正常",
    2: "故障",
    3: "维护中"
  };
  return statusMap[status] || "未知";
};

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    1: "success",  // 正常 - 绿色
    2: "danger",   // 故障 - 红色
    3: "warning"   // 维护中 - 黄色
  };
  return statusMap[status] || "";
};

// 返回设备管理页面
const goBack = () => {
  router.push('/basic-data/equipment');
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.deviceName = searchValue.value;
  queryParams.value.installPosition = positionFilter.value;
  queryParams.value.status = statusFilter.value;
  currentPage.value = 1;
  getList();
};

// 获取列表数据
const getList = async () => {
  try {
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      deviceName: searchValue.value,
      installPosition: positionFilter.value,
      status: statusFilter.value
    };
    const res = await getCheckpointDeviceList(params);
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error('获取数据失败');
  }
};

// 处理分页
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getList();
};

// 表格选择项改变
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 批量删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  const ids = selectedRows.value.map(item => item.id).join(',');

  ElMessageBox.confirm(`确定要删除选中的${selectedRows.value.length}条设备信息吗？`, '警告', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteCheckpointDevice(ids);
      ElMessage.success('删除成功');
      selectedRows.value = [];
      getList();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
};
// 日期格式化函数
const formatDate = (row, column, cellValue) => {
  return cellValue ? cellValue.split('T')[0] : '';
};


// 新增
const handleAdd = () => {
  dialogType.value = 'add';
  form.value = {
    id: '',
    deviceCode: '',
    deviceName: '',
    installPosition: '',
    laneNumber: '',
    deviceType: '',
    model: '',
    manufacturer: '',
    installDate: '',
    ipAddress: '',
    status: 1,
    lastMaintainDate: '',
    responsiblePerson: '',
    contactPhone: ''
  };
  dialogVisible.value = true;
};

// 编辑
const handleEdit = async (row: any) => {
  try {
    const res = await getCheckpointDeviceDetail(row.id);
    form.value = res.data;
    dialogType.value = 'edit';
    dialogVisible.value = true;
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error('获取详情失败');
  }
};

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确认删除该设备信息吗？', '警告', {
    type: 'warning'
  })
    .then(async () => {
      try {
        await deleteCheckpointDevice(row.id);
        ElMessage.success('删除成功');
        getList();
      } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {});
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await addCheckpointDevice(form.value);
          ElMessage.success('新增成功');
        } else {
          await updateCheckpointDevice(form.value);
          ElMessage.success('修改成功');
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        console.error('保存失败:', error);
        ElMessage.error('保存失败');
      }
    }
  });
};

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  height: 100%;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.list-page {
  flex: 1;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  overflow: auto;
}

.header-area {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-bg-color-overlay);
  border-radius: 4px;
}

.filter-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: var(--el-bg-color-overlay);
  padding: 16px;
  border-radius: 4px;

  .search-input {
    width: 200px;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: var(--el-bg-color-overlay);
}

.el-dialog {
  border-radius: 12px;

  :deep(.el-dialog__body) {
    padding: 24px;
  }
}

.device-form {
  width: 100%;
  padding: 20px;
}

.operation-area {
  display: flex;
  gap: 12px;

  .custom-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition:
        width 0.6s ease,
        height 0.6s ease;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      &::before {
        width: 300px;
        height: 300px;
      }
    }

    &:active {
      transform: translateY(1px);
    }
  }

  .primary-button {
    background: var(--el-color-primary);
    border-color: var(--el-color-primary);
    color: white;

    &:hover {
      background: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
  }
}
</style>



