<template>
  <div class="page-container">
    <BridgeVerticalTabNav :bridge-id="bridgeId" :bridge-name="bridgeName" />

    <div class="list-page">
      <!-- 搜索和操作区域 -->
      <div class="filter-area">
        <div class="search-area">
          <el-input
            v-model="queryParams.equipName"
            placeholder="请输入设备名称搜索"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          ></el-input>
        </div>

        <div class="operation-area">
          <el-button class="custom-button primary-button" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon>
            新增设备
          </el-button>
          <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="batchDelete">删除</el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        class="custom-table"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="bridgeId" label="桥梁名称">
          <template #default="{ row }">
            <span class="bridge-name">
              {{
                bridgeOptions.find((item) => item.value === Number(row.bridgeId))?.label ||
                row.bridgeName
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="80" align="center" />
        <el-table-column label="设备名称" prop="equipName" min-width="150" />
        <el-table-column label="设备类型" prop="equipType" width="150" align="center" />
        <el-table-column label="安装位置" prop="installPosition" min-width="170" align="center" />
        <el-table-column label="状态" prop="status" width="120" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 1" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.status === 2" type="warning">故障</el-tag>
            <el-tag v-else-if="scope.row.status === 3" type="info">停用</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="最后检查时间" prop="lastCheckTime" width="180" align="center">
          <template #default="scope">
            {{ formatDate(scope.row.lastCheckTime) }}
          </template>
        </el-table-column>
        <el-table-column label="最新监测值" prop="monitorValue" width="150" align="center" />
        <el-table-column label="报警状态" prop="alarmStatus" width="120" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.alarmStatus === 0" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.alarmStatus === 1" type="danger">报警</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 编辑/新增弹窗 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="50%"
        class="custom-dialog"
      >
        <div style="height: 500px; display: flex; justify-content: center; align-items: center">
          <BridgeSafetyEquipForm
            :id="String(currentRow.id)"
            :is-edit="dialogType === 'edit'"
            :edit-data="currentRow"
            :bridge-options="bridgeOptions"
            @success="handleSuccess"
            @close="dialogVisible = false"
          />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Plus } from '@element-plus/icons-vue';
import { getBridgeSafetyEquipList, addBridgeSafetyEquip, updateBridgeSafetyEquip, deleteBridgeSafetyEquip, getBridgeSafetyEquipDetail } from '@/api/bridge/bisc/bridgeSafetyEquip';
import BridgeSafetyEquipForm from './components/bridge-safety-equip-form.vue';
import BridgeVerticalTabNav from './components/BridgeVerticalTabNav.vue';
import { getBridgeList } from '@/api/bridge/bisc/bridge';
import dayjs from 'dayjs';

const route = useRoute();

// 获取桥梁ID和名称
const bridgeId = ref(route.query.bridgeId || '');
const bridgeName = ref(route.query.bridgeName || '');

// 查询参数
const queryParams = ref<any>({
  pageNum: 1,
  pageSize: 10,
  equipName: '',
  bridgeId: bridgeId.value
});

// 桥梁选项列表
const bridgeOptions = ref<{ value: number; label: string }[]>([]);

// 加载状态
const loading = ref(false);

// 弹窗标题
const dialogTitle = computed(() => {
  return dialogType.value === 'edit' ? '编辑设备' : '新增设备';
});

// 表格数据
const tableData = ref([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const selectedRows = ref([]);

// 弹窗控制
const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');

// 当前行数据
const currentRow = ref<{
  id?: string | number;
  [key: string]: any;
}>({});

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};

// 获取桥梁列表
const getBridgeListData = async () => {
  try {
    const res = await getBridgeList({ pageSize: 9999 });
    bridgeOptions.value = res.rows.map((item) => ({
      value: item.id,
      label: item.bridgeName
    }));
  } catch (error) {
    console.error('获取桥梁列表失败:', error);
  }
};

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true;
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      equipName: queryParams.value.equipName,
      bridgeId: bridgeId.value
    };
    const res = await getBridgeSafetyEquipList(params);
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理分页
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  getList();
};

// 新增
const handleAdd = () => {
  dialogType.value = 'add';
  currentRow.value = {};
  dialogVisible.value = true;
};

// 编辑
const handleEdit = async (row: any) => {
  try {
    const res = await getBridgeSafetyEquipDetail(row.id);
    currentRow.value = res.data;
    dialogType.value = 'edit';
    dialogVisible.value = true;
  } catch (error) {
    console.error('获取详情失败:', error);
  }
};

// 删除
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确认删除该设备信息吗？', '警告', {
    type: 'warning'
  })
    .then(async () => {
      try {
        await deleteBridgeSafetyEquip(row.id);
        ElMessage.success('删除成功');
        getList();
      } catch (error) {
        console.error('删除失败:', error);
      }
    })
    .catch(() => {});
};

// 提交表单成功回调
const handleSuccess = () => {
  dialogVisible.value = false;
  getList();
};

// 添加日期格式化函数
const formatDate = (date: string | number | Date) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 表格选择项改变
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 批量删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  // 获取选中行的ID列表
  const ids = selectedRows.value.map(item => item.id).join(',');

  ElMessageBox.confirm(`确定要删除选中的${selectedRows.value.length}条设备信息吗？`, '警告', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteBridgeSafetyEquip(ids);
      ElMessage.success('删除成功');
      // 清空选中项
      selectedRows.value = [];
      // 刷新列表数据
      getList();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

onMounted(() => {
  getList();
  getBridgeListData();
});
</script>

<style scoped lang="scss">
.page-container {
  display: flex;
  height: 100%;
  background: var(--el-bg-color-page);
  padding: 16px;
  box-sizing: border-box;
  gap: 16px;
}

.list-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.filter-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: var(--el-bg-color-overlay);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

.search-area {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.search-input {
  width: 300px;
}

.operation-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
}

.custom-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

.primary-button {
  background-color: var(--el-color-primary);
  color: white;
  border: none;

  &:hover {
    background-color: var(--el-color-primary-light-3);
  }
}
</style>