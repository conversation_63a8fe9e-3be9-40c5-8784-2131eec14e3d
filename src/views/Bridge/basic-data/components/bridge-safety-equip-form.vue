<template>
  <div class="form-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
        <el-form-item label="桥梁名称" prop="bridgeName">
          <el-select
            v-model="formData.bridgeName"
            placeholder="请选择桥梁"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="item in bridgeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </el-form-item>
      </el-col>
        <el-col :span="12">
          <el-form-item label="设备名称" prop="equipName">
            <el-input v-model="formData.equipName" placeholder="请输入设备名称" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型" prop="equipType">
            <el-select v-model="formData.equipType" placeholder="请选择设备类型" clearable style="width: 100%">
              <el-option label="监控" value="监控" />
              <el-option label="传感器" value="传感器" />
              <el-option label="消防" value="消防" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="安装位置" prop="installPosition">
            <el-input v-model="formData.installPosition" placeholder="请输入安装位置" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" clearable style="width: 100%">
              <el-option label="正常" :value="1" />
              <el-option label="故障" :value="2" />
              <el-option label="停用" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最后检查时间" prop="lastCheckTime">
            <el-date-picker
              v-model="formData.lastCheckTime"
              type="datetime"
              placeholder="请选择最后检查时间"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最新监测值" prop="monitorValue">
            <el-input v-model="formData.monitorValue" placeholder="请输入最新监测值" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报警状态" prop="alarmStatus">
            <el-select v-model="formData.alarmStatus" placeholder="请选择报警状态" clearable style="width: 100%">
              <el-option label="正常" :value="0" />
              <el-option label="报警" :value="1" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <div class="form-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { addBridgeSafetyEquip, updateBridgeSafetyEquip, getBridgeSafetyEquipDetail } from '@/api/bridge/bisc/bridgeSafetyEquip';

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  },
  bridgeOptions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['success', 'close']);

const formRef = ref();
const submitLoading = ref(false);

// 表单数据
const formData = reactive({
  id: '',
  bridgeId: '',
  bridgeName: '',
  equipName: '',
  equipType: '',
  installPosition: '',
  status: 1,
  lastCheckTime: '',
  monitorValue: '',
  alarmStatus: 0
});

// 表单验证规则
const rules = {
  bridgeName: [
    { required: true, message: '请选择桥梁', trigger: 'change' }
  ],
  equipName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  equipType: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  installPosition: [
    { required: true, message: '请输入安装位置', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 初始化数据
const initData = () => {
  if (props.isEdit && props.editData) {
    // 编辑模式下，填充表单数据
    Object.keys(formData).forEach(key => {
      if (props.editData[key] !== undefined) {
        formData[key] = props.editData[key];
      }
    });
  } else if (props.id && props.id === undefined) {
    // 如果有ID但不是编辑模式，可能是从详情页进入
    getDetail(props.id);
  }
};

// 获取详情
const getDetail = async (id: string) => {
  try {
    const res = await getBridgeSafetyEquipDetail(id);
    Object.keys(formData).forEach(key => {
      if (res.data[key] !== undefined) {
        formData[key] = res.data[key];
      }
    });
  } catch (error) {
    console.error('获取详情失败:', error);
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true;
      try {
        // 在提交前，根据bridgeName查找对应的bridgeId
        const selectedBridge = props.bridgeOptions.find(
          (item: any) => item.label === formData.bridgeName
        );
        
        const submitData = {
          ...formData,
          bridgeId: selectedBridge ? selectedBridge.value : ''
        };
        
        if (props.isEdit) {
          // 编辑
          await updateBridgeSafetyEquip(submitData);
          ElMessage.success('编辑成功');
        } else {
          // 新增
          await addBridgeSafetyEquip(submitData);
          ElMessage.success('新增成功');
        }
        emit('success');
      } catch (error) {
        console.error('保存失败:', error);
        ElMessage.error(props.isEdit ? '编辑失败' : '新增失败');
      } finally {
        submitLoading.value = false;
      }
    }
  });
};

// 取消
const handleCancel = () => {
  emit('close');
};

onMounted(() => {
  initData();
});
</script>

<style scoped lang="scss">
.form-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.form {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.form-footer {
  display: flex;
  justify-content: center;
  padding: 20px;
  gap: 20px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>