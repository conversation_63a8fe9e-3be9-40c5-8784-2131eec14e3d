<template>
  <div class="vertical-tab-nav" :class="{ 'is-collapsed': isCollapsed }">
    <div class="collapse-button" @click="toggleCollapse">
      <el-icon>
        <ArrowLeft v-if="!isCollapsed" />
        <ArrowRight v-else />
      </el-icon>
    </div>
    
    <div class="tabs-container">
      <div class="nav-tabs">
        <div 
          v-for="item in navItems" 
          :key="item.key"
          class="nav-tab-item"
          :class="{ 'is-active': activeTab === item.key }"
          @click="handleTabChange(item.key)"
        >
          <el-icon><component :is="item.icon" /></el-icon>
          <span v-show="!isCollapsed" class="tab-text">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, markRaw } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { 
  House, 
  DataLine, 
  Odometer, 
  Tools, 
  Monitor, 
  ArrowLeft, 
  ArrowRight 
} from '@element-plus/icons-vue';

const props = defineProps({
  bridgeId: {
    type: String,
    default: ''
  },
  bridgeName: {
    type: String,
    default: ''
  }
});

// 导航项列表
const navItems = [
  { key: 'list', label: '桥梁列表', icon: markRaw(House), path: '/basic-data/bridge' },
  { key: 'health', label: '健康数据', icon: markRaw(DataLine), path: '/basic-data/two-pages/bridge' },
  { key: 'cycle', label: '保养周期', icon: markRaw(Odometer), path: '/basic-data/three-pages/bridge' },
  { key: 'maintenance', label: '保养记录', icon: markRaw(Tools), path: '/basic-data/four-pages/bridge' },
  { key: 'maintain', label: '运维记录', icon: markRaw(Tools), path: '/basic-data/bridge-maintain' },
  { key: 'safety-equip', label: '安全设备', icon: markRaw(Monitor), path: '/basic-data/bridge-safety-equip' }
];

const router = useRouter();
const route = useRoute();
const activeTab = ref('list');
const isCollapsed = ref(false);

// 切换标签页
const handleTabChange = (tabName: string) => {
  if (activeTab.value === tabName) return; // 避免重复切换
  
  activeTab.value = tabName;
  const selectedItem = navItems.find(item => item.key === tabName);
  if (!selectedItem) return;
  
  const query = props.bridgeId ? { bridgeId: props.bridgeId, bridgeName: props.bridgeName } : {};
  router.push({ path: selectedItem.path, query });
};

// 根据当前路由设置活动标签
const setActiveTabFromRoute = (path: string) => {
  // 精确匹配路径
  for (const item of navItems) {
    if (path === item.path || path.startsWith(item.path + '/')) {
      activeTab.value = item.key;
      return;
    }
  }
  
  // 默认设为桥梁列表
  if (path === '/basic-data/bridge' || path === '/basic-data/bridge/') {
    activeTab.value = 'list';
  }
};

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  // 将折叠状态保存到本地存储中
  localStorage.setItem('bridgeVerticalTabNavCollapsed', isCollapsed.value ? 'true' : 'false');
};

onMounted(() => {
  // 初始化时从路由设置活动标签
  setActiveTabFromRoute(route.path);
  
  // 从本地存储中恢复折叠状态
  const savedCollapsedState = localStorage.getItem('bridgeVerticalTabNavCollapsed');
  if (savedCollapsedState !== null) {
    isCollapsed.value = savedCollapsedState === 'true';
  }
});

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    setActiveTabFromRoute(newPath);
  }
);
</script>

<style scoped lang="scss">
.vertical-tab-nav {
  position: relative;
  height: 100%;
  width: 100px;
  background: inherit;
  border-right: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  overflow: hidden;
  
  &.is-collapsed {
    width: 60px;
  }
  
  .collapse-button {
    position: absolute;
    right: 0;
    top: 10px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: var(--el-color-primary-light-9);
    border-radius: 4px 0 0 4px;
    color: var(--el-color-primary);
    transition: all 0.3s ease;
    z-index: 10;
    
    &:hover {
      background: var(--el-color-primary-light-7);
    }
  }
  
  .tabs-container {
    height: 100%;
    padding: 20px 0;
  }
  
  .nav-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .nav-tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 6px;
      padding: 16px 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: transparent;
        transition: background-color 0.3s ease;
      }
      
      &.is-active {
        color: var(--el-color-primary);
        font-weight: bold;
        
        &::after {
          background-color: var(--el-color-primary);
        }
        
        .el-icon {
          transform: translateY(-2px) scale(1.1);
        }
      }
      
      &:hover {
        background-color: var(--el-color-primary-light-9);
        
        .el-icon {
          transform: translateY(-2px) scale(1.1);
        }
      }
      
      .el-icon {
        font-size: 20px;
        transition: transform 0.3s ease;
      }
      
      .tab-text {
        font-size: 12px;
        white-space: nowrap;
        transition: opacity 0.3s ease;
      }
    }
  }
}
</style>