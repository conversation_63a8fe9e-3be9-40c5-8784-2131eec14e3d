<template>
  <div class="bridge-form">
    <div class="form-header">
      <div class="title">{{ isEdit ? '编辑桥梁' : '新增桥梁' }}</div>
    </div>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="form-content">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="桥梁名称" prop="bridgeName">
            <el-input v-model="form.bridgeName" placeholder="请输入桥梁名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁结构类型" prop="bridgeType">
            <el-input v-model="form.bridgeType" placeholder="请输入桥梁结构类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁类型" prop="type">
            <el-input v-model="form.type" placeholder="请输入桥梁类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="通航等级" prop="navigationGrade">
            <el-input v-model="form.navigationGrade" placeholder="请输入通航等级" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属区域" prop="region">
            <el-input v-model="form.region" placeholder="请输入所属区域" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建成日期" prop="buildDate">
            <el-date-picker
              v-model="form.buildDate"
              type="date"
              placeholder="选择日期时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :default-time="new Date(2000, 1, 1, 0, 0, 0)"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁长度(米)" prop="bridgeLength">
            <el-input-number v-model="form.bridgeLength" :min="0" :precision="2" style="width: 100%" placeholder="请输入桥梁长度(米)" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁宽度(米)" prop="bridgeWidth">
            <el-input-number v-model="form.bridgeWidth" :min="0" :precision="2" style="width: 100%" placeholder="请输入桥梁宽度(米)" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="桥梁高度(米)" prop="bridgeHeight">
            <el-input-number v-model="form.bridgeHeight" :min="0" :precision="2" style="width: 100%" placeholder="请输入桥梁高度(米)" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设计荷载(吨)" prop="designLoad">
            <el-input-number v-model="form.designLoad" :min="0" :precision="2" style="width: 100%" placeholder="请输入设计荷载(吨)" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input v-model="form.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="桥梁图片">
            <el-upload
              :action="uploadFileUrl"
              :headers="headers"
              :file-list="uploadFileList"
              :on-success="handleUploadSuccess"
              :on-remove="handleUploadRemove"
              :before-upload="beforeUpload"
              :on-error="handleUploadError"
              @progress="handleUploadProgress"
              multiple
              list-type="picture-card"
              name="file"
              accept="image/*"
            >
              <el-icon><Plus /></el-icon>
              <template #file="{ file }">
                <div class="upload-file-item">
                  <el-image 
                    :src="file.url" 
                    :preview-src-list="[file.url]" 
                    class="upload-image" 
                    fit="cover" 
                  />
                  <div class="file-name">{{ file.originalName || file.name }}</div>
                  <el-icon
                    class="delete-icon"
                    @click.stop="handleUploadRemove(file, uploadFileList)"
                  ><Delete /></el-icon>
                </div>
              </template>
            </el-upload>
            <div class="el-upload__tip">支持上传jpg、jpeg、png、gif等图片格式，单个文件不超过5MB</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="form-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit" :disabled="uploading">确 定</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Close, Plus, Delete } from '@element-plus/icons-vue';
import { addBridge, updateBridge } from '@/api/bridge/bisc/bridge';
import dayjs from 'dayjs';
import { listByIds, delOss } from '@/api/system/oss';
import { globalHeaders } from '@/utils/request';

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  }
});

// 添加默认表单数据
const defaultFormData = {
  id:null,
  bridgeName: '',
  bridgeType: '',
  type: '',
  navigationGrade: '',
  bridgeLength: 0,
  bridgeWidth: 0,
  bridgeHeight: 0,
  designLoad: 0,
  buildDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  region: '',
  remarks: '',
  fileId: ''
};

function initForm() {
  // 如果是编辑模式，使用编辑数据
  if (props.isEdit && props.editData) {
    form.value = {
      ...defaultFormData,
      ...props.editData
    };
  } else {
    // 如果是新增模式，使用默认数据
    form.value = { ...defaultFormData };
  }
}

const emit = defineEmits(['success', 'close']);

const formRef = ref();
const form = ref({ ...defaultFormData });

// 监听 isEdit 和 editData 的变化
watch([() => props.isEdit, () => props.editData], () => {
  initForm();
}, { immediate: true });

const rules = {
  bridgeName: [{ required: true, message: '请输入桥梁名称', trigger: 'blur' }],
  bridgeType: [{ required: true, message: '请选择桥梁类型', trigger: 'change' }],
  bridgeLength: [{ required: true, message: '请输入桥梁长度', trigger: 'blur' }],
  bridgeWidth: [{ required: true, message: '请输入桥梁宽度', trigger: 'blur' }],
  region: [{ required: true, message: '请输入所属区域', trigger: 'blur' }],
  buildDate: [{ required: true, message: '请选择建成日期', trigger: 'change' }]
};

const handleClose = () => {
  emit('close');
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const submitData = {
          ...form.value,
          id: props.isEdit ? props.id : undefined,
          buildDate: form.value.buildDate ? dayjs(form.value.buildDate).format('YYYY-MM-DD HH:mm:ss') : undefined
        };

        if (props.isEdit) {
          await updateBridge(submitData);
        } else {
          await addBridge(submitData);
        }
        ElMessage.success(props.isEdit ? '修改成功' : '新增成功');
        emit('success');
      } catch (error) {
        console.error(props.isEdit ? '修改失败' : '新增失败', error);
      }
    }
  });
};

const uploadFileUrl = import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload';
const headers = globalHeaders();

// 附件上传相关响应式数据
const uploadFileList = ref([]);
const uploading = ref(false);

// 根据fileId获取文件列表
watch(() => props.editData.fileId, async (newVal) => {
  if (newVal) {
    const res = await listByIds(newVal);
    if (res.data) {
      uploadFileList.value = res.data.map(item => ({
        name: item.originalName || item.fileName,
        url: item.url,
        ossId: item.ossId
      }));
    } else {
      uploadFileList.value = [];
    }
  } else {
    uploadFileList.value = [];
  }
}, { immediate: true });

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
};

const handleUploadProgress = (event, file, fileList) => {
  uploading.value = true;
};

const handleUploadSuccess = (response, file, fileList) => {
  if (response.code === 200) {
    let ids = form.value.fileId ? form.value.fileId.split(',') : [];
    ids.push(response.data.ossId);
    form.value.fileId = ids.join(',');
    ElMessage.success('上传成功');
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
  // 检查是否还有文件在上传
  uploading.value = fileList.some(f => f.status === 'uploading');
};

const handleUploadError = () => {
  ElMessage.error('上传文件失败');
  uploading.value = false;
};

const handleUploadRemove = async (file, fileList) => {
  let ids = form.value.fileId ? form.value.fileId.split(',') : [];
  ids = ids.filter(id => id !== file.ossId);
  form.value.fileId = ids.join(',');
  try {
    await delOss(file.ossId);
    // 更新文件列表，移除已删除的文件
    uploadFileList.value = uploadFileList.value.filter(item => item.ossId !== file.ossId);
    ElMessage.success('删除成功');
  } catch (e) {
    ElMessage.error('删除失败');
  }
  // 检查是否还有文件在上传
  uploading.value = fileList.some(f => f.status === 'uploading');
};

initForm();
</script>

<style scoped lang="scss">
.bridge-form {
  background: transparent;
  border-radius: 0;
  box-shadow: none;

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    background: transparent;
    border-radius: 0;
    border-bottom: none;
    padding: 16px 24px;
    background: var(--el-bg-color-page);
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid var(--el-border-color-light);

    .title {
      font-size: 18px;
      color: #303133;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .close-btn {
      transition: all 0.3s;
      padding: 10px;
      border-radius: 6px;

      &:hover {
        background-color: #f4f4f5;
        color: #909399;
      }
    }
  }

  .form-content {
    padding: 24px 0;
    background: transparent;

    :deep(.el-form-item) {
      margin-bottom: 24px;

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
      }
    }

    :deep(.el-input),
    :deep(.el-select),
    :deep(.el-date-editor),
    :deep(.el-input-number) {
      width: 100%;
    }

    :deep(.el-input-number) {
      .el-input__inner {
        text-align: left;
      }
    }
  }

  .form-footer {
    padding: 16px 0;
    text-align: right;
    border-top: none;
    background: transparent;
    border-radius: 0;

    .el-button {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 10px 24px;
      border-radius: 6px;

      &:hover {
        transform: translateY(-1px);
      }

      &.el-button--primary:hover {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

/* 文件上传相关样式 */
.upload-file-item {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  overflow: hidden;
  background-color: #f5f7fa;
  transition: all 0.3s;

  &:hover {
    .delete-icon {
      opacity: 1;
    }
  }

  .upload-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .file-name {
    width: 100%;
    padding: 4px 8px;
    font-size: 12px;
    color: #606266;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: rgba(255, 255, 255, 0.9);
  }

  .delete-icon {
    position: absolute;
    top: 4px;
    right: 4px;
    padding: 4px;
    font-size: 16px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;

    &:hover {
      background-color: var(--el-color-danger);
    }
  }
}

:deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  line-height: 120px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
  margin: 0 8px 8px 0;
}
</style>
