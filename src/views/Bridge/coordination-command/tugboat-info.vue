<template>
  <div class="list-page">
    <EmergencyVerticalTabNav />
    <div class="content-container">
      <div class="mb-[10px]">
        <el-card shadow="hover">
          <el-form
            ref="queryFormRef"
            :model="queryParams"
            :inline="true"
            label-width="80px"
            style="display: flex;justify-content: space-between;width: 100%;"
          >
            <div>
              <el-form-item label="拖轮名称" prop="tugboatName">
                <el-input
                  v-model="queryParams.tugboatName"
                  placeholder="请输入拖轮名称"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 200px"
                >
                  <el-option
                    v-for="dict in statusOptions"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
              </el-form-item>
            </div>
            <div>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Plus" class="add-button" @click="handleAdd">新增</el-button>
              <el-button type="danger" icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
            </div>
          </el-form>
        </el-card>
      </div>

      <!-- 列表区域 -->
      <el-card shadow="hover" class="w-full">
        <el-table
          v-loading="loading"
          :data="tugboatList"
          @selection-change="handleSelectionChange"
          border
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="拖轮编号" align="center" prop="tugboatNo" />
          <el-table-column label="拖轮名称" align="center" prop="tugboatName" />
          <el-table-column label="所属船队公司" align="center" prop="fleetCompany" />
          <el-table-column label="拖轮类型" align="center" prop="tugboatType" />
          <el-table-column label="功率(马力)" align="center" prop="powerHorsepower" />
          <el-table-column label="建造年份" align="center" prop="buildYear" />
          <el-table-column label="母港" align="center" prop="homePort" />
          <el-table-column label="作业区域" align="center" prop="operationArea" />
          <el-table-column label="船长姓名" align="center" prop="captainName" />
          <el-table-column label="船长联系电话" align="center" prop="captainPhone" />
          <el-table-column label="船员人数" align="center" prop="crewCount" />
          <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="当前位置" align="center" prop="currentPosition" />
          <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                icon="Edit"
                @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                type="text"
                size="small"
                icon="Delete"
                @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            background
            :current-page="queryParams.pageNum"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="queryParams.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

    <!-- 添加或修改拖轮信息对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="50%"
      center
      destroy-on-close
      class="custom-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <span class="dialog-title">{{ title }}</span>
        </div>
      </template>
      <el-form
        ref="tugboatFormRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="dialog-form"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="拖轮编号" prop="tugboatNo">
              <el-input v-model="form.tugboatNo" placeholder="请输入拖轮编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="拖轮名称" prop="tugboatName">
              <el-input v-model="form.tugboatName" placeholder="请输入拖轮名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属船队公司" prop="fleetCompany">
              <el-input v-model="form.fleetCompany" placeholder="请输入所属船队公司" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="拖轮类型" prop="tugboatType">
              <el-input v-model="form.tugboatType" placeholder="请输入拖轮类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="功率(马力)" prop="powerHorsepower">
              <el-input-number
                v-model="form.powerHorsepower"
                controls-position="right"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="建造年份" prop="buildYear">
              <el-date-picker
                v-model="form.buildYear"
                type="year"
                value-format="YYYY"
                placeholder="请选择建造年份"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="母港" prop="homePort">
              <el-input v-model="form.homePort" placeholder="请输入母港" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="作业区域" prop="operationArea">
              <el-input v-model="form.operationArea" placeholder="请输入作业区域" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="船长姓名" prop="captainName">
              <el-input v-model="form.captainName" placeholder="请输入船长姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="船长联系电话" prop="captainPhone">
              <el-input v-model="form.captainPhone" placeholder="请输入船长联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="船员人数" prop="crewCount">
              <el-input-number
                v-model="form.crewCount"
                controls-position="right"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option
                  v-for="dict in statusOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="配备设备" prop="equipmentConfig">
              <el-input
                v-model="form.equipmentConfig"
                type="textarea"
                placeholder="请输入配备设备"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="当前位置" prop="currentPosition">
              <el-input
                v-model="form.currentPosition"
                type="textarea"
                placeholder="请输入当前位置"
                :rows="2"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </template>
    </el-dialog>
  </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElForm, ElMessage, ElMessageBox } from 'element-plus';
import EmergencyVerticalTabNav from './components/EmergencyVerticalTabNav.vue';
import {
  getTugboatList,
  getTugboatDetail,
  addTugboat,
  updateTugboat,
  deleteTugboat
} from '@/api/bridge/command/tugboat';

// 定义状态选项
const statusOptions = ref([
  { value: 1, label: '在港待命' },
  { value: 2, label: '作业中' },
  { value: 3, label: '维修' },
  { value: 4, label: '坞修' }
]);

// 获取状态名称
const getStatusName = (status: number) => {
  const statusMap: { [key: number]: string } = {
    1: '在港待命',
    2: '作业中',
    3: '维修',
    4: '坞修'
  };
  return statusMap[status] || '未知';
};

// 获取状态类型
const getStatusType = (status: number) => {
  const typeMap: { [key: number]: string } = {
    1: 'success',
    2: 'warning',
    3: 'info',
    4: 'danger'
  };
  return typeMap[status] || 'info';
};

// 定义查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  tugboatName: '',
  status: null
});

// 定义表单数据
const form = reactive({
  tugboatId: undefined,
  tugboatNo: '',
  tugboatName: '',
  fleetCompany: '',
  tugboatType: '',
  powerHorsepower: undefined,
  buildYear: undefined,
  homePort: '',
  operationArea: '',
  captainName: '',
  captainPhone: '',
  crewCount: undefined,
  equipmentConfig: '',
  status: 1,
  currentPosition: ''
});

// 定义验证规则
const rules = reactive({
  tugboatNo: [{ required: true, message: '拖轮编号不能为空', trigger: 'blur' }],
  tugboatName: [{ required: true, message: '拖轮名称不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
});

// 定义其他变量
const tugboatList = ref([]);
const loading = ref(false);
const total = ref(0);
const open = ref(false);
const title = ref('');
const single = ref(true);
const multiple = ref(true);
const ids = ref([]);

// 定义表单引用
const queryFormRef = ref(ElForm);
const tugboatFormRef = ref(ElForm);

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    const response = await getTugboatList(queryParams);
    tugboatList.value = response.rows;
    total.value = response.total;
  } finally {
    loading.value = false;
  }
};

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryFormRef.value.resetFields();
  handleQuery();
};

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map(item => item.tugboatId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 批量删除
const handleDelete = async (row?: any) => {
  const deleteIds = row?.tugboatId || ids.value;
  if (!deleteIds) return;

  try {
    await ElMessageBox.confirm('是否确认删除选中的数据项?', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await deleteTugboat(deleteIds);
    ElMessage.success('删除成功');
    getList();
  } catch (error) {
    // 用户取消删除或出现错误
  }
};

// 新增
const handleAdd = () => {
  open.value = true;
  title.value = '添加拖轮';
  tugboatFormRef.value.resetFields();
  Object.assign(form, {
    tugboatId: undefined,
    tugboatNo: '',
    tugboatName: '',
    fleetCompany: '',
    tugboatType: '',
    powerHorsepower: undefined,
    buildYear: undefined,
    homePort: '',
    operationArea: '',
    captainName: '',
    captainPhone: '',
    crewCount: undefined,
    equipmentConfig: '',
    status: 1,
    currentPosition: ''
  });
};

// 修改
const handleUpdate = async (row?: any) => {
  const tugboatId = row?.tugboatId || ids.value[0];
  if (!tugboatId) return;

  try {
    const response = await getTugboatDetail(tugboatId);
    Object.assign(form, response.data);
    open.value = true;
    title.value = '修改拖轮';
  } catch (error) {
    ElMessage.error('获取详情失败');
  }
};

// 表单提交
const submitForm = async () => {
  tugboatFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return;

    try {
      if (form.tugboatId) {
        await updateTugboat(form);
        ElMessage.success('修改成功');
      } else {
        await addTugboat(form);
        ElMessage.success('新增成功');
      }
      open.value = false;
      getList();
    } catch (error) {
      // 错误处理
    }
  });
};

// 取消
const cancel = () => {
  open.value = false;
  tugboatFormRef.value.resetFields();
};

// 分页处理
const handleSizeChange = (val: number) => {
  queryParams.pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.pageNum = val;
  getList();
};

// 页面挂载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
$bg-color-page: var(--el-bg-color-page);
$bg-color-overlay: var(--el-bg-color-overlay);
$box-shadow-light: var(--el-box-shadow-light);
$box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
$border-radius: 4px;
$border-radius-dialog: 8px;
$border-color-light: var(--el-border-color-light);
$color-primary: var(--el-color-primary);
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$deep-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
$header-padding: 15px 20px;
$general-padding: 20px;
$footer-padding: 10px 20px;
$divider-color: #f0f0f0;

.list-page {
  padding: 20px;
  background: $bg-color-page;
  border-radius: $border-radius;
  box-shadow: $box-shadow-light;
  display: flex;
  gap: 20px;
}

.content-container {
  flex: 1;
  overflow: hidden;
}

.filter-area {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  background: $bg-color-overlay;
  padding: 16px;
  border-radius: $border-radius;
}

.search-input {
  width: 300px;
  margin-left: auto;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: $bg-color-overlay;

  :deep(th) {
    background: $bg-color-page !important;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: $bg-color-page;
  border-radius: $border-radius-dialog $border-radius-dialog 0 0;
  border-bottom: 1px solid $border-color-light;

  .dialog-title {
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }
}

.btn-group {
  .edit-btn,
  .cancel-btn,
  .save-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 10px 20px;
    border-radius: 6px;
  }

  .edit-btn {
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .cancel-btn {
    &:hover {
      background-color: #f4f4f5;
      color: #909399;
    }
  }

  .save-btn {
    padding: 10px 24px;

    &:hover {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }
  }
}

.disabled-field {
  :deep(.el-input__inner) {
    background-color: #f8f9fa;
    border-color: #e4e7ed;
    color: #909399;
    cursor: not-allowed;
  }
}

.error-message {
  color: red;
  font-size: 14px;
}

.custom-dialog {
  :deep(.el-dialog) {
    border-radius: $border-radius-dialog;
    box-shadow: $box-shadow;

    .el-dialog__body {
      padding: 24px;
      background: $bg-color-overlay;
    }

    .el-dialog__header {
      padding: 0;
      margin: 0;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #303133;
        font-size: 24px;
        z-index: 1000;

        &:hover {
          color: $color-primary;
        }
      }
    }

    .el-dialog__footer {
      padding: 10px 20px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.dialog-form {
  :deep(.el-form-item__label) {
    color: var(--el-text-color-regular);
  }

  :deep(.el-input__inner) {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
    color: var(--el-text-color-primary);
  }
}
</style>
