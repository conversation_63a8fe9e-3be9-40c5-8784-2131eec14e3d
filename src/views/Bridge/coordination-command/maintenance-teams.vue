<template>
  <div class="list-page">
    <EmergencyVerticalTabNav />
    <div class="content-container">
      <div class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" style="display: flex;justify-content: space-between;width: 100%;">
            <div>
              <el-form-item label="队伍名称" prop="teamName">
                <el-input v-model="queryParams.teamName" placeholder="请输入队伍名称" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="工作状态" prop="workStatus">
                <el-select v-model="queryParams.workStatus" placeholder="请选择工作状态" clearable style="width: 180px">
                  <el-option label="待命" :value="1" />
                  <el-option label="作业中" :value="2" />
                  <el-option label="休整" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              </el-form-item>
            </div>
            <div>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Plus" class="add-button" @click="handleAdd">新增</el-button>
              <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="batchDelete">删除</el-button>
            </div>
          </el-form>
        </el-card>
      </div>

      <!-- 列表区域 -->
      <el-card shadow="hover" class="w-full">
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
          border
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" width="80" align="center">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="teamCode" label="队伍编码" min-width="120" />
          <el-table-column prop="teamName" label="队伍名称" min-width="150" />
          <el-table-column prop="responsibleBridgeNames" label="负责桥梁" min-width="200" />
          <el-table-column prop="teamLeader" label="队长姓名" width="120" />
          <el-table-column prop="leaderPhone" label="队长联系电话" width="150" />
          <el-table-column prop="teamSize" label="队伍人数" width="100" align="center" />
          <el-table-column prop="specialty" label="专业领域" min-width="150" />
          <el-table-column prop="baseLocation" label="基地位置" min-width="150" />
          <el-table-column prop="workStatus" label="工作状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getWorkStatusTag(row.workStatus)">
                {{ getWorkStatusName(row.workStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button size="small" type="danger" link @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            background
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 新增/编辑弹窗 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="50%"
        center
        destroy-on-close
        class="custom-dialog"
      >
        <template #header>
          <div class="dialog-header">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </div>
        </template>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="dialog-form"
        >
          <el-form-item label="队伍编码" prop="teamCode">
            <el-input v-model="form.teamCode" placeholder="请输入队伍编码" />
          </el-form-item>
          <el-form-item label="队伍名称" prop="teamName">
            <el-input v-model="form.teamName" placeholder="请输入队伍名称" />
          </el-form-item>
          <el-form-item label="负责桥梁" prop="responsibleBridgeIds">
            <el-select 
              v-model="form.responsibleBridgeIds" 
              placeholder="请选择负责桥梁" 
              multiple 
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="item in bridgeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="队长姓名" prop="teamLeader">
            <el-input v-model="form.teamLeader" placeholder="请输入队长姓名" />
          </el-form-item>
          <el-form-item label="队长联系电话" prop="leaderPhone">
            <el-input v-model="form.leaderPhone" placeholder="请输入队长联系电话" />
          </el-form-item>
          <el-form-item label="队伍人数" prop="teamSize">
            <el-input-number v-model="form.teamSize" :min="1" style="width: 100%" />
          </el-form-item>
          <el-form-item label="专业领域" prop="specialty">
            <el-input v-model="form.specialty" placeholder="请输入专业领域" />
          </el-form-item>
          <el-form-item label="配备设备" prop="equipmentConfig">
            <el-input 
              v-model="form.equipmentConfig" 
              placeholder="请输入配备设备" 
              type="textarea"
              :rows="3"
            />
          </el-form-item>
          <el-form-item label="基地位置" prop="baseLocation">
            <el-input v-model="form.baseLocation" placeholder="请输入基地位置" />
          </el-form-item>
          <el-form-item label="工作状态" prop="workStatus">
            <el-radio-group v-model="form.workStatus">
              <el-radio :label="1">待命</el-radio>
              <el-radio :label="2">作业中</el-radio>
              <el-radio :label="3">休整</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { Plus, Search, RefreshRight } from '@element-plus/icons-vue';
import { 
  getMaintenanceTeamList, 
  getMaintenanceTeamDetail, 
  addMaintenanceTeam, 
  updateMaintenanceTeam, 
  deleteMaintenanceTeam 
} from '@/api/bridge/command/maintenanceTeam';
import EmergencyVerticalTabNav from "./components/EmergencyVerticalTabNav.vue";

// 路由
const router = useRouter();

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const selectedRows = ref([]);
const queryFormRef = ref();

// 桥梁选项
const bridgeOptions = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  teamName: '',
  workStatus: null
});

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref();
const form = ref({
  teamId: null,
  teamCode: "",
  teamName: "",
  responsibleBridgeIds: [],
  teamLeader: "",
  leaderPhone: "",
  teamSize: 1,
  specialty: "",
  equipmentConfig: "",
  baseLocation: "",
  workStatus: 1
});

// 表单验证规则
const rules = {
  teamCode: [
    { required: true, message: "请输入队伍编码", trigger: "blur" }
  ],
  teamName: [
    { required: true, message: "请输入队伍名称", trigger: "blur" }
  ],
  teamLeader: [
    { required: true, message: "请输入队长姓名", trigger: "blur" }
  ],
  leaderPhone: [
    { required: true, message: "请输入队长联系电话", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
  ],
  baseLocation: [
    { required: true, message: "请输入基地位置", trigger: "blur" }
  ],
  workStatus: [
    { required: true, message: "请选择工作状态", trigger: "change" }
  ]
};

// 获取工作状态名称
const getWorkStatusName = (status) => {
  const statusMap = {
    1: "待命",
    2: "作业中",
    3: "休整"
  };
  return statusMap[status] || "未知";
};

// 获取工作状态标签样式
const getWorkStatusTag = (status) => {
  const statusMap = {
    1: "success",  // 待命 - 绿色
    2: "danger",   // 作业中 - 红色
    3: "warning"   // 休整 - 黄色
  };
  return statusMap[status] || "";
};

// 获取养护队伍列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await getMaintenanceTeamList({
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      teamName: queryParams.teamName,
      workStatus: queryParams.workStatus
    });
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error("获取养护队伍列表失败", error);
    ElMessage.error("获取养护队伍列表失败");
  } finally {
    loading.value = false;
  }
};

// 获取桥梁列表（模拟数据）
const getBridgeList = async () => {
  // 这里应该调用实际的桥梁列表接口
  // 暂时使用模拟数据
  bridgeOptions.value = [
    { value: 1, label: "南京长江大桥" },
    { value: 2, label: "武汉长江大桥" },
    { value: 3, label: "重庆朝天门大桥" },
    { value: 4, label: "苏通长江大桥" },
    { value: 5, label: "杭州湾跨海大桥" }
  ];
};

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.teamName = '';
  queryParams.workStatus = null;
  handleQuery();
};

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  // 获取选中行的ID列表
  const ids = selectedRows.value.map(item => item.teamId).join(',');

  ElMessageBox.confirm('确定要删除选中的养护队伍吗？', '警告', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteMaintenanceTeam(ids);
      ElMessage.success('删除成功');
      // 清空选中项
      selectedRows.value = [];
      // 刷新列表数据
      getList();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理新增
const handleAdd = () => {
  dialogTitle.value = "新增养护队伍";
  form.value = {
    teamId: null,
    teamCode: "",
    teamName: "",
    responsibleBridgeIds: [],
    teamLeader: "",
    leaderPhone: "",
    teamSize: 1,
    specialty: "",
    equipmentConfig: "",
    baseLocation: "",
    workStatus: 1
  };
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  dialogTitle.value = "编辑养护队伍";
  try {
    const res = await getMaintenanceTeamDetail(row.teamId);
    // 处理负责桥梁ID集合
    if (res.data.responsibleBridgeIds) {
      res.data.responsibleBridgeIds = res.data.responsibleBridgeIds.split(',').map(id => parseInt(id));
    } else {
      res.data.responsibleBridgeIds = [];
    }
    form.value = res.data;
    dialogVisible.value = true;
  } catch (error) {
    console.error("获取养护队伍详情失败", error);
    ElMessage.error("获取养护队伍详情失败");
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除养护队伍"${row.teamName}"吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteMaintenanceTeam(row.teamId);
      ElMessage.success("删除成功");
      getList();
    } catch (error) {
      console.error("删除养护队伍失败", error);
      ElMessage.error("删除养护队伍失败");
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 处理负责桥梁ID集合
        const formData = { ...form.value };
        if (Array.isArray(formData.responsibleBridgeIds)) {
          formData.responsibleBridgeIds = formData.responsibleBridgeIds.join(',');
        }
        
        if (form.value.teamId) {
          // 编辑
          await updateMaintenanceTeam(formData);
          ElMessage.success("更新成功");
        } else {
          // 新增
          await addMaintenanceTeam(formData);
          ElMessage.success("新增成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        console.error("操作失败", error);
        ElMessage.error("操作失败");
      }
    }
  });
};

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  queryParams.pageSize = val;
  getList();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  queryParams.pageNum = val;
  getList();
};

onMounted(() => {
  getList();
  getBridgeList();
});
</script>

<style scoped lang="scss">
$bg-color-page: var(--el-bg-color-page);
$bg-color-overlay: var(--el-bg-color-overlay);
$box-shadow-light: var(--el-box-shadow-light);
$box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
$border-radius: 4px;
$border-radius-dialog: 8px;
$border-color-light: var(--el-border-color-light);
$color-primary: var(--el-color-primary);
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$deep-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
$header-padding: 15px 20px;
$general-padding: 20px;
$footer-padding: 10px 20px;
$divider-color: #f0f0f0;

.list-page {
  padding: 20px;
  background: $bg-color-page;
  border-radius: $border-radius;
  box-shadow: $box-shadow-light;
  display: flex;
  gap: 20px;
}

.content-container {
  flex: 1;
  overflow: hidden;
}

.filter-area {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  background: $bg-color-overlay;
  padding: 16px;
  border-radius: $border-radius;
}

.search-input {
  width: 300px;
  margin-left: auto;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: $bg-color-overlay;

  :deep(th) {
    background: $bg-color-page !important;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: $bg-color-page;
  border-radius: $border-radius-dialog $border-radius-dialog 0 0;
  border-bottom: 1px solid $border-color-light;

  .dialog-title {
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }
}

.btn-group {
  .edit-btn,
  .cancel-btn,
  .save-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 10px 20px;
    border-radius: 6px;
  }

  .edit-btn {
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .cancel-btn {
    &:hover {
      background-color: #f4f4f5;
      color: #909399;
    }
  }

  .save-btn {
    padding: 10px 24px;

    &:hover {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }
  }
}

.disabled-field {
  :deep(.el-input__inner) {
    background-color: #f8f9fa;
    border-color: #e4e7ed;
    color: #909399;
    cursor: not-allowed;
  }
}

.error-message {
  color: red;
  font-size: 14px;
}

.custom-dialog {
  :deep(.el-dialog) {
    border-radius: $border-radius-dialog;
    box-shadow: $box-shadow;

    .el-dialog__body {
      padding: 24px;
      background: $bg-color-overlay;
    }

    .el-dialog__header {
      padding: 0;
      margin: 0;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #303133;
        font-size: 24px;
        z-index: 1000;

        &:hover {
          color: $color-primary;
        }
      }
    }

    .el-dialog__footer {
      padding: 10px 20px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.dialog-form {
  :deep(.el-form-item__label) {
    color: var(--el-text-color-regular);
  }

  :deep(.el-input__inner) {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
    color: var(--el-text-color-primary);
  }
}
</style>