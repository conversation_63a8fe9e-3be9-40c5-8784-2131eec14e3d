<template>
  <div class="list-page">
    <EmergencyVerticalTabNav />
    <div class="content-container">
      <div class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" style="display: flex;justify-content: space-between;width: 100%;">
            <div>
              <el-form-item label="单位名称" prop="unitName">
                <el-input v-model="queryParams.unitName" placeholder="请输入单位名称" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="医疗分队" prop="medicalTeamName">
                <el-input v-model="queryParams.medicalTeamName" placeholder="请输入医疗分队名称" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
                  <el-option label="待命" :value="1" />
                  <el-option label="执行任务" :value="2" />
                  <el-option label="休整" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              </el-form-item>
            </div>
            <div>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Plus" class="add-button" @click="handleAdd">新增</el-button>
              <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="batchDelete">删除</el-button>
            </div>
          </el-form>
        </el-card>
      </div>

      <!-- 列表区域 -->
      <el-card shadow="hover" class="w-full">
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
          border
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" width="80" align="center">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="unitName" label="单位名称" min-width="150" />
          <el-table-column prop="medicalTeamName" label="医疗分队" min-width="120" />
          <el-table-column prop="teamLeader" label="负责人" width="100" />
          <el-table-column prop="leaderQualification" label="负责人资质" width="120" />
          <el-table-column prop="teamMemberCount" label="队员人数" width="100" align="center" />
          <el-table-column prop="qualifiedDoctorCount" label="医生人数" width="100" align="center" />
          <el-table-column prop="nurseCount" label="护士人数" width="100" align="center" />
          <el-table-column prop="ambulanceCount" label="救护车数量" width="100" align="center" />
          <el-table-column prop="emergencyContact" label="紧急联系电话" width="150" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button size="small" type="danger" link @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            background
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 新增/编辑弹窗 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="70%"
        center
        destroy-on-close
        class="custom-dialog"
      >
        <template #header>
          <div class="dialog-header">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </div>
        </template>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="dialog-form"
        >
          <el-tabs v-model="activeTab" type="border-card">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="单位名称" prop="unitName">
                    <el-input v-model="form.unitName" placeholder="请输入单位名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="医疗分队名称" prop="medicalTeamName">
                    <el-input v-model="form.medicalTeamName" placeholder="请输入医疗分队名称" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="分队负责人" prop="teamLeader">
                    <el-input v-model="form.teamLeader" placeholder="请输入分队负责人" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="负责人资质" prop="leaderQualification">
                    <el-input v-model="form.leaderQualification" placeholder="如主治医师/急救师等" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="紧急联系电话" prop="emergencyContact">
                    <el-input v-model="form.emergencyContact" placeholder="请输入紧急联系电话" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                      <el-radio :label="1">待命</el-radio>
                      <el-radio :label="2">执行任务</el-radio>
                      <el-radio :label="3">休整</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 人员信息 -->
            <el-tab-pane label="人员信息" name="personnel">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="医疗队员人数" prop="teamMemberCount">
                    <el-input-number v-model="form.teamMemberCount" :min="0" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="持证医生人数" prop="qualifiedDoctorCount">
                    <el-input-number v-model="form.qualifiedDoctorCount" :min="0" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="护士人数" prop="nurseCount">
                    <el-input-number v-model="form.nurseCount" :min="0" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="救护车数量" prop="ambulanceCount">
                    <el-input-number v-model="form.ambulanceCount" :min="0" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>

            <!-- 设备物资 -->
            <el-tab-pane label="设备物资" name="equipment">
              <el-form-item label="医疗设备清单" prop="medicalEquipment">
                <el-input
                  v-model="form.medicalEquipment"
                  type="textarea"
                  :rows="3"
                  placeholder="如除颤仪、呼吸机等，多个设备请用逗号分隔"
                />
              </el-form-item>
              <el-form-item label="急救物资清单" prop="firstAidSupplies">
                <el-input
                  v-model="form.firstAidSupplies"
                  type="textarea"
                  :rows="3"
                  placeholder="如绷带、消毒液等，多个物资请用逗号分隔"
                />
              </el-form-item>
              <el-form-item label="药品库存清单" prop="medicineStock">
                <el-input
                  v-model="form.medicineStock"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入药品库存清单，多个药品请用逗号分隔"
                />
              </el-form-item>
            </el-tab-pane>

            <!-- 其他信息 -->
            <el-tab-pane label="其他信息" name="other">
              <el-form-item label="服务范围" prop="serviceScope">
                <el-input
                  v-model="form.serviceScope"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入服务范围"
                />
              </el-form-item>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="上次物资清点" prop="lastCheckDate">
                    <el-date-picker
                      v-model="form.lastCheckDate"
                      type="date"
                      format="YYYY-MM-DD"
                      placeholder="选择日期"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="下次物资清点" prop="nextCheckDate">
                    <el-date-picker
                      v-model="form.nextCheckDate"
                      type="date"
                      format="YYYY-MM-DD"
                      placeholder="选择日期"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="最近医疗培训" prop="recentTrainingDate">
                <el-date-picker
                  v-model="form.recentTrainingDate"
                  type="date"
                  format="YYYY-MM-DD"
                  placeholder="选择培训日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { Plus, Search, RefreshRight } from '@element-plus/icons-vue';
import {
  getMedicalLedgerList,
  getMedicalLedgerDetail,
  addMedicalLedger,
  updateMedicalLedger,
  deleteMedicalLedger
} from '@/api/bridge/command/medical-ledger';
import EmergencyVerticalTabNav from "./components/EmergencyVerticalTabNav.vue";

// 路由
const router = useRouter();

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const selectedRows = ref([]);
const queryFormRef = ref();

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  unitName: '',
  medicalTeamName: '',
  status: null
});

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref();
const activeTab = ref("basic");
const form = ref({
  id: null,
  unitName: "",
  medicalTeamName: "",
  teamLeader: "",
  leaderQualification: "",
  teamMemberCount: 0,
  qualifiedDoctorCount: 0,
  nurseCount: 0,
  ambulanceCount: 0,
  medicalEquipment: "",
  firstAidSupplies: "",
  medicineStock: "",
  lastCheckDate: "",
  nextCheckDate: "",
  emergencyContact: "",
  serviceScope: "",
  recentTrainingDate: "",
  status: 1
});

// 表单验证规则
const rules = {
  unitName: [
    { required: true, message: "请输入单位名称", trigger: "blur" }
  ],
  medicalTeamName: [
    { required: true, message: "请输入医疗分队名称", trigger: "blur" }
  ],
  teamLeader: [
    { required: true, message: "请输入分队负责人", trigger: "blur" }
  ],
  emergencyContact: [
    { required: true, message: "请输入紧急联系电话", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
  ],
  status: [
    { required: true, message: "请选择状态", trigger: "change" }
  ]
};

// 获取状态名称
const getStatusName = (status) => {
  const statusMap = {
    1: "待命",
    2: "执行任务",
    3: "休整"
  };
  return statusMap[status] || "未知";
};

// 获取状态标签样式
const getStatusTag = (status) => {
  const statusMap = {
    1: "success",  // 待命 - 绿色
    2: "danger",   // 执行任务 - 红色
    3: "warning"   // 休整 - 黄色
  };
  return statusMap[status] || "";
};

// 获取医疗台账列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await getMedicalLedgerList({
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      unitName: queryParams.unitName,
      medicalTeamName: queryParams.medicalTeamName,
      status: queryParams.status
    });
    tableData.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error("获取医疗台账列表失败", error);
    ElMessage.error("获取医疗台账列表失败");
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.unitName = '';
  queryParams.medicalTeamName = '';
  queryParams.status = null;
  handleQuery();
};

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 批量删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  const ids = selectedRows.value.map(item => item.id).join(',');

  ElMessageBox.confirm('确定要删除选中的医疗台账吗？', '警告', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteMedicalLedger(ids);
      ElMessage.success('删除成功');
      selectedRows.value = [];
      getList();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
};

// 处理新增
const handleAdd = () => {
  dialogTitle.value = "新增医疗台账";
  form.value = {
    id: null,
    unitName: "",
    medicalTeamName: "",
    teamLeader: "",
    leaderQualification: "",
    teamMemberCount: 0,
    qualifiedDoctorCount: 0,
    nurseCount: 0,
    ambulanceCount: 0,
    medicalEquipment: "",
    firstAidSupplies: "",
    medicineStock: "",
    lastCheckDate: "",
    nextCheckDate: "",
    emergencyContact: "",
    serviceScope: "",
    recentTrainingDate: "",
    status: 1
  };
  activeTab.value = "basic";
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  dialogTitle.value = "编辑医疗台账";
  try {
    const res = await getMedicalLedgerDetail(row.id);
    form.value = res.data;
    activeTab.value = "basic";
    dialogVisible.value = true;
  } catch (error) {
    console.error("获取医疗台账详情失败", error);
    ElMessage.error("获取医疗台账详情失败");
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除医疗台账"${row.unitName}"吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteMedicalLedger(row.id);
      ElMessage.success("删除成功");
      getList();
    } catch (error) {
      console.error("删除医疗台账失败", error);
      ElMessage.error("删除医疗台账失败");
    }
  }).catch(() => {});
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (form.value.id) {
          await updateMedicalLedger(form.value);
          ElMessage.success("更新成功");
        } else {
          await addMedicalLedger(form.value);
          ElMessage.success("新增成功");
        }
        dialogVisible.value = false;
        getList();
      } catch (error) {
        console.error("操作失败", error);
        ElMessage.error("操作失败");
      }
    }
  });
};

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  queryParams.pageSize = val;
  getList();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  queryParams.pageNum = val;
  getList();
};

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
$bg-color-page: var(--el-bg-color-page);
$bg-color-overlay: var(--el-bg-color-overlay);
$box-shadow-light: var(--el-box-shadow-light);
$box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
$border-radius: 4px;
$border-radius-dialog: 8px;
$border-color-light: var(--el-border-color-light);
$color-primary: var(--el-color-primary);

.list-page {
  padding: 20px;
  background: $bg-color-page;
  border-radius: $border-radius;
  box-shadow: $box-shadow-light;
  display: flex;
  gap: 20px;
}

.content-container {
  flex: 1;
  overflow: hidden;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: $bg-color-overlay;

  :deep(th) {
    background: $bg-color-page !important;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: $bg-color-page;
  border-radius: $border-radius-dialog $border-radius-dialog 0 0;
  border-bottom: 1px solid $border-color-light;

  .dialog-title {
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }
}

.custom-dialog {
  :deep(.el-dialog) {
    border-radius: $border-radius-dialog;
    box-shadow: $box-shadow;

    .el-dialog__body {
      padding: 24px;
      background: $bg-color-overlay;
    }

    .el-dialog__header {
      padding: 0;
      margin: 0;
    }

    .el-dialog__footer {
      padding: 10px 20px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.dialog-form {
  :deep(.el-form-item__label) {
    color: var(--el-text-color-regular);
  }

  :deep(.el-input__inner) {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
    color: var(--el-text-color-primary);
  }
}
</style>
