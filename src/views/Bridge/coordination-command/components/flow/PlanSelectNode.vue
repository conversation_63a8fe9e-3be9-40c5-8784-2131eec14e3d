<template>
  <div class="plan-select-node">
    <div class="node-header">
      <h3 class="node-title">预案选择</h3>
      <div class="button-group">
        <el-button
          v-if="isCurrentNode"
          type="primary"
          @click="handleSavePlan"
          :disabled="!matchForm.selectedPlan">
          保存
        </el-button>
        <el-button
          type="warning"
          @click="handleSendNotice"
          :disabled="!matchForm.selectedPlan || !isFormValid">
          发送通知
        </el-button>
        <el-tooltip
          v-if="isCurrentNode"
          content="点击下一步将同时发送通知"
          placement="top">
          <el-button
            type="success"
            @click="handleNextStepWithNotice"
            :disabled="!matchForm.selectedPlan || !isFormValid">
            下一步
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 添加事件详情区域 -->
    <div class="event-details-section">
      <div class="section-title">事件详情</div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="事件标题">{{ eventData.eventName || '无标题' }}</el-descriptions-item>
        <el-descriptions-item label="事件等级">
          <el-tag :type="getEventLevelTag(eventData.eventLevel)">
            {{ getEventLevelName(eventData.eventLevel) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="发生地点">{{ eventData.location || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="事件类型">
          <el-tag :type="getEventCategoryTag(eventData.eventCategory)">
            {{ getEventCategoryName(eventData.eventCategory) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="事件描述" :span="2">{{ eventData.description || '无描述' }}</el-descriptions-item>
        <el-descriptions-item label="发生时间">{{ formatDateTime(eventData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="报告人员">{{ eventData.reporter || '未知' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <el-form ref="matchFormRef" :model="matchForm" :rules="matchRules" label-width="120px" class="dialog-form">
      <!-- <el-form-item label="预案类型" prop="planType">
        <el-radio-group v-model="matchForm.planType" @change="handlePlanTypeChange" :disabled="isCompleted">
          <el-radio :label="1">总体预案</el-radio>
          <el-radio :label="2">专项预案</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="选择预案" prop="planId">
        <el-select v-model="matchForm.planId" placeholder="请选择预案" class="w-full" @change="handlePlanChange" filterable>
          <el-option v-for="plan in filteredPlanList" :key="plan.id" :label="plan.planName" :value="plan.id" />
        </el-select>
      </el-form-item>

      <!-- 预案详情展示区域 -->
      <template v-if="matchForm.selectedPlan">
        <div class="plan-details-card">
          <div class="card-header">
            <div class="card-title">预案详情</div>
            <el-tag size="small" :type="matchForm.selectedPlan.planType === 1 ? 'primary' : 'warning'">
              {{ matchForm.selectedPlan.planType === 1 ? '总体预案' : '专项预案' }}
            </el-tag>
          </div>
          <div class="card-content">
            <el-descriptions border :column="2">
              <el-descriptions-item label="预案名称" :span="2">
                {{ matchForm.selectedPlan.planName }}
              </el-descriptions-item>
              <el-descriptions-item label="适用事件类型">
                <el-tag :type="getEventCategoryTag(matchForm.selectedPlan.eventCategory)">
                  {{ getEventCategoryName(matchForm.selectedPlan.eventCategory) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="适用事件等级">
                <el-tag :type="getEventLevelTag(matchForm.selectedPlan.eventLevel)">
                  {{ getEventLevelName(matchForm.selectedPlan.eventLevel) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="发布单位" :span="2">
                {{ matchForm.selectedPlan.publishUnit }}
              </el-descriptions-item>
              <el-descriptions-item label="发布时间" :span="2">
                {{ formatDateTime(matchForm.selectedPlan.publishTime) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>

        <!-- 通知方式选择 -->
        <div class="notify-method-card">
          <div class="card-header">
            <div class="card-title">通知方式</div>
          </div>
          <div class="card-content">
            <el-form-item label="通知方式" prop="notifyMethod">
              <el-select
                v-model="noticeForm.notifyMethod"
                placeholder="请选择通知方式"
                class="w-full"
                multiple
              >
                <el-option
                  v-for="option in notifyMethodOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 通知附加内容 -->
        <div class="sms-template-card">
          <div class="card-header">
            <div class="card-title">通知附加内容</div>
          </div>
          <div class="card-content">
            <el-form-item label="通知附加内容">
              <el-input
                v-model="noticeForm.postContent"
                type="textarea"
                :rows="3"
                placeholder="请输入通知附加内容"
                maxlength="200"
                show-word-limit
              />
              <el-text size="small" type="info" style="margin-top: 5px;">
                {{ smsPreviewText }}
              </el-text>
            </el-form-item>
          </div>
        </div>

        <!-- 部门通知人员展示 -->
        <div class="dept-tree-card">
          <div class="card-header">
            <div class="card-title">预案通知人员</div>
            <span class="text-sm text-gray-500">已选择 {{ selectedPersonnelCount }} 人</span>
          </div>
          <div class="card-content">
            <el-row :gutter="20">
              <!-- 左侧：部门树选择区域 -->
              <el-col :lg="12" :xs="24">
                <div class="tree-selection-panel">
                  <div class="panel-header">
                    <h4>选择部门/人员</h4>
                  </div>
                  <div class="tree-container">
                    <el-tree
                      ref="deptTreeRef"
                      :data="deptOptions"
                      :props="treeProps"
                      show-checkbox
                      node-key="nodeKey"
                      default-expand-all
                      :render-content="renderContent"
                      @check="handleCheck"
                      @check-change="handleCheckChange"
                      class="w-full"
                      :disabled="!isCurrentNode"
                    />
                  </div>
                </div>
              </el-col>

              <!-- 右侧：选中人员展示区域 -->
              <el-col :lg="12" :xs="24">
                <div class="selected-personnel-panel">
                  <div class="panel-header">
                    <h4>已选人员 ({{ selectedPersonnelCount }})</h4>
                  </div>
                  <div class="personnel-container">
                    <div v-if="selectedPersonnelCount === 0" class="empty-state">
                      <el-empty description="暂无选中人员" :image-size="80" />
                    </div>
                    <div v-else class="personnel-list">
                      <div
                        v-for="userKey in selectedPersonnel"
                        :key="userKey"
                        class="personnel-item"
                      >
                        <div class="user-info">
                          <div class="user-name">{{ getUserDisplayName(userKey) }}</div>
                          <div class="user-dept">{{ getUserDeptName(userKey) }}</div>
                          <div v-if="getUserPhone(userKey)" class="user-phone">{{ getUserPhone(userKey) }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
            <div v-if="deptOptions.length === 0" class="text-red-500 text-sm mt-1">
              暂无部门数据可选
            </div>
          </div>
        </div>
      </template>
    </el-form>
    
    <div class="tip-section" v-if="matchForm.selectedPlan">
      <el-alert
        title="预案选择完成后，点击'保存'将预案信息保存到事件中，或点击'下一步'进入险情同步环节"
        type="success"
        :closable="false"
        show-icon
      />
    </div>
    <div class="tip-section" v-else>
      <el-alert
        title="请先选择一个应急预案"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, defineProps, defineEmits, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { Folder } from '@element-plus/icons-vue';
import { getEmergencyPlanList, getEmergencyPlanById } from '@/api/bridge/command/emergency';
import { updateEmergencyEvent } from '@/api/bridge/command/event';
import { listDept } from '@/api/system/dept';
import { getUserInfoList } from '@/api/bridge/user/user';
import { getPlanNoticeRecord } from '@/api/bridge/command/dept';
import { listData } from "@/api/system/dict/data";
import type { DictDataQuery } from '@/api/system/dict/data/types';
import { addNotice, publishNotice } from '@/api/bridge/command/notice';
import useUserStore from '@/store/modules/user';

const props = defineProps({
  eventData: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['next-step']);

// 匹配表单相关
const matchFormRef = ref();
const matchForm = ref({
  planType: 1,
  planId: null,
  selectedPlan: null
});

// 通知表单相关
const noticeForm = ref({
  title: '应急事件通知',
  content: '',
  warnType: "6", // 应急通知类型
  status: '1',
  notifyMethod: [],
  sendTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  userId: '',
  eventId: '',
  warnLevel: '1', // 默认I级
  notifyPersonnel: [],
  postContent: '' // 短信通知附加内容
});

// 表单校验规则
const matchRules = {
  planType: [{ required: true, message: '请选择预案类型', trigger: 'change' }],
  planId: [{ required: true, message: '请选择预案', trigger: 'change' }]
};

const emergencyPlanList = ref([]);

// 通知方式选项
const notifyMethodOptions = ref<any[]>([]);

// 部门树相关
const deptTreeRef = ref();
const deptOptions = ref([]);
const allUsers = ref([]);
const selectedPersonnel = ref([]);

// 计算属性：将selectedPersonnel转换为DeptTreeSelector需要的格式
const selectedUserIds = computed(() => {
  return selectedPersonnel.value
    .filter(key => key.includes('-'))
    .map(key => key.split('-')[1]);
});

// 计算已选择的人员数量
const selectedPersonnelCount = computed(() => {
  return selectedPersonnel.value.length;
});

// 树形控件属性配置
const treeProps = {
  label: (data) => {
    if (data.type === 'user') {
      return data.nickName || data.userName;
    } else {
      return data.deptName;
    }
  },
  children: 'children'
};



// 计算筛选后的预案列表
const filteredPlanList = computed(() => {
  return emergencyPlanList.value.filter(plan => plan.planType === matchForm.value.planType);
});

// 判断是否为当前节点
const isCurrentNode = computed(() => {
  return props.eventData && props.eventData.flowStatus === 1;
});

// 判断是否已完成
const isCompleted = computed(() => {
  return props.eventData && props.eventData.flowStatus > 1;
});

// 移除短信附加内容显示条件，始终显示通知附加内容
// const showSmsPostContent = computed(() => {
//   return noticeForm.value.notifyMethod.includes('sms');
// });

// 判断表单是否有效，用于发送通知按钮的禁用状态
const isFormValid = computed(() => {
  return noticeForm.value.notifyMethod.length > 0 && selectedPersonnel.value.length > 0;
});

// 生成短信预览文本
const smsPreviewText = computed(() => {
  const eventData = props.eventData;
  if (!eventData) {
    return '【紧急预警】[通知人员的姓名]：[发生时间] 在 [发生地点] 发生"[预警类型]"预警。请速登录系统"事件"管理，了解详情。[用户自己输入内容]';
  }

  // 获取发生时间
  const happenTime = formatDateTime(eventData.createTime || eventData.happenTime);

  // 获取发生地点
  const location = eventData.location || '[发生地点]';

  // 获取预警类型
  const eventType = eventData.eventName || '[预警类型]';

  // 获取用户输入的附加内容
  const userContent = noticeForm.value.postContent || '[用户自己输入内容]';

  return `【紧急预警】[通知人员的姓名]：${happenTime} 在 ${location} 发生"${eventType}"预警。请速登录系统"事件"管理，了解详情。${userContent}`;
});

// 自定义节点渲染
const renderContent = (h, { node, data }) => {
  // 如果是用户节点，渲染用户信息
  if (data.type === 'user') {
    return h('div', { class: 'custom-user-node' }, [
      h('el-checkbox', {
        modelValue: isUserSelected(data),
        'onUpdate:modelValue': (val) => handleUserCheck(data, val),
        onClick: (e) => e.stopPropagation(),
        size: 'small'
      }, [
        h('span', { class: 'user-label' }, [
          h('span', { class: 'user-name' }, data.nickName || data.userName),
          data.phonenumber ? h('span', { class: 'user-phone' }, ` (${data.phonenumber})`) : null
        ])
      ])
    ]);
  }

  // 如果是部门节点，渲染部门信息
  return h('div', { class: 'custom-dept-node' }, [
    h('span', { class: 'dept-label' }, [
      h('span', { class: 'dept-name' }, node.label),
      h('span', { class: 'user-count' }, ` (${data.users?.length || 0}人)`)
    ])
  ]);
};

// 处理部门选择
const handleCheck = (data, { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }) => {
  updateSelectedUsers();
};

// 处理部门选择状态变化
const handleCheckChange = (data, checked, indeterminate) => {
  updateParentNodeStatus(data);
};

// 处理用户选择
const handleUserSelect = (user, node) => {
  event?.stopPropagation();
};

// 处理用户复选框变化
const handleUserCheck = (user, checked) => {
  const userKey = `${user.deptId}-${user.userId}`;
  if (checked) {
    if (!selectedPersonnel.value.includes(userKey)) {
      selectedPersonnel.value.push(userKey);
    }
  } else {
    selectedPersonnel.value = selectedPersonnel.value.filter(key => key !== userKey);
  }

  updateDeptNodeStatus(user.deptId);
};

// 检查用户是否被选中
const isUserSelected = (user) => {
  const userKey = `${user.deptId}-${user.userId}`;
  return selectedPersonnel.value.includes(userKey);
};

// 更新选中的用户列表
const updateSelectedUsers = () => {
  const tree = deptTreeRef.value;
  if (!tree) return;

  const checkedNodes = tree.getCheckedNodes();
  const halfCheckedNodes = tree.getHalfCheckedNodes();

  const selectedUsers = [];

  // 处理完全选中的节点
  checkedNodes.forEach(node => {
    if (node.type === 'user') {
      // 如果是用户节点，直接添加
      const userKey = `${node.deptId}-${node.userId}`;
      if (!selectedUsers.includes(userKey)) {
        selectedUsers.push(userKey);
      }
    } else if (node.users) {
      // 如果是部门节点，添加其下所有用户
      node.users.forEach(user => {
        const userKey = `${user.deptId}-${user.userId}`;
        if (!selectedUsers.includes(userKey)) {
          selectedUsers.push(userKey);
        }
      });
    }
  });

  // 处理半选中的节点（主要是部门节点）
  halfCheckedNodes.forEach(node => {
    if (node.users) {
      node.users.forEach(user => {
        const userKey = `${user.deptId}-${user.userId}`;
        if (isUserSelected(user) && !selectedUsers.includes(userKey)) {
          selectedUsers.push(userKey);
        }
      });
    }
  });

  selectedPersonnel.value = selectedUsers;
};

// 更新部门节点的选中状态
const updateDeptNodeStatus = (deptId) => {
  const tree = deptTreeRef.value;
  if (!tree) return;

  const node = tree.getNode(deptId);
  if (!node) return;

  // 获取该部门下所有选中的用户
  const selectedUsers = selectedPersonnel.value.filter(key =>
    key.startsWith(`${deptId}-`)
  );

  // 获取该部门下所有用户
  const allUsers = node.data.users || [];

  // 更新节点状态
  if (selectedUsers.length === allUsers.length && allUsers.length > 0) {
    tree.setChecked(node, true, false);
  } else if (selectedUsers.length > 0) {
    tree.setChecked(node, false, false);
    node.indeterminate = true;
  } else {
    tree.setChecked(node, false, false);
  }

  // 递归更新父节点状态
  updateParentNodeStatus(node.data);
};

// 更新父节点状态
const updateParentNodeStatus = (data) => {
  const tree = deptTreeRef.value;
  if (!tree) return;

  const node = tree.getNode(data.deptId);
  if (!node || !node.parent || node.parent.key === 0) return;

  const parentNode = node.parent;
  const siblings = parentNode.childNodes;

  // 检查所有兄弟节点的状态
  const allChecked = siblings.every(sibling => sibling.checked);
  const someChecked = siblings.some(sibling => sibling.checked || sibling.indeterminate);

  if (allChecked) {
    tree.setChecked(parentNode, true, false);
  } else {
    tree.setChecked(parentNode, false, false);
    parentNode.indeterminate = someChecked;
  }

  // 递归更新上级节点
  updateParentNodeStatus(parentNode.data);
};

// 获取用户显示名称
const getUserDisplayName = (userKey) => {
  if (!userKey.includes('-')) return userKey;
  const [deptId, userId] = userKey.split('-');
  const user = findUserInfo(deptId, userId);
  return user ? (user.nickName || user.userName) : '未知用户';
};

// 获取用户部门名称
const getUserDeptName = (userKey) => {
  if (!userKey.includes('-')) return '';
  const [deptId, userId] = userKey.split('-');
  const user = findUserInfo(deptId, userId);
  return user ? user.deptName : '未知部门';
};

// 获取用户电话
const getUserPhone = (userKey) => {
  if (!userKey.includes('-')) return '';
  const [deptId, userId] = userKey.split('-');
  const user = findUserInfo(deptId, userId);
  return user ? user.phonenumber : '';
};

// 获取应急预案列表方法
const loadEmergencyPlanList = async () => {
  try {
    const res = await getEmergencyPlanList({
      pageNum: 1,
      pageSize: 1000000
    });
    if (res.code === 200) {
      emergencyPlanList.value = res.rows;
    }
  } catch (error) {
    console.error("获取应急预案列表失败:", error);
  }
};

// 获取通知方式字典
const getNotifyMethodDict = async () => {
  try {
    const queryParams: DictDataQuery = {
      dictType: "notification_method",
      pageNum: 1,
      pageSize: 100,
      dictName: "",
      dictLabel: ""
    };
    const res = await listData(queryParams);
    if (res.code === 200) {
      notifyMethodOptions.value = res.rows.map(item => ({
        label: item.dictLabel,
        value: item.dictValue
      }));
      
      // 如果没有选择通知方式，默认全选
      if (noticeForm.value.notifyMethod.length === 0 && notifyMethodOptions.value.length > 0) {
        noticeForm.value.notifyMethod = notifyMethodOptions.value.map(option => option.value);
      }
    } else {
      // 如果获取失败，使用默认选项
      notifyMethodOptions.value = [
        { label: '短信', value: '1' },
        { label: '邮件', value: '2' },
        { label: '电话', value: '3' },
        { label: '应用推送', value: '4' }
      ];
    }
  } catch (error) {
    console.error("获取通知方式字典失败:", error);
    // 使用默认选项
    notifyMethodOptions.value = [
      { label: '短信', value: '1' },
      { label: '邮件', value: '2' },
      { label: '电话', value: '3' },
      { label: '应用推送', value: '4' }
    ];
  }
};

// 处理预案类型变化
const handlePlanTypeChange = () => {
  // 重置表单
  matchForm.value = {
    planType: matchForm.value.planType,
    planId: null,
    selectedPlan: null
  };
  
  // 重置部门树选中状态
  if (deptTreeRef.value) {
    deptTreeRef.value.setCheckedKeys([]);
  }
  selectedPersonnel.value = [];
  
  // 重置通知方式
  noticeForm.value.notifyMethod = [];
};

// 处理预案变化
const handlePlanChange = async (planId) => {
  try {
    if (!planId) {
      matchForm.value.selectedPlan = null;
      return;
    }

    // 先从当前列表中查找预案
    const plan = emergencyPlanList.value.find(p => String(p.id) === String(planId));
    if (plan) {
      matchForm.value.selectedPlan = { ...plan };
      // 确保planType值与选中的预案类型一致
      matchForm.value.planType = plan.planType;
    } else {
      // 如果在列表中找不到，则通过API获取
      const res = await getEmergencyPlanById(planId);
      if (res.code === 200) {
        matchForm.value.selectedPlan = res.data;
        // 确保planType值与选中的预案类型一致
        matchForm.value.planType = res.data.planType;
      } else {
        ElMessage.error("获取预案详情失败");
        return;
      }
    }
    
    // 获取通知方式数据
    await getNotifyMethodDict();
    
    // 初始化通知模板
    initNoticeTemplate();
    
    // 获取部门树数据
    await getDeptTreeData();
    
    // 获取预案通知记录
    if (planId) {
      setTimeout(async () => {
        await getPlanNoticeRecords(planId);
      }, 300);
    }
  } catch (error) {
    console.error("处理预案变化失败:", error);
    ElMessage.error("获取预案详情失败");
  }
};

// 生成默认模板
const generateDefaultTemplate = () => {
  if (!matchForm.value.selectedPlan) {
    ElMessage.warning('请先选择预案');
    return;
  }
  
  const eventData = props.eventData;
  if (!eventData) {
    ElMessage.warning('事件数据不存在');
    return;
  }
  
  // 设置通知标题
  noticeForm.value.title = `${eventData.eventName || '应急事件'}通知`;
  
  // 设置默认通知内容
  noticeForm.value.content = `【应急事件通知】
  
发生时间：${formatDateTime(eventData.createTime)}
发生地点：${eventData.location || '未知'}
事件描述：${eventData.description || '无'}

请相关人员按照预案要求，立即开展应急处置工作。`;

  // 保存到本地存储
  saveCustomSettings();
};

// 生成详细模板
const generateDetailedTemplate = () => {
  if (!matchForm.value.selectedPlan) {
    ElMessage.warning('请先选择预案');
    return;
  }
  
  const plan = matchForm.value.selectedPlan;
  const eventData = props.eventData;
  if (!eventData) {
    ElMessage.warning('事件数据不存在');
    return;
  }
  
  const eventCategories = {
    1: '自然灾害',
    2: '事故灾难',
    3: '公共卫生事件',
    4: '社会安全事件',
    5: '全部'
  };
  
  // 获取当前时间
  const currentTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  
  // 设置通知标题
  noticeForm.value.title = `${eventData.eventName || '应急事件'}通知`;
  
  // 设置详细通知内容
  noticeForm.value.content = `【应急事件通知】
  
发布时间：${currentTime}

预案类型：${plan.planType === 1 ? '总体预案' : '专项预案'}
预案名称：${plan.planName}
适用事件类型：${eventCategories[plan.eventCategory] || '未知'}
适用事件等级：${getEventLevelName(plan.eventLevel)}
发布单位：${plan.publishUnit}

事件信息：
事件名称：${eventData.eventName || '无标题'}
发生地点：${eventData.location || '未知'}
事件描述：${eventData.description || '无'}
事件级别：${eventData.eventLevel ? getEventLevelName(eventData.eventLevel) : '待定'}
发生时间：${formatDateTime(eventData.createTime)}

请相关人员按照预案要求，立即开展应急处置工作。各部门人员需做好以下准备：
1. 按照职责分工，迅速开展应急响应
2. 做好人员、物资、设备的调度准备
3. 保持通讯畅通，及时报告现场情况`;

  // 保存到本地存储
  saveCustomSettings();
};

// 初始化通知模板
const initNoticeTemplate = () => {
  // 尝试从本地存储中获取通知模板
  const eventId = props.eventData?.id;
  if (eventId) {
    const savedSettings = localStorage.getItem(`customPlanSettings_${eventId}`);
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);
      noticeForm.value.notifyMethod = settings.notifyMethod || [];
      noticeForm.value.postContent = settings.postContent || ''; // 短信通知附加内容
      return;
    }
  }

  // 如果没有保存的设置，清空相关字段
  noticeForm.value.notifyMethod = [];
  noticeForm.value.postContent = '';
};

// 保存自定义设置
const saveCustomSettings = () => {
  const eventId = props.eventData?.id;
  if (!eventId) return;

  const settings = {
    notifyMethod: noticeForm.value.notifyMethod,
    selectedPersonnel: selectedPersonnel.value,
    planId: matchForm.value.planId,
    postContent: noticeForm.value.postContent // 短信通知附加内容
  };

  localStorage.setItem(`customPlanSettings_${eventId}`, JSON.stringify(settings));
};

// 保存预案信息到事件
const handleSavePlan = async () => {
  if (!matchFormRef.value) return;
  await matchFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!matchForm.value.selectedPlan) {
        ElMessage.warning('请先选择一个预案');
        return;
      }

      try {
        // 保存自定义设置
        saveCustomSettings();

        // 更新事件的planId字段
        const updateData = {
          ...props.eventData,
          planId: matchForm.value.planId
        };
        const res = await updateEmergencyEvent(updateData);
        
        if (res.code === 200) {
          ElMessage.success('预案信息已保存');
          
          // 存储选中的预案信息，以便下一步使用
          localStorage.setItem('selectedPlan', JSON.stringify(matchForm.value.selectedPlan));
          
          // 询问是否进入险情同步阶段
          ElMessageBox.confirm(
            '预案信息已保存，是否进入险情同步阶段？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'info'
            }
          ).then(() => {
            handleNextStep();
          }).catch(() => {
            // 用户选择取消操作
          });
        } else {
          ElMessage.error(res.msg || '保存失败');
        }
      } catch (error) {
        console.error('保存预案信息失败:', error);
        ElMessage.error('保存失败');
      }
    }
  });
};

// 发送通知
const handleSendNotice = async () => {
  if (!matchFormRef.value) return;
  
  await matchFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!matchForm.value.selectedPlan) {
        ElMessage.warning('请先选择一个预案');
        return;
      }

      try {
        // 保存自定义设置
        saveCustomSettings();

        // 确保notifyPersonnel是最新的选中状态
        updateSelectedUsers();
        
        // 构建通知记录数据
        const notifyList = [];

        // 处理用户选择(带有 deptId-userId 格式的记录)
        selectedPersonnel.value.forEach(userKey => {
          if (userKey.includes('-')) {
            const [deptId, userId] = userKey.split('-');
            // 从部门树中查找用户信息
            const userInfo = findUserInfo(deptId, userId);
            if (userInfo) {
              notifyList.push({
                departmentId: deptId,
                departmentName: userInfo.deptName,
                personnelId: userId,
                personnelName: userInfo.nickName || userInfo.userName,
                phonenumber: userInfo.phonenumber
              });
            }
          }
        });

        if (notifyList.length === 0) {
          ElMessage.warning('请选择至少一个通知对象');
          return;
        }

        // 更新事件的planId字段
        const updateData = {
          ...props.eventData,
          planId: matchForm.value.planId
        };
        
        const updateRes = await updateEmergencyEvent(updateData);
        
        if (updateRes.code !== 200) {
          ElMessage.error(updateRes.msg || '更新事件信息失败');
          return;
        }

        // 自动生成通知标题和内容
        const autoTitle = `${props.eventData.eventName || '应急事件'}通知`;
        const autoContent = `【应急事件通知】

发生时间：${formatDateTime(props.eventData.createTime)}
发生地点：${props.eventData.location || '未知'}
事件描述：${props.eventData.description || '无'}

请相关人员按照预案要求，立即开展应急处置工作。`;

        // 准备通知数据
        const noticeData = {
          title: autoTitle,
          content: autoContent,
          warnType: noticeForm.value.warnType,
          status: noticeForm.value.status,
          notifyMethod: Array.isArray(noticeForm.value.notifyMethod)
            ? noticeForm.value.notifyMethod.join(',')
            : noticeForm.value.notifyMethod,
          sendTime: noticeForm.value.sendTime,
          userId: noticeForm.value.userId,
          eventId: props.eventData.id,
          warnLevel: noticeForm.value.warnLevel,
          notifyList: notifyList,
          happenTime: props.eventData.happenTime,
          // 添加notifyTargets字段，兼容两种格式
          notifyTargets: [],
          postContent: noticeForm.value.postContent // 短信通知附加内容
        };

        // 发送通知
        const res = await addNotice(noticeData);
        if (res.code === 200) {
          await publishNotice(res.data);
          ElMessage.success('通知已发送');
          
          // 存储选中的预案信息，以便下一步使用
          const enhancedPlan = {
            ...matchForm.value.selectedPlan,
            customSettings: {
              notifyMethod: noticeForm.value.notifyMethod,
              selectedPersonnel: selectedPersonnel.value,
              postContent: noticeForm.value.postContent
            }
          };
          localStorage.setItem('selectedPlan', JSON.stringify(enhancedPlan));
          localStorage.setItem('selectedPlanId', String(matchForm.value.planId));
          
          // 询问是否进入险情同步阶段
          ElMessageBox.confirm(
            '通知已发送，是否继续进入险情同步阶段？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'success'
            }
          ).then(() => {
            handleNextStep();
          }).catch(() => {
            // 用户选择取消操作
          });
        } else {
          ElMessage.error(res.msg || '发送通知失败');
        }
      } catch (error) {
        console.error('发送通知失败:', error);
        ElMessage.error('发送通知失败');
      }
    }
  });
};

// 进入下一步
const handleNextStep = async () => {
  if (!matchFormRef.value) return;
  
  await matchFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!matchForm.value.selectedPlan) {
        ElMessage.warning('请先选择一个预案');
        return;
      }

      try {
        // 保存自定义设置
        saveCustomSettings();

        // 保存增强的预案信息，包含自定义内容
        const enhancedPlan = {
          ...matchForm.value.selectedPlan,
          customSettings: {
            notifyMethod: noticeForm.value.notifyMethod,
            selectedPersonnel: selectedPersonnel.value,
            postContent: noticeForm.value.postContent
          }
        };
        localStorage.setItem('selectedPlan', JSON.stringify(enhancedPlan));
        localStorage.setItem('selectedPlanId', String(matchForm.value.planId));

        // 仅更新planId，而不更新flowStatus
        // 避免与flowLog.vue中的goToNextNode重复调用更新接口
        const updateData = {
          ...props.eventData,
          planId: matchForm.value.planId
        };
        
        const res = await updateEmergencyEvent(updateData);
        
        if (res.code === 200) {
          ElMessage.success('进入险情同步阶段');
          emit('next-step');
        } else {
          ElMessage.error(res.msg || '操作失败');
        }
      } catch (error) {
        console.error('进入下一步失败:', error);
        ElMessage.error('操作失败');
      }
    }
  });
};

// 进入下一步并发送通知（当flowStatus为1时使用）
const handleNextStepWithNotice = async () => {
  if (!matchFormRef.value) return;
  
  await matchFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!matchForm.value.selectedPlan) {
        ElMessage.warning('请先选择一个预案');
        return;
      }

      try {
        // 先执行发送通知的逻辑
        // 保存自定义设置
        saveCustomSettings();

        // 确保notifyPersonnel是最新的选中状态
        updateSelectedUsers();
        
        // 构建通知记录数据
        const notifyList = [];

        // 处理用户选择(带有 deptId-userId 格式的记录)
        selectedPersonnel.value.forEach(userKey => {
          if (userKey.includes('-')) {
            const [deptId, userId] = userKey.split('-');
            // 从部门树中查找用户信息
            const userInfo = findUserInfo(deptId, userId);
            if (userInfo) {
              notifyList.push({
                departmentId: deptId,
                departmentName: userInfo.deptName,
                personnelId: userId,
                personnelName: userInfo.nickName || userInfo.userName,
                phonenumber: userInfo.phonenumber
              });
            }
          }
        });

        if (notifyList.length === 0) {
          ElMessage.warning('请选择至少一个通知对象');
          return;
        }

        // 更新事件的planId字段
        const updateData = {
          ...props.eventData,
          planId: matchForm.value.planId
        };
        
        const updateRes = await updateEmergencyEvent(updateData);
        
        if (updateRes.code !== 200) {
          ElMessage.error(updateRes.msg || '更新事件信息失败');
          return;
        }

        // 自动生成通知标题和内容
        const autoTitle = `${props.eventData.eventName || '应急事件'}通知`;
        const autoContent = `【应急事件通知】

发生时间：${formatDateTime(props.eventData.createTime)}
发生地点：${props.eventData.location || '未知'}
事件描述：${props.eventData.description || '无'}

请相关人员按照预案要求，立即开展应急处置工作。`;

        // 准备通知数据
        const noticeData = {
          title: autoTitle,
          content: autoContent,
          warnType: noticeForm.value.warnType,
          status: noticeForm.value.status,
          notifyMethod: Array.isArray(noticeForm.value.notifyMethod)
            ? noticeForm.value.notifyMethod.join(',')
            : noticeForm.value.notifyMethod,
          sendTime: noticeForm.value.sendTime,
          userId: noticeForm.value.userId,
          eventId: props.eventData.id,
          warnLevel: noticeForm.value.warnLevel,
          notifyList: notifyList,
          happenTime: props.eventData.happenTime,
          // 添加notifyTargets字段，兼容两种格式
          notifyTargets: [],
          postContent: noticeForm.value.postContent // 短信通知附加内容
        };

        // 发送通知
        const res = await addNotice(noticeData);
        if (res.code === 200) {
          await publishNotice(res.data);
          ElMessage.success('通知已发送');
        } else {
          ElMessage.error(res.msg || '发送通知失败');
          return;
        }
        
        // 然后执行下一步的逻辑
        // 保存增强的预案信息，包含自定义内容
        const enhancedPlan = {
          ...matchForm.value.selectedPlan,
          customSettings: {
            notifyMethod: noticeForm.value.notifyMethod,
            selectedPersonnel: selectedPersonnel.value,
            postContent: noticeForm.value.postContent
          }
        };
        localStorage.setItem('selectedPlan', JSON.stringify(enhancedPlan));
        localStorage.setItem('selectedPlanId', String(matchForm.value.planId));

        // 仅更新planId，而不更新flowStatus
        // 避免与flowLog.vue中的goToNextNode重复调用更新接口
        
        ElMessage.success('进入险情同步阶段');
        emit('next-step');
      } catch (error) {
        console.error('进入下一步并发送通知失败:', error);
        ElMessage.error('操作失败');
      }
    }
  });
};

// 获取事件类型名称
const getEventCategoryName = (category) => {
  const categories = {
    1: '自然灾害',
    2: '事故灾难',
    3: '公共卫生事件',
    4: '社会安全事件',
    5: '全部'
  };
  return categories[category] || '未知';
};

// 获取事件类型标签样式
const getEventCategoryTag = (category) => {
  const tags = {
    1: 'danger',
    2: 'warning',
    3: 'success',
    4: 'info',
    5: 'primary'
  };
  return tags[category] || '';
};

// 获取事件等级名称
const getEventLevelName = (level) => {
  const levels = {
    1: 'Ⅰ级',
    2: 'Ⅱ级',
    3: 'Ⅲ级',
    4: 'Ⅳ级',
    5: '全部'
  };
  return levels[level] || '未知';
};

// 获取事件等级标签样式
const getEventLevelTag = (level) => {
  const tags = {
    1: 'danger',
    2: 'warning',
    3: 'success',
    4: 'info',
    5: 'primary'
  };
  return tags[level] || '';
};

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取部门树状数据
const getDeptTreeData = async () => {
  try {
    // 获取所有部门数据
    const deptRes = await listDept({
      pageNum: 1,
      pageSize: 10000
    });

    // 获取所有用户数据
    const userRes = await getUserInfoList({
      pageNum: 1,
      pageSize: 10000
    });

    if (deptRes.code === 200 && deptRes.data && deptRes.data.length > 0) {
      // 构建用户映射表，按部门ID分组
      const userMap = {};
      if (userRes.code === 200 && userRes.rows) {
        allUsers.value = userRes.rows.map(user => ({
          userId: String(user.userId),
          userName: user.userName,
          phonenumber: user.phonenumber,
          nickName: user.nickName,
          deptId: String(user.deptId),
          deptName: user.deptName,
          type: 'user'
        }));

        userRes.rows.forEach(user => {
          const deptId = String(user.deptId);
          if (!userMap[deptId]) {
            userMap[deptId] = [];
          }
          userMap[deptId].push({
            userId: user.userId,
            userName: user.userName,
            phonenumber: user.phonenumber,
            nickName: user.nickName,
            deptId: deptId,
            deptName: user.deptName
          });
        });
      }

      // 构建树形结构
      const buildDeptTree = (data) => {
        const normalize = data.map(item => ({
          ...item,
          deptId: String(item.deptId),
          parentId: String(item.parentId),
          users: userMap[String(item.deptId)] || [],
          nodeKey: String(item.deptId), // 添加nodeKey属性
          type: 'dept' // 添加type属性
        }));

        const map = {};
        normalize.forEach(item => {
          map[item.deptId] = { ...item, children: [] };
        });

        const tree = [];
        normalize.forEach(item => {
          const parentId = item.parentId;
          const id = item.deptId;

          if (map[parentId]) {
            map[parentId].children.push(map[id]);
          } else {
            if (parentId === '0') {
              tree.push(map[id]);
            }
          }
        });

        return tree;
      };

      const treeData = buildDeptTree(deptRes.data);

      // 将用户作为部门的子节点添加到树形结构中
      const addUsersToTree = (nodes) => {
        nodes.forEach(node => {
          if (node.users && node.users.length > 0) {
            // 将用户添加到children数组中
            node.users.forEach(user => {
              const userNode = {
                ...user,
                type: 'user',
                deptId: node.deptId,
                deptName: node.deptName,
                userId: String(user.userId), // 确保userId是字符串
                // 设置用户节点的唯一标识
                nodeKey: `user_${node.deptId}_${user.userId}`
              };
              node.children.push(userNode);
            });
          }
          if (node.children) {
            // 递归处理子部门（只处理部门类型的子节点）
            const deptChildren = node.children.filter(child => child.type !== 'user');
            addUsersToTree(deptChildren);
          }
        });
      };

      if (treeData.length === 0 && deptRes.data.length > 0) {
        const { proxy } = getCurrentInstance();
        if (proxy?.handleTree) {
          deptOptions.value = proxy.handleTree(deptRes.data, 'deptId', 'parentId');
          // 为每个部门添加用户子节点
          deptOptions.value.forEach(dept => {
            dept.users = userMap[String(dept.deptId)] || [];
          });
          addUsersToTree(deptOptions.value);
        } else {
          deptOptions.value = deptRes.data.map(item => ({
            ...item,
            deptId: String(item.deptId),
            label: item.deptName,
            value: String(item.deptId),
            children: [],
            users: userMap[String(item.deptId)] || []
          }));
          addUsersToTree(deptOptions.value);
        }
      } else {
        addUsersToTree(treeData);
        deptOptions.value = treeData;
      }
    } else {
      deptOptions.value = [];
      allUsers.value = [];
    }
  } catch (error) {
    console.error("获取部门数据失败:", error);
    deptOptions.value = [];
  }
};

// 获取预案通知记录
const getPlanNoticeRecords = async (planId) => {
  try {
    const res = await getPlanNoticeRecord({ planId });
    if (res.code === 200 && res.rows) {
      // 将通知记录转换为前端需要的格式
      const planPersonnel = res.rows.map(record => {
        if (record.personnelId) {
          // 如果有用户ID，使用 deptId-userId 格式
          return `${record.departmentId}-${record.personnelId}`;
        } else {
          // 如果只有部门ID，只返回部门ID
          return String(record.departmentId);
        }
      }).filter(Boolean);

      // 更新选中人员
      selectedPersonnel.value = planPersonnel;

      // 在树形控件中设置选中状态
      const setTreeSelections = () => {
        if (!deptTreeRef.value) {
          return;
        }

        // 先重置所有选中状态
        deptTreeRef.value.setCheckedKeys([]);

        // 收集需要选中的用户节点key
        const userKeysToCheck = [];

        planPersonnel.forEach(key => {
          if (key.includes('-')) {
            const [deptId, userId] = key.split('-');
            // 构建用户节点的key（用户节点在树中的key格式）
            const userNodeKey = `user_${deptId}_${userId}`;
            userKeysToCheck.push(userNodeKey);
          }
        });

        // 直接设置选中的keys
        if (userKeysToCheck.length > 0) {
          deptTreeRef.value.setCheckedKeys(userKeysToCheck);
        }

        // 更新所有部门的选中状态
        const allDeptIds = new Set();
        planPersonnel.forEach(key => {
          if (key.includes('-')) {
            const [deptId] = key.split('-');
            allDeptIds.add(deptId);
          }
        });

        allDeptIds.forEach(deptId => {
          updateDeptNodeStatus(deptId);
        });
      };

      // 启动设置流程，使用nextTick确保DOM更新完成
      nextTick(() => {
        setTimeout(setTreeSelections, 100);
      });
    } else {
      selectedPersonnel.value = [];
    }
  } catch (error) {
    console.error("获取预案通知记录失败:", error);
    selectedPersonnel.value = [];
  }
};









onMounted(async () => {
  // 获取用户信息
  const userStore = useUserStore();
  if (userStore.userId) {
    noticeForm.value.userId = String(userStore.userId); // 确保userId是字符串类型
  }

  await loadEmergencyPlanList();
  
  // 获取通知方式字典
  await getNotifyMethodDict();
  
  // 如果事件已有planId，则自动选择对应的预案
  if (props.eventData && props.eventData.planId) {
    const planId = props.eventData.planId;
    
    // 从列表中查找预案
    const plan = emergencyPlanList.value.find(p => String(p.id) === String(planId));
    if (plan) {
      // 先设置预案类型，再设置预案ID
      matchForm.value.planType = plan.planType;
      matchForm.value.planId = planId;
      await handlePlanChange(planId);
    } else {
      // 如果在列表中找不到，通过API获取
      try {
        const res = await getEmergencyPlanById(planId);
        if (res.code === 200 && res.data) {
          // 先设置预案类型，再设置预案ID
          matchForm.value.planType = res.data.planType;
          matchForm.value.planId = planId;
          await handlePlanChange(planId);
        } else {
          matchForm.value.planId = null;
          ElMessage.warning('未找到该预案信息');
        }
      } catch (error) {
        console.error('获取预案详情失败:', error);
        matchForm.value.planId = null;
      }
    }
  }
});

// 根据部门ID和用户ID查找用户信息
const findUserInfo = (deptId, userId) => {
  // 从allUsers中查找用户信息
  const user = allUsers.value.find(u =>
    String(u.deptId) === String(deptId) && String(u.userId) === String(userId)
  );
  return user || null;
};
</script>

<style scoped lang="scss">
.plan-select-node {
  padding: 20px;
  
  .node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .node-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: var(--el-text-color-primary);
    }
    
    .button-group {
      display: flex;
      gap: 10px;
    }
  }
  
  .event-details-section {
    margin-bottom: 24px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    overflow: hidden;
    
    .section-title {
      padding: 12px 16px;
      font-size: 16px;
      font-weight: 600;
      background-color: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-light);
      color: var(--el-text-color-primary);
    }
  }
  
  .tip-section {
    margin-top: 20px;
  }
  
  .w-full {
    width: 100%;
  }
  
  .plan-details-card, .dept-tree-card, .notify-method-card, .sms-template-card {
    margin: 20px 0;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    overflow: hidden;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-light);
      
      .card-title {
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .card-content {
      padding: 16px;
    }
  }
  
  .template-buttons {
    display: flex;
    gap: 8px;
    margin-top: 8px;
  }

  // 两栏布局样式
  .tree-selection-panel, .selected-personnel-panel {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 16px;
    background: var(--el-bg-color-overlay);
    height: 400px;

    .panel-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--el-border-color-light);

      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .tree-container, .personnel-container {
      height: calc(100% - 50px);
      overflow-y: auto;
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
      padding: 8px;
      background: var(--el-fill-color-blank);
    }
  }

  .personnel-list {
    .personnel-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      margin-bottom: 8px;
      background: var(--el-bg-color-overlay);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        border-color: var(--el-color-primary-light-7);
        background: var(--el-color-primary-light-9);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .user-info {
        flex: 1;

        .user-name {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-primary);
          margin-bottom: 2px;
        }

        .user-dept {
          font-size: 12px;
          color: var(--el-text-color-regular);
          margin-bottom: 2px;
        }

        .user-phone {
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
  }

  // 自定义树节点样式
  :deep(.el-tree) {
    .el-tree-node__content {
      height: auto;
      min-height: 28px;
    }
  }

  .custom-dept-node {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-right: 8px;
    width: 100%;

    .dept-label {
      display: flex;
      align-items: center;
      gap: 8px;

      .dept-name {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .user-count {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .custom-user-node {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 14px;
    padding-right: 8px;
    width: 100%;
    margin-left: 20px;

    .user-label {
      display: flex;
      align-items: center;
      gap: 4px;

      .user-name {
        color: var(--el-text-color-primary);
      }

      .user-phone {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }

  .node-header {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
    flex-wrap: wrap;
  }

  .node-label {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  .user-count {
    color: #909399;
    font-size: 12px;
  }

  .user-list-inline {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    margin-left: 8px;
  }

  .user-item-inline {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    margin-right: 8px;
  }

  .user-item-inline:hover {
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
  }


  
  // 描述列表样式优化
  :deep(.el-descriptions) {
    .el-descriptions__label {
      width: 120px;
      color: var(--el-text-color-secondary);
    }
    
    .el-descriptions__content {
      color: var(--el-text-color-primary);
    }
  }
}
</style>