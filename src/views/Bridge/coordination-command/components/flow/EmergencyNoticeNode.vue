<template>
  <div class="emergency-notice-node">
    <div class="node-header">
      <h3 class="node-title">险情同步</h3>
      <div class="button-group">
        <!--
        <el-button type="warning" @click="handleSendNotice" :disabled="!isFormValid">
          发送通知
        </el-button>
        -->
        <el-button
          v-if="isCurrentNode"
          type="primary"
          @click="handleNextStep">
          下一步
        </el-button>
      </div>
    </div>

    <!-- 事件详情展示 -->
    <div class="event-details-section" v-if="localEventData || props.eventData">
      <div class="section-title">事件详情</div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="事件标题">
          {{ (localEventData || props.eventData)?.eventName || '无标题' }}
        </el-descriptions-item>
        <el-descriptions-item label="事件等级">
          <el-tag :type="getEventLevelTag((localEventData || props.eventData)?.eventLevel)">
            {{ getEventLevelName((localEventData || props.eventData)?.eventLevel) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="发生地点">
          {{ (localEventData || props.eventData)?.location || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="事件类型">
          <el-tag :type="getEventCategoryTag((localEventData || props.eventData)?.eventCategory)">
            {{ getEventCategoryName((localEventData || props.eventData)?.eventCategory) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="事件描述" :span="2">
          {{ (localEventData || props.eventData)?.description || '无描述' }}
        </el-descriptions-item>
        <el-descriptions-item label="发生时间">
          {{ formatDateTime((localEventData || props.eventData)?.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="报告人员">
          {{ (localEventData || props.eventData)?.reporter || '未知' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 预案信息展示 -->
    <div class="plan-details-card" v-if="selectedPlan">
      <div class="card-header">
        <div class="card-title">已选预案信息</div>
        <el-tag size="small" :type="selectedPlan.planType === 1 ? 'primary' : 'warning'">
          {{ selectedPlan.planType === 1 ? '总体预案' : '专项预案' }}
        </el-tag>
      </div>
      <div class="card-content">
        <el-descriptions border :column="2" size="small">
          <el-descriptions-item label="预案名称" :span="2">
            {{ selectedPlan.planName }}
          </el-descriptions-item>
          <el-descriptions-item label="适用事件类型">
            <el-tag :type="getEventCategoryTag(selectedPlan.eventCategory)">
              {{ getEventCategoryName(selectedPlan.eventCategory) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="适用事件等级">
            <el-tag :type="getEventLevelTag(selectedPlan.eventLevel)">
              {{ getEventLevelName(selectedPlan.eventLevel) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发布单位" :span="2">
            {{ selectedPlan.publishUnit || '未知' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <!-- 同步信息列表 -->
    <div class="sync-info-card">
      <div class="card-header">
        <div class="card-title">同步信息列表</div>
        <el-button size="small" type="primary" @click="loadNotificationList">刷新</el-button>
      </div>
      <div class="card-content">
        <el-table v-loading="loading" :data="notificationList" style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" >
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
          </el-table-column>
          <el-table-column label="协同单位" prop="deptName" min-width="120" />
          <el-table-column label="负责人" prop="userName" min-width="100" />
          <el-table-column label="联系电话" prop="contactMethod" min-width="120" />
          <el-table-column label="同步方式" min-width="120">
            <template #default="scope">
              <el-tag type="info">{{ getNotifyMethodName(scope.row.notifyMethod) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="同步时间" min-width="160">
            <template #default="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="是否已读" width="90">
            <template #default="scope">
              <el-tag :type="scope.row.readStatus === 1 ? 'success' : 'warning'">
                {{ scope.row.readStatus === 1 ? '已读' : '未读' }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button 
                v-if="scope.row.readStatus !== 1" 
                type="primary" 
                size="small" 
                @click="handleResendNotification(scope.row)"
              >
                重新同步
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <el-pagination
          style="margin-top: 10px;margin-left: 10px;"
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 15, 20, 30]"
          layout="total, sizes, prev, pager, next"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
        <div class="empty-data" v-if="notificationList.length === 0">
          暂无同步信息数据
        </div>
      </div>
    </div>

    <!-- 提示信息 -->
    <div class="tip-section">
      <el-alert
        title="查看同步信息后，点击'下一步'进入现场处置阶段"
        type="success"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, defineEmits, defineProps, nextTick, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { getEmergencyPlanById } from '@/api/bridge/command/emergency';
import { getEmergencyEventById } from '@/api/bridge/command/event';
import { getNoticeList, publishNotice } from '@/api/bridge/command/notice';
import { useRoute } from 'vue-router';
import { listData } from "@/api/system/dict/data";
import type { DictDataQuery } from '@/api/system/dict/data/types';

const route = useRoute();
const props = defineProps({
  eventData: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['next-step']);

// 从localStorage获取选中的预案
const selectedPlan = ref(null);
// 添加事件数据的响应式引用
const localEventData = ref(null);

// 通知方式选项
const notifyMethodOptions = ref<any[]>([]);
// 加载状态
const loading = ref(false);
// 通知列表数据
const notificationList = ref([]);
// 分页参数
const pagination = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0
});

// 判断是否为当前节点
const isCurrentNode = computed(() => {
  return props.eventData && props.eventData.flowStatus === 2;
});

// 判断是否已完成
const isCompleted = computed(() => {
  return props.eventData && props.eventData.flowStatus > 2;
});

// 获取通知方式字典
const getNotifyMethodDict = async () => {
  try {
    const queryParams: DictDataQuery = {
      dictType: "notification_method",
      pageNum: 1,
      pageSize: 100,
      dictName: "",
      dictLabel: ""
    };
    const res = await listData(queryParams);
    if (res.code === 200) {
      notifyMethodOptions.value = res.rows.map(item => ({
        label: item.dictLabel,
        value: item.dictValue
      }));
    }
  } catch (error) {
    console.error("获取通知方式字典失败:", error);
    notifyMethodOptions.value = [
      { label: '短信', value: '1' },
      { label: '邮件', value: '2' },
      { label: '电话', value: '3' },
      { label: '应用推送', value: '4' }
    ];
  }
};

// 获取事件等级名称
const getEventLevelName = (level) => {
  const levels = {
    1: 'Ⅰ级',
    2: 'Ⅱ级',
    3: 'Ⅲ级',
    4: 'Ⅳ级',
    5: '全部'
  };
  return levels[level] || '未知';
};

// 获取事件等级标签样式
const getEventLevelTag = (level) => {
  const tags = {
    1: 'danger',
    2: 'warning',
    3: 'success',
    4: 'info',
    5: 'primary'
  };
  return tags[level] || '';
};

// 获取事件类型名称
const getEventCategoryName = (category) => {
  const categories = {
    1: '自然灾害',
    2: '事故灾难',
    3: '公共卫生事件',
    4: '社会安全事件',
    5: '全部'
  };
  return categories[category] || '未知';
};

// 获取事件类型标签样式
const getEventCategoryTag = (category) => {
  const tags = {
    1: 'danger',
    2: 'warning',
    3: 'success',
    4: 'info',
    5: 'primary'
  };
  return tags[category] || '';
};

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取通知方式名称
const getNotifyMethodName = (method) => {
  if (!method) return '-';
  // 确保 method 是一个数组，即使只有一个通知方式
  const methodArray = Array.isArray(method) ? method : method.split(',');
  return methodArray
    .map((m) => {
      const found = notifyMethodOptions.value.find((item) => item.value === m);
      return found ? found.label : m;
    })
    .join('、');
};

// 获取事件详情
const fetchEventData = async (eventId: string) => {
  try {
    const res = await getEmergencyEventById(eventId);
    if (res.code === 200) {
      localEventData.value = res.data;
      return res.data;
    } else {
      ElMessage.warning('获取事件信息失败');
      return null;
    }
  } catch (error) {
    console.error('获取事件详情失败:', error);
    ElMessage.error('获取事件详情失败');
    return null;
  }
};

// 从预案ID获取预案信息
const fetchPlanDetails = async () => {
  try {
    // 获取事件数据（优先使用props中的eventData，如果没有则使用本地的localEventData）
    let eventData = props.eventData;
    
    // 如果props中没有eventData或者没有planId
    if (!eventData?.planId) {
      // 尝试从路由参数获取事件ID
      const eventId = route.query.eventId as string;
      if (eventId) {
        // 通过API获取事件详情
        const fetchedEventData = await fetchEventData(eventId);
        if (fetchedEventData) {
          eventData = fetchedEventData;
          localEventData.value = fetchedEventData;
        }
      }
    }
    
    // 从事件数据中获取预案ID
    if (eventData && eventData.planId) {
      // 获取预案基本信息
      const res = await getEmergencyPlanById(eventData.planId);
      if (res.code === 200) {
        selectedPlan.value = res.data;
        return res.data.id; // 返回预案ID以便后续使用
      } else {
        ElMessage.warning('获取预案信息失败');
      }
    } else {
      // 尝试从localStorage获取预案信息
      const planJson = localStorage.getItem('selectedPlan');
      const planId = localStorage.getItem('selectedPlanId');
      
      if (planJson) {
        const plan = JSON.parse(planJson);
        selectedPlan.value = plan;
        return planId; // 返回预案ID以便后续使用
      } else {
        ElMessage.warning('未找到预案信息，请返回上一步选择预案');
      }
    }
    
    return null;
  } catch (error) {
    console.error('获取预案详情失败:', error);
    ElMessage.error('获取预案详情失败');
    return null;
  }
};

// 加载通知列表
const loadNotificationList = async () => {
  loading.value = true;
  try {
    // 获取事件ID
    const eventId = (props.eventData || localEventData.value)?.id;
    if (!eventId) {
      ElMessage.warning('无法获取事件ID');
      loading.value = false;
      return;
    }

    // 查询该事件的所有通知记录
    const res = await getNoticeList({
      eventId: eventId,
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize
    });

    if (res && res.rows) {
      notificationList.value = res.rows;
    } else {
      notificationList.value = [];
      pagination.value.total = 0;
      console.log('未获取到通知记录');
    }
  } catch (error) {
    console.error('获取通知列表失败:', error);
    ElMessage.error('获取通知列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handlePageChange = (page: number) => {
  pagination.value.pageNum = page;
  loadNotificationList();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size;
  pagination.value.pageNum = 1;
  loadNotificationList();
};

// 重新发送通知
const handleResendNotification = async (row) => {
  try {
    const res = await publishNotice(row.id);
    if (res.code === 200) {
      ElMessage.success('通知已重新发送');
      // 重新加载通知列表
      await loadNotificationList();
    } else {
      ElMessage.error(res.msg || '发送通知失败');
    }
  } catch (error) {
    console.error('重新发送通知失败:', error);
    ElMessage.error('重新发送通知失败');
  }
};

// 处理下一步
const handleNextStep = async () => {
  try {
    // 只触发next-step事件，不在此更新事件状态
    ElMessage.success('进入现场处置阶段');
    emit('next-step');
  } catch (error) {
    console.error('进入下一步失败:', error);
    ElMessage.error('操作失败');
  }
};

onMounted(async () => {
  try {
    // 获取通知方式字典
    await getNotifyMethodDict();
    
    // 获取预案信息
    await fetchPlanDetails();
    
    // 加载通知列表
    await loadNotificationList();
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
});
</script>

<style scoped lang="scss">
.emergency-notice-node {
  padding: 20px;
  
  .node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .node-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: var(--el-text-color-primary);
    }
    
    .button-group {
      display: flex;
      gap: 10px;
    }
  }
  
  .event-details-section {
    margin-bottom: 24px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    overflow: hidden;
    
    .section-title {
      padding: 12px 16px;
      font-size: 16px;
      font-weight: 600;
      background-color: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-light);
      color: var(--el-text-color-primary);
    }
  }
  
  .plan-details-card, .sync-info-card {
    margin: 20px 0;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    overflow: hidden;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background-color: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-light);
      
      .card-title {
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }
    
    .card-content {
      padding: 16px;
    }
  }
  
  .tip-section {
    margin-top: 20px;
  }
  
  .w-full {
    width: 100%;
  }
  
  .empty-data {
    padding: 30px;
    text-align: center;
    color: var(--el-text-color-secondary);
    font-size: 14px;
  }
}
</style> 