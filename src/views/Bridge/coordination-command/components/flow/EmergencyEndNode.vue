<template>
  <div class="emergency-end-node">
    <div class="node-header">
      <h3 class="node-title">应急终止评估</h3>
      <div class="button-group">
        <el-button
          v-if="evaluationList.length === 0 && isCurrentNode"
          type="primary"
          @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增评估
        </el-button>
        <el-button
          v-if="isCurrentNode"
          type="primary"
          @click="$emit('next-step')">
          完成评估
        </el-button>
      </div>
    </div>

    <!-- 事件详情 -->
    <div class="event-details-section">
      <div class="section-title">事件详情</div>
      <el-descriptions border :column="2">
        <el-descriptions-item label="事件标题">{{ eventData.eventName || '无标题' }}</el-descriptions-item>
        <el-descriptions-item label="发生地点">{{ eventData.location }}</el-descriptions-item>
        <el-descriptions-item label="事件描述" :span="2">{{ eventData.description }}</el-descriptions-item>
        <el-descriptions-item label="发生时间">{{ formatDateTime(eventData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="持续时间">{{ getDuration() }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 评估列表 -->
    <div class="evaluation-section">
      <div class="section-title">处置效果评估列表</div>
      
      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="evaluationList" class="custom-table">
        <el-table-column prop="deptId" label="评估部门" width="120" />
        <el-table-column prop="evaluationTime" label="评估时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.evaluationTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalScore" label="最终评分" width="100" align="center">
          <template #default="{ row }">
            <span :class="getScoreClass(row.totalScore)">{{ row.totalScore || '0' }}分</span>
          </template>
        </el-table-column>
        <el-table-column prop="comments" label="评估总结" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="180" fixed="right" align="center">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleView(row)" class="operation-button">查看</el-button>
            <el-button
              v-if="isCurrentNode"
              link
              type="primary"
              @click="handleEdit(row)"
              class="operation-button">编辑</el-button>
            <el-button
              v-if="isCurrentNode"
              link
              @click="handleDelete(row)"
              class="operation-button danger">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          background
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="60%" class="custom-dialog">
      <el-form ref="formRef" :model="evaluationForm" :rules="evaluationRules" label-width="120px" class="dialog-form">
        <el-form-item label="评估部门" prop="deptId">
          <el-input v-model="evaluationForm.deptId" placeholder="请输入评估部门" />
        </el-form-item>
        <el-form-item label="评估时间" prop="evaluationTime">
          <el-date-picker
            v-model="evaluationForm.evaluationTime"
            type="datetime"
            placeholder="选择评估时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="w-full"
          />
        </el-form-item>
        
        <!-- 评分表格 -->
        <div class="score-table-title">评分项目</div>
        <el-table :data="scoreItems" border class="score-table">
          <el-table-column prop="content" label="评分维度" min-width="150" />
          <el-table-column prop="maxScore" label="最高分值" width="100" align="center" />
          <el-table-column prop="weight" label="权重" width="100" align="center">
            <template #default="{ row }">
              {{ (row.weight * 100).toFixed(0) }}%
            </template>
          </el-table-column>
          <el-table-column label="得分" width="150" align="center">
            <template #default="{ row }">
              <el-input-number 
                v-model="row.score" 
                :min="0" 
                :max="row.maxScore" 
                :precision="1"
                controls-position="right"
                @change="calculateTotalScore"
              />
            </template>
          </el-table-column>
        </el-table>
        
        <div class="total-score-display">
          <span class="label">最终评分：</span>
          <span class="value" :class="getScoreClass(evaluationForm.totalScore)">{{ evaluationForm.totalScore || '0' }}分</span>
        </div>
        
        <el-form-item label="评估总结" prop="comments">
          <el-input
            type="textarea"
            v-model="evaluationForm.comments"
            :rows="4"
            placeholder="请输入评估总结和补充说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </template>
    </el-dialog>

    <!-- 查看详情弹窗 -->
    <el-dialog title="评估详情" v-model="viewDialogVisible" width="60%" class="custom-dialog">
      <el-form :model="detailData" label-width="120px" class="dialog-form" disabled>
        <el-form-item label="评估部门">
          <el-input v-model="detailData.deptId" readonly />
        </el-form-item>
        <el-form-item label="评估时间">
          <el-date-picker
            v-model="detailData.evaluationTime"
            type="datetime"
            readonly
            disabled
            class="w-full"
          />
        </el-form-item>
        
        <!-- 评分表格 -->
        <div class="score-table-title">评分项目</div>
        <el-table :data="detailScoreItems" border class="score-table">
          <el-table-column prop="content" label="评分维度" min-width="150" />
          <el-table-column prop="maxScore" label="最高分值" width="100" align="center" />
          <el-table-column prop="weight" label="权重" width="100" align="center">
            <template #default="{ row }">
              {{ (row.weight * 100).toFixed(0) }}%
            </template>
          </el-table-column>
          <el-table-column label="得分" width="150" align="center">
            <template #default="{ row }">
              <span>{{ row.score }}</span>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="total-score-display">
          <span class="label">最终评分：</span>
          <span class="value" :class="getScoreClass(detailData.totalScore)">{{ detailData.totalScore || '0' }}分</span>
        </div>
        
        <el-form-item label="评估总结">
          <el-input
            type="textarea"
            v-model="detailData.comments"
            :rows="4"
            readonly
            disabled
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关 闭</el-button>
      </template>
    </el-dialog>

    <!-- 提示信息 -->
    <div class="tip-section">
      <el-alert
        title="完成评估后，点击'完成评估'进入后期处置阶段"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { 
  getDisposalEvaluationList,
  getDisposalEvaluationDetail,
  addDisposalEvaluation, 
  updateDisposalEvaluation,
  deleteDisposalEvaluation,
  getEvaluationRuleList 
} from "@/api/bridge/command/evaluation";

dayjs.extend(duration);

const props = defineProps({
  eventData: {
    type: Object,
    required: true
  }
});

defineEmits(['next-step']);

// 判断是否为当前节点
const isCurrentNode = computed(() => {
  return props.eventData && props.eventData.flowStatus === 4;
});

// 判断是否已完成
const isCompleted = computed(() => {
  return props.eventData && props.eventData.flowStatus > 4;
});

// 终止时间默认为当前时间
const endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

// 表格相关
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const evaluationList = ref<any[]>([]);

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = ref('');
const formRef = ref();
const viewDialogVisible = ref(false);
const detailData = ref(null);
const detailScoreItems = ref([]);

// 评估表单
const evaluationForm = ref({
  id: "",
  eventId: "",
  deptId: "",
  evaluationTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
  totalScore: 0,
  comments: "",
  scoreItems: []
});

// 评分项数据
const scoreItems = ref([]);

// 表单校验规则
const evaluationRules = {
  deptId: [{ required: true, message: "请输入评估部门", trigger: "blur" }],
  evaluationTime: [{ required: true, message: "请选择评估时间", trigger: "change" }],
  comments: [{ required: true, message: "请输入评估总结", trigger: "blur" }]
};

// 计算事件持续时间
const getDuration = () => {
  if (!props.eventData.startTime) return '-';
  const start = dayjs(props.eventData.startTime);
  const end = dayjs(endTime);
  const diff = dayjs.duration(end.diff(start));
  
  const days = diff.days();
  const hours = diff.hours();
  const minutes = diff.minutes();
  
  let result = '';
  if (days > 0) result += `${days}天`;
  if (hours > 0 || days > 0) result += `${hours}小时`;
  result += `${minutes}分钟`;
  
  return result;
};

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取评分样式
const getScoreClass = (score) => {
  if (!score) return 'score-low';
  score = parseFloat(score);
  if (score >= 80) return 'score-high';
  if (score >= 60) return 'score-medium';
  return 'score-low';
};

// 获取评估列表
const fetchEvaluationList = async () => {
  loading.value = true;
  try {
    if (!props.eventData || !props.eventData.id) return;
    
    const params = {
      eventId: props.eventData.id,
      pageNum: currentPage.value,
      pageSize: pageSize.value
    };
    
    const res = await getDisposalEvaluationList(params);
    if (res.code === 200) {
      evaluationList.value = res.rows || [];
      total.value = res.total || 0;
    }
  } catch (error) {
    console.error('获取评估列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理新增
const handleAdd = async () => {
  dialogTitle.value = '新增处置效果评估';
  evaluationForm.value = {
    id: '',
    eventId: props.eventData.id,
    deptId: '',
    evaluationTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    totalScore: 0,
    comments: '',
    scoreItems: []
  };
  await initScoreItems();
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getDisposalEvaluationDetail(row.id);
    if (res.code === 200) {
      evaluationForm.value = {
        ...res.data,
        evaluationTime: res.data.evaluationTime
          ? dayjs(res.data.evaluationTime).format('YYYY-MM-DD HH:mm:ss')
          : dayjs().format('YYYY-MM-DD HH:mm:ss'),
        scoreItems: res.data.scoreItems || []
      };

      // 初始化评分项
      await initScoreItems();

      // 如果有历史评分数据，填充到表格中
      if (res.data.scoreItems && res.data.scoreItems.length > 0) {
        res.data.scoreItems.forEach((item) => {
          const index = scoreItems.value.findIndex((i) => i.ruleId === item.ruleId);
          if (index !== -1) {
            scoreItems.value[index].score = item.score;
          }
        });
      }

      calculateTotalScore();
      dialogTitle.value = '编辑处置效果评估';
      dialogVisible.value = true;
    }
  } catch (error) {
    ElMessage.error('获取详情失败');
  }
};

// 处理查看
const handleView = async (row) => {
  try {
    const res = await getDisposalEvaluationDetail(row.id);
    if (res.code === 200) {
      detailData.value = {
        ...res.data,
        evaluationTime: res.data.evaluationTime
          ? dayjs(res.data.evaluationTime).format('YYYY-MM-DD HH:mm:ss')
          : dayjs().format('YYYY-MM-DD HH:mm:ss'),
      };

      // 获取评分项详情
      const rules = await getScoreRules();
      
      // 处理评分项
      if (res.data.scoreItems && res.data.scoreItems.length > 0) {
        detailScoreItems.value = res.data.scoreItems.map((item) => {
          const rule = rules.find((r) => r.id === item.ruleId);
          return {
            ...item,
            content: rule ? rule.content : item.content || '未知维度',
            maxScore: rule ? rule.maxScore : item.maxScore || 0,
            weight: rule ? rule.weight : item.weight || 0
          };
        });
      } else {
        // 如果没有评分项，使用默认项
        detailScoreItems.value = [
          { ruleId: '1', content: '处置及时性', maxScore: 10, weight: 0.2, score: 0 },
          { ruleId: '2', content: '信息传递准确性', maxScore: 10, weight: 0.2, score: 0 },
          { ruleId: '3', content: '资源调配效率', maxScore: 10, weight: 0.2, score: 0 },
          { ruleId: '4', content: '协作配合水平', maxScore: 10, weight: 0.2, score: 0 },
          { ruleId: '5', content: '预期目标达成度', maxScore: 10, weight: 0.2, score: 0 }
        ];
      }

      viewDialogVisible.value = true;
    }
  } catch (error) {
    ElMessage.error('获取详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该评估记录吗？', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteDisposalEvaluation(row.id);
      if (res.code === 200) {
        ElMessage.success('删除成功');
        await fetchEvaluationList();
      } else {
        ElMessage.error(res.msg || '删除失败');
      }
    } catch (error) {
      ElMessage.error('删除失败');
    }
  });
};

// 分页相关方法
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchEvaluationList();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchEvaluationList();
};

// 获取评分规则列表
const getScoreRules = async () => {
  try {
    const res = await getEvaluationRuleList({
      pageNum: 1,
      pageSize: 1000,
      status: 1 // 只获取启用状态的规则
    });
    if (res.code === 200) {
      return res.rows;
    }
    return [];
  } catch (error) {
    console.error('获取评分规则失败:', error);
    return [];
  }
};

// 初始化评分项
const initScoreItems = async () => {
  try {
    const rules = await getScoreRules();
    
    if (rules && rules.length > 0) {
      scoreItems.value = rules.map(rule => ({
        ruleId: rule.id,
        content: rule.content,
        maxScore: rule.maxScore,
        weight: rule.weight,
        score: 0
      }));
    } else {
      // 如果获取失败，使用默认评分项
      scoreItems.value = [
        { ruleId: '1', content: '处置及时性', maxScore: 10, weight: 0.2, score: 0 },
        { ruleId: '2', content: '信息传递准确性', maxScore: 10, weight: 0.2, score: 0 },
        { ruleId: '3', content: '资源调配效率', maxScore: 10, weight: 0.2, score: 0 },
        { ruleId: '4', content: '协作配合水平', maxScore: 10, weight: 0.2, score: 0 },
        { ruleId: '5', content: '预期目标达成度', maxScore: 10, weight: 0.2, score: 0 }
      ];
    }
  } catch (error) {
    console.error('获取评分规则失败:', error);
    // 使用默认评分项
    scoreItems.value = [
      { ruleId: '1', content: '处置及时性', maxScore: 10, weight: 0.2, score: 0 },
      { ruleId: '2', content: '信息传递准确性', maxScore: 10, weight: 0.2, score: 0 },
      { ruleId: '3', content: '资源调配效率', maxScore: 10, weight: 0.2, score: 0 },
      { ruleId: '4', content: '协作配合水平', maxScore: 10, weight: 0.2, score: 0 },
      { ruleId: '5', content: '预期目标达成度', maxScore: 10, weight: 0.2, score: 0 }
    ];
  }
};

// 计算总分
const calculateTotalScore = () => {
  if (scoreItems.value.length === 0) return 0;
  
  let totalScore = 0;
  for (const item of scoreItems.value) {
    totalScore += (item.score * item.weight);
  }
  
  evaluationForm.value.totalScore = parseFloat(totalScore.toFixed(2));
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 组装提交数据
        const submitData = {
          ...evaluationForm.value,
          eventId: props.eventData.id,
          scoreItems: scoreItems.value.map((item) => ({
            ruleId: item.ruleId,
            score: item.score
          }))
        };

        let res;
        if (evaluationForm.value.id) {
          res = await updateDisposalEvaluation(submitData);
        } else {
          res = await addDisposalEvaluation(submitData);
        }
        if (res.code === 200) {
          ElMessage.success(evaluationForm.value.id ? '修改成功' : '新增成功');
          dialogVisible.value = false;
          await fetchEvaluationList();
        } else {
          ElMessage.error(res.msg || (evaluationForm.value.id ? '修改失败' : '新增失败'));
        }
      } catch (error) {
        ElMessage.error('操作失败');
      }
    }
  });
};

onMounted(async () => {
  // 设置事件ID
  if (props.eventData && props.eventData.id) {
    evaluationForm.value.eventId = props.eventData.id;
    await fetchEvaluationList();
  } else {
    await initScoreItems();
  }
});
</script>

<style scoped lang="scss">
.emergency-end-node {
  padding: 20px;
  
  .node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .node-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: var(--el-text-color-primary);
    }
    
    .button-group {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: var(--el-color-primary);
      margin-right: 8px;
      border-radius: 2px;
    }
  }
  
  .event-details-section,
  .evaluation-section {
    margin-bottom: 24px;
    padding: 20px;
    background-color: var(--el-bg-color-overlay);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
  }
  
  .w-full {
    width: 100%;
  }
  
  .tip-section {
    margin-top: 20px;
  }
  
  /* 评分相关样式 */
  .score-table-title,
  .score-detail-title {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 10px;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: var(--el-color-primary);
      margin-right: 8px;
      border-radius: 2px;
    }
  }
  
  .score-table {
    margin-bottom: 20px;
    
    :deep(.el-input-number) {
      width: 120px;
    }
  }
  
  .total-score-display {
    margin: 20px 0;
    padding: 15px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
    display: flex;
    align-items: center;
    
    .label {
      font-size: 16px;
      font-weight: 600;
      margin-right: 10px;
    }
    
    .value {
      font-size: 24px;
      font-weight: 700;
    }
  }
  
  /* 评分颜色 */
  .score-high {
    color: var(--el-color-success);
  }
  
  .score-medium {
    color: var(--el-color-warning);
  }
  
  .score-low {
    color: var(--el-color-danger);
  }
  
  /* 表格样式 */
  .custom-table {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    background: var(--el-bg-color-overlay);

    :deep(th) {
      background: var(--el-bg-color-page) !important;
    }

    :deep(.el-table__row) {
      background-color: var(--el-bg-color-overlay);
    }

    .operation-button {
      padding: 2px 8px;
      margin: 0 2px;
      font-size: 12px;

      &.danger {
        color: var(--el-color-danger);
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  /* 详情查看样式 */
  .detail-view {
    .detail-item {
      margin-bottom: 15px;
      display: flex;

      .label {
        width: 120px;
        font-weight: 600;
        color: var(--el-text-color-regular);
      }

      .value {
        flex: 1;
        color: var(--el-text-color-primary);
      }

      &.detail-comments {
        display: block;

        .label {
          display: block;
          margin-bottom: 8px;
        }

        .comments-text {
          padding: 15px;
          background-color: var(--el-fill-color-light);
          border-radius: 4px;
          min-height: 80px;
          white-space: pre-line;
        }
      }
    }
  }
}
</style> 