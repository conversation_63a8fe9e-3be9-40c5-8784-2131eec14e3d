<template>
  <div class="field-processing-node">
    <div class="node-header">
      <h3 class="node-title">现场处置</h3>
      <el-button
        v-if="isCurrentNode"
        type="danger"
        @click="handleEndProcess">
        结束处置
      </el-button>
    </div>

    <div class="processing-layout">
      <!-- 左侧处理进展 -->
      <div class="processing-progress">
        <div class="section-title">处理进展</div>
        
        <div class="progress-records">
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in nodeList"
              :key="record.id || index"
              :timestamp="formatDateTime(record.createTime)"
              :type="record.status === 1 ? 'success' : 'primary'"
            >
              {{ record.nodeName }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>

      <!-- 右侧处理节点列表 -->
      <div class="processing-nodes">
        <div class="section-title">处理节点</div>
        
        <div class="table-operations">
          <el-button
            v-if="isCurrentNode"
            type="primary"
            @click="handleAdd">
            <el-icon><Plus /></el-icon>新增节点
          </el-button>
        </div>
        
        <div class="table-container">
          <el-table v-loading="loading" :data="nodeList" stripe border>
            <el-table-column label="节点名称" prop="nodeName" min-width="120" show-overflow-tooltip />
            <el-table-column label="预计耗时(小时)" prop="expectedHours" min-width="100" />
            <el-table-column label="节点状态" prop="status" width="90">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'warning' : 'success'" size="small">
                  {{ row.status === 1 ? '进行中' : '已完成' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="节点进行时间" width="150">
              <template #default="{ row }">
                {{ formatDateTime(row.processTime || row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="节点类型" prop="nodeType" width="90">
              <template #default="{ row }">
                <el-tag :type="getNodeTypeTag(row.nodeType)" size="small">
                  {{ getNodeTypeName(row.nodeType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="note" min-width="150" show-overflow-tooltip />
            <el-table-column label="附件" width="80" align="center">
              <template #default="{ row }">
                <el-button size="small" type="primary" link @click="handleViewAttachments(row)">详情</el-button>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="isCurrentNode"
                link
                type="primary"
                @click="handleEdit(row)">编辑</el-button>
              <el-button
                v-if="row.status === 1 && isCurrentNode"
                link
                type="success"
                @click="handleCompleteNode(row)">完成</el-button>
              <el-button
                v-if="isCurrentNode"
                link
                type="danger"
                @click="handleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        </div>
        
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 提示信息 -->
    <div class="tip-section">
      <el-alert
        title="添加并完成所有处理节点后，点击'结束处置'进入后期处置阶段"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" append-to-body>
      <el-form ref="nodeFormRef" :model="nodeForm" :rules="nodeRules" label-width="100px">
        <el-form-item label="节点名称" prop="nodeName">
          <el-input v-model="nodeForm.nodeName" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="预计耗时" prop="expectedHours">
          <el-input-number v-model="nodeForm.expectedHours" :min="0.5" :step="0.5" placeholder="请输入预计耗时(小时)" style="width: 100%" />
        </el-form-item>
        <el-form-item label="节点状态" prop="status">
          <el-select v-model="nodeForm.status" placeholder="请选择节点状态" style="width: 100%">
            <el-option label="进行中" :value="1" />
            <el-option label="已完成" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="节点进行时间" prop="processTime">
          <el-date-picker
            v-model="nodeForm.processTime"
            type="datetime"
            placeholder="选择节点进行时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="节点类型" prop="nodeType">
          <el-select v-model="nodeForm.nodeType" placeholder="请选择节点类型" style="width: 100%">
            <el-option label="审批节点" :value="1" />
            <el-option label="处理节点" :value="2" />
            <el-option label="结束节点" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input v-model="nodeForm.note" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="附件">
          <el-upload
            :action="uploadFileUrl"
            :headers="headers"
            :file-list="uploadFileList"
            :on-success="handleUploadSuccess"
            :on-remove="handleUploadRemove"
            :before-upload="beforeUpload"
            :on-error="handleUploadError"
            @progress="handleUploadProgress"
            :multiple="false"
            list-type="picture-card"
            name="file"
          >
            <el-icon><Plus /></el-icon>
            <template #file="{ file }">
              <div class="upload-file-item">
                <template v-if="isImage(file.url)">
                  <el-image 
                    :src="file.url" 
                    :preview-src-list="[file.url]" 
                    class="upload-image" 
                    fit="cover" 
                  />
                  <div class="file-name">{{ file.originalName || file.name }}</div>
                </template>
                <template v-else>
                  <div class="file-document">
                    <el-icon><Document /></el-icon>
                    <span class="file-name">{{ file.originalName || file.name }}</span>
                  </div>
                </template>
                <el-icon
                  class="delete-icon"
                  @click.stop="handleUploadRemove(file, uploadFileList)"
                ><Delete /></el-icon>
              </div>
            </template>
          </el-upload>
          <div class="el-upload__tip">支持上传图片、Word、Excel、PDF等文件，单个文件不超过5MB</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 结束处置确认框 -->
    <el-dialog v-model="endDialogVisible" title="结束现场处置" width="400px">
      <div class="confirm-content">
        <el-icon :size="48" color="#E6A23C"><Warning /></el-icon>
        <p class="confirm-message">确定要结束现场处置阶段吗？</p>
        <p class="confirm-desc">结束后将进入后期处置阶段，无法返回修改</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="endDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEnd">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 附件详情弹窗 -->
    <el-dialog
      v-model="attachmentDialogVisible"
      title="附件详情"
      width="50%"
      class="attachment-dialog"
    >
      <div v-if="currentAttachments && currentAttachments.length > 0" class="attachment-list">
        <div v-for="(file, index) in currentAttachments" :key="index" class="attachment-item">
          <template v-if="isImage(file.url)">
            <el-image 
              :src="file.url" 
              :preview-src-list="[file.url]" 
              class="attachment-image" 
              fit="cover" 
            />
            <div class="file-name file-link" @click="handleDownload(file)">
                <el-link 
                  :underline="false"
                  class="file-link"
                >
                  {{ file.originalName || file.name }}
                </el-link>
              </div>
          </template>
          <template v-else>
            <div class="file-document">
              <el-icon><Document /></el-icon>
              <el-link 
                :underline="false"
                class="file-link"
                @click="handleDownload(file)"
              >
                {{ file.originalName || file.name }}
              </el-link>
            </div>
          </template>
        </div>
      </div>
      <div v-else class="no-attachment">
        <el-empty description="暂无附件" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="attachmentDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, defineProps, defineEmits, watch, getCurrentInstance } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getFlowList, getFlowDetail, addFlow, updateFlow, deleteFlow } from '@/api/bridge/command/flow';
import { updateEmergencyEvent } from '@/api/bridge/command/event';
import dayjs from 'dayjs';
import { Plus, Warning, Document, Delete } from '@element-plus/icons-vue';
import { listByIds, delOss } from '@/api/system/oss';
import { globalHeaders } from '@/utils/request';

const props = defineProps({
  eventData: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['end-process']);

// 数据列表
const loading = ref(false);
const nodeList = ref([]);
const total = ref(0);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  type: 1, // 现场处置类型
  eventId: '',
});

// 对话框显示控制
const dialogVisible = ref(false);
const dialogTitle = computed(() => {
  return nodeForm.value.id ? '编辑节点' : '新增节点';
});
const nodeFormRef = ref();
const nodeForm = ref<Record<string, any>>({
  id: undefined,
  nodeName: '',
  expectedHours: 1,
  status: 1, // 默认进行中
  nodeType: 2, // 默认处理节点
  note: '',
  type: 1, // 现场处置类型
  eventId: '',
  processTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 默认当前时间
});

// 表单验证规则
const nodeRules = {
  nodeName: [{ required: true, message: '请输入节点名称', trigger: 'blur' }],
  expectedHours: [{ required: true, message: '请输入预计耗时', trigger: 'blur' }],
  nodeType: [{ required: true, message: '请选择节点类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择节点状态', trigger: 'change' }],
  processTime: [{ required: true, message: '请选择节点进行时间', trigger: 'change' }],
};

// 节点类型字典
const getNodeTypeName = (nodeType: number) => {
  const types = {
    1: '审批节点',
    2: '处理节点',
    3: '结束节点'
  };
  return types[nodeType] || '未知';
};

// 节点类型标签
const getNodeTypeTag = (nodeType: number) => {
  const tags: Record<number, 'primary' | 'success' | 'danger' | 'info' | 'warning'> = {
    1: 'primary',
    2: 'success',
    3: 'danger'
  };
  return tags[nodeType] || 'info';
};

// 获取节点列表
const getNodeList = async () => {
  loading.value = true;
  try {
    // 确保设置了eventId
    if (!queryParams.eventId && props.eventData && props.eventData.id) {
      queryParams.eventId = props.eventData.id;
    }
    
    const res = await getFlowList(queryParams);
    if (res.code === 200) {
      nodeList.value = res.rows || [];
      total.value = res.total || 0;
    }
  } catch (error) {
    console.error('获取节点列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  getNodeList();
};

const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page;
  getNodeList();
};

// 新增节点
const handleAdd = () => {
  resetForm();
  nodeForm.value.eventId = props.eventData.id;
  nodeForm.value.type = 1; // 现场处置类型
  nodeForm.value.processTime = dayjs().format('YYYY-MM-DD HH:mm:ss'); // 默认当前时间
  dialogVisible.value = true;
};

// 编辑节点
const handleEdit = async (row: any) => {
  resetForm();
  const id = row.id || row.processFlowLogId;
  try {
    const res = await getFlowDetail(id);
    if (res.code === 200) {
      nodeForm.value = {
        ...res.data,
        processTime: res.data.processTime || res.data.createTime || dayjs().format('YYYY-MM-DD HH:mm:ss')
      };
    }
  } catch (error) {
    console.error('获取节点详情失败:', error);
  }
  dialogVisible.value = true;
};

// 完成节点
const handleCompleteNode = async (row: any) => {
  try {
    const data = { 
      ...row, 
      status: 2, // 设置为已完成
      note: row.note || `节点"${row.nodeName}"已完成`
    };
    const res = await updateFlow(data);
    if (res.code === 200) {
      ElMessage.success('节点已完成');
      getNodeList();
    } else {
      ElMessage.error(res.msg || '操作失败');
    }
  } catch (error) {
    console.error('完成节点失败:', error);
    ElMessage.error('操作失败');
  }
};

// 添加用户操作记录
const addOperationRecord = async (operationName: string) => {
  const operationRecord = {
    nodeName: operationName,
    expectedHours: 0,
    status: 2, // 已完成
    nodeType: 2, // 处理节点
    note: `用户执行了操作: ${operationName}`,
    type: 1, // 现场处置类型
    eventId: props.eventData.id,
    processTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
  };
  
  try {
    await addFlow(operationRecord);
  } catch (error) {
    console.error('添加操作记录失败:', error);
  }
};

// 删除节点
const handleDelete = (row: any) => {
  const id = row.id || row.processFlowLogId;
  ElMessageBox.confirm('确定要删除该节点吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteFlow(id);
      if (res.code === 200) {
        ElMessage.success('删除成功');
        getNodeList();
      } else {
        ElMessage.error(res.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除节点失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
};

// 重置表单
const resetForm = () => {
  nodeForm.value = {
    id: undefined,
    nodeName: '',
    expectedHours: 1,
    status: 1,
    nodeType: 2,
    note: '',
    type: 1, // 现场处置类型
    eventId: props.eventData ? props.eventData.id : '',
    processTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 默认当前时间
  };
  
  if (nodeFormRef.value) {
    nodeFormRef.value.resetFields();
  }
};

// 提交表单
const submitForm = async () => {
  if (!nodeFormRef.value) return;
  await nodeFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const hasId = !!nodeForm.value.id;
        const actionApi = hasId ? updateFlow : addFlow;
        const res = await actionApi(nodeForm.value);
        
        if (res.code === 200) {
          ElMessage.success(hasId ? '修改成功' : '新增成功');
          dialogVisible.value = false;
          
          getNodeList();
        } else {
          ElMessage.error(res.msg || (hasId ? '修改失败' : '新增失败'));
        }
      } catch (error) {
        const hasId = !!nodeForm.value.id;
        console.error(hasId ? '修改节点失败:' : '新增节点失败:', error);
        ElMessage.error(hasId ? '修改失败' : '新增失败');
      }
    }
  });
};

// 结束处置相关
const endDialogVisible = ref(false);

// 处理结束处置按钮点击
const handleEndProcess = () => {
  // 检查是否有未完成的节点
  const hasUnfinishedNode = nodeList.value.some(node => node.status === 1);
  if (hasUnfinishedNode) {
    ElMessageBox.confirm('还有未完成的节点，确定要结束处置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      endDialogVisible.value = true;
    }).catch(() => {});
  } else {
    endDialogVisible.value = true;
  }
};

// 确认结束处置
const confirmEnd = async () => {
  try {
    // 添加操作记录
    await addOperationRecord("结束现场处置");
    
    // 不直接调用API更新事件状态，避免与父组件的状态更新重复
    // 只触发事件，由父组件统一处理状态更新
    ElMessage.success('处置已结束，进入后期处置阶段');
    endDialogVisible.value = false;
    emit('end-process');
  } catch (error) {
    console.error('结束处置失败:', error);
    ElMessage.error('操作失败');
  }
};

// 格式化日期时间
const formatDateTime = (date: string) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 判断是否为当前节点
const isCurrentNode = computed(() => {
  return props.eventData && props.eventData.flowStatus === 3;
});

// 判断是否已完成
const isCompleted = computed(() => {
  return props.eventData && props.eventData.flowStatus > 3;
});

// 附件上传相关
const uploadFileUrl = import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload';
const headers = globalHeaders();
const uploadFileList = ref([]);
const uploading = ref(false);

// 监听fileId变化
watch(() => nodeForm.value.fileId, async (newVal) => {
  if (newVal) {
    const res = await listByIds(newVal);
    if (res.data) {
      uploadFileList.value = res.data.map(item => ({
        name: item.originalName || item.fileName,
        url: item.url,
        ossId: item.ossId
      }));
    } else {
      uploadFileList.value = [];
    }
  } else {
    uploadFileList.value = [];
  }
}, { immediate: true });

const beforeUpload = (file) => {
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 5MB!');
    return false;
  }
  return true;
};

const handleUploadProgress = (event, file, fileList) => {
  uploading.value = true;
};

const handleUploadSuccess = async (response, file, fileList) => {
  if (response.code === 200) {
    try {
      // 获取新上传文件的信息
      const fileRes = await listByIds(response.data.ossId);
      if (fileRes.data && fileRes.data.length > 0) {
        const fileInfo = fileRes.data[0];
        // 更新文件列表
        const newFile = {
          name: fileInfo.originalName || fileInfo.fileName,
          url: fileInfo.url,
          ossId: fileInfo.ossId
        };
        
        // 更新fileId
        let ids = nodeForm.value.fileId ? nodeForm.value.fileId.split(',') : [];
        ids.push(response.data.ossId);
        nodeForm.value.fileId = ids.join(',');
        
        // 更新显示列表
        uploadFileList.value = [...uploadFileList.value, newFile];
        ElMessage.success('上传成功');
      }
    } catch (error) {
      console.error('获取文件信息失败:', error);
      ElMessage.error('获取文件信息失败');
    }
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
  uploading.value = fileList.some(f => f.status === 'uploading');
};

const handleUploadError = () => {
  ElMessage.error('上传文件失败');
  uploading.value = false;
};

const handleUploadRemove = async (file, fileList) => {
  let ids = nodeForm.value.fileId ? nodeForm.value.fileId.split(',') : [];
  ids = ids.filter(id => id !== file.ossId);
  nodeForm.value.fileId = ids.join(',');
  try {
    await delOss(file.ossId);
    ElMessage.success('删除成功');
  } catch (e) {
    ElMessage.error('删除失败');
  }
  uploading.value = fileList.some(f => f.status === 'uploading');
};

function isImage(url) {
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
}

// 更新表单接口
interface NodeFormState {
  id?: string | number;
  nodeName: string;
  expectedHours: number;
  status: number;
  nodeType: number;
  note: string;
  type: number;
  eventId: string;
  processTime: string;
  fileId?: string;
}

// 附件详情相关
const attachmentDialogVisible = ref(false);
const currentAttachments = ref<any[]>([]);

// 查看附件
const handleViewAttachments = async (row) => {
  if (row.fileId) {
    const res = await listByIds(row.fileId);
    if (res.data) {
      currentAttachments.value = res.data.map(item => ({
        name: item.originalName || item.fileName,
        url: item.url,
        ossId: item.ossId
      }));
    } else {
      currentAttachments.value = [];
    }
  } else {
    currentAttachments.value = [];
  }
  attachmentDialogVisible.value = true;
};

// 下载附件
const { proxy } = getCurrentInstance() as any;
const handleDownload = (file) => {
  if (proxy && proxy.$download && typeof proxy.$download.oss === 'function' && file.ossId) {
    proxy.$download.oss(file.ossId);
  } else {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.originalName || file.name || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

onMounted(() => {
  // 设置事件ID
  if (props.eventData && props.eventData.id) {
    queryParams.eventId = props.eventData.id;
    nodeForm.value.eventId = props.eventData.id;
  }
  
  // 获取节点列表
  getNodeList();
});
</script>

<style scoped lang="scss">
.field-processing-node {
  padding: 20px;

  @media (max-width: 768px) {
    padding: 16px;
  }

  @media (max-width: 480px) {
    padding: 12px;
  }

  .node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 20px;
    }

    .node-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      color: var(--el-text-color-primary);

      @media (max-width: 768px) {
        font-size: 16px;
      }
    }
  }
  
  .processing-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    margin-bottom: 24px;

    @media (max-width: 992px) {
      grid-template-columns: 1fr;
    }

    @media (max-width: 768px) {
      gap: 16px;
      margin-bottom: 20px;
    }

    @media (max-width: 480px) {
      gap: 12px;
      margin-bottom: 16px;
    }
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: var(--el-color-primary);
      margin-right: 8px;
      border-radius: 2px;
    }
  }
  
  .processing-progress {
    background-color: var(--el-bg-color-overlay);
    border-radius: 4px;
    padding: 16px;
    box-shadow: var(--el-box-shadow-light);
    
    .progress-records {
      max-height: 400px;
      overflow-y: auto;
      padding-right: 8px;
    }
  }
  
  .processing-nodes {
    background-color: var(--el-bg-color-overlay);
    border-radius: 4px;
    padding: 16px;
    box-shadow: var(--el-box-shadow-light);

    @media (max-width: 768px) {
      padding: 12px;
    }

    .table-operations {
      margin-bottom: 16px;
      display: flex;
      justify-content: flex-end;

      @media (max-width: 768px) {
        justify-content: center;
        margin-bottom: 12px;
      }
    }

    .table-container {
      width: 100%;
      overflow-x: auto;

      @media (max-width: 1200px) {
        overflow-x: scroll;
      }
    }

    .el-pagination {
      margin-top: 16px;
      justify-content: flex-end;

      @media (max-width: 768px) {
        justify-content: center;
        margin-top: 12px;
      }
    }

    // 表格响应式处理
    :deep(.el-table) {
      min-width: 800px; // 设置最小宽度，防止表格过度压缩

      @media (max-width: 768px) {
        font-size: 12px;
        min-width: 600px;

        .el-table__cell {
          padding: 8px 4px;
        }
      }

      // 确保表格不会超出容器宽度
      table-layout: fixed;
      width: 100%;
    }
  }
  
  .tip-section {
    margin-top: 20px;
  }
  
  .confirm-content {
    text-align: center;
    padding: 20px 0;
    
    .confirm-message {
      font-size: 16px;
      font-weight: 500;
      margin: 16px 0 8px;
      color: var(--el-text-color-primary);
    }
    
    .confirm-desc {
      color: var(--el-text-color-secondary);
      margin: 0;
    }
  }
}

.attachment-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

.attachment-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  padding: 16px;
  
  .attachment-item {
    position: relative;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    
    .attachment-image {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: 4px;
    }
    
    .file-name {
      margin-top: 8px;
      font-size: 12px;
      color: var(--el-text-color-regular);
      word-break: break-all;
    }
    
    .file-document {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      
      .el-icon {
        font-size: 32px;
        color: var(--el-text-color-secondary);
        margin-bottom: 8px;
      }
    }
  }
}

.upload-file-item {
  position: relative;
  width: 100%;
  height: 100%;
  
  .upload-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .file-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 4px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 12px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .file-document {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 8px;
    
    .el-icon {
      font-size: 24px;
      color: var(--el-text-color-secondary);
      margin-bottom: 4px;
    }
    
    .file-name {
      position: static;
      background: none;
      color: var(--el-text-color-regular);
    }
  }
  
  .delete-icon {
    position: absolute;
    top: 4px;
    right: 4px;
    padding: 4px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
    
    &:hover {
      background: var(--el-color-danger);
    }
  }
  
  &:hover .delete-icon {
    opacity: 1;
  }
}

.no-attachment {
  padding: 40px 0;
  text-align: center;
}

.file-link {
  margin-top: 8px;
  font-size: 14px;
  
  &:hover {
    color: var(--el-color-primary-light-3);
  }
}
</style> 