<template>
  <div class="list-page">
    <!-- <EmergencyVerticalTabNav /> -->
    <div class="content-container">
      <div class="mb-[10px]">
        <el-card shadow="hover">
          <div style="display: flex;  align-items: center;">
            <el-radio-group v-model="queryParams.type" @change="handleTypeChange">
              <el-radio-button :label="null">全部</el-radio-button>
              <el-radio-button :label="1">现场处置</el-radio-button>
              <el-radio-button :label="2">后期处置</el-radio-button>
            </el-radio-group>
            <el-button type="primary" @click="handleAdd" style="margin-left: auto;">
               新增
            </el-button>
            <el-button type="danger" icon="Delete" :disabled="selectedRows.length === 0" @click="batchDelete">删除</el-button>
          </div>
        </el-card>
      </div>
      
      <!-- 列表区域 -->
      <el-card shadow="hover" class="w-full">
        <el-table
          v-loading="loading"
          :data="tableData"
          stripe
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" width="80" align="center">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="事件描述" min-width="150" show-overflow-tooltip />
          <el-table-column prop="nodeName" label="节点名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="type" label="处置类型" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="row.type === 1 ? 'danger' : 'success'" class="status-tag">
                {{ row.type === 1 ? '现场处置' : '后期处置' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="节点状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'warning' : 'success'" class="status-tag">
                {{ row.status === 1 ? '进行中' : '已完成' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="节点进行时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.processTime || row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="expectedHours" label="预计耗时(小时)" width="120" />
          <el-table-column prop="note" label="备注" min-width="150" show-overflow-tooltip />
          <el-table-column label="附件" width="120" align="center">
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="handleViewAttachments(row)">详情</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" fixed="right" align="center">
            <template #default="{ row }">
              <el-button size="small" type="primary" link @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button v-if="row.status === 1" size="small" type="success" link @click="handleCompleteNode(row)">
                完成
              </el-button>
              <el-button size="small" type="danger" link @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-if="total > 0"
            background
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 新增/编辑弹窗 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="1000px"
        append-to-body
        class="custom-dialog"
      >
        <template #header>
          <div class="dialog-header">
            <span class="dialog-title">{{ dialogTitle }}</span>
          </div>
        </template>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="dialog-form"
        >
          <el-form-item label="关联事件" prop="eventId">
            <el-select
              v-model="form.eventId"
              placeholder="请选择关联事件"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="event in eventList"
                :key="event.id"
                :label="event.description"
                :value="event.id"
              >
                <div class="event-option">
                  <span class="event-description">{{ event.description }}</span>
                  <span class="event-time">{{ formatDateTime(event.happenTime) }}</span>
                  <el-tag size="small" :type="event.status === 1 ? 'info' : event.status === 2 ? 'warning' : 'success'">
                    {{ event.status === 1 ? '未启动' : event.status === 2 ? '进行中' : '已控制' }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="节点名称" prop="nodeName">
            <el-input v-model="form.nodeName" placeholder="请输入节点名称" />
          </el-form-item>
          <el-form-item label="处置类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择处置类型" style="width: 100%">
              <el-option label="现场处置" :value="1" />
              <el-option label="后期处置" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="预计耗时" prop="expectedHours">
            <el-input-number v-model="form.expectedHours" :min="0.5" :step="0.5" placeholder="请输入预计耗时(小时)" style="width: 100%" />
          </el-form-item>
          <el-form-item label="节点状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择节点状态" style="width: 100%">
              <el-option label="进行中" :value="1" />
              <el-option label="已完成" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="节点进行时间" prop="processTime">
            <el-date-picker
              v-model="form.processTime"
              type="datetime"
              placeholder="选择节点进行时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="节点类型" prop="nodeType">
            <el-select v-model="form.nodeType" placeholder="请选择节点类型" style="width: 100%">
              <el-option label="审批节点" :value="1" />
              <el-option label="处理节点" :value="2" />
              <el-option label="结束节点" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="note">
            <el-input
              type="textarea"
              v-model="form.note"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
          <el-form-item label="附件">
            <el-upload
              :action="uploadFileUrl"
              :headers="headers"
              :file-list="uploadFileList"
              :on-success="handleUploadSuccess"
              :on-remove="handleUploadRemove"
              :before-upload="beforeUpload"
              :on-error="handleUploadError"
              @progress="handleUploadProgress"
              :multiple="false"
              list-type="picture-card"
              name="file"
            >
              <el-icon><Plus /></el-icon>
              <template #file="{ file }">
                <div class="upload-file-item">
                  <template v-if="isImage(file.url)">
                    <el-image 
                      :src="file.url" 
                      :preview-src-list="[file.url]" 
                      class="upload-image" 
                      fit="cover" 
                    />
                    <div class="file-name">{{ file.originalName || file.name }}</div>
                  </template>
                  <template v-else>
                    <div class="file-document">
                      <el-icon><Document /></el-icon>
                      <span class="file-name">{{ file.originalName || file.name }}</span>
                    </div>
                  </template>
                  <el-icon
                    class="delete-icon"
                    @click.stop="handleUploadRemove(file, uploadFileList)"
                  ><Delete /></el-icon>
                </div>
              </template>
            </el-upload>
            <div class="el-upload__tip">支持上传图片、Word、Excel、PDF等文件，单个文件不超过5MB</div>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 文件预览弹窗 -->
      <el-dialog
        v-model="fileDialogVisible"
        title="相关文件"
        width="50%"
        class="file-dialog"
      >
        <div class="file-list">
          <div v-for="(file, index) in currentFiles" :key="index" class="file-item">
            <el-link :href="file" target="_blank" type="primary">
              <el-icon><Document /></el-icon>
              {{ getFileName(file) }}
            </el-link>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="fileDialogVisible = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 附件详情弹窗 -->
      <el-dialog
        v-model="attachmentDialogVisible"
        title="附件详情"
        width="50%"
        class="attachment-dialog"
      >
        <div v-if="currentAttachments && currentAttachments.length > 0" class="attachment-list">
          <div v-for="(file, index) in currentAttachments" :key="index" class="attachment-item">
            <template v-if="isImage(file.url)">
              <el-image 
                :src="file.url" 
                :preview-src-list="[file.url]" 
                class="attachment-image" 
                fit="cover" 
              />
              <div class="file-name file-link" @click="handleDownload(file)">
                <el-link 
                  :underline="false"
                  class="file-link"
                >
                  {{ file.originalName || file.name }}
                </el-link>
              </div>
            </template>
            <template v-else>
              <div class="file-document">
                <el-icon><Document /></el-icon>
                <el-link 
                  :underline="false"
                  class="file-link"
                  @click="handleDownload(file)"
                >
                  {{ file.originalName || file.name }}
                </el-link>
              </div>
            </template>
          </div>
        </div>
        <div v-else class="no-attachment">
          <el-empty description="暂无附件" />
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="attachmentDialogVisible = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { 
  getFlowList, 
  getFlowDetail, 
  addFlow, 
  updateFlow, 
  deleteFlow 
} from '@/api/bridge/command/flow';
import { getEmergencyEventById, getEmergencyEventList } from "@/api/bridge/command/event";
import dayjs from "dayjs";
import { useRouter, useRoute } from "vue-router";
import { Plus, Back, Document, Warning, Delete } from '@element-plus/icons-vue';
import { listByIds, delOss } from '@/api/system/oss';
import { globalHeaders } from '@/utils/request';
import { getCurrentInstance } from 'vue';
// import EmergencyVerticalTabNav from "@/components/EmergencyVerticalTabNav.vue";

// 路由相关
const router = useRouter();
const route = useRoute();

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const selectedRows = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  type: null, // null表示全部，1表示现场处置，2表示后期处置
});

// 弹窗相关
const dialogVisible = ref(false);
const dialogTitle = computed(() => {
  return form.value.id ? "编辑处置记录" : "新增处置记录"
});

interface FormState {
  id?: string | number;
  eventId: string | number;
  nodeName: string;
  type: number;
  expectedHours: number;
  status: number;
  processTime: string;
  nodeType: number;
  note: string;
  fileId?: string;
}

// 事件列表
const eventList = ref([]);

// 获取事件列表
const getEventList = async () => {
  try {
    const res = await getEmergencyEventList({
      pageNum: 1,
      pageSize: 1000,
    });
    if (res.code === 200) {
      eventList.value = res.rows || [];
    }
  } catch (error) {
    console.error("获取事件列表失败:", error);
  }
};

const formRef = ref();
const form = ref<FormState>({
  id: undefined,
  eventId: "",
  nodeName: "",
  type: null,
  expectedHours: 1,
  status: 1,
  processTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
  nodeType: 2, // 默认处理节点
  note: "",
  fileId: ""
});

// 文件预览弹窗
const fileDialogVisible = ref(false);
const currentFiles = ref([]);

// 附件上传相关
const uploadFileUrl = import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload';
const headers = globalHeaders();
const uploadFileList = ref([]);
const uploading = ref(false);

// 监听fileId变化
watch(() => form.value.fileId, async (newVal) => {
  if (newVal) {
    const res = await listByIds(newVal);
    if (res.data) {
      uploadFileList.value = res.data.map(item => ({
        name: item.originalName || item.fileName,
        url: item.url,
        ossId: item.ossId
      }));
    } else {
      uploadFileList.value = [];
    }
  } else {
    uploadFileList.value = [];
  }
}, { immediate: true });

const beforeUpload = (file) => {
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 5MB!');
    return false;
  }
  return true;
};

const handleUploadProgress = (event, file, fileList) => {
  uploading.value = true;
};

const handleUploadSuccess = async (response, file, fileList) => {
  if (response.code === 200) {
    try {
      // 获取新上传文件的信息
      const fileRes = await listByIds(response.data.ossId);
      if (fileRes.data && fileRes.data.length > 0) {
        const fileInfo = fileRes.data[0];
        // 更新文件列表
        const newFile = {
          name: fileInfo.originalName || fileInfo.fileName,
          url: fileInfo.url,
          ossId: fileInfo.ossId
        };
        
        // 更新fileId
        let ids = form.value.fileId ? form.value.fileId.split(',') : [];
        ids.push(response.data.ossId);
        form.value.fileId = ids.join(',');
        
        // 更新显示列表
        uploadFileList.value = [...uploadFileList.value, newFile];
        ElMessage.success('上传成功');
      }
    } catch (error) {
      console.error('获取文件信息失败:', error);
      ElMessage.error('获取文件信息失败');
    }
  } else {
    ElMessage.error(response.msg || '上传失败');
  }
  uploading.value = fileList.some(f => f.status === 'uploading');
};

const handleUploadError = () => {
  ElMessage.error('上传文件失败');
  uploading.value = false;
};

const handleUploadRemove = async (file, fileList) => {
  let ids = form.value.fileId ? form.value.fileId.split(',') : [];
  ids = ids.filter(id => id !== file.ossId);
  form.value.fileId = ids.join(',');
  try {
    await delOss(file.ossId);
    ElMessage.success('删除成功');
  } catch (e) {
    ElMessage.error('删除失败');
  }
  uploading.value = fileList.some(f => f.status === 'uploading');
};

function isImage(url) {
  return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url);
}

// 表单校验规则
const rules = {
  eventId: [{ required: true, message: "请选择关联事件", trigger: "change" }],
  nodeName: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  type: [{ required: true, message: "请选择处置类型", trigger: "change" }],
  expectedHours: [{ required: true, message: "请输入预计耗时", trigger: "blur" }],
  status: [{ required: true, message: "请选择节点状态", trigger: "change" }],
  processTime: [{ required: true, message: "请选择节点进行时间", trigger: "change" }],
  nodeType: [{ required: true, message: "请选择节点类型", trigger: "change" }]
};

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
};

// 获取处置阶段标签类型
const getStageTagType = (stage) => {
  const stageMap = {
    '预警': 'warning',
    '先期处置': 'info',
    '分级响应': 'primary',
    '现场处置': 'danger',
    '后期处置': 'success'
  };
  return stageMap[stage] || '';
};

// 获取文件名
const getFileName = (fileUrl) => {
  if (!fileUrl) return '';
  return fileUrl.split('/').pop() || fileUrl;
};

// 处理类型变更
const handleTypeChange = () => {
  getList();
};

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      type: queryParams.type
    };
    const res = await getFlowList(params);
    if (res.code === 200) {
      // 使用类型断言确保eventIds是有效的数组类型
      const eventIds = [...new Set(res.rows.map(item => item.eventId).filter(Boolean))] as (string | number)[];
      
      // 获取事件详情并添加描述
      if (eventIds.length > 0) {
        const eventDetailsPromises = eventIds.map(eventId => getEmergencyEventById(eventId));
        const eventDetailsResults = await Promise.allSettled(eventDetailsPromises);
        
        // 创建事件ID到描述的映射
        const eventDescriptionMap: Record<string, string> = {};
        eventDetailsResults.forEach((result, index) => {
          if (result.status === 'fulfilled' && result.value?.code === 200 && result.value?.data) {
            const eventId = String(eventIds[index]);
            eventDescriptionMap[eventId] = result.value.data.description;
          }
        });
        
        // 添加描述到表格数据
        tableData.value = res.rows.map(row => ({
          ...row,
          description: row.eventId ? eventDescriptionMap[String(row.eventId)] || '暂无描述' : '暂无描述'
        }));
      } else {
        tableData.value = res.rows || [];
      }
      
      total.value = res.total || 0;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 处理新增
const handleAdd = async () => {
  // 获取事件列表
  await getEventList();
  
  form.value = {
    id: undefined,
    eventId: "",
    nodeName: "",
    type: queryParams.type || null,
    expectedHours: 1,
    status: 1,
    processTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    nodeType: 2,
    note: "",
    fileId: ""
  };
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    // 获取事件列表
    await getEventList();
    
    const id = row.id || row.processFlowLogId;
    const res = await getFlowDetail(id);
    if (res.code === 200) {
      form.value = {
        ...res.data,
        processTime: res.data.processTime || res.data.createTime || dayjs().format('YYYY-MM-DD HH:mm:ss')
      };
      dialogVisible.value = true;
    }
  } catch (error) {
    ElMessage.error("获取详情失败");
  }
};

// 处理完成节点
const handleCompleteNode = async (row) => {
  try {
    const id = row.id || row.processFlowLogId;
    const data = { 
      ...row, 
      status: 2, // 设置为已完成
      note: row.note || `节点"${row.nodeName}"已完成`
    };
    const res = await updateFlow(data);
    if (res.code === 200) {
      ElMessage.success('节点已完成');
      getList();
    } else {
      ElMessage.error(res.msg || '操作失败');
    }
  } catch (error) {
    console.error('完成节点失败:', error);
    ElMessage.error('操作失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  const id = row.id || row.processFlowLogId;
  ElMessageBox.confirm("确认删除该处置记录吗？", "提示", {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: "warning",
  }).then(async () => {
    try {
      const res = await deleteFlow(id);
      if (res.code === 200) {
        ElMessage.success("删除成功");
        getList();
      } else {
        ElMessage.error(res.msg || "删除失败");
      }
    } catch (error) {
      ElMessage.error("删除失败");
    }
  }).catch(() => {});
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const submitData = { ...form.value };
        
        if (form.value.id) {
          const res = await updateFlow(submitData);
          if (res.code === 200) {
            ElMessage.success("修改成功");
            dialogVisible.value = false;
            getList();
          } else {
            ElMessage.error(res.msg || "修改失败");
          }
        } else {
          const res = await addFlow(submitData);
          if (res.code === 200) {
            ElMessage.success("新增成功");
            dialogVisible.value = false;
            getList();
          } else {
            ElMessage.error(res.msg || "新增失败");
          }
        }
      } catch (error) {
        ElMessage.error("操作失败");
      }
    }
  });
};

// 分页相关方法
const handleSizeChange = (val) => {
  queryParams.pageSize = val;
  getList();
};

const handleCurrentChange = (val) => {
  queryParams.pageNum = val;
  getList();
};

// 选择行改变
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 删除
const batchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的数据');
    return;
  }

  // 获取选中行的ID列表
  const ids = selectedRows.value.map(item => item.id).join(',');

  ElMessageBox.confirm('确定要删除选中的记录吗？', '警告', {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(async () => {
    try {
      await deleteFlow(ids);
      ElMessage.success('删除成功');
      // 清空选中项
      selectedRows.value = [];
      // 刷新列表数据
      getList();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 附件详情相关
const attachmentDialogVisible = ref(false);
const currentAttachments = ref<any[]>([]);

// 查看附件
const handleViewAttachments = async (row) => {
  if (row.fileId) {
    const res = await listByIds(row.fileId);
    if (res.data) {
      currentAttachments.value = res.data.map(item => ({
        name: item.originalName || item.fileName,
        url: item.url,
        ossId: item.ossId
      }));
    } else {
      currentAttachments.value = [];
    }
  } else {
    currentAttachments.value = [];
  }
  attachmentDialogVisible.value = true;
};

// 下载处理函数
const { proxy } = getCurrentInstance() as any;
const handleDownload = (file) => {
  if (proxy && proxy.$download && typeof proxy.$download.oss === 'function' && file.ossId) {
    proxy.$download.oss(file.ossId);
  } else {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.originalName || file.name || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
$bg-color-page: var(--el-bg-color-page);
$bg-color-overlay: var(--el-bg-color-overlay);
$box-shadow-light: var(--el-box-shadow-light);
$box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
$border-radius: 4px;
$border-radius-dialog: 8px;
$border-color-light: var(--el-border-color-light);
$color-primary: var(--el-color-primary);
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$deep-box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
$header-padding: 15px 20px;
$general-padding: 20px;
$footer-padding: 10px 20px;
$divider-color: #f0f0f0;

.list-page {
  padding: 20px;
  background: $bg-color-page;
  border-radius: $border-radius;
  box-shadow: $box-shadow-light;
  display: flex;
  gap: 20px;
}

.content-container {
  flex: 1;
  overflow: hidden;
}

.filter-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .el-radio-button {
    margin-right: 10px;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  margin-top: 16px;
  background: $bg-color-overlay;

  :deep(th) {
    background: $bg-color-page !important;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: $bg-color-page;
  border-radius: $border-radius-dialog $border-radius-dialog 0 0;
  border-bottom: 1px solid $border-color-light;

  .dialog-title {
    font-size: 18px;
    color: #303133;
    font-weight: 600;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }
}

.btn-group {
  .edit-btn,
  .cancel-btn,
  .save-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 10px 20px;
    border-radius: 6px;
  }

  .edit-btn {
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .cancel-btn {
    &:hover {
      background-color: #f4f4f5;
      color: #909399;
    }
  }

  .save-btn {
    padding: 10px 24px;

    &:hover {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }
  }
}

.custom-dialog {
  :deep(.el-dialog) {
    border-radius: $border-radius-dialog;
    box-shadow: $box-shadow;

    .el-dialog__body {
      padding: 24px;
      background: $bg-color-overlay;
    }

    .el-dialog__header {
      padding: 0;
      margin: 0;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: #303133;
        font-size: 24px;
        z-index: 1000;

        &:hover {
          color: $color-primary;
        }
      }
    }

    .el-dialog__footer {
      padding: 10px 20px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

/* 状态标签特殊样式 */
.status-tag {
  font-weight: bold;
}

.event-option {
  display: flex;
  align-items: center;
  gap: 10px;
  
  .event-description {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .event-time {
    color: var(--el-text-color-secondary);
    font-size: 12px;
  }
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .file-item {
    padding: 10px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    background-color: var(--el-bg-color);
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

.attachment-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

.attachment-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  padding: 16px;
  
  .attachment-item {
    position: relative;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    padding: 8px;
    text-align: center;
    
    .attachment-image {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: 4px;
    }
    
    .file-name {
      margin-top: 8px;
      font-size: 12px;
      color: var(--el-text-color-regular);
      word-break: break-all;
    }
    
    .file-document {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      
      .el-icon {
        font-size: 32px;
        color: var(--el-text-color-secondary);
        margin-bottom: 8px;
      }
    }
  }
}

.upload-file-item {
  position: relative;
  width: 100%;
  height: 100%;
  
  .upload-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .file-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 4px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-size: 12px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .file-document {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 8px;
    
    .el-icon {
      font-size: 24px;
      color: var(--el-text-color-secondary);
      margin-bottom: 4px;
    }
    
    .file-name {
      position: static;
      background: none;
      color: var(--el-text-color-regular);
    }
  }
  
  .delete-icon {
    position: absolute;
    top: 4px;
    right: 4px;
    padding: 4px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
    
    &:hover {
      background: var(--el-color-danger);
    }
  }
  
  &:hover .delete-icon {
    opacity: 1;
  }
}

.no-attachment {
  padding: 40px 0;
  text-align: center;
}

.file-link {
  margin-top: 8px;
  font-size: 14px;
  
  &:hover {
    color: var(--el-color-primary-light-3);
  }
}
</style>