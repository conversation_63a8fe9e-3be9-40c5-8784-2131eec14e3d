<template>
  <div>
    <div class="flex-column">
      <!-- <div class="flex-row" style="justify-content: space-between"  v-if="hideTitle?false:true">
        <div class="content-txt1">
          <img src="@/assets/images/sxj.png" alt="" />
          视频资源
        </div>
        <div @click="handToParentClose()">
          <img class="close" src="@/assets/images/guanbi.png" alt="" />
        </div>
      </div> -->
      <!-- <div class="inpBox">
        <img src="@/assets/images/search.png" alt="" />
        <input
          type="text"
          placeholder="请输入关键字过滤"
          class="inp"
        />
      </div> -->

      <el-input style="width: 80%; margin: 10px auto" placeholder="输入关键字进行过滤" v-model="filterText">
      </el-input>

      <el-tree class="filter-tree" highlight-current :data="data" :props="defaultProps" default-expand-all
        :filter-node-method="filterNode" @node-click="handleNodeClick" ref="treeRef">
      </el-tree>

      <!-- <div v-infinite-scroll="load" class="vidList">
        <div
          class="videoItem"
          v-for="(item, index) in vidList"
          :key="index"
          @dblclick="getdeviceSerial(item)"
        >
          <div>{{ item.deviceName }}</div>
          <div @click.stop="getdeviceSerial(item)">
            <el-button type="text">查看</el-button>
          </div>
        </div>
      </div> -->
    </div>

    <el-dialog v-model="videoShow" :modal="false" @close="closeVideo" width="50%" top="15vh" append-to-body>
      <div class="video-pop">
        <!-- left:video -->
        <div>
          <video id="videoElement" class="centeredVideo" controls autoplay style="width: 100%; height: 100%">
            不支持播放
          </video>
          <!-- <video
          id="my-video"
          class="video-js vjs-default-skin"
          controls
          preload="auto"
          style="width: 100%;height: 100%;"
        >
          <source
            :src="videoUrl"
            type="rtmp/flv"
          />
        </video> -->
          <!-- <videoPlayer
          class="vjs-custom-skin videoPlayer"
          id="jkvid"
          :options="palyerOptions"
        >
        </videoPlayer> -->
        </div>
        <!-- <div>
        <el-button type="text" @click="controlVid(0)">上</el-button>
        <el-button type="text" @click="controlVid(1)">下</el-button>
        <el-button type="text" @click="controlVid(2)">左</el-button>
        <el-button type="text" @click="controlVid(3)">右</el-button>
        <el-button type="text" @click="controlVid(4)">左上</el-button>
        <el-button type="text" @click="controlVid(5)">左下</el-button>
        <el-button type="text" @click="controlVid(6)">右上</el-button>
        <el-button type="text" @click="controlVid(7)">右下</el-button>
        <el-button type="text" @click="controlVid(8)">放大</el-button>
        <el-button type="text" @click="controlVid(9)">缩小</el-button>
        <el-button type="text" @click="controlVid(10)">近焦距</el-button>
        <el-button type="text" @click="controlVid(11)">远焦距</el-button>
        <el-button type="text" @click="controlVid(16)">自动控制</el-button>
        <el-button type="warning" plain @click="stopVideo">停止控制</el-button>
      </div> -->
        <!-- right:box -->
        <div class="right-box">
          <div class="right-top">
            <div class="speed-div">
              <span class="speed-span">速度:</span>
              <el-select size="small" v-model="value" class="speed-select" placeholder="">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div class="player-contorl">
              <div class="position-relative">
                <el-card class="box-card">
                  <ul class="pie">
                    <li class="slice-one slice" @mouseenter="changeActive($event)" @mouseleave="removeActive($event)">
                      <a href="#" @click="controlVid(0)"><img src="@/assets/images/icon4.png" alt="" /></a>
                    </li>
                    <li class="slice-two slice" @mouseenter="changeActive($event)" @mouseleave="removeActive($event)">
                      <a href="#" @click="controlVid(6)"><img src="@/assets/images/icon4.png" alt="" /></a>
                    </li>
                    <li class="slice-three slice" @mouseenter="changeActive($event)" @mouseleave="removeActive($event)">
                      <a href="#" @click="controlVid(3)"><img src="@/assets/images/icon4.png" alt="" /></a>
                    </li>
                    <li class="slice-four slice" @mouseenter="changeActive($event)" @mouseleave="removeActive($event)">
                      <a href="#" @click="controlVid(7)"><img src="@/assets/images/icon4.png" alt="" /></a>
                    </li>
                    <li class="slice-five slice" @mouseenter="changeActive($event)" @mouseleave="removeActive($event)">
                      <a href="#" @click="controlVid(1)"><img src="@/assets/images/icon4.png" alt="" /></a>
                    </li>
                    <li class="slice-six slice" @mouseenter="changeActive($event)" @mouseleave="removeActive($event)">
                      <a href="#" @click="controlVid(5)"><img src="@/assets/images/icon4.png" alt="" /></a>
                    </li>
                    <li class="slice-seven slice" @mouseenter="changeActive($event)" @mouseleave="removeActive($event)">
                      <a href="#" @click="controlVid(2)"><img src="@/assets/images/icon4.png" alt="" /></a>
                    </li>
                    <li class="slice-eight slice" @mouseenter="changeActive($event)" @mouseleave="removeActive($event)">
                      <a href="#" @click="controlVid(4)"><img src="@/assets/images/icon4.png" alt="" /></a>
                    </li>
                    <div class="center-fbox">
                      <div class="center"></div>
                    </div>
                  </ul>
                </el-card>
              </div>
            </div>
            <div class="command-div">
              <div class="lens-commands">
                <el-button class="ptz-cmd-btn" @click="controlVid(9)"><i class="el-icon-zoom-out"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(8)"><i class="el-icon-zoom-in"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(10)"><i class="el-icon-copy-document"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(11)"><i class="el-icon-copy-document"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(8)"><i class="el-icon-s-opportunity"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(9)"><i class="el-icon-odometer"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(10)"><i class="el-icon-copy-document"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(11)"><i class="el-icon-camera-solid"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(8)"><i class="el-icon-s-platform"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(9)"><i class="el-icon-zoom-in"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(10)"><i class="el-icon-copy-document"></i></el-button>
                <el-button class="ptz-cmd-btn" @click="controlVid(11)"><i class="el-icon-s-flag"></i></el-button>
              </div>
            </div>
          </div>
          <div class="player-list-title">播放列表</div>
          <div class="player-list-div">
            <div>
              <i class="el-icon-video-camera"></i>
              视频1
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
    
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue';
import type { Ref } from 'vue';
import flvjs from 'flv.js';
import {
  videoList,
  deviceList,
  deviceSerial,
  controlVideo,
  stopControlVideo,
} from "@/api/map/video.js";
import type { ElTree } from 'element-plus';
import { ElMessage } from 'element-plus';

interface VideoOption {
  value: string;
  label: string;
}

interface DeviceInfo {
  deviceSerial: string;
  deviceName: string;
  idx?: number;
  status?: number;
  channelNo?: number;
  flvAddress?: string;
  lon?: number;
  lat?: number;
  [key: string]: any;
}

interface TreeNodeData {
  id: number;
  deviceName: string;
  children: DeviceInfo[];
}

const props = defineProps({
  map: Object,
  hideTitle: Number
});

const emit = defineEmits(['closeList']);

const filterText = ref('');
const marker = ref(null);
const videoShow = ref(false);
const videoUrl = ref<string | null>(null);
const vidList = ref<DeviceInfo[]>([]);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
});
const player = ref<HTMLElement | null>(null);
const flvPlayer = ref<any>(null);
const total = ref(0);
const onVideo = ref<DeviceInfo>({} as DeviceInfo);
const command = ref<number | null>(null);
const value = ref('');
const treeRef = ref<InstanceType<typeof ElTree>>();

const options = reactive<VideoOption[]>([
  { value: "选项1", label: "x1" },
  { value: "选项2", label: "x2" },
  { value: "选项3", label: "x3" },
  { value: "选项4", label: "x4" },
  { value: "选项5", label: "x5" },
  { value: "选项6", label: "x6" },
  { value: "选项7", label: "x7" },
]);

const defaultProps = reactive({
  label: "deviceName",
});

const data = reactive<TreeNodeData[]>([
  { id: 1, deviceName: "探照灯", children: [] },
  { id: 2, deviceName: "云台", children: [] },
  { id: 3, deviceName: "鹰眼", children: [] },
]);

watch(() => filterText.value, (val) => {
  treeRef.value?.filter(val);
});

onMounted(() => {
  getDeviceListCz();
});

const changeActive = (e: MouseEvent) => {
  const target = e.currentTarget as HTMLElement;
  const prevClass = (e.target as any)._prevClass.trim().split(/\s+/)[0];
  target.className = prevClass + " " + "activeLi";
};

const removeActive = (e: MouseEvent) => {
  const target = e.currentTarget as HTMLElement;
  target.className = (e.target as any)._prevClass;
};

const handleNodeClick = (e: DeviceInfo) => {
  console.log(e);
  getdeviceSerialCz(e);
};

const filterNode = (value: string, data: DeviceInfo) => {
  if (!value) return true;
  return data.deviceName.indexOf(value) !== -1;
};

const handToParentClose = () => {
  if (marker.value) {
    props.map?.removeOverLay(marker.value);
  }
  emit("closeList", 9);
};

// 点击摄像头定位
const setCenter = (val: DeviceInfo) => {
  if (marker.value) {
    props.map?.removeOverLay(marker.value);
  }
  if (val.lon && val.lat && props.map) {
    const T = window.T;
    props.map.centerAndZoom(new T.LngLat(val.lon, val.lat));
    const icon = new T.Icon({
      iconUrl: "@/static/jingzhun.png",
      iconSize: new T.Point(25, 25),
      iconAnchor: new T.Point(12, 12),
    });
    //创建标注对象
    marker.value = new T.Marker(new T.LngLat(val.lon, val.lat), {
      icon: icon,
    });
    //向地图上添加标注
    props.map.addOverLay(marker.value);
  }
};

const getVideoVal = () => {
  videoShow.value = true;
  nextTick(() => {
    player.value = document.getElementById("videoElement");
    if (player.value && flvjs.isSupported()) {
      flvPlayer.value = flvjs.createPlayer({
        type: "flv",
        url: videoUrl.value as string,
      });
      flvPlayer.value.attachMediaElement(player.value);
      console.log(flvPlayer.value);
      flvPlayer.value.load();
      flvPlayer.value.play();
    }
  });
};

// 获取设备-册子
const getDeviceListCz = () => {
  // videoList(queryParams).then((res) => {
  //   if (res.code == 200) {
  //     if (queryParams.pageNum == 1) {
  //       data[2].children = res.data.data;
  //     } else {
  //       data[2].children.push(...res.data.data);
  //     }
  //     total.value = res.data.page.total;
  //   }
  // });
};

// 获取设备-金塘
const getDeviceListJt = () => {
  deviceList(queryParams).then((res) => {
    if (res.code == 200) {
      let arr = res.data.data;
      let obj = {} as DeviceInfo;
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].deviceName.indexOf("金塘") >= 0) {
          arr[i].idx = 1;
          data[2].children.push(arr[i]);
          obj = JSON.parse(JSON.stringify(arr[i]));
        }
      }
      obj.idx = 2;
      data[2].children.push(obj);
      total.value = res.data.page.total;
    }
  });
};

// 通过设备号获取视频
const getdeviceSerial = (val: DeviceInfo) => {
  onVideo.value = val;
  deviceSerial(val.deviceSerial, val.idx).then((res) => {
    if (res.code == 200) {
      videoUrl.value = res.data.data.url;
      getVideoVal();
    }
  });
};

const getdeviceSerialCz = (val: DeviceInfo) => {
  onVideo.value = val;
  videoUrl.value = val.flvAddress;
  getVideoVal();
};

// 加载下一页
const load = () => {
  if (vidList.value.length < total.value) {
    queryParams.pageNum += 1;
    // getDeviceList();
  }
};

// 关闭video弹窗
const closeVideo = () => {
  if (flvPlayer.value) {
    flvPlayer.value.pause();
    flvPlayer.value.unload();
    flvPlayer.value.detachMediaElement();
    flvPlayer.value.destroy();
    flvPlayer.value = null;
  }
};

// 控制云台
const controlVid = (val: number) => {
  if (!command.value) {
    let num = onVideo.value.idx ? onVideo.value.idx : 1;
    command.value = val;
    let obj = {
      deviceSerial: onVideo.value.deviceSerial,
      channelNo: num,
      direction: val,
      speed: 1,
    };
    controlVideo(obj).then(() => {
      setTimeout(() => {
        // 停止控制
        stopVideo();
      }, 500);
    });
  } else {
    // 使用Element Plus的消息组件
    ElMessage({
      message: `当前正在控制${onVideo.value.deviceName}，请先停止对其的控制`,
      type: "warning",
    });
  }
};

// 停止控制
const stopVideo = () => {
  let num = onVideo.value.idx ? onVideo.value.idx : 1;
  let obj = {
    deviceSerial: onVideo.value.deviceSerial,
    channelNo: num,
    direction: command.value,
  };
  stopControlVideo(obj).then(() => {
    command.value = null;
  });
};

// 声明全局类型
declare global {
  interface Window {
    T: any;
  }
}
</script>
    
<style scoped>
.box-card {
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 110px;

  /* height: 100%; */
  border: 1px solid transparent;
}

.pie {
  border: 2px solid #3876a9;
  position: relative;
  margin: 0;
  padding: 0;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  list-style: none;
  overflow: hidden;
  /* background: url("@/assets/images/buttonbg.png") no-repeat center center /
    100% 100%; */
}

.center-fbox {
  position: absolute;
  width: 50%;
  height: 50%;
  top: 25%;
  left: 25%;
  border-radius: 50%;
  background: #3c4167;
  display: flex;
  justify-content: center;
  align-items: center;
  /* background: url("@/assets/images/center.png") no-repeat center center /
    100% 100%; */
}

.center {
  width: 60%;
  height: 60%;
  border-radius: 50%;
  background: url("@/assets/images/center.png") no-repeat center center / 100% 100%;
  /* position: absolute;
  width: 50%;
  height: 50%;
  top:25%;
  left: 25%;
 

*/
  /*   */
}

.slice {
  overflow: hidden;
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 50%;
  transform-origin: 0% 100%;
  border: 1px solid #3c4167;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1b2c54;
}

.slice-one {
  transform: rotate(-22.5deg) skewY(-45deg);
}

.slice-one img {
  transform: skewY(45deg) rotate(22.5deg);
  margin-top: 70%;
  width: 30%;
  height: 30%;
}

.slice-two {
  transform: rotate(22.5deg) skewY(-45deg);
}

.slice-two img {
  transform: skewY(45deg) rotate(22.5deg);
  margin-top: 70%;
  width: 30%;
  height: 30%;
  /* margin-left: 30px; */
}

.slice-three {
  transform: rotate(67.5deg) skewY(-45deg);
}

.slice-three img {
  transform: skewY(45deg) rotate(22.5deg);
  margin-top: 70%;
  width: 30%;
  height: 30%;
  /* margin-left: 30px; */
}

.slice-four {
  transform: rotate(112.5deg) skewY(-45deg);
}

.slice-four img {
  transform: skewY(45deg) rotate(22.5deg);
  margin-top: 70%;
  width: 30%;
  height: 30%;
  /* margin-left: 30px; */
}

.slice-five {
  transform: rotate(157.5deg) skewY(-45deg);
}

.slice-five img {
  transform: skewY(45deg) rotate(22.5deg);
  margin-top: 70%;
  width: 30%;
  height: 30%;
  /* margin-left: 30px; */
}

.slice-six {
  transform: rotate(202.5deg) skewY(-45deg);
}

.slice-six img {
  transform: skewY(45deg) rotate(22.5deg);
  margin-top: 70%;
  width: 30%;
  height: 30%;
  /* margin-left: 30px; */
}

.slice-seven {
  transform: rotate(247.5deg) skewY(-45deg);
}

.slice-seven img {
  transform: skewY(45deg) rotate(22.5deg);
  margin-top: 70%;
  width: 30%;
  height: 30%;
  /* margin-left: 30px; */
}

.slice-eight {
  transform: rotate(292.5deg) skewY(-45deg);
}

.slice-eight img {
  transform: skewY(45deg) rotate(22.5deg);
  margin-top: 70%;
  width: 30%;
  height: 30%;
  /* margin-left: 30px; */
}

/* 分界线 */
.activeLi {
  background-color: #3c7fb4;
  overflow: hidden;
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 50%;
  transform-origin: 0% 100%;
  border: 1px solid #3c4167;
  display: flex;
  justify-content: center;
  align-items: center;
}

.player-list-div {
  overflow: auto;
  border-top: 1px solid rgba(128, 128, 128, 0.3);
  padding: 10px 5px;
  color: lightgreen;
}

.player-list-title {
  padding: 3px 8px;
  font-size: 15px;
  font-weight: 500;
  border-top: 1px solid rgba(128, 128, 128, 0.3);
  margin-top: 10px;
  color: #ffffff;
}

.ptz-cmd-btn {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  padding: 0;
  display: inline-block;
  margin: 2px;
  padding: 0;
  font-size: 14px;
  background: #4c5176;
  color: #ecf1ff;
  margin-left: 10px !important;
}

.lens-commands {
  /* height: 32px; */
}

.player-contorl {
  height: 100px;
  width: 100px;
  margin: 10px;
}

.position-relative {
  /* position: relative !important; */
}

.speed-span {
  line-height: 30px;
}

.speed-select {
  width: 60%;
  border-radius: 4px;
  background: rgba(43, 46, 73, 0.9);
  color: white;
  /* margin-left: 0px; */
}

.speed-div {
  padding: 5px;
  width: 85%;
  /* border: 1px solid pink; */
  display: flex;
  justify-content: space-between;
  color: #ffffff;
  font-weight: 500;
  font-size: 12px;
}

.video-pop {
  display: flex;
  /* width: 1000px;
  min-width: 1000px; */
  height: 600px;
  min-height: 600px;
}

.right-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 333;
}

.right-box {
  width: 150px;
  border: 1px solid rgba(128, 128, 128, 0.3);
  border-radius: 5px;
  margin-left: 5px;

  /* justify-content: center; */
}

.content {
  /* width: 350px; */
  height: 100%;
  /* background: rgba(0, 0, 0, 0.5); */
  /* position: fixed; */
  /* right: 0;
  top: 60px; */
  z-index: 1000;
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  width: 100%;
  height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 0 10px 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #cccccc;
}

.content-txt1 {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: rgb(203, 208, 246);
}

.content-txt1>img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.close {
  width: 15px;
  height: 15px;
}

.el-icon-circle-close {
  font-size: 22px;
}

.inpBox {
  position: relative;
}

.inpBox img {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 15px;
  height: 15px;
}

.inp {
  width: 100%;
  height: 30px;
  border: 1px solid rgb(94, 102, 160);
  padding: 2px 30px;
  outline: none;
  /*清除input点击之后的黑色边框*/
  background: #2d2f4c;
  color: #ffffff;
  font-size: 12px;
  border-radius: 4px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  color: #4386c6;
}

.custom-tree-node-label {
  display: block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.shipNum {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.vidList {
  height: 750px;
  overflow: auto;
}

.collItem:hover {
  cursor: pointer;
}

.collItem {
  color: #409eff;
  margin-bottom: 10px;
}

.videoItem {
  height: 30px;
  line-height: 30px;
  color: #ffffff;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.videoItem:hover {
  cursor: pointer;
  color: #409eff;
}

/* 
/deep/.el-dialog__body {
  height: 95% !important;
} */
</style>
<style >
/* .el-dialog {
  width: 54% !important;
} */
</style>
<style scoped src="@/assets/datePark.css"></style>
