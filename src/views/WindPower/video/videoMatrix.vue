<template>
  <el-dialog title="视频监控" v-model="isShow" width="80%" @close="closeVid">
    <div class="box">
      <div class="vidBox">
        <div class="vid" v-for="(item, index) in videoList" :key="index">
          <video
            :id="'videoElement' + index"
            class="centeredVideo"
            controls
            autoplay
            style="width: 100%; height: 100%"
          >
            不支持播放
          </video>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue';
import { getPlace, getDeviceSerial, deviceSerial } from "@/api/map/video.js";
import flvjs from 'flv.js';

interface VideoItem {
  deviceSerial: string;
  channelNo: number;
  status: number;
  [key: string]: any;
}

const emit = defineEmits(['closeVid']);
const props = defineProps({});

const videoList = ref<VideoItem[]>([]);
const flvPlayers = ref<any[]>([]);
const isShow = ref(true);

onMounted(() => {
  getVideo();
});

// 获取设备
const getVideo = () => {
  getPlace(1).then((res) => {
    if (res.data && res.data.length > 0) {
      for (let i = 0; i < res.data.length; i++) {
        let obj = JSON.parse(res.data[i].ownAttr);
        console.log(obj);
        getDeviceSerial(obj.deviceSerial)
          .then((r) => {
            if (r.data && r.data.length > 0) {
              let arr = r.data.filter((item: VideoItem) => {
                return item.status == 1;
              });

              videoList.value.push(...arr);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      }
      
      setTimeout(() => {
        for (let i = 0; i < videoList.value.length; i++) {
          deviceSerial(
            videoList.value[i].deviceSerial,
            videoList.value[i].channelNo,
            4
          ).then((r) => {
            nextTick(() => {
              console.log("---------------------------------");
              let vidId = "videoElement" + i;
              let player = document.getElementById(vidId);
              if (player && flvjs.isSupported()) {
                let flvPlayer = flvjs.createPlayer({
                  type: "flv",
                  url: r.data.data.url,
                });
                flvPlayer.attachMediaElement(player);

                flvPlayer.load();
                flvPlayer.play();
                flvPlayers.value.push(flvPlayer);
              }
            });
          });
        }
      }, 1000);
    }
  });
};

const closeVid = () => {
  console.log("准备关闭");
  flvPlayers.value.forEach((ele) => {
    ele.pause();
    ele.unload();
    ele.detachMediaElement();
    ele.destroy();
  });
  emit('closeVid');
};
</script>

<style scoped lang="less">
.box {
  width: 100%;
  height: 80vh;

  .vidBox {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    overflow: auto;
  }
  .vid {
    flex: 0 0 31%;
    // width: 30%;
    height: 48%;
    margin: 0 1%;
    background-color: #000000;
  }
}
</style>