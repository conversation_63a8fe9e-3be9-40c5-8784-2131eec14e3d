<template>
  <div>
    <div class="flex-column">
      <!-- <div
        class="flex-row"
        style="justify-content: space-between; padding: 5px 0"
      >
        <div class="content-txt1">
          <i class="el-icon-video-camera"></i
          ><span class="el-icon">视频资源</span>
        </div>
        <div @click="handToParentClose()">
          <i class="el-icon-close" style="color: #fefeff; opacity: 0.5"></i>
        </div>
      </div>

      <div class="line"></div> -->

      <div class="condition">
        <div class="condition_item">
          视频
          <div class="condition_item_inp">
            <input type="text" v-model="form.name" class="inp" />
          </div>
        </div>
        <div class="condition_item">
          开始
          <div class="condition_item_inp">
            <el-date-picker style="width: 100%" v-model="form.startTime" type="datetime" placeholder="选择日期时间"
              popper-class="elDatePicker">
            </el-date-picker>
          </div>
        </div>
        <div class="condition_item">
          结束
          <div class="condition_item_inp">
            <el-date-picker style="width: 100%" v-model="form.endTime" type="datetime" placeholder="选择日期时间"
              popper-class="elDatePicker">
            </el-date-picker>
          </div>
        </div>
        <div style="display: flex; align-items: center; justify-content: flex-end;width:90%;">
          <el-button type="primary" size="medium" :icon="Search"></el-button>
        </div>
      </div>
      <div class="line"></div>
      <div class="vidBox">
        <videoList :hideTitle="1" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import videoList from "@/views/WindPower/video/videoList";
import { Search } from '@element-plus/icons-vue';

interface FormState {
  name: string | null;
  startTime: Date | null;
  endTime: Date | null;
}

const emit = defineEmits(['closeList']);

const form = reactive<FormState>({
  name: null,
  startTime: null,
  endTime: null,
});

const handToParentClose = () => {
  emit("closeList", 10);
};
</script>

<style scoped lang="less">
.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.el-icon-circle-close {
  font-size: 22px;
}

.content-txt1 {
  font-size: 16px;
  color: #cbd0f6;
}

.line {
  width: 100%;
  height: 1px;
  margin: 1px auto;
  background: #d5d5d5;
}

.condition {
  margin: 10px 0 25px 0;
}

.condition_item {
  display: flex;
  align-items: center;
  padding-left: 10px;
  font-size: 14px;
  margin-bottom: 15px;
}

.condition_item_inp {
  width: 200px;
  height: 40px;
  margin-left: 10px;
}

.inp {
  width: 200px;
  height: 40px;
  border: 1px solid #DCDFE6 !important;
  padding: 0 15px;
  outline: none;
  /*清除input点击之后的黑色边框*/
  background: #2d2f4c;
  color: #ffffff;
  font-size: 16px;
  border-radius: 4px;
  // margin-left: 10px;
}

.vidBox {
  margin-top: 10px;
}

.elDateSeletc.el-select-dropdown {
  color: #fff;
  background: #3c4167;

  .el-select-dropdown__item {
    color: #fff;
    background: #3c4167;
  }

  .el-select-dropdown__item:hover {
    color: #1890ff;
    background: #3c4167;
  }

  .selected {
    color: #1890ff;
    background: #3c4167;
  }
}

.elDatePicker.el-picker-panel {
  color: #fff; //设置当前面板的月份的字体为白色，记为1
  background: #3c4167; //定义整体面板的颜色

  .el-picker-panel__sidebar {
    color: #fff;
    background: #3c4167;
  }

  .el-picker-panel__shortcut {
    color: #ffffff;
  }

  .el-picker-panel__shortcut:hover {
    color: #1890ff;
  }

  .el-picker-panel__icon-btn {
    //设置年份月份调节按钮颜色，记为2
    color: #ffffff;
  }

  .el-date-picker__header-label {
    //设置年月显示颜色，记为3
    color: #ffffff;
  }

  .el-date-table th {
    //设置星期颜色，记为4
    color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
    background: #3c4167;
  }

  .el-picker-panel__footer {
    color: #fff;
    background: #3c4167;
  }

  .el-button--text {
    color: #fff;
  }

  .el-button--mini.is-plain {
    color: #fff;
    background: #3c4167;
    border: 1px solid #fff;
  }

  .el-time-panel {
    background: #3c4167;
  }

  .el-time-spinner__item {
    color: #fff;
  }

  .el-time-spinner__item.active {
    color: #1890ff;
  }

  .el-time-spinner__item:hover {
    color: #1890ff;
    background: #3c4167;
  }

  .el-time-panel__btn.cancel {
    color: #fff;
  }

  .el-time-panel__btn.confirm {
    color: #fff;
    background: #3c4167;
    border: 1px solid #fff;
  }

  .available.in-range {
    color: #1890ff;
  }

  .el-year-table td .cell {
    color: #fff;
  }

  .el-year-table td.current:not(.disabled) .cell {
    color: #1890ff;
  }
}
</style>