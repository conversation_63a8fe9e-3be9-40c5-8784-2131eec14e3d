<template>
  <div class="popup-sidebar-container">
    <div class="popup-sidebar-content" :class="[isdrawer ? 'drawer' : 'open', isCollapsed ? 'collapsed' : '']">
      <div class="popup-sidebar-header">
        <span class="popup-sidebar-title">电子围栏</span>
        <div class="popup-sidebar-controls">
          <!-- <button class="control-btn" @click="toggleCollapse" :title="isCollapsed ? '展开面板' : '收起面板'">
            <el-icon><component :is="isCollapsed ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
          </button> -->
          <button class="control-btn" @click="changedrawer" :title="isdrawer ? '展开侧边栏' : '收起侧边栏'">
            <el-icon><component :is="isdrawer ? 'ArrowLeft' : 'ArrowRight'" /></el-icon>
          </button>
        </div>
      </div>
      <div v-if="!isCollapsed" class="popup-sidebar-body">
        <component
          :is="components.leftContent"
          :map="map"
          :list="searchList"
          @popup-right-val="handleVal"
          @renewData="renewData"
          @reloadShipData="handleReloadShipData"
          ref="plugPage">
        </component>
      </div>
       <div v-else class="loading-state">
         <!-- Collapsed state placeholder or minimal content -->
       </div>
    </div>
  </div>
</template>
    
<script setup lang="ts">
import { ref, reactive, watch, onMounted, defineProps, defineEmits, defineComponent } from 'vue';
// 导入 Element Plus 图标
import { ArrowLeft, ArrowRight, ArrowUp, ArrowDown } from '@element-plus/icons-vue';
// 直接导入组件
import shipList from "@/views/WindPower/monitor/shipList.vue";
import electronicFence from "@/views/WindPower/riskWarn/electronicFence.vue";
// import systemSetting from "@/views/WindPower/riskWarn/systemSetting.vue";
import regShip from "@/views/WindPower/regionShip.vue";
import regionVessel from "@/views/WindPower/riskWarn/regionVessel.vue";
import videoList from "@/views/WindPower/video/videoList.vue";
import flllow from "@/views/WindPower/monitor/follow.vue";
import realTime from "@/views/WindPower/monitor/realTime.vue";
import playback from "@/views/WindPower/video/playback.vue";
import leftContent from "@/views/WindPower/left-content.vue";
import warningRules from "@/views/WindPower/windowPages/warningRules.vue";
import plot from "@/views/WindPower/plot.vue";
import searchRes from "@/views/WindPower/searchRes.vue";
import fence from "@/views/WindPower/fence/index.vue";
import shipGrouping from "@/views/WindPower/shipGrouping.vue";

// 组件注册 - 只保留需要的 leftContent
const components = {
  leftContent
};

// interface FunItem {
//   name: string;
//   Popup: string;
//   icon: string;
// }

const props = defineProps({
  map: {
    type: Object,
    default: null
  },
  list: {
    type: Array,
    default: () => []
  },
  // Popup prop 可能不再需要，除非有其他用途
  // Popup: {
  //   type: String,
  //   default: ""
  // },
  envirShow: {
    type: Boolean,
    default: false
  },
  searchList: {
    type: Array,
    default: () => []
  },
  isCollapsed: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['closeCollection', 'isdrawer', 'renewData', 'isCollapsedChange', 'reloadShipData']);

// const activeName = ref("leftContent"); // 默认激活leftContent
// const funList = ref<FunItem[]>([]); // 不再需要
const drawer = ref(true); // 状态可能由父组件控制或内部逻辑决定
const isdrawer = ref(false); // 控制展开/收起状态
const isCollapsedInternal = ref(false); // 控制内容折叠/展开状态
// const tabPosition = ref("left"); // 不再需要
const currentSelectProps = ref(null); // 保留，可能leftContent会用到
const plugPage = ref(null); // 保留

// 不再需要 allTabs
// const allTabs = [
//   { name: "电子围栏", Popup: "leftContent", icon: "icon-quyuweilan" },
// ];

onMounted(() => {
  // 移除标签页初始化逻辑
  // initAllTabs();
  
  // 如果有指定的Popup，则激活该标签 - 逻辑可能需要调整，因为只有一个组件
  // if (props.Popup) {
  //   activateTab(props.Popup);
  // } else {
     // 默认激活 leftContent
     // activeName.value = "leftContent";
     // 通知父组件
     emit("renewData", "leftContent"); 
  // }

  // 初始化内部折叠状态
  isCollapsedInternal.value = props.isCollapsed;
});

// 移除标签页相关方法: initAllTabs, activateTab, closeList, handleClick
// const initAllTabs = () => { ... };
// const activateTab = (val: string) => { ... };
// const closeList = (index: number) => { ... };
// const handleClick = (tab: any) => { ... };

const handleVal = (val: string) => {
  // 这个逻辑可能需要调整，取决于 leftContent 内部触发 popup-right-val 的意图
  // 也许是切换到其他侧边栏功能？目前先保留
  // if (val) {
  //   activateTab(val); 
  // }
  console.log("handleVal received:", val); 
};

// Change drawer state
const changedrawer = () => {
  isdrawer.value = !isdrawer.value;
  emit("isdrawer", isdrawer.value);
  // 当收起侧边栏时，通知父组件关闭整个侧边栏
  if (isdrawer.value) {
    emit("closeCollection");
  }
};

// 折叠/展开面板
const toggleCollapse = () => {
  isCollapsedInternal.value = !isCollapsedInternal.value;
  // 不需要立即通知父组件，通过watch监听内部状态变化通知
};

// Pass through renewData event
const renewData = () => {
  // 传递 "leftContent" 作为参数，因为现在只有一个功能
  emit("renewData", "leftContent");
};

// Handle reload ship data event
const handleReloadShipData = () => {
  // 转发船舶数据重新加载事件到父组件
  emit("reloadShipData");
};

// Watch for changes in the Popup prop - 可能不再需要
// watch(() => props.Popup, (val) => {
//   if (val) {
//     activateTab(val);
//   }
// }, { immediate: true });

// 监听外部传入的isCollapsed状态
watch(() => props.isCollapsed, (val) => {
  isCollapsedInternal.value = val;
}, { immediate: true });

// 监听内部isCollapsed状态变化并通知父组件
watch(() => isCollapsedInternal.value, (val) => {
  // 只有在内部状态和外部prop不同时才触发事件，避免无限循环
  if (props.isCollapsed !== val) {
    emit('isCollapsedChange', val);
  }
});
</script>

<style scoped lang="less">
// 通用侧边栏样式
.popup-sidebar-container {
  position: fixed;
  top: 60px;
  right: 0;
  bottom: 0;
  height: calc(100vh - 60px);
  z-index: 1010;
  pointer-events: auto;
  background-color: transparent;
  display: flex;
  justify-content: flex-end;
}

.popup-sidebar-content {
  height: 100%; 
  margin:25px 0 -25px 0;
  background-color: #ffffff; // 内容区域白色背景
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1); // 左侧阴影
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease, max-height 0.3s ease; // 平滑过渡宽度和高度
  overflow: hidden; // 隐藏溢出内容
  position: relative;
  pointer-events: auto; 
  border-left: 1px solid #e4e7ed; // 左边框

  &.open {
    width: 360px; // 展开宽度
  }

  &.drawer {
    width: 0; // 收起时宽度为0，实现隐藏效果
    border-left: none; // 收起时无边框
  }

  &.collapsed {
    max-height: 48px; // 折叠时的高度，仅显示头部
    overflow: hidden;
    width: 360px; // 折叠时保持宽度以便显示头部
     &.drawer { // 如果同时是 drawer 和 collapsed，宽度优先为0
       width: 0;
     }
  }
}

.popup-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  height: 48px; // 固定头部高度
  border-bottom: 1px solid #e4e7ed; // 底部边框
  flex-shrink: 0; // 防止头部被压缩
  background-color: #f5f7fa; // 浅灰色背景，符合Element Plus风格
}

.popup-sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.popup-sidebar-controls {
  display: flex;
  align-items: center;
  gap: 8px; // 控制按钮间距
}

.popup-sidebar-body {
  flex-grow: 1; // 占据剩余空间
  overflow-y: auto; // 内容溢出时显示滚动条
  padding: 15px; // 内容区域内边距
}

// 控制按钮样式
.control-btn {
  width: 28px;
  height: 28px; // 调整按钮大小
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa; // 按钮背景色
  border: 1px solid #dcdfe6; // 按钮边框
  border-radius: 4px; // 圆角
  cursor: pointer;
  color: #000000; // 图标颜色
  transition: all 0.2s ease; // 平滑过渡效果

  &:hover {
    color: var(--el-color-primary, #409EFF); // 悬停时高亮
    background-color: #ecf5ff;
    border-color: #c6e2ff;
  }
  
  .el-icon {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.loading-state {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

/* 添加自定义弹窗样式 */
.popup-sidebar-container :deep(.el-dialog) {
  border-radius: 12px;
  background: var(--el-bg-color-overlay, white);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .el-dialog__header {
    background: var(--el-bg-color-page, #f5f7fa);
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);

    .el-dialog__title {
      color: var(--el-text-color-primary, #303133);
      font-weight: 600;
      font-size: 16px;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: var(--el-text-color-secondary, #909399);
        font-size: 18px;

        &:hover {
          color: var(--el-color-primary, #409EFF);
        }
      }
    }
  }

  .el-dialog__body {
    padding: 20px;
    background: var(--el-bg-color-overlay, white);
  }

  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-light, #e4e7ed);
    padding: 15px 20px;
  }
}

/* 添加按钮样式 */
.popup-sidebar-container :deep(.el-button) {
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(1px);
  }

  &.el-button--primary {
    &:hover {
      background: var(--el-color-primary-light-3);
      border-color: var(--el-color-primary-light-3);
    }
  }

  &.el-button--success {
    &:hover {
      background: #4cd69b;
      border-color: #4cd69b;
    }
  }

  &.el-button--danger {
    &:hover {
      background: var(--el-color-danger-light-3);
      border-color: var(--el-color-danger-light-3);
    }
  }
}

/* 统一图标按钮样式 */
.popup-sidebar-container .icon-button {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s;
  color: var(--el-text-color-regular, #606266);

  &:hover {
    color: var(--el-color-primary, #409EFF);
    background-color: var(--el-fill-color-light, #f5f7fa);
  }
}

/* 全局弹窗样式，确保统一风格 */
.popup-sidebar-container :deep(.global-dialog) {
  background-color: white !important;
  border-radius: 12px !important;
  border: 1px solid var(--el-border-color-light, #e4e7ed) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
}

.popup-sidebar-container :deep(.global-dialog .el-dialog__header) {
  background-color: var(--el-bg-color-page, #f5f7fa) !important;
  padding: 15px 20px !important;
  margin-right: 0 !important;
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed) !important;
}

.popup-sidebar-container :deep(.global-dialog .el-dialog__title) {
  color: var(--el-text-color-primary, #303133) !important;
  font-weight: 600 !important;
}

.popup-sidebar-container :deep(.global-dialog .el-dialog__body) {
  color: var(--el-text-color-primary, #303133) !important;
  padding: 20px !important;
  background-color: white !important;
}

.popup-sidebar-container :deep(.global-dialog .el-dialog__footer) {
  padding: 10px 20px !important;
  border-top: 1px solid var(--el-border-color-light, #e4e7ed) !important;
  background-color: white !important;
}

.popup-sidebar-container :deep(.global-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: var(--el-text-color-secondary, #909399) !important;
}

.popup-sidebar-container :deep(.global-dialog .el-dialog__headerbtn:hover .el-dialog__close) {
  color: var(--el-color-primary, #409EFF) !important;
}

/* 统一表格样式 */
.popup-sidebar-container :deep(.el-table) {
  --el-table-border-color: var(--el-border-color-light, #e4e7ed);
  --el-table-header-bg-color: var(--el-bg-color-page, #f5f7fa);
  --el-table-row-hover-bg-color: var(--el-fill-color-light, #f5f7fa);
  border-radius: 8px;
  overflow: hidden;
}

.popup-sidebar-container :deep(.el-table th.el-table__cell) {
  background-color: var(--el-bg-color-page, #f5f7fa) !important;
  color: var(--el-text-color-primary, #303133);
  font-weight: 600;
}

.popup-sidebar-container :deep(.el-table .el-table__row) {
  background-color: white;
}

/* 统一表单样式 */
.popup-sidebar-container :deep(.el-form-item__label) {
  color: var(--el-text-color-regular, #606266);
  font-weight: 500;
}

.popup-sidebar-container :deep(.el-input__inner) {
  background-color: var(--el-bg-color-overlay, white);
  border-color: var(--el-border-color, #dcdfe6);
  color: var(--el-text-color-primary, #303133);
}

/* 卡片统一样式 */
.popup-sidebar-container .card-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  padding: 16px;
  margin-bottom: 16px;
}

.popup-sidebar-container .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);
  margin-bottom: 16px;
}

.popup-sidebar-container .card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary, #303133);
}

.popup-sidebar-container .card-content {
  color: var(--el-text-color-regular, #606266);
}
</style>

<style scoped src="@/assets/datePark.css"></style>