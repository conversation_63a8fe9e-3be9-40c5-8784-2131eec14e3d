<template>
    <el-dialog v-dialogDrag title=" 预警发送记录" v-model="isDialog" width="1400px" @close="closeHisor"
        custom-class="customDialog" :modal="false">
        <el-table :data="list" height="500" :row-class-name="tableRowClassName">
            <el-table-column prop="mmsi" label="mmsi" min-width="150"></el-table-column>
            <el-table-column prop="aisContent" label="ais报文内容" min-width="250"></el-table-column>
            <el-table-column prop="vhfContent" label="VHF喊话内容" min-width="250"></el-table-column>
            <el-table-column prop="gmtCreate" label="时间" min-width="150"></el-table-column>
            <el-table-column prop="" label="状态" min-width="150">
                <template #default="scope">
                    <div v-if="scope.row.id">发送成功</div>
                </template>
            </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: flex-end; margin-top: 15px">
            <el-pagination :page-sizes="[10, 20, 30]" :page-size="100" layout="total, sizes, prev, pager, next, jumper"
                :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="queryParams.pageNum">
            </el-pagination>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { getDisposeLog } from "@/api/wan/index"

// 定义接口
interface QueryParams {
    pageNum: number
    pageSize: number
    isAsc: string
    orderByColumn: string
}

interface DisposeLogItem {
    id?: string | number
    mmsi?: string
    aisContent?: string
    vhfContent?: string
    gmtCreate?: string
    [key: string]: any
}

// 定义 props
const props = defineProps<{
    isHistor: boolean
}>()

// 定义需要触发的事件
const emit = defineEmits<{
    (e: 'closeTabData'): void
    (e: 'getIsDialog', data: { row: any, type: string }): void
}>()

// 响应式数据
const isdetails = ref(false)
const list = ref<DisposeLogItem[]>([])
const tableData = ref<any[]>([])
const tableDataItem = ref<any[]>([])
const total = ref(0)
const onInfo = ref({})
const activeName = ref('')
const isDialog = ref(false)
const qDate = ref(null)
const typeList = ref<any[]>([])

const queryParams = reactive<QueryParams>({
    pageNum: 1,
    pageSize: 10,
    isAsc: "desc",
    orderByColumn: "id",
})

// 监听 props 变化
watch(() => props.isHistor, (newVal) => {
    isDialog.value = newVal
    if (newVal) {
        getList()
    }
})

// 生命周期钩子
onMounted(() => {
    getList()
})

// 关闭历史记录弹窗
function closeHisor() {
    emit("closeTabData")
}

// 获取弹窗数据
function getIsDialog(row: any, type: string) {
    emit("getIsDialog", { 'row': row, 'type': type })
}

// 处理分页大小变化
function handleSizeChange(val: number) {
    queryParams.pageSize = val
    list.value = []
    getList()
}

// 处理页码变化
function handleCurrentChange(val: number) {
    queryParams.pageNum = val
    list.value = []
    getList()
}

// 获取列表数据
function getList() {
    getDisposeLog(queryParams).then((res: any) => {
        if (res.code == 200) {
            list.value = res.rows
            total.value = res.total
        }
    })
}

// 取消
function cancel() {
    emit("closeTabData")
}

// 表格行样式
function tableRowClassName({ row, rowIndex }: { row: any, rowIndex: number }) {
    if (rowIndex % 2 == 0) {
        return "warning-row"
    }
    return ''
}
</script>

<style scoped>
:deep(.el-dialog.customDialog) {
    margin-top: 15vh !important;
    background: #3c4167 !important;
}


/* 表格 */
:deep(.el-table::before) {
    height: 0;
}

:deep(.el-table__fixed-right::before) {
    height: 0;
}

:deep(.el-table th.el-table__cell.is-leaf) {
    border: none;
}

:deep(.el-table td.el-table__cell) {
    border: none;
}

:deep(.el-table::before) {
    height: 0;
}

:deep(.el-table__fixed::before) {
    height: 0;
}

:deep(.el-table__fixed-right::before) {
    height: 0;
}

:deep(.el-form-item__label) {
    color: #ffffff;
    margin-right: 10px;
}

:deep(.el-table__body-wrapper) {
    background: #3c4167;
}

:deep(.el-table__empty-text) {
    color: #ffffff;
}

:deep(.el-table .el-table__body tr.hover-row>td) {
    background-color: rgba(7, 40, 79, 0.2) !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td) {
    background-color: rgba(7, 40, 79, 0.2) !important;
}

:deep(.pagination-container) {
    background: none !important;
}

:deep(.el-pager li.active) {
    background: rgba(7, 40, 79, 0.8) !important;
}

:deep(.el-table__fixed-right-patch) {
    width: 0 !important;
}
</style>

<style scoped lang="less">
:deep(.el-table .warning-row) {
    background: rgba(7, 40, 79);
}

.box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 20px;
    /* background-color: #ffffff; */
    color: #ffffff;
    z-index: 1000;
    width: 1500px;
    // background-color: rgba(31, 63, 130, 0.8);
    background-color: rgba(60, 65, 103, 0.95);
    box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
}

.box_title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    justify-content: space-between;
}

.box_title img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.box_title>img:hover {
    cursor: pointer;
}

.box_condition {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    color: #fff;
}

.box_condition>div {
    margin: 0 20px 10px 0;
}

.box_condition_btns {
    margin: 0;
    display: flex;
    align-items: center;
}

.box_condition_btn {
    width: 60px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    background-color: #6771b7;
    border-radius: 4px;
    font-size: 14px;
}

.box_condition_btn:hover {
    cursor: pointer;
}

.addBox_list_inp {
    width: 180px;
    height: 30px;
    margin-left: 20px;
    /* border: 1px solid #4376ec; */
    /* background-color: #000000; */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
}

.elWidth {
    width: 120px;
    margin-left: 10px;
}

input {
    border: none;
    outline: none;
    /* background: #000000; */
    /* color: #ffffff; */
    /* background: #000000;
  color: #ffffff; */
}


.detaBox {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    padding: 10px;
    z-index: 2000;
    /* background-color: #ffffff; */
    color: #ffffff;
    /* border: 1px solid #000000; */
    // background-color: rgba(31, 63, 130);
    background-color: rgba(60, 65, 103, 0.95);
    box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
    min-height: 800px;
}

.detaBox_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.detaBox_title img {
    width: 20px;
    height: 20px;
}

.detaBox_title>img:hover {
    cursor: pointer;
}

.detaBox_info {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.detaBox_info_item {
    flex: 0 0 33%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
}

/* .picker {
  height: 40px;
  background-color: #000000;
  color: #ffffff;
} */

/* /deep/.el-input__inner {
  background: #000000 !important;
  border: 1px solid #000000;
  color: #fff;
  height: 40px;
} */

.elDatePicker.el-picker-panel {
    color: #fff; //设置当前面板的月份的字体为白色，记为1
    background: #3c4167; //定义整体面板的颜色

    .el-picker-panel__sidebar {
        color: #fff;
        background: #3c4167;
    }

    .el-picker-panel__shortcut {
        color: #ffffff;
    }

    .el-picker-panel__shortcut:hover {
        color: #1890ff;
    }

    .el-picker-panel__icon-btn {
        //设置年份月份调节按钮颜色，记为2
        color: #ffffff;
    }

    .el-date-picker__header-label {
        //设置年月显示颜色，记为3
        color: #ffffff;
    }

    .el-date-table th {
        //设置星期颜色，记为4
        color: #ffffff;
    }

    .el-input__inner {
        color: #fff;
        background: #3c4167;
    }

    .el-picker-panel__footer {
        color: #fff;
        background: #3c4167;
    }

    .el-button--text {
        color: #fff;
    }

    .el-button--mini.is-plain {
        color: #fff;
        background: #3c4167;
        border: 1px solid #fff;
    }

    .el-time-panel {
        background: #3c4167;
    }

    .el-time-spinner__item {
        color: #fff;
    }

    .el-time-spinner__item.active {
        color: #1890ff;
    }

    .el-time-spinner__item:hover {
        color: #1890ff;
        background: #3c4167;
    }

    .el-time-panel__btn.cancel {
        color: #fff;
    }

    .el-time-panel__btn.confirm {
        color: #fff;
        background: #3c4167;
        border: 1px solid #fff;
    }

    .available.in-range {
        color: #1890ff;
    }

    .el-year-table td .cell {
        color: #fff;
    }

    .el-year-table td.current:not(.disabled) .cell {
        color: #1890ff;
    }
}

.elDateSeletc.el-select-dropdown {
    color: #fff; //设置当前面板的月份的字体为白色，记为1
    background: #3c4167; //定义整体面板的颜色

    .el-select-dropdown__item {
        color: #fff;
        background: #3c4167;
    }

    .el-select-dropdown__item:hover {
        color: #1890ff;
        background: #3c4167;
    }

    .selected {
        color: #1890ff;
        background: #3c4167;
    }
}
</style>


