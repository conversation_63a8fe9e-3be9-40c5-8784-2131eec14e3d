<template>
  <div class="">
    <div class="flex-column">
      <div class="flex-row" style="justify-content: space-between"  v-if="hideTitle?false:true">
        <div class="content-txt1">
          <img src="@/assets/images/sxj.png" alt="" />
          视频资源
        </div>
        <div @click="handToParentClose()">
          <img class="close" src="@/assets/images/guanbi.png" alt="" />
        </div>
      </div>
      <!-- <div class="inpBox">
        <img src="@/assets/images/search.png" alt="" />
        <input
          type="text"
          placeholder="请输入关键字过滤"
          class="inp"
        />
      </div> -->

      <el-input placeholder="输入关键字进行过滤" v-model="filterText">
      </el-input>

      <el-tree
        class="filter-tree"
        highlight-current
        :data="data"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        ref="tree"
      >
      </el-tree>

      <!-- <div v-infinite-scroll="load" class="vidList">
        <div
          class="videoItem"
          v-for="(item, index) in vidList"
          :key="index"
          @dblclick="getdeviceSerial(item)"
        >
          <div>{{ item.deviceName }}</div>
          <div @click.stop="getdeviceSerial(item)">
            <el-button type="text">查看</el-button>
          </div>
        </div>
      </div> -->
    </div>

    <el-dialog v-model="videoShow" :modal="false" @close="closeVideo">
      <div>
        <video
          id="videoElement"
          class="centeredVideo"
          controls
          autoplay
          style="width: 100%; height: 100%"
        >
          不支持播放
        </video>
        <!-- <video
          id="my-video"
          class="video-js vjs-default-skin"
          controls
          preload="auto"
          style="width: 100%;height: 100%;"
        >
          <source
            :src="videoUrl"
            type="rtmp/flv"
          />
        </video> -->
        <!-- <videoPlayer
          class="vjs-custom-skin videoPlayer"
          id="jkvid"
          :options="palyerOptions"
        >
        </videoPlayer> -->
      </div>
      <div>
        <el-button type="text" @click="controlVid(0)">上</el-button>
        <el-button type="text" @click="controlVid(1)">下</el-button>
        <el-button type="text" @click="controlVid(2)">左</el-button>
        <el-button type="text" @click="controlVid(3)">右</el-button>
        <el-button type="text" @click="controlVid(4)">左上</el-button>
        <el-button type="text" @click="controlVid(5)">左下</el-button>
        <el-button type="text" @click="controlVid(6)">右上</el-button>
        <el-button type="text" @click="controlVid(7)">右下</el-button>
        <el-button type="text" @click="controlVid(8)">放大</el-button>
        <el-button type="text" @click="controlVid(9)">缩小</el-button>
        <el-button type="text" @click="controlVid(10)">近焦距</el-button>
        <el-button type="text" @click="controlVid(11)">远焦距</el-button>
        <el-button type="text" @click="controlVid(16)">自动控制</el-button>
        <el-button type="warning" plain @click="stopVideo">停止控制</el-button>
      </div>
    </el-dialog>
  </div>
</template>
    
<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  videoList,
  deviceList,
  deviceSerial,
  controlVideo,
  stopControlVideo,
} from "@/api/map/video.ts"

// 定义接口
interface DeviceItem {
  deviceSerial: string
  deviceName: string
  id?: number
  children?: DeviceItem[]
  [key: string]: any
}

interface QueryParams {
  pageNum: number
  pageSize: number
}

interface ControlParams {
  deviceSerial: string
  channelNo: number
  direction: number
  speed?: number
}

// 定义 props
const props = defineProps<{
  map: any
  hideTitle?: boolean
}>()

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'closeList', val: number): void
}>()

// 响应式数据
const filterText = ref('')
const marker = ref<any>(null)
const videoShow = ref(false)
const videoUrl = ref<string | null>(null)
const vidList = ref<DeviceItem[]>([])
const tree = ref<any>(null)
const player = ref<HTMLVideoElement | null>(null)
const flvPlayer = ref<any>(null)
const total = ref(0)
const onVideo = ref<DeviceItem>({} as DeviceItem)
const command = ref<number | null>(null)

const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 20,
})

const defaultProps = {
  label: 'deviceName'
}

const data = reactive<DeviceItem[]>([
  {
    id: 1,
    deviceName: "探照灯",
    deviceSerial: '',
    children: [],
  },
  {
    id: 2,
    deviceName: "云台",
    deviceSerial: '',
    children: [],
  },
  {
    id: 3,
    deviceName: "鹰眼",
    deviceSerial: '',
    children: [],
  },
])

// 监听过滤文本变化
watch(filterText, (val) => {
  tree.value?.filter(val)
})

// 生命周期钩子
onMounted(() => {
  getDeviceList()
})

// 节点点击事件
function handleNodeClick(node: DeviceItem) {
  console.log(node)
  getdeviceSerial(node)
}

// 过滤节点方法
function filterNode(value: string, data: DeviceItem) {
  if (!value) return true
  return data.deviceName.indexOf(value) !== -1
}

// 关闭列表
function handToParentClose() {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }

  //关闭弹窗
  emit('closeList', 9)
}

// 点击摄像头定位
function setCenter(val: { lon: number, lat: number }) {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }
  props.map.centerAndZoom(new T.LngLat(val.lon, val.lat))
  
  let icon = new T.Icon({
    iconUrl: "@/static/jingzhun.png",
    iconSize: new T.Point(25, 25),
    iconAnchor: new T.Point(12, 12),
  })
  
  //创建标注对象
  marker.value = new T.Marker(new T.LngLat(val.lon, val.lat), {
    icon: icon,
  })
  
  //向地图上添加标注
  props.map.addOverLay(marker.value)
}

// 获取视频播放
function getVideoVal() {
  videoShow.value = true
  
  setTimeout(() => {
    player.value = document.getElementById('videoElement') as HTMLVideoElement
    if (flvjs.isSupported()) {
      flvPlayer.value = flvjs.createPlayer({
        type: "flv",
        url: videoUrl.value as string,
      })
      flvPlayer.value.attachMediaElement(player.value)
      console.log(flvPlayer.value)
      flvPlayer.value.load()
      flvPlayer.value.play()
    }
  }, 0)
}

// 获取设备列表
function getDeviceList() {
  deviceList(queryParams).then((res: any) => {
    if (res.code == 200) {
      if (queryParams.pageNum == 1) {
        data[2].children = res.data.data
      } else {
        data[2].children?.push(...res.data.data)
      }
      total.value = res.data.page.total
    }
  })
}

// 通过设备号获取视频
function getdeviceSerial(val: DeviceItem) {
  onVideo.value = val
  deviceSerial(val.deviceSerial).then((res: any) => {
    if (res.code == 200) {
      videoUrl.value = res.data.data.url
      getVideoVal()
    }
  })
}

// 加载下一页
function load() {
  if (vidList.value.length < total.value) {
    queryParams.pageNum += 1
    getDeviceList()
  }
}

// 关闭video弹窗
function closeVideo() {
  flvPlayer.value.pause()
  flvPlayer.value.unload()
  flvPlayer.value.detachMediaElement()
  flvPlayer.value.destroy()
  flvPlayer.value = null
}

// 控制云台
function controlVid(val: number) {
  if (!command.value) {
    command.value = val
    const obj: ControlParams = {
      deviceSerial: onVideo.value.deviceSerial,
      channelNo: 1,
      direction: val,
      speed: 1,
    }
    controlVideo(obj).then(() => {})
  } else {
    ElMessage({
      message: `当前正在控制${onVideo.value.deviceName}，请先停止对其的控制`,
      type: "warning",
    })
  }
}

// 停止控制
function stopVideo() {
  const obj: ControlParams = {
    deviceSerial: onVideo.value.deviceSerial,
    channelNo: 1,
    direction: command.value as number,
  }
  stopControlVideo(obj).then(() => {
    command.value = null
  })
}
</script>
    
<style scoped>
.content {
  /* width: 350px; */
  height: 100%;
  /* background: rgba(0, 0, 0, 0.5); */
  /* position: fixed; */
  /* right: 0;
  top: 60px; */
  z-index: 1000;
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  width: 100%;
  height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 0 10px 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #cccccc;
}

.content-txt1 {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: rgb(203, 208, 246);
}
.content-txt1 > img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}
.close {
  width: 15px;
  height: 15px;
}
.el-icon-circle-close {
  font-size: 22px;
}
.inpBox {
  position: relative;
}
.inpBox img {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 15px;
  height: 15px;
}
.inp {
  width: 100%;
  height: 30px;
  border: 1px solid rgb(94, 102, 160);
  padding: 2px 30px;
  outline: none; /*清除input点击之后的黑色边框*/
  background: #2d2f4c;
  color: #ffffff;
  font-size: 12px;
  border-radius: 4px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  color: #4386c6;
}

.custom-tree-node-label {
  display: block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.shipNum {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.vidList {
  height: 750px;
  overflow: auto;
}
.collItem:hover {
  cursor: pointer;
}
.collItem {
  color: #409eff;
  margin-bottom: 10px;
}
.videoItem {
  height: 30px;
  line-height: 30px;
  color: #ffffff;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}
.videoItem:hover {
  cursor: pointer;
  color: #409eff;
}
</style>
<style scoped src="@/assets/datePark.css"></style>
