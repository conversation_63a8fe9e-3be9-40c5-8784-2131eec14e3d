<template>
  <div class="box">
    <el-dialog v-dialogDrag v-model="isDialog" top="15vh" width="1500px" @close="closeHisor" @opened="initOpen"
      custom-class="customDialog" :modal="false">
      <template #title>
        <div class="dialog-title">
          <div class="box_title">
            <div style="display: flex; align-items: center">
              <img style="margin-right: 10px" src="@/assets/images/shipIcon.png" alt="" />
              统计分析(鱼山)
            </div>
          </div>
        </div>
      </template>
      <div class="content">
        <div class="content_left">
          <div>
            <div class="content_left_top">
              <img src="@/assets/images/sailing.png" class="sailing" alt="" />
              <div>
                <div>辖区船舶总数</div>
                <div class="content_left_shipNum">38</div>
              </div>
            </div>
            <div class="content_left_region">
              <div>鱼山</div>
              <div style="color: rgb(5, 227, 221)">38</div>
            </div>
          </div>
          <div class="content_left_bottom">
            <div>船长(米)</div>
            <div class="content_left_bottom_condition">
              <div class="inpBox">
                <el-input v-model="inpValue" style="text-align: center;"></el-input>
              </div>
              <div class="inpBox_line">-</div>
              <div class="inpBox">
                <el-input v-model="inpValue" style="text-align: center;"></el-input>
              </div>
              <div class="content_left_bottom_btn">确定</div>
              <div class="content_left_bottom_btn">重置</div>
            </div>
            <div class="btn" @click="tabulation">详细列表</div>
          </div>
        </div>
        <div class="echartsBox">
          <div style="position: relative">
            <div id="echartsStatus"></div>
            <div class="echartsBox_info">
              <div>在航/锚泊/靠泊统计</div>
              <div>46艘</div>
            </div>
          </div>
          <div style="position: relative">
            <div id="echartsType"></div>
            <div class="echartsBox_info">
              <div>船舶类型</div>
              <div>46艘</div>
            </div>
          </div>
          <div style="position: relative">
            <div id="echartsBelong"></div>
            <div class="echartsBox_info">
              <div>国内外统计</div>
              <div>46艘</div>
            </div>
          </div>
          <div style="position: relative">
            <div id="echartsArea"></div>
            <div class="echartsBox_info flex">
              <div>数量趋势</div>
              <div style="display: flex;align-items: center;">
                <div class="flex_item" :class="cycle == 1 ? 'onflex' : ''" @click="chanCycle(1)">日</div>
                <div class="flex_item" :class="cycle == 2 ? 'onflex' : ''" @click="chanCycle(2)">周</div>
                <div class="flex_item" :class="cycle == 3 ? 'onflex' : ''" @click="chanCycle(3)">月</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="isTabulation">
        <areaList @close="closeAreaList" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import areaList from "@/views/WindPower/areaList.vue";
import * as echarts from 'echarts';

// 定义 props
const props = defineProps<{
  isStatistics: boolean
}>();

// 定义 emit 事件
const emit = defineEmits<{
  (e: 'closeStatis'): void
}>();

// 定义响应式数据
const type = ref<null | string>(null);
const qy = ref<null | string>(null);
const inpValue = ref<null | string>(null);
const cycle = ref(1);
const isTabulation = ref(false);
const isDialog = ref(false);

// 监听 props 变化
watch(() => props.isStatistics, (val) => {
  isDialog.value = val;
});

// 初始化图表
function initOpen() {
  console.log('---');
  nextTick(() => {
    setechartsType();
    setechartsBelong();
    setechartsStatus();
    setechartsArea();
  });
}

// 关闭统计弹窗
function closeHisor() {
  emit("closeStatis");
}

// 取消
function cancel() {
  emit("closeStatis");
}

// 切换周期
function chanCycle(index: number) {
  if (cycle.value != index) {
    cycle.value = index;
  }
}

// 详细列表
function tabulation() {
  isTabulation.value = true;
}

// 关闭区域列表
function closeAreaList() {
  isTabulation.value = false;
}

// 设置船舶类型图表
function setechartsType() {
  const chart = document.getElementById("echartsType") as HTMLElement;
  const myChart = echarts.init(chart, null, { renderer: 'svg' });
  const piename = ["拖船", "工程船", "其他", "普通货船", "客船", "液货船"];
  const pievale = [8, 3, 24, 11, 1, 2];
  const colors = ["#6699FF", "#66FFCC", "#FFCC33", "#66CCFF", "#9999FF"];
  let sum1 = 0;
  for (let j = 0; j < pievale.length; j++) {
    sum1 += pievale[j];
  }

  const piedata = [];
  for (let i = 0; i < piename.length; i++) {
    piedata.push({
      name: piename[i],
      value: pievale[i],
      icon: 'circle',
    });
  }
  const circularGraph = {
    head: true,
    tuglie: ["拖船", "工程船", "其他", "普通货船", "客船", "液货船"],
    data: piedata,
    sum: {
      name: ["总计"],
      number: sum1.toFixed(2),
    },
    color: ["#3aa0ff", "#36cbcb", "#4dcb73", "#975fe4"],
  };

  const option = {
    title: {
      left: "center",
      show: circularGraph.head, //是否显示标题组件
    },
    tooltip: {
      trigger: "item",
      padding: 3,
      formatter: "{b}:{c} ({d}%)",
    },
    legend: {
      //翻页按钮
      type: "scroll",
      pageIconColor: "#fff",
      pageTextStyle: {
        color: "#fff",
      },
      orient: "vertical",
      top: "20%",
      left: "1%",
      itemWidth: 15,
      itemHeight: 15,
      itemGap: 10,
      textStyle: {
        color: "#fff",
        fontSize: 12,
      },
      selectedMode: false,
      data: piedata,
    },
    series: [
      {
        type: "pie",
        radius: ["0", "40%"],
        center: ["60%", "40%"],
        startAngle: 50, //指引线的角度改变
        itemStyle: {
          normal: {
            label: {
              show: true,
              textStyle: {
                color: "#fff",
                fontSize: "14",
              },
              formatter: function (val: any) {
                //让series 中的文字进行换行
                return val.name + ":" + val.percent + "%";
              },
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 15,
              lineStyle: {
                color: "#fff",
              },
            },
          },
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
            textColor: "#fff",
          },
        },
        data: circularGraph.data, //数据
      },
    ],
    color: circularGraph.color, //颜
  };
  myChart.setOption(option);
}

// 设置船舶所属图表
function setechartsBelong() {
  const chart = document.getElementById("echartsBelong") as HTMLElement;
  const myChart = echarts.init(chart, null, { renderer: 'svg' });
  const piename = ["国内", "未知", "国外"];
  const pievale = [20, 22, 7];
  const colors = ["#6699FF", "#66FFCC", "#FFCC33", "#66CCFF", "#9999FF"];
  let sum1 = 0;
  for (let j = 0; j < pievale.length; j++) {
    sum1 += pievale[j];
  }

  const piedata = [];
  for (let i = 0; i < piename.length; i++) {
    piedata.push({
      name: piename[i],
      value: pievale[i],
      icon: 'circle',
    });
  }
  const circularGraph = {
    head: true,
    tuglie: ["国内", "未知", "国外"],
    data: piedata,
    sum: {
      name: ["总计"],
      number: sum1.toFixed(2),
    },
    color: ["#3aa0ff", "#36cbcb", "#4dcb73", "#975fe4"],
  };

  const option = {
    title: {
      left: "center",
      show: circularGraph.head, //是否显示标题组件
    },
    tooltip: {
      trigger: "item",
      padding: 3,
      formatter: "{b}:{c} ({d}%)",
    },
    legend: {
      //翻页按钮
      type: "scroll",
      pageIconColor: "#fff",
      pageTextStyle: {
        color: "#fff",
      },
      orient: "vertical",
      top: "20%",
      left: "1%",
      itemWidth: 15,
      itemHeight: 15,
      itemGap: 10,
      textStyle: {
        color: "#fff",
        fontSize: 12,
      },
      selectedMode: false,
      data: piedata,
    },
    series: [
      {
        type: "pie",
        radius: ["0", "40%"],
        center: ["60%", "40%"],
        startAngle: 50, //指引线的角度改变
        itemStyle: {
          normal: {
            label: {
              show: true,
              textStyle: {
                color: "#fff",
                fontSize: "14",
              },
              formatter: function (val: any) {
                //让series 中的文字进行换行
                return val.name + ":" + val.percent + "%";
              },
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 15,
              lineStyle: {
                color: "#fff",
              },
            },
          },
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
            textColor: "#fff",
          },
        },
        data: circularGraph.data, //数据
      },
    ],
    color: circularGraph.color, //颜
  };
  myChart.setOption(option);
}

// 设置船舶状态图表
function setechartsStatus() {
  const chart = document.getElementById("echartsStatus") as HTMLElement;
  const myChart = echarts.init(chart, null, { renderer: 'svg' });
  const piename = ["停航", "在航"];
  const pievale = [38, 11];
  const colors = ["#6699FF", "#66FFCC", "#FFCC33", "#66CCFF", "#9999FF"];
  let sum1 = 0;
  for (let j = 0; j < pievale.length; j++) {
    sum1 += pievale[j];
  }

  const piedata = [];
  for (let i = 0; i < piename.length; i++) {
    piedata.push({
      name: piename[i],
      value: pievale[i],
      icon: 'circle'
    });
  }
  const circularGraph = {
    head: true,
    tuglie: ["停航", "在航"],
    data: piedata,
    sum: {
      name: ["总计"],
      number: sum1.toFixed(2),
    },
    color: ["#3aa0ff", "#36cbcb", "#4dcb73", "#975fe4"],
  };

  const option = {
    title: {
      left: "center",
      show: circularGraph.head, //是否显示标题组件
    },
    tooltip: {
      trigger: "item",
      padding: 3,
      formatter: "{b}:{c} ({d}%)",
    },
    legend: {
      //翻页按钮
      type: "scroll",
      pageIconColor: "#fff",
      pageTextStyle: {
        color: "#fff",
      },
      orient: "vertical",
      top: "20%",
      left: "1%",
      itemWidth: 15,
      itemHeight: 15,
      itemGap: 10,
      textStyle: {
        color: "#fff",
        fontSize: 12,
      },
      selectedMode: false,
      data: piedata,
    },
    series: [
      {
        type: "pie",
        radius: ["0", "40%"],
        center: ["60%", "40%"],
        startAngle: 50, //指引线的角度改变
        itemStyle: {
          normal: {
            label: {
              show: true,
              textStyle: {
                color: "#fff",
                fontSize: "14",
              },
              formatter: function (val: any) {
                //让series 中的文字进行换行
                return val.name + ":" + val.percent + "%";
              },
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 15,
              lineStyle: {
                color: "#fff",
              },
            },
          },
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
            textColor: "#fff",
          },
        },
        data: circularGraph.data, //数据
      },
    ],
    color: circularGraph.color, //颜
  };
  myChart.setOption(option);
}

// 设置船舶数量趋势图表
function setechartsArea() {
  const chart = document.getElementById("echartsArea") as HTMLElement;
  const myChart = echarts.init(chart, null, { renderer: 'svg' });
  const xData = [
    "2023-02-16",
    "2023-02-17",
    "2023-02-18",
    "2023-02-19",
    "2023-02-20",
    "2023-02-21",
    "2023-02-22",
  ];
  const option = {
    grid: {
      left: "5%",
      right: "10%",
      top: "20%",
      bottom: "15%",
      containLabel: true,
    },
    tooltip: {
      show: true,
      trigger: "axis",
    },
    xAxis: [
      {
        type: "category",
        axisLine: {
          show: true,
          lineStyle: {
            color: "#85C1D9",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "#8BC4F2",
          margin: 6,
        },
        splitLine: {
          show: false,
        },
        boundaryGap: ["5%", "5%"],
        data: xData,
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          color: "#8BC4F2",
          margin: 6,
        },
        splitLine: {
          lineStyle: {
            color: "#355C84",
            type: "dashed",
          },
        },
      },
    ],
    series: [
      {
        name: "数量",
        type: "line",
        stack: "总量",
        symbolSize: 6,
        itemStyle: {
          color: "#55EFF1",
          borderColor: "#55EFF1",
          borderWidth: 2,
        },
        data: [0, 8, 11, 21, 12, 5, 12],
      },
    ],
  };
  myChart.setOption(option);
}
</script>

<style scoped>
:deep(.el-dialog.customDialog) {
  margin-top: 15vh !important;
  background: #3c4167 !important;
}

:deep(.el-dialog__body) {
  color: #ffffff;
}

.box {
  width: auto;
  color: #ffffff;
}

.box_title {
  padding: 10px 20px 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.box_title img {
  width: 25px;
  height: 20px;
}

.box_title>img:hover {
  cursor: pointer;
}

.content {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  height: 600px;
}

.content_left {
  width: 210px;
  border-right: 1px solid rgba(255, 255, 255, 0.15);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.sailing {
  width: 70px;
  height: 70px;
}

.content_left_top {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.content_left_shipNum {
  height: 36px;
  line-height: 36px;
  font-size: 28px;
  text-align: center;
  color: rgb(255, 179, 75);
}

.content_left_region {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid rgb(0, 173, 169);
  width: 185px;
}

.content_left_region:hover {
  cursor: pointer;
}

.content_left_region>div {
  height: 24px;
  line-height: 24px;
}

.inpBox {
  width: 40px;
}

.content_left_bottom {
  /* width: 100%; */
  padding-bottom: 30px;
}

.content_left_bottom_condition {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.inpBox_line {
  margin: 0 4px;
}

.content_left_bottom_btn {
  width: 40px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2a8a88;
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 10px;
}

.btn {
  width: 191px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2a8a88;
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 10px;
}

.btn:hover {
  cursor: pointer;
}

.echartsBox {
  width: 1088px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.echartsBox_info {
  position: absolute;
  left: 5%;
  top: 0;
}

.flex {
  width: 90%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex_item {
  margin: 0 8px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  color: deepskyblue;
}

.onflex {
  border: 1px solid deepskyblue;
}

.flex_item:hover {
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.2);
}

#echartsType,
#echartsBelong,
#echartsStatus,
#echartsArea {
  width: 544px;
  height: 302px;
}

.echartsTitle {
  position: absolute;
  top: 5%;
  left: 50%;
  transform: translate(-50%);
}
</style>