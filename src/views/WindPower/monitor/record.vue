<template>
  <div class="box">
    <div class="box_title">
      <div style="display: flex; align-items: center">
        <img
          style="margin-right: 10px"
          src="@/static/wenjian.png"
          alt=""
        />
        船舶流量监控
      </div>
      <img src="@/static/guanbi.png" @click="cancel" alt="" />
    </div>
    <div class="condition">
      <!-- <div class="condition_item">
        时间:
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :append-to-body="false"
        >
        </el-date-picker>
      </div> -->
      <div class="condition_item">
        时间:
        <el-select v-model="days" placeholder="请选择" @change="changeTime">
          <el-option
            key="7"
            label="近7天"
            :value="7"
          >
          </el-option>
          <el-option
            key="15"
            label="近15天"
            :value="15"
          >
          </el-option>
        </el-select>
      </div>
      <el-button type="primary" size="medium" :icon="Search">搜索</el-button>
    </div>
    <div style="display: flex; align-items: flex-start">
      <!-- <div style="width: 50%">
        <el-table :data="tableData" style="width: 100%" height="500" >
          <el-table-column prop="location" label="操作" width="50">
          </el-table-column>
          <el-table-column prop="name" label="船名" width="120">
          </el-table-column>
          <el-table-column prop="MMSI" label="MMSI" width="110">
          </el-table-column>
          <el-table-column prop="type" label="船舶类型" width="80">
          </el-table-column>
          <el-table-column prop="length" label="船长" width="80">
          </el-table-column>
          <el-table-column prop="weigth" label="总吨" width="80">
          </el-table-column>
          <el-table-column prop="direction" label="方向" width="80">
          </el-table-column>
          <el-table-column prop="time" label="时间" width="150">
          </el-table-column>
        </el-table>
      </div> -->
      <div style="width: 100%">
        <!-- <div style="display: flex; align-items: center">
          <div style="position: relative; width: 50%">
            <div id="echartsType"></div>
            <div class="echartsName">船舶分类统计</div>
          </div>
          <div style="position: relative; width: 50%">
            <div id="echartsLength"></div>
            <div class="echartsName">船长统计</div>
          </div>
        </div> -->
        <div style="width: 100%; position: relative; margin-top: 10px">
          <div id="echartsFlow"></div>
          <!-- <div class="echartsName">流量趋势</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { historyShip } from "@/api/map/map";
import * as echarts from 'echarts';

interface FormData {
  time: string;
  type: string;
  region: string;
  rule: string;
  keyword: string;
}

const emit = defineEmits(['closeRecord']);

const form = reactive<FormData>({
  time: "",
  type: "",
  region: "",
  rule: "",
  keyword: "",
});

const days = ref(7);
const tableData = ref<any[]>([]);

onMounted(() => {
  getData();
  // this.setechartsType();
  // this.setechartsLength();
});

const changeTime = () => {
  getData();
};

const getData = () => {
  historyShip(days.value).then((res) => {
    if (res.code == 200 && res.data) {
      let arr = [];
      for (let k in res.data) {
        let str = k.split(":")[1];
        let r = `${[str]}:${res.data[k]}`;
        arr.push(r);
      }

      for (var i = 0; i < arr.length; i++) {
        for (var j = 0; j < arr.length - i - 1; j++) {
          if (arr[j].split(":")[0] > arr[j + 1].split(":")[0]) {
            var swap = arr[j];
            arr[j] = arr[j + 1];
            arr[j + 1] = swap;
          }
        }
      }
      console.log(arr);
      let xData = [];
      let yData = [];
      for (let i = 0; i < arr.length; i++) {
        xData.push(arr[i].split(":")[0]);
        yData.push(arr[i].split(":")[1]);
      }
      setechartsFlow(xData, yData);
    }
  });
};

const setechartsFlow = (x: string[], y: string[]) => {
  let chart = document.getElementById("echartsFlow");
  if (!chart) return;
  
  let myChart = echarts.init(chart);

  let option = {
    //  backgroundColor: '#000',
    grid: {
      left: "5%",
      right: "5%",
      top: "20%",
      bottom: "15%",
      containLabel: true,
    },
    tooltip: {
      show: true,
      trigger: "axis",
    },
    xAxis: [
      {
        type: "category",
        axisLine: {
          show: true,
          lineStyle: {
            color: "#85C1D9",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "#8BC4F2",
          margin: 6,
        },
        splitLine: {
          show: false,
        },
        boundaryGap: ["5%", "5%"],
        data: x,
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          color: "#8BC4F2",
          margin: 6,
        },
        splitLine: {
          lineStyle: {
            color: "#355C84",
            type: "dashed",
          },
        },
      },
    ],
    series: [
      {
        name: "数量",
        type: "line",
        stack: "总量",
        symbolSize: 6,
        itemStyle: {
          color: "#55EFF1",
          borderColor: "#55EFF1",
          borderWidth: 2,
        },
        data: y,
      },
    ],
  };
  myChart.setOption(option);
};

const cancel = () => {
  emit("closeRecord");
};
</script>

<style scoped>
.box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(60, 65, 103, 0.9);
  padding: 20px;
  z-index: 1000;
  color: #ffffff;
  min-width: 1300px;
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
}
.box_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.box_title img {
  width: 20px;
  height: 20px;
}
.box_title > img:hover {
  cursor: pointer;
}
.condition {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}
.condition_item {
  margin-right: 20px;
}
.addBox_list_inp {
  width: 180px;
  height: 36px;
  margin-left: 20px;
  border: 1px solid #4376ec;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}
.elWidth {
  width: 180px;
  margin-left: 6px;
}
input {
  border: none;
  outline: none;

  background: #000000;
  color: #ffffff;
}
#echartsType {
  width: 100%;
  height: 250px;
}
#echartsLength {
  width: 100%;
  height: 250px;
}
#echartsFlow {
  width: 100%;
  height: 500px;
}
.echartsName {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
</style>