<template>
  <div style="flex:1;">
    <div class="flex-column">
      <!-- <div class="flex-row" style="justify-content: space-between">
        <div class="content-txt1">实时预警列表</div>
        <div @click="handToParentClose()">
          <i class="el-icon-circle-close" style="color: #409eff"></i>
        </div>
      </div> -->
      <!-- <div class="line"></div> -->
      <div class="operate" style="margin: 0 0 20px;">
        <div style="display: flex; align-items: center; justify-content: center">
          <div class="checkbox">
            <el-checkbox v-model="checked" label="全选"></el-checkbox>
          </div>
          <el-button type="primary" size="small" class="endBtn">结束</el-button>
          <el-button type="primary" size="small" class="endBtn" @click="showHistory">历史</el-button>
          <!-- <el-button type="primary" size="mini" class="endBtn">规则</el-button> -->
        </div>
        <!-- <div
          style="display: flex; align-items: center; justify-content: center"
        >
          <el-button
            type="primary"
            size="mini"
            class="endBtn"
            @click="changedetaWarn"
            >详细报警列表</el-button
          >
        </div> -->
      </div>
      <div style="flex: 1;" ref="scrollDiv">
        <el-scrollbar ref="scrollform" :style="{ height: heightCss }">
          <div class="list" v-for="(item, index) in list" :key="index">
            <div class="checkbox" style="width:40px;">
              <el-checkbox v-model="checked"></el-checkbox>
            </div>
            <div style="width:70%">
              <div class="list_title">{{ item.eventContent }}</div>
              <div class="list_text">
                <div>船名</div>
                <div>{{ item.shipName }}</div>
              </div>
              <div class="list_text">
                <div>mmsi</div>
                <div>{{ item.mmsi }}</div>
              </div>
              <div class="list_text">
                <div>时间</div>
                <div v-if="item.warningTime">{{
                  new Date(item.warningTime * 1000).toLocaleString()
                }}</div>
              </div>
              <div class="list_text">
                <div>地点</div>
                <div>{{ item.address }}</div>
              </div>
              <div style="
                display: flex;
                align-items: center;
                justify-content: flex-end;
                margin-top: 10px;
              ">
                <div class="icon" @click="getIsDialog(item)">
                  <el-tooltip class="item" effect="dark" content="预警处理" placement="bottom">
                    <i class="el-icon-setting"></i>
                  </el-tooltip>
                </div>
                <div class="icon" @click="getEndOrIgnore1(item)">
                  <el-tooltip class="item" effect="dark" content="结束" placement="bottom">
                    <i class="el-icon-mouse"></i>
                  </el-tooltip>
                </div>
                <div class="icon" @click="locate(item)">
                  <el-tooltip class="item" effect="dark" content="定位船舶" placement="bottom">
                    <i class="el-icon-thumb"></i>
                  </el-tooltip>
                </div>
                <div class="icon" @click="locate(item)">
                  <el-tooltip class="item" effect="dark" content="报警位置" placement="bottom">
                    <i class="el-icon-location-outline"></i>
                  </el-tooltip>
                </div>
                <div class="icon" @click="setCenter(item)">
                  <el-tooltip class="item" effect="dark" content="报警区域" placement="bottom">
                    <i class="el-icon-crop"></i>
                  </el-tooltip>
                </div>
                <div class="icon" @click="seePath(item)">
                  <el-tooltip class="item" effect="dark" content="航迹" placement="bottom">
                    <i class="el-icon-position"></i>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <!-- 历史预警弹窗 -->
    <historical ref="historical1" :isHistor="isHistor" @closeTabData="closeTabData" :map="map"
      @getIsDialog="getIsDialogItem">
    </historical>
    <div v-if="isdetaWarn">
      <detaWarn @closeDateWarn="closeDateWarn"></detaWarn>
    </div>
    <el-dialog v-dialogDrag :title="title" v-model="isDialog" width="500px" custom-class="customDialog"
      @close="handleClose" :modal="false">
      <div class="diaBox">
        <div class="shipInfo">
          <div class="shipInfo_item">预警船舶：{{ isDialogItem.shipName }}</div>
          <div class="shipInfo_item">MMSI：{{ isDialogItem.mmsi }}</div>
          <div class="shipInfo_item">预警时间：{{ new Date(isDialogItem.warningTime * 1000).toLocaleString() }}</div>
          <div class="shipInfo_item">预警规则：{{ isDialogItem.eventContent }}</div>
          <div class="shipInfo_item">预警区域：{{ isDialogItem.address }}</div>
        </div>
        <div class="warnType">
          <div style="display: flex; align-items: center">
            <div class="title">异常类型：</div>
            <el-select v-model="isDialogItem.excepitionType" placeholder="请选择" class="elwidth">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.value" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div style="display: flex; align-items: center">
            <div class="title">处置方式：</div>
            <el-select v-model="isDialogItem.handlingWay" placeholder="请选择" class="elwidth">
              <el-option v-for="item in wayOptions" :key="item.value" :label="item.value" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="describe">
          <div class="title">详细描述：</div>
          <div class="describeText">
            <textarea v-model="isDialogItem.detail" class="textArea"></textarea>
          </div>
        </div>
        <div class="measure">
          <div class="title">处置措施：</div>
          <el-input v-model="isDialogItem.treatmentMeasure" placeholder="请输入内容" class="elinp"></el-input>
        </div>
        <div class="warnType">
          <div style="display: flex; align-items: center">
            <div class="title">纠正情况：</div>
            <el-select v-model="isDialogItem.correctTheSituation" placeholder="请选择" class="elwidth">
              <el-option v-for="item in correctOptions" :key="item.value" :label="item.value" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div style="display: flex; align-items: center">
            <div class="title">是否处罚：</div>
            <el-select v-model="isDialogItem.punish" placeholder="请选择" class="elwidth">
              <el-option v-for="item in punishOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="measure">
          <div class="title">备注：</div>
          <el-input v-model="isDialogItem.remark" placeholder="请输入内容" class="elinp"></el-input>
        </div>
        <!-- <div class="annex">
          <div class="title">附件：</div>
          <div>
            <el-upload drag action="https://jsonplaceholder.typicode.com/posts/" multiple>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <div class="el-upload__text">
                附件不超过五个,单个附件大小不超过2M
              </div>
            </el-upload>
          </div>
        </div> -->
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import historical from "@/views/WindPower/monitor/historical.vue"
import detaWarn from "@/views/WindPower/windowPages/detaWarn.vue"
import { getEndOrIgnore } from '@/api/wan/realTime'
import {
  getdispose,
  getdisposeDetail,
  getdisposeDetailList,
} from "@/api/wan/realTime"

// 定义接口
interface WarningItem {
  id: string | number
  shipName?: string
  mmsi?: string | number
  warningTime?: number
  eventContent?: string
  address?: string
  lon?: number
  lat?: number
  [key: string]: any
}

interface DialogItem {
  shipName: string | null
  mmsi: string | number | null
  warningTime: number | null
  eventContent: string | null
  address: string | null
  id: string | number | null
  excepitionType: string | null
  handlingWay: string | null
  detail: string | null
  treatmentMeasure: string | null
  correctTheSituation: string | null
  punish: boolean | null
  remark: string | null
}

interface OptionItem {
  value: string | boolean
  label?: string
}

// 定义 props
const props = defineProps<{
  map: any
}>()

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'closeList', val: number): void
  (e: 'closeTabData'): void
}>()

// 使用Vue Router
const router = useRouter()

// 获取DOM元素引用
const scrollDiv = ref<HTMLElement | null>(null)
const scrollform = ref<any>(null)
const historical1 = ref<any>(null)

// 响应式数据
const checked = ref(false)
const isDialog = ref(false)
const isHistor = ref(false)
const isdetaWarn = ref(false)
const title = ref('')
const list = ref<WarningItem[]>([])
const timer = ref<number | null>(null)
const marker = ref<any>(null)
const heightCss = ref('800px')
const isDialogSHowType = ref(0)

const isDialogItem = reactive<DialogItem>({
  shipName: null,
  mmsi: null,
  warningTime: null,
  eventContent: null,
  address: null,
  id: null,
  excepitionType: null,
  handlingWay: null,
  detail: null,
  treatmentMeasure: null,
  correctTheSituation: null,
  punish: null,
  remark: null,
})

const form = reactive({
  type: null,
  way: null,
  describe: null,
  measure: null,
  correct: null,
  punish: null,
  remark: null,
})

// 选项数据
const typeOptions: OptionItem[] = [
  { value: "违法违规" },
  { value: "安全风险" },
  { value: "其他" },
]

const wayOptions: OptionItem[] = [
  { value: "远程处置" },
  { value: "现场联动处置" },
  { value: "移交其他部门" },
]

const correctOptions: OptionItem[] = [
  { value: "远程纠正" },
  { value: "现场纠正" },
  { value: "未纠正" },
]

const punishOptions: OptionItem[] = [
  { value: true, label: "处罚" },
  { value: false, label: "不处罚" },
]

// 生命周期钩子
onMounted(() => {
  // 设置container高度
  if (scrollDiv.value) {
    heightCss.value = window.getComputedStyle(scrollDiv.value).height
  }
  getData()
})

onBeforeUnmount(() => {
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
})

// 方法定义
// 历史预警处理
function getIsDialogItem(item: { type: number, row: WarningItem }) {
  console.log(item)
  isDialogSHowType.value = item.type
  if (item.type == 2) {
    Object.assign(isDialogItem, {
      shipName: item.row.shipName,
      mmsi: item.row.mmsi,
      warningTime: item.row.warningTime,
      eventContent: item.row.eventContent,
      address: item.row.address,
      id: item.row.id,
      excepitionType: null,
      handlingWay: null,
      detail: null,
      treatmentMeasure: null,
      correctTheSituation: null,
      punish: null,
      remark: null,
    })
    isDialog.value = true
  } else {
    getdisposeDetail(item.row.id).then((response: any) => {
      Object.assign(isDialogItem, {
        shipName: item.row.shipName,
        mmsi: item.row.mmsi,
        warningTime: item.row.warningTime,
        eventContent: item.row.eventContent,
        address: item.row.address,
        id: item.row.id,
        excepitionType: response.data.warningDispose.excepitionType,
        handlingWay: response.data.warningDispose.handlingWay,
        detail: response.data.warningDispose.detail,
        treatmentMeasure: response.data.warningDispose.treatmentMeasure,
        correctTheSituation: response.data.warningDispose.correctTheSituation,
        punish: response.data.warningDispose.punish,
        remark: response.data.warningDispose.remark,
      })
      isDialog.value = true
    })
  }
}

// 结束或忽略预警
function getEndOrIgnore1(item: WarningItem) {
  ElMessageBox.confirm('是否确认结束')
    .then(() => {
      return getEndOrIgnore(item.id)
    })
    .then((response: any) => {
      ElMessage.success('操作成功')
      getData()
    })
    .catch(() => {})
}

// 提交表单
function submitForm() {
  const data = {
    earlyId: isDialogItem.id,
    excepitionType: isDialogItem.excepitionType,
    handlingWay: isDialogItem.handlingWay,
    detail: isDialogItem.detail,
    treatmentMeasure: isDialogItem.treatmentMeasure,
    correctTheSituation: isDialogItem.correctTheSituation,
    punish: isDialogItem.punish,
    remark: isDialogItem.remark,
  }
  getdispose(data).then((response: any) => {
    handleClose()
    historical1.value.getList()
    getData()
    ElMessage.success('操作成功')
  })
}

// 预警处理
function getIsDialog(item: WarningItem) {
  console.log(item)
  Object.assign(isDialogItem, {
    shipName: item.shipName,
    mmsi: item.mmsi,
    warningTime: item.warningTime,
    eventContent: item.eventContent,
    address: item.address,
    id: item.id,
    excepitionType: null,
    handlingWay: null,
    detail: null,
    treatmentMeasure: null,
    correctTheSituation: null,
    punish: null,
    remark: null,
  })
  isDialog.value = true
}

// 查看航迹
function seePath(val: WarningItem) {
  const route = router.resolve({
    path: "/flightPath",
    query: {
      count: val.mmsi,
    },
  })

  window.open(route.href, "_blank")
}

// 设置地图中心
function setCenter(val: WarningItem) {
  props.map.panTo(new T.LngLat(val.lon, val.lat))
}

// 设置船舶中心
function setCenterShip(val: WarningItem) {
  props.map.panTo(new T.LngLat(val.lon, val.lat))
}

// 定位船舶
function locate(val: WarningItem) {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }
  props.map.panTo(new T.LngLat(val.lon, val.lat))
  
  // 创建图片对象
  const icon = new T.Icon({
    iconUrl: "http://api.tianditu.gov.cn/img/map/marker.png",
    iconSize: new T.Point(19, 27),
    iconAnchor: new T.Point(10, 25),
  })
  
  // 向地图上添加自定义标注
  marker.value = new T.Marker(new T.LngLat(val.lon, val.lat), {
    icon: icon,
  })
  props.map.addOverLay(marker.value)

  emit("closeTabData")
  setTimeout(() => {
    props.map.removeOverLay(marker.value)
  }, 10000)
}

// 获取预警数据
function getData() {
  const data = {
    pageNum: 1,
    pageSize: 10,
    isAsc: "desc",
    orderByColumn: "id",
    dispose: false,
    finish: false,
    ignore: false,
  }
  // getdisposeDetailList(data).then((response: any) => {
  //   list.value = response.rows
  // })
}

// 关闭预警弹窗
function handToParentClose() {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }

  // 关闭弹窗
  emit("closeList", 5)
}

// 处理预警
function handleWarn() {
  title.value = "dongfeng 1 在 xxxxxx 违反黄色预警"
  isDialog.value = true
}

// 关闭处理预警弹窗
function handleClose() {
  isDialog.value = false
  Object.assign(isDialogItem, {
    shipName: null,
    mmsi: null,
    warningTime: null,
    eventContent: null,
    address: null,
    id: null,
    excepitionType: null,
    handlingWay: null,
    detail: null,
    treatmentMeasure: null,
    correctTheSituation: null,
    punish: null,
    remark: null,
  })
}

// 历史记录
function showHistory() {
  isHistor.value = true
}

// 关闭历史记录
function closeTabData() {
  isHistor.value = false
}

// 改变详细警告
function changedetaWarn() {
  isdetaWarn.value = true
}

// 关闭详细警告
function closeDateWarn() {
  isdetaWarn.value = false
}
</script>

<style scoped>
.content {
  /* width: 350px; */
  height: 100%;
  /* background: rgba(0, 0, 0, 0.5); */
  position: fixed;
  right: 0;
  top: 60px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.flex-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
}

.content-txt1 {
  font-size: 16px;
  color: #409eff;
}

.el-icon-circle-close {
  font-size: 22px;
}

.line {
  width: 100%;
  height: 1px;
  margin: 10px auto;
  background: #d5d5d5;
}

.operate {
  /* display: flex;
  align-items: center; */
  margin: 20px 0;
}

.operate>div:last-child {
  margin-top: 10px;
  width: 100%;
}

.operate>div:last-child>.endBtn {
  width: 100px;
}

.checkbox {
  width: 60px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  color: #4386c6;
}

.custom-tree-node-label {
  display: block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.shipNum {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.collItem:hover {
  cursor: pointer;
}

.collItem {
  color: #409eff;
  margin-bottom: 10px;
}

.videoItem {
  height: 30px;
  line-height: 30px;
}

.videoItem:hover {
  cursor: pointer;
  color: #409eff;
}

.endBtn {
  margin-left: 10px;
}

.endBtn:hover {
  cursor: pointer;
}

.listBox {
  overflow-y: auto;
  height: 700px;
}

.list {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  font-size: 14px;
  margin-bottom: 20px;
  padding-left: 10px;
}

.list_title {
  padding-bottom: 8px;
  border-bottom: 2px solid #5cc9f2;
}

.list_text {
  display: flex;
  align-items: center;
  height: 28px;
}

.list_text>div:first-child {
  width: 60px;
}

.list i {
  margin-left: 10px;
}

.list i:hover {
  cursor: pointer;
}

.icon {
  font-size: 20px;
}

.shipInfo {
  display: flex;
  flex-wrap: wrap;
  color: #ffffff;
  margin-bottom: 10px;
}

.shipInfo_item {
  flex: 0 0 50%;
  margin-bottom: 20px;
}

.warnType {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #ffffff;
  margin-bottom: 20px;
}

.elwidth {
  width: 150px;
}

.elinp {
  width: 450px;
}

.describe {
  width: 100%;
  height: 180px;
  display: flex;
  color: #ffffff;
  margin-bottom: 20px;
}

.describeText {
  width: 450px;
  background-color: #253a5e;
}

.textArea {
  width: 100%;
  height: 180px;
  background-color: transparent;
  border: none;
  outline: none;
  color: #ffffff;
  line-height: 30px;
  padding: 10px 20px;
}

.measure {
  margin-bottom: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
}

.annex {
  display: flex;
  color: #ffffff;
}

.title {
  width: 80px;
}

.diaBox {
  height: 570px;
  overflow: auto;
}

::-webkit-scrollbar {
  width: 4px;
  height: 10px;
  background-color: #f5f5f5;
}

.list:last-child {
  margin-bottom: 100px;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #4376ec;
}

:deep(.el-dialog.customDialog) {
  margin-top: 15vh !important;
  background: #3c4167 !important;
}

/* //隐藏横向滚动条 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
  overflow-y: scroll;
}

:deep(.el-scrollbar__bar.is-horizontal) {
  height: 0 !important;
}
</style>

