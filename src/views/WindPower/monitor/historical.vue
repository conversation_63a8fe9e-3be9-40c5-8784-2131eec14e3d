<template>
  <div>
    <el-dialog v-dialogDrag title="报警查询" v-model="isDialog" top="15vh" width="1400px" @close="closeHisor"
      custom-class="customDialog" :modal="false">
      <!-- <div class="box_title">
        <div style="display: flex; align-items: center">
          <img src="@/static/wenjian.png" alt="" /> 报警查询
        </div>
        <img src="@/static/guanbi.png" alt="" @click="cancel" />
      </div> -->
      <div class="box_condition">
        <div>
          选择时间:
          <el-date-picker v-model="qDate" type="datetimerange" range-separator="-" start-placeholder="开始日期"
            :default-time="['00:00:00', '23:59:59']" end-placeholder="结束日期" popper-class="elDatePicker"
            value-format="yyyy/MM/dd HH:mm:ss" @change="chanTime">
          </el-date-picker>
        </div>
        <div>
          报警区域:
          <el-select v-model="queryParams.address" class="elWidth" placeholder="全部类型" popper-class="elDateSeletc"
            clearable @change="chanRegion">
            <el-option v-for="item in typeList" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </div>

        <div>
          报警规则:
          <el-select v-model="queryParams.eventContent" class="elWidth" placeholder="全部规则" popper-class="elDateSeletc"
            clearable @change="changeRule">
            <el-option :key="1" value="红色预警" label="红色预警"></el-option>
            <el-option :key="2" value="橙色预警" label="橙色预警"></el-option>
            <el-option :key="3" value="蓝色预警" label="蓝色预警"></el-option>
          </el-select>
        </div>
        <div>
          关键字:
          <el-input v-model="queryParams.shipName" placeholder="请输入船名或mmsi" class="elWidth" style="width:200px;"
            clearable></el-input>
        </div>
        <!-- <div>
          mmsi:
          <el-input v-model="queryParams.mmsi" placeholder="请输入mmsi" class="elWidth" clearable
            style="width: 150px;"></el-input>
        </div> -->
        <!-- <div style="display: flex; align-items: center">
        关键字:
        <el-input v-model="form.keyword" class="elWidth"></el-input>
      </div> -->
        <!-- <div>
        处置状态:
        <el-select
          v-model="form.rule"
          class="elWidth"
          placeholder="请选择"
          
          popper-class="elDateSeletc"
        >
          <el-option :key="1" :value="1" label="已确认"></el-option>
          <el-option :key="2" :value="2" label="已排除"></el-option>
          <el-option :key="3" :value="3" label="正常作业"></el-option>
          <el-option :key="4" :value="4" label="其他"></el-option>
        </el-select>
      </div> -->
        <div class="box_condition_btns">
          <!-- <div class="box_condition_btn" style="margin-right: 10px">查询</div>
        <div class="box_condition_btn">导出</div> -->
          <el-button type="primary" size="small" @click="searchList">查询</el-button>
          <!-- <el-button type="info" size="mini">导出</el-button> -->
        </div>
      </div>
      <div>
        <el-table :data="list" height="500" :row-class-name="tableRowClassName">
          <el-table-column prop="shipName" label="船名" width="200">
          </el-table-column>
          <el-table-column prop="mmsi" label="mmsi" width="140">
          </el-table-column>
          <el-table-column prop="address" label="预警区域" width="130">
          </el-table-column>
          <el-table-column prop="eventContent" label="预警规则" width="130">
          </el-table-column>
          <el-table-column prop="item.warningTime" label="预警时间" width="160">
            <template #default="scope">
              <div v-if="scope.row.warningTime">
                {{ new Date(scope.row.warningTime * 1000).toLocaleString() }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="disposer" label="处理人" width="130"></el-table-column>
          <el-table-column prop="dispose" label="处置状态" width="120">
            <template #default="scope">
              <div v-if="scope.row.dispose">已处置</div>
              <div v-if="!scope.row.dispose" style="color: #FF4949;">未处置</div>
            </template>
          </el-table-column>
          <el-table-column prop="disposetime" label="处置时间" width="160">
            <template #default="scope">
              <div>
                {{ scope.row.disposeTime }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="scope">
              <!-- <el-button type="text" size="small" @click="openDetails(scope.row)"
              >详情</el-button
            > -->
              <div style="width:100%;display:flex;flex-direction:row;align-items:center;">
                <div title="查看" v-if="scope.row.dispose" @click="getIsDialog(scope.row, 1)">
                  <i class="el-icon-view"
                    style="color:rgba(79,178,211);margin-right:20px;cursor: pointer;font-size:20px;"></i>
                </div>
                <div title="处置" v-if="!scope.row.dispose" @click="getIsDialog(scope.row, 2)">
                  <i class="el-icon-thumb"
                    style="color:rgba(79,178,211);margin-right:20px;cursor: pointer;font-size:20px;"></i>
                </div>
                <div title="定位" @click="locate(scope.row)">
                  <i class="el-icon-location-outline"
                    style="color:rgba(79,178,211);margin-right:20px;cursor: pointer;font-size:20px;"></i>
                </div>
                <div title="航迹" @click="trajectory(scope.row)">
                  <i class="el-icon-ship"
                    style="color:rgba(79,178,211);margin-right:20px;cursor: pointer;font-size:20px;"></i>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: flex-end; margin-top: 15px">
          <el-pagination :page-sizes="[10, 20, 30]" :page-size="100" layout="total, sizes, prev, pager, next, jumper"
            :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="queryParams.pageNum">
          </el-pagination>
        </div>
      </div>
      <div class="detaBox" v-if="isdetails">
        <div class="detaBox_title">
          <div style="display: flex; align-items: center">
            <img src="@/static/wenjian.png" style="margin-right: 10px" alt="" />
            详情
          </div>
          <img src="@/static/guanbihui.png" alt="" @click="isdetails = false" />
        </div>
        <div class="detaBox_info">
          <div class="detaBox_info_item">船舶：{{ onInfo.shipName }}</div>
          <div class="detaBox_info_item">MMSI：{{ onInfo.mmsi }}</div>
          <div class="detaBox_info_item">
            时间：{{ new Date(onInfo.warningTime).toLocaleString() }}
          </div>
          <div class="detaBox_info_item">报警区域：{{ onInfo.address }}</div>
          <div class="detaBox_info_item">处置状态：</div>
          <div class="detaBox_info_item">报警规则：{{ onInfo.eventContent }}</div>
        </div>
        <div style="margin-top: 20px; border-bottom: 1px solid #cccccc">
          <el-tabs v-model="activeName" tab-position="top" v-if="isdetails">
            <el-tab-pane name="first">
              <template #label>
                <div style="height: 100%; display: flex; justify-content: center">
                  处置信息
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane name="second">
              <template #label>
                <div style="height: 100%; display: flex; justify-content: center">
                  抓拍照片
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane name="third">
              <template #label>
                <div style="height: 100%; display: flex; justify-content: center">
                  抓拍视频
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div style="display: flex; justify-content: flex-end">
          <el-button type="primary" size="small" class="endBtn" @click="cancel">关闭</el-button>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, PropType } from 'vue'
import { useRouter } from 'vue-router'
import { warnList } from "@/api/map/earlyWarning"
import { warn, warningList } from "@/api/map/earlyWarning"
import { getEnvTypeList } from "@/api/wan/index"
import { Place } from "@/utils/request"

// 全局T对象
const T = window.T;

// 接口定义
interface TypeListItem {
  label: string
  value: string
}

interface QueryParams {
  pageNum: number
  pageSize: number
  isAsc: string
  orderByColumn: string
  place: string | null
  gmtCreate: string | null
  gmtModified: string | null
  eventContent: string | null
  address: string | null
  mmsi: string | null
  shipName: string | null
}

interface WarningItem {
  id?: string | number
  shipName?: string
  mmsi?: string | number
  warningTime?: number
  eventContent?: string
  address?: string
  dispose?: boolean
  disposeTime?: string
  disposer?: string
  lon?: number
  lat?: number
  [key: string]: any
}

// props定义
const props = defineProps({
  map: {
    type: Object,
    required: true
  },
  isHistor: {
    type: Boolean,
    default: false
  }
})

// emit定义
const emit = defineEmits<{
  (e: 'closeTabData'): void
  (e: 'getIsDialog', data: { row: any, type: number }): void
}>()

// 路由
const router = useRouter()

// 响应式数据
const isdetails = ref(false)
const list = ref<WarningItem[]>([])
const tableData = ref<any[]>([])
const tableDataItem = ref<any[]>([])
const total = ref(0)
const onInfo = ref<WarningItem>({})
const activeName = ref('')
const isDialog = ref(false)
const qDate = ref(null)
const typeList = ref<TypeListItem[]>([])
const marker = ref<any>(null)

const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  isAsc: "desc",
  orderByColumn: "id",
  place: null,
  gmtCreate: null,
  gmtModified: null,
  eventContent: null,
  address: null,
  mmsi: null,
  shipName: null,
})

// 监听 props 变化
watch(() => props.isHistor, (val) => {
  isDialog.value = val
  if (val) {
    getList()
  }
})

// 生命周期钩子
onMounted(() => {
  queryParams.place = Place
  getwarn()
  getList()
})

// 关闭历史记录弹窗
function closeHisor() {
  emit("closeTabData")
}

// 获取弹窗数据
function getIsDialog(row: WarningItem, type: number) {
  emit("getIsDialog", { 'row': row, 'type': type })
}

// 处理分页大小变化
function handleSizeChange(val: number) {
  queryParams.pageSize = val
  list.value = []
  getList()
}

// 处理页码变化
function handleCurrentChange(val: number) {
  queryParams.pageNum = val
  list.value = []
  getList()
}

// 时间选择器变化
function chanTime(e: any) {
  if (e) {
    queryParams.gmtCreate = e[0]
    queryParams.gmtModified = e[1]
  } else {
    queryParams.gmtCreate = null
    queryParams.gmtModified = null
  }
  queryParams.pageNum = 1
}

// 规则变化
function changeRule(e: string | null) {
  queryParams.eventContent = e
  queryParams.pageNum = 1
}

// 区域变化
function chanRegion(e: string | null) {
  queryParams.address = e
  queryParams.pageNum = 1
}

// 获取报警区域
function getwarn() {
  getEnvTypeList().then((res: any) => {
    if (res.code == 200) {
      let arr: TypeListItem[] = []
      if (res.data && Array.isArray(res.data)) {
        res.data.forEach((ele: any) => {
          let obj = {
            label: ele.name,
            value: ele.name,
          }
          arr.push(obj)
        })
      }
      typeList.value = arr
    }
  })
}

// 搜索列表
function searchList() {
  queryParams.pageNum = 1
  getList()
}

// 获取列表数据
function getList() {
  // warningList(queryParams).then((res: any) => {
  //   if (res.code == 200) {
  //     list.value = res.rows
  //     total.value = res.total
  //   }
  // })
}

// 取消
function cancel() {
  emit("closeTabData")
}

// 打开详情
function openDetails(val: WarningItem) {
  onInfo.value = val
  isdetails.value = true
}

// 表格行样式
function tableRowClassName({ row, rowIndex }: { row: WarningItem, rowIndex: number }) {
  if (rowIndex % 2 == 0) {
    return "warning-row"
  }
  return ''
}

// 定位
function locate(val: WarningItem) {
  props.map.panTo(new T.LngLat(val.lon, val.lat))
  //创建图片对象
  let icon = new T.Icon({
    iconUrl: "http://api.tianditu.gov.cn/img/map/marker.png",
    iconSize: new T.Point(19, 27),
    iconAnchor: new T.Point(10, 25),
  })
  //向地图上添加自定义标注
  marker.value = new T.Marker(new T.LngLat(val.lon, val.lat), {
    icon: icon,
  })
  props.map.addOverLay(marker.value)

  emit("closeTabData")
  setTimeout(() => {
    props.map.removeOverLay(marker.value)
  }, 10000)
}

// 航迹
function trajectory(val: WarningItem) {
  let routeUrl = router.resolve({
    path: "/flightPath",
    query: {
      count: val.mmsi,
    },
  })

  window.open(routeUrl.href, "_blank")
}
</script>

<style scoped>
:deep(.el-dialog.customDialog) {
  margin-top: 15vh !important;
  background: #3c4167 !important;
}


/* 表格 */
:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table__fixed-right::before) {
  height: 0;
}

:deep(.el-table th.el-table__cell.is-leaf) {
  border: none;
}

:deep(.el-table td.el-table__cell) {
  border: none;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table__fixed::before) {
  height: 0;
}

:deep(.el-table__fixed-right::before) {
  height: 0;
}

:deep(.el-form-item__label) {
  color: #ffffff;
  margin-right: 10px;
}

:deep(.el-table__body-wrapper) {
  background: #3c4167;
}

:deep(.el-table__empty-text) {
  color: #ffffff;
}

:deep(.el-table .el-table__body tr.hover-row>td) {
  background-color: rgba(7, 40, 79, 0.2) !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td) {
  background-color: rgba(7, 40, 79, 0.2) !important;
}

:deep(.pagination-container) {
  background: none !important;
}

:deep(.el-pager li.active) {
  background: rgba(7, 40, 79, 0.8) !important;
}

:deep(.el-table__fixed-right-patch) {
  width: 0 !important;
}
</style>

<style scoped lang="less">
:deep(.el-table .warning-row) {
  background: rgba(7, 40, 79);
}

.box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  /* background-color: #ffffff; */
  color: #ffffff;
  z-index: 1000;
  width: 1500px;
  // background-color: rgba(31, 63, 130, 0.8);
  background-color: rgba(60, 65, 103, 0.95);
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
}

.box_title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  justify-content: space-between;
}

.box_title img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.box_title>img:hover {
  cursor: pointer;
}

.box_condition {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  color: #fff;
}

.box_condition>div {
  margin: 0 20px 10px 0;
}

.box_condition_btns {
  margin: 0;
  display: flex;
  align-items: center;
}

.box_condition_btn {
  width: 60px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  background-color: #6771b7;
  border-radius: 4px;
  font-size: 14px;
}

.box_condition_btn:hover {
  cursor: pointer;
}

.addBox_list_inp {
  width: 180px;
  height: 30px;
  margin-left: 20px;
  /* border: 1px solid #4376ec; */
  /* background-color: #000000; */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.elWidth {
  width: 120px;
  margin-left: 10px;
}

input {
  border: none;
  outline: none;
  /* background: #000000; */
  /* color: #ffffff; */
  /* background: #000000;
  color: #ffffff; */
}


.detaBox {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  padding: 10px;
  z-index: 2000;
  /* background-color: #ffffff; */
  color: #ffffff;
  /* border: 1px solid #000000; */
  // background-color: rgba(31, 63, 130);
  background-color: rgba(60, 65, 103, 0.95);
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
  min-height: 800px;
}

.detaBox_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.detaBox_title img {
  width: 20px;
  height: 20px;
}

.detaBox_title>img:hover {
  cursor: pointer;
}

.detaBox_info {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.detaBox_info_item {
  flex: 0 0 33%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
}

/* .picker {
  height: 40px;
  background-color: #000000;
  color: #ffffff;
} */

/* /deep/.el-input__inner {
  background: #000000 !important;
  border: 1px solid #000000;
  color: #fff;
  height: 40px;
} */

.elDatePicker.el-picker-panel {
  color: #fff; //设置当前面板的月份的字体为白色，记为1
  background: #3c4167; //定义整体面板的颜色

  .el-picker-panel__sidebar {
    color: #fff;
    background: #3c4167;
  }

  .el-picker-panel__shortcut {
    color: #ffffff;
  }

  .el-picker-panel__shortcut:hover {
    color: #1890ff;
  }

  .el-picker-panel__icon-btn {
    //设置年份月份调节按钮颜色，记为2
    color: #ffffff;
  }

  .el-date-picker__header-label {
    //设置年月显示颜色，记为3
    color: #ffffff;
  }

  .el-date-table th {
    //设置星期颜色，记为4
    color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
    background: #3c4167;
  }

  .el-picker-panel__footer {
    color: #fff;
    background: #3c4167;
  }

  .el-button--text {
    color: #fff;
  }

  .el-button--mini.is-plain {
    color: #fff;
    background: #3c4167;
    border: 1px solid #fff;
  }

  .el-time-panel {
    background: #3c4167;
  }

  .el-time-spinner__item {
    color: #fff;
  }

  .el-time-spinner__item.active {
    color: #1890ff;
  }

  .el-time-spinner__item:hover {
    color: #1890ff;
    background: #3c4167;
  }

  .el-time-panel__btn.cancel {
    color: #fff;
  }

  .el-time-panel__btn.confirm {
    color: #fff;
    background: #3c4167;
    border: 1px solid #fff;
  }

  .available.in-range {
    color: #1890ff;
  }

  .el-year-table td .cell {
    color: #fff;
  }

  .el-year-table td.current:not(.disabled) .cell {
    color: #1890ff;
  }
}

.elDateSeletc.el-select-dropdown {
  color: #fff; //设置当前面板的月份的字体为白色，记为1
  background: #3c4167; //定义整体面板的颜色

  .el-select-dropdown__item {
    color: #fff;
    background: #3c4167;
  }

  .el-select-dropdown__item:hover {
    color: #1890ff;
    background: #3c4167;
  }

  .selected {
    color: #1890ff;
    background: #3c4167;
  }
}
</style>



