<template>
  <div class="content">
    <div class="box_title">
      <div style="display: flex; align-items: center;flex-shrink: 0;">
        <img src="@/static/wenjian.png" alt="" /> 
        <div style="flex-shrink: 0;">船舶档案</div>
        <el-input class="search" clearable v-model="queryParams.mmsi" placeholder="请输入mmsi"></el-input>
        <el-input class="search" clearable v-model="queryParams.shipNameEn" placeholder="请输入英文船名"></el-input>
        <el-button @click="search">搜索</el-button>
        <el-button type="primary" style="margin-left: 20px" @click="addShip">新增</el-button>
      </div>
      <img src="@/static/guanbi.png" alt="" @click="close" />
    </div>
    <div class="tableBox">
      <el-table :data="tableData" border style="width: 100%" height="500" v-loading="loading">
        <el-table-column prop="mmsi" label="mmsi" width="120">
        </el-table-column>
        <el-table-column prop="shipNameEn" label="英文船名" width="120">
        </el-table-column>
        <el-table-column fixed prop="localName" label="船名" width="150">
        </el-table-column>
        <el-table-column prop="shipTypeCode" label="船舶类型" width="120">
        </el-table-column>
        <el-table-column prop="flagCode" label="国籍" width="120">
        </el-table-column>
        <el-table-column prop="callsign" label="呼号" width="120">
        </el-table-column>
        <el-table-column prop="imo" label="imo" width="120"> </el-table-column>
        <el-table-column prop="draught" label="吃水" width="120">
        </el-table-column>
        <el-table-column prop="contactNo" label="联系方式" width="120">
        </el-table-column>
        <el-table-column prop="shipNo" label="船舶编号" width="120">
        </el-table-column>
        <el-table-column prop="shipyard" label="造船厂" width="300">
        </el-table-column>
        <el-table-column prop="loa" label="船长" width="120"> </el-table-column>
        <el-table-column prop="bm" label="船宽" width="120"> </el-table-column>
        <el-table-column prop="depth" label="型深" width="120">
        </el-table-column>
        <el-table-column prop="lbp" label="型宽" width="120"> </el-table-column>
        <el-table-column prop="gross" label="总吨" width="120">
        </el-table-column>
        <el-table-column prop="net" label="净吨" width="120"> </el-table-column>
        <el-table-column prop="minFreeboard" label="最小干舷" width="120">
        </el-table-column>
        <el-table-column prop="buildDate" label="建成日期" width="120">
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template #default="scope">
            <el-button type="text" size="small" @click="edit(scope.row)">修改</el-button>
            <el-button type="text" size="small" @click="del(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[20, 30, 50]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <el-dialog
      :title="title"
      v-model="isShow"
      width="100%"
      :modal="false"
    >
      <div>
        <el-form
          :inline="true"
          :model="form"
          class="demo-form-inline"
          label-width="100px"
          :rules="rules"
          ref="ruleForm"
        >
          <el-form-item>
            <template #label><span style="color: red;">*</span>mmsi</template>
            <div style="position: relative;">
              <el-input class="aaaa" type="number" max="9" v-model="form.mmsi" placeholder="请输入内容" @blur="verifyMmsi"></el-input>
              <div class="tips" v-if="tipsShow">mmsi已存在</div>
            </div>
            
          </el-form-item>
          <el-form-item>
            <template #label><span style="color: red;">*</span>英文船名</template>
            <el-input
              v-model="form.shipNameEn"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="船名">
            <el-input
              v-model="form.localName"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>

          
          <el-form-item label="船舶类型">
            <el-input
              v-model="form.shipTypeCode"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="国籍">
            <el-input
              v-model="form.flagCode"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="呼号">
            <el-input
              v-model="form.callsign"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="imo">
            <el-input v-model="form.imo" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="吃水">
            <el-input
              v-model="form.draught"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系方式">
            <el-input
              v-model="form.contactNo"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="船舶编号">
            <el-input v-model="form.shipNo" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="造船厂">
            <el-input
              v-model="form.shipyard"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="船长">
            <el-input v-model="form.loa" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="船宽">
            <el-input v-model="form.bm" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="型深">
            <el-input v-model="form.depth" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="型宽">
            <el-input v-model="form.lbp" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="总吨">
            <el-input v-model="form.gross" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="净吨">
            <el-input v-model="form.net" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item label="最小干舷">
            <el-input
              v-model="form.minFreeboard"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="建成日期">
            <el-input
              v-model="form.buildDate"
              placeholder="请输入内容"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cance">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { shipAdminPage, existByMMSI, addShipInfo, editShipInfo, delShipInfo } from "@/api/shipAdmin/index.ts";

// 定义接口
interface QueryParams {
  pageNum: number;
  pageSize: number;
  mmsi: string | null;
  shipNameEn: string | null;
}

interface ShipForm {
  id?: string | number;
  localName: string;
  mmsi: string | number;
  shipNameEn: string;
  shipTypeCode: string;
  flagCode: string;
  callsign: string;
  imo: string;
  draught: string;
  contactNo: string;
  shipNo: string;
  shipyard: string;
  loa: string;
  bm: string;
  depth: string;
  lbp: string;
  gross: string;
  net: string;
  minFreeboard: string;
  buildDate: string;
}

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'close'): void
}>();

// 响应式数据
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 20,
  mmsi: null,
  shipNameEn: null,
});

const total = ref(0);
const tableData = ref<any[]>([]);
const title = ref<string | null>(null);
const isShow = ref(false);
const tipsShow = ref(false);
const loading = ref(false);
const ruleForm = ref<any>(null);

// 表单数据
const form = reactive<ShipForm>({
  localName: "",
  mmsi: "",
  shipNameEn: "",
  shipTypeCode: "",
  flagCode: "",
  callsign: "",
  imo: "",
  draught: "",
  contactNo: "",
  shipNo: "",
  shipyard: "",
  loa: "",
  bm: "",
  depth: "",
  lbp: "",
  gross: "",
  net: "",
  minFreeboard: "",
  buildDate: "",
});

// 表单校验规则
const rules = {
  mmsi: [
    { required: true, message: '请输入mmsi', trigger: 'blur' },
    { min: 1, max: 9, trigger: 'blur' }
  ],
};

// 组件挂载时执行
onMounted(() => {
  getList();
});

// 获取船舶列表
function getList() {
  loading.value = true;
  shipAdminPage(queryParams).then((res: any) => {
    if (res.code == 200) {
      loading.value = false;
      total.value = res.total;
      tableData.value = res.rows;
    }
  }).catch(() => {
    loading.value = false;
  });
}

// 分页大小变化
function handleSizeChange(e: number) {
  queryParams.pageSize = e;
  queryParams.pageNum = 1;
  tableData.value = [];
  getList();
}

// 页码变化
function handleCurrentChange(e: number) {
  queryParams.pageNum = e;
  tableData.value = [];
  getList();
}

// 新增船舶
function addShip() {
  title.value = "新增";
  resetForm();
  isShow.value = true;
}

// 修改船舶
function edit(val: ShipForm) {
  Object.assign(form, val);
  title.value = "修改";
  isShow.value = true;
}

// 提交表单
function submit() {
  if (!form.mmsi && form.mmsi !== 0) {
    return ElMessage.error("请输入mmsi");
  }
  if (!form.shipNameEn) {
    return ElMessage.error("请输入英文船名");
  }
  
  if (form.id) {
    // 修改
    editShipInfo(form).then((res: any) => {
      if (res.code == 200) {
        ElMessage.success("操作成功");
        isShow.value = false;
        tableData.value = [];
        getList();
      }
    });
  } else {
    // 新增
    addShipInfo(form).then((res: any) => {
      if (res.code == 200) {
        ElMessage.success("操作成功");
        isShow.value = false;
        tableData.value = [];
        getList();
      }
    });
  }
}

// 删除船舶
function del(val: ShipForm) {
  ElMessageBox.confirm('是否确认删除？').then(() => {
    return delShipInfo(val.id as number);
  }).then(() => {
    tableData.value = [];
    getList();
    ElMessage.success("删除成功");
  }).catch(() => {});
}

// 取消操作
function cance() {
  resetForm();
  isShow.value = false;
}

// 重置表单
function resetForm() {
  Object.assign(form, {
    localName: "",
    mmsi: "",
    shipNameEn: "",
    shipTypeCode: "",
    flagCode: "",
    callsign: "",
    imo: "",
    draught: "",
    contactNo: "",
    shipNo: "",
    shipyard: "",
    loa: "",
    bm: "",
    depth: "",
    lbp: "",
    gross: "",
    net: "",
    minFreeboard: "",
    buildDate: "",
  });
}

// 验证mmsi是否存在
function verifyMmsi() {
  if (form.mmsi && form.mmsi.toString().length > 0 && form.mmsi !== 0) {
    existByMMSI(form.mmsi).then((res: any) => {
      if (res.data) {
        ElMessage.error("mmsi已存在");
        tipsShow.value = res.data;
      }
    });
  }
}

// 搜索船舶
function search() {
  tableData.value = [];
  queryParams.pageNum = 1;
  getList();
}

// 关闭页面
function close() {
  emit('close');
}
</script>

<style scoped>
/* 表格 */
:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table__fixed-right::before) {
  height: 0;
}

:deep(.el-table th.el-table__cell.is-leaf) {
  border: none;
}

:deep(.el-table td.el-table__cell) {
  border: none;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table__fixed::before) {
  height: 0;
}

:deep(.el-table__fixed-right::before) {
  height: 0;
}

:deep(.el-form-item__label) {
  color: #ffffff;
  margin-right: 10px;
}

:deep(.el-table__body-wrapper) {
  background: #3c4167;
}

:deep(.el-table__empty-text) {
  color: #ffffff;
}

:deep(.el-table .el-table__body tr.hover-row > td) {
  background-color: rgba(7, 40, 79, 0.2) !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: rgba(7, 40, 79, 0.2) !important;
}

:deep(.pagination-container) {
  background: none !important;
}

:deep(.el-pager li.active) {
  background: rgba(7, 40, 79, 0.8) !important;
}

:deep(.el-table__fixed-right-patch) {
  width: 0 !important;
}

/* 输入框 */
:deep(.aaaa input::-webkit-outer-spin-button),
:deep(.aaaa input::-webkit-inner-spin-button) {
    -webkit-appearance: none;
}
:deep(.aaaa input[type="number"]) {
    -moz-appearance: textfield;
}
</style>

<style scoped lang="less">
.content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  /* background-color: #ffffff; */
  color: #ffffff;
  z-index: 1000;
  // width: 1500px;
  // background-color: rgba(31, 63, 130, 0.8);
  background-color: rgba(60, 65, 103, 0.95);
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);

  .box_title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    justify-content: space-between;
  }

  .box_title img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }

  .box_title > img:hover {
    cursor: pointer;
  }
  .tableBox {
    width: 1000px;
  }
  .search{
    width: 200px;
    margin:0 20px;
  }

  .tips{
    font-size: 12px;
    color: red;
    position: absolute;
    left: 0;
    top: -30px;
  }
}
</style>