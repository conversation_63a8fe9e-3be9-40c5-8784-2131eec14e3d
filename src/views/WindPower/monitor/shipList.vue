<template>
  <div>
    <div class="flex-column">
      <div class="flex-row" style="justify-content: space-between">
        <div class="content-txt1">在港船舶列表</div>
        <div @click="handToParentClose()">
          <i class="el-icon-circle-close" style="color: #409eff"></i>
        </div>
      </div>
      <!-- <div class="line"></div> -->
      <div class="area" style="display: flex; align-items: center">
        区域
        <el-select
          v-model="selectVal"
          style="width: 160px; margin-left: 6px"
          placeholder="请选择"
          :popper-append-to-body="false"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <!-- <div class="line"></div> -->
      <div class="shipNum">
        <span>船舶总数：{{ list.length }}</span>
      </div>
      <!-- <div class="line"></div> -->
      <div>
        <el-collapse>
          <el-collapse-item title="三无船舶" name="1">
            <div
              v-for="(item, index) in threeNone"
              :key="index"
              @click="getShip(item)"
            >
              {{ item.name }}
            </div>
          </el-collapse-item>
          <el-collapse-item title="渔船" name="2">
            <div
              v-for="(item, index) in fishing"
              :key="index"
              @click="getShip(item)"
            >
              {{ item.name }}
            </div>
          </el-collapse-item>
          <el-collapse-item title="执法船" name="3">
            <div
              v-for="(item, index) in enforce"
              :key="index"
              @click="getShip(item)"
            >
              {{ item.name }}
            </div>
          </el-collapse-item>
          <el-collapse-item title="重点关注船舶" name="4">
            <div
              v-for="(item, index) in interest"
              :key="index"
              @click="getShip(item)"
            >
              {{ item.name }}
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>
  
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getShipList } from "@/api/map/map.ts"

// 声明 T 变量，从全局 window 对象获取
const T = window.T

interface Option {
  value: number
  label: string
}

interface ShipItem {
  name: string
  lon: number
  lat: number
  [key: string]: any
}

interface ShipParams {
  [key: string]: any
}

// 定义 props
const props = defineProps<{
  map: any
}>()

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'closeList', val: number): void
}>()

// 响应式数据
const marker = ref<any>(null)
const options = ref<Option[]>([
  {
    value: 1,
    label: "A区",
  },
  {
    value: 2,
    label: "B区",
  },
])
const selectVal = ref(1)
const threeNone = ref<ShipItem[]>([])
const fishing = ref<ShipItem[]>([])
const enforce = ref<ShipItem[]>([])
const interest = ref<ShipItem[]>([])
const list = ref<ShipItem[]>([])
const tableData = ref<any[]>([])
const shipTotal = ref(0)
const shipParams = reactive<ShipParams>({})

// 生命周期钩子
onMounted(() => {
  // 注释掉的代码保留
  // list.value.forEach((ele, index) => {
  //   if (index % 4 == 1) {
  //     threeNone.value.push(ele);
  //   } else if (index % 4 == 2) {
  //     fishing.value.push(ele);
  //   } else if (index % 4 == 3) {
  //     enforce.value.push(ele);
  //   } else {
  //     interest.value.push(ele);
  //   }
  // });
})

// 获取船信息
function getShipInfo() {
  // getShipList(shipParams).then((res: any) => {
  //   if (res.code == 200) {
  //     tableData.value = res.rows
  //     shipTotal.value = res.total
  //   }
  // })
}

function handToParentClose() {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }

  //关闭弹窗
  emit('closeList', 1)
}

// 点击船，查看位置
function getShip(val: ShipItem) {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }

  let lng = val.lon
  let lat = val.lat
  props.map.panTo(new T.LngLat(lng, lat))
  
  // 设置标记点
  let icon = new T.Icon({
    iconUrl: require("@/assets/images/jingzhun.png"),
    iconSize: new T.Point(25, 25),
    iconAnchor: new T.Point(12, 25),
  })
  //创建标注对象
  marker.value = new T.Marker(new T.LngLat(lng, lat), {
    icon: icon,
  })
  //向地图上添加标注
  props.map.addOverLay(marker.value)
}
</script>
  
<style scoped>
.content {
  /* width: 350px; */
  height: 100%;
  /* background: rgba(0, 0, 0, 0.5); */
  position: fixed;
  right: 0;
  top: 60px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.el-icon-circle-close{
  font-size: 22px;
}
.content-txt1 {
  font-size: 16px;
  color: #409eff;
}

.line {
  width: 100%;
  height: 1px;
  margin: 10px auto;
  background: #d5d5d5;
}
.area{
  margin: 20px 0;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  color: #4386c6;
}

.custom-tree-node-label {
  display: block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.shipNum {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.collItem:hover {
  cursor: pointer;
}
.collItem {
  color: #409eff;
  margin-bottom: 10px;
}
</style>