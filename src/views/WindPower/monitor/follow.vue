<template>
  <div>
    <div class="flex-column">
      <div class="flex-row" style="justify-content: space-between">
        <div class="content-txt1">重点关注船舶</div>
        <div @click="handToParentClose()">
          <i class="el-icon-circle-close" style="color: #409eff"></i>
        </div>
      </div>
      <!-- <div class="line"></div> -->
      <div class="addShip" @click="addShip">
        <i class="el-icon-zoom-in"></i>
        添加重点关注船舶
      </div>
      <!-- <div class="line"></div> -->
      <div v-infinite-scroll="load" class="shipContent">
        <div
          class="videoItem"
          v-for="(item, index) in List"
          :key="index"
          @click="setCenter(item)"
        >
          <div class="videoItem_name">
            {{ item.englishName }}
          </div>

          <div style="display: flex; align-items: center">
            <i class="el-icon-edit" @click.stop="changeShip(item)"></i>
            <i
              class="el-icon-delete"
              style="margin: 0 10px"
              @click.stop="delImpShip(item)"
            ></i>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加弹窗 -->

    <el-dialog
      title="添加重点关注船舶"
      v-model="isAdd"
      width="50%"
      :modal="false"
    >
      <div class="parameter">
        搜索：
        <div class="inp">
          
          <el-input
            v-model="shipParams.search"
            placeholder="请输入船名或MMSI"
            
          ></el-input>
        </div>
        <el-button type="primary" @click="condQuery">查询</el-button>
        <el-button type="info" @click="resetting">重置</el-button>
      </div>
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="name" label="船名" width="250">
        </el-table-column>
        <el-table-column prop="id" label="ID" width="250"> </el-table-column>
        <el-table-column prop="mmsi" label="mmsi" width="250">
        </el-table-column>
        <el-table-column prop="chineseName" label="中文船名" width="250">
        </el-table-column>
        <el-table-column prop="englishName" label="英文船名" width="250">
        </el-table-column>

        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="increase(scope.row)"
              >添加</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="shipSizeChange"
        @current-change="shipCurrentChange"
        :current-page="shipParams.pageNum"
        :page-sizes="[10, 20, 30]"
        :page-size="shipParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="shipTotal"
      >
      </el-pagination>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="isAdd = false">取 消</el-button>
          <el-button type="primary">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <div class="addBox" v-if="isChange">
      <div class="addBox_head">
        <div class="addBox_head_title">
          <img src="@/static/wenjian.png" alt="" />
          编辑
        </div>
        <img
          class="addBox_head_close"
          src="@/static/guanbi.png"
          alt=""
          @click="cancel"
        />
      </div>
      <div>
        <div class="addBox_list">
          <span>mmsi</span>
          <div class="addBox_list_inp">
            <input type="text" v-model="form.mmsi" />
          </div>
        </div>
        <div class="addBox_list">
          <span>中文名称</span>
          <div class="addBox_list_inp">
            <input type="text" v-model="form.chineseName" />
          </div>
        </div>
        <div class="addBox_list">
          <span>英文名称</span>
          <div class="addBox_list_inp">
            <input type="text" v-model="form.englishName" />
          </div>
        </div>
        <div class="addBox_list">
          <span>备注</span>
          <div class="addBox_list_inp">
            <input type="text" v-model="form.remark" />
          </div>
        </div>
      </div>
      <div class="addBox_btns">
        <div class="addBox_btn submit" @click="submit">保存</div>
        <div class="addBox_btn cancel" @click="cancel">取消</div>
      </div>
    </div>
  </div>
</template>
      
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { impShipList, addImpShip, delShip, putShip } from "@/api/map/follow.ts"
import { getShipList } from "@/api/map/map.ts"
import { ElMessage, ElMessageBox } from 'element-plus'

// 定义接口
interface QueryParams {
  pageNum: number
  pageSize: number
}

interface ShipParams {
  pageNum: number
  pageSize: number
  search: string | null
}

interface ShipItem {
  id: string | number
  name?: string
  englishName?: string
  chineseName?: string
  mmsi?: string | number
  remark?: string
  [key: string]: any
}

// 定义 props
const props = defineProps<{
  map: any
}>()

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'closeList', val: number): void
}>()

// 响应式数据
const marker = ref<any>(null)
const isAdd = ref(false) //展开/收起添加船舶弹窗
const isChange = ref(false) //修改弹窗
const form = reactive<ShipItem>({} as ShipItem)
const List = ref<ShipItem[]>([])
const impTotal = ref(0) //重点船舶列表总数
const tableData = ref<any[]>([]) //添加重点船舶时的表格数据
const shipTotal = ref(0) //添加重点船舶列表总数

const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 30,
})

const shipParams = reactive<ShipParams>({
  pageNum: 1,
  pageSize: 10,
  search: null,
})

// 生命周期钩子
onMounted(() => {
  getimpShipList()
})

// 获取重点关注船舶列表
function getimpShipList() {
  impShipList(queryParams).then((res: any) => {
    console.log(res)
    if (res.code == 200) {
      List.value.push(...res.rows)
      impTotal.value = res.total
    }
  })
}

function handToParentClose() {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }

  //关闭弹窗
  emit('closeList', 4)
}

// 点击船
function setCenter(val: ShipItem) {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }
  props.map.centerAndZoom(new T.LngLat(121.809, 30.005))

  let icon = new T.Icon({
    iconUrl: require('@/assets/images/jingzhun.png'),
    iconSize: new T.Point(25, 25),
    iconAnchor: new T.Point(12, 12),
  })
  //创建标注对象
  marker.value = new T.Marker(new T.LngLat(121.809, 30.005), {
    icon: icon,
  })
  //向地图上添加标注
  props.map.addOverLay(marker.value)
}

//添加重点关注-打开弹窗
function addShip() {
  isAdd.value = true
  getShipInfo()
}

// 编辑
function changeShip(val: ShipItem) {
  Object.assign(form, val)
  isChange.value = true
}

// 关闭添加重点窗口
function cancel() {
  isChange.value = false
  Object.keys(form).forEach(key => {
    delete form[key]
  })
}

// 添加-保存
function submit() {
  putShip(form).then((res: any) => {
    if (res.code == 200) {
      Object.keys(form).forEach(key => {
        delete form[key]
      })
      ElMessage.success(res.msg)
      isChange.value = false
    }
  })
}

// 获取船信息
function getShipInfo() {
  // getShipList(shipParams).then((res: any) => {
  //   if (res.code == 200) {
  //     tableData.value = res.rows
  //     shipTotal.value = res.total
  //   }
  // })
}

// 条件查询
function condQuery() {
  shipParams.pageNum = 1
  tableData.value = []
  getShipInfo()
}

// 条件重置
function resetting() {
  shipParams.pageNum = 1
  shipParams.search = null
  tableData.value = []
  getShipInfo()
}

//改变分页
function shipSizeChange(val: number) {
  shipParams.pageSize = val
}

function shipCurrentChange(val: number) {
  shipParams.pageNum = val
  getShipInfo()
}

//添加
function increase(val: ShipItem) {
  ElMessageBox.confirm('是否确认将船名为"' + val.name + '"添加为重点关注船舶？')
    .then(() => {
      return addImpShip(val.id)
    })
    .then((res: any) => {
      ElMessage.success(res.msg)
      queryParams.pageNum = 1
      List.value = []
      getimpShipList()
    })
    .catch(() => {})
}

// 重点船舶加载下一页
function load() {
  if (List.value.length < impTotal.value) {
    queryParams.pageNum += 1
    getimpShipList()
  }
}

// 移出重点船舶
function delImpShip(val: ShipItem) {
  ElMessageBox.confirm('是否确认将船名为"' + val.name + '"移除重点关注船舶？')
    .then(() => {
      return delShip(val.id)
    })
    .then((res: any) => {
      if (res.code == 200) {
        ElMessage.success(res.msg)
        queryParams.pageNum = 1
        List.value = []
        getimpShipList()
      }
    })
    .catch(() => {})
}
</script>
      
<style scoped>
.content {
  /* width: 350px; */
  height: 100%;
  /* background: rgba(0, 0, 0, 0.5); */
  position: fixed;
  right: 0;
  top: 60px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
}

.content-txt1 {
  font-size: 16px;
  color: #409eff;
}

.el-icon-circle-close{
  font-size: 22px;
}
.line {
  width: 100%;
  height: 1px;
  margin: 10px auto;
  background: #d5d5d5;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  color: #4386c6;
}

.custom-tree-node-label {
  display: block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.shipNum {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.collItem:hover {
  cursor: pointer;
}
.collItem {
  color: #409eff;
  margin-bottom: 10px;
}
.videoItem {
  height: 30px;
  /* line-height: 30px; */
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.videoItem:hover {
  cursor: pointer;
  color: #409eff;
}
.videoItem_name {
  width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.shipContent {
  height: 700px;
  overflow: auto;
}
.addShip:hover {
  cursor: pointer;
}
.addShip{
  margin: 20px 0;
}
.addBox {
  width: 300px;
  padding: 10px;
  background-color: #424a6f;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.addBox_head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.addBox_head img {
  width: 20px;
  height: 20px;
}
.addBox_head_title {
  display: flex;
  align-items: center;
  color: #ffffff;
}
.addBox_head_title > img {
  margin-right: 10px;
}
.addBox_head_close:hover {
  cursor: pointer;
}
.addBox_list {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #ffffff;
  margin-bottom: 10px;
  font-size: 14px;
}
.addBox_list_inp {
  width: 180px;
  height: 30px;
  margin-left: 20px;
  border: 1px solid #4376ec;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.addBox_btns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.addBox_btn {
  width: 80px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  border-radius: 4px;
}
.addBox_btn:hover {
  cursor: pointer;
}

.submit {
  background-color: #6771b7;
  color: #ffffff;
  margin-right: 20px;
}
.cancel {
  background-color: #ffffff;
  color: #333333;
}

input {
  border: none;
  outline: none;
  background: #000000;
  color: #ffffff;
}

.parameter {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #ffffff;
}

::-webkit-scrollbar {
  width: 4px;
  height: 10px;
  background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #4376ec;
}

.inp{
  width: 200px;
  margin-right: 10px;
}
</style>