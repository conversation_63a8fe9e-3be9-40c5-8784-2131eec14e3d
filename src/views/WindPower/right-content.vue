<template>
  <div class="content-right-btn" :style="{ 'right': contentWidth }">
    <!-- 定位 -->
    <div class="content-item">
      <div class="tooltip" title="定位">
        <el-button type="primary" :icon="Location" size="small" @click="toggleLocationPanel" circle></el-button>
      </div>

      <div class="pointBox" v-if="locationPanelOpen">
        <div class="pointBox_head">
          <div class="pointBox_head_left">
            <img src="@/assets/images/wenjian.png" alt="" />
            <span>定位</span>
          </div>
          <img src="@/assets/images/guanbi.png" alt="" @click="toggleLocationPanel" />
        </div>
        <div class="pointBox_body">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="定位" name="location">
              <div class="padding-style">
                <div class="pointBox_inpBox">
                  <span>经度</span>
                  <div class="pointBox_inpBox_inp">
                    <el-input v-model="coordinates.longitude" placeholder="请输入经度" />
                  </div>
                </div>
                <div class="pointBox_inpBox">
                  <span>纬度</span>
                  <div class="pointBox_inpBox_inp">
                    <el-input v-model="coordinates.latitude" placeholder="请输入纬度" />
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div style="display: flex; align-items: center; justify-content: flex-end">
          <el-button class="el-button-primary btn-style" plain size="small" type="primary"
            @click="pickCoordinates">拾取</el-button>
          <el-button class="el-button-primary btn-style" type="primary" plain size="small"
            @click="goToLocation">定位</el-button>
          <el-button class="el-button-primary btn-style" plain size="small"
            @click="toggleLocationPanel">关闭</el-button>
        </div>
      </div>
      
      <div v-if="isPickingLocation" class="pointTips" :style="{ left: cursorPosition.x + 'px', top: cursorPosition.y + 'px' }">
        <div>点击设置位置</div>
        <div class="round"></div>
      </div>
    </div>
    
    <!-- 地图类型切换 -->
    <div class="content-item">
      <div class="tooltip" title="地图类型">
        <el-button type="primary" :icon="MapLocation" size="small" @click="toggleMapTypePanel" circle></el-button>
      </div>
      
      <div class="pointBox" v-if="mapTypePanelOpen">
        <div class="pointBox_head">
          <div class="pointBox_head_left">
            <img src="@/assets/images/wenjian.png" alt="" />
            <span>地图类型</span>
          </div>
          <img src="@/assets/images/guanbi.png" alt="" @click="toggleMapTypePanel" />
        </div>
        <div class="pointBox_body">
          <div class="map-types-container">
            <div 
              class="map-type-item" 
              :class="{ active: currentMapType === 'default' }"
              @click="changeMapType('default')"
            >
              <div class="map-type-icon">
                <el-icon><MapLocation /></el-icon>
              </div>
              <div class="map-type-label">标准地图</div>
            </div>
            <div 
              class="map-type-item" 
              :class="{ active: currentMapType === 'satellite' }"
              @click="changeMapType('satellite')"
            >
              <div class="map-type-icon">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="map-type-label">卫星影像</div>
            </div>
            <div 
              class="map-type-item" 
              :class="{ active: currentMapType === 'terrain' }"
              @click="changeMapType('terrain')"
            >
              <div class="map-type-icon">
                <el-icon><Histogram /></el-icon>
              </div>
              <div class="map-type-label">地形图</div>
            </div>
            <div 
              class="map-type-item" 
              :class="{ active: currentMapType === 'sea' }"
              @click="changeMapType('sea')"
            >
              <div class="map-type-icon">
                <el-icon><Ship /></el-icon>
              </div>
              <div class="map-type-label">海图</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图层控制 -->
    <div class="content-item">
      <div class="tooltip" title="图层控制">
        <el-button type="primary" :icon="Files" size="small" @click="toggleLayersPanel" circle></el-button>
      </div>
      
      <div class="pointBox layers-panel" v-if="layersPanelOpen">
        <div class="pointBox_head">
          <div class="pointBox_head_left">
            <img src="@/assets/images/wenjian.png" alt="" />
            <span>图层控制</span>
          </div>
          <img src="@/assets/images/guanbi.png" alt="" @click="toggleLayersPanel" />
        </div>
        <div class="pointBox_body">
          <div class="padding-style">
            <div class="layer-control-title">基础图层</div>
            <div class="layer-control-item">
              <el-checkbox v-model="layerControls.labels" @change="updateLayers('locationName')">地名标注</el-checkbox>
            </div>
            
            <div class="layer-control-title">业务图层</div>
            <div class="layer-control-item">
              <el-checkbox v-model="layerControls.ships" @change="updateLayers('ship')">船舶</el-checkbox>
            </div>
            <div class="layer-control-item">
              <el-checkbox v-model="layerControls.shipLegend" @change="updateLayers('shipLegend')">船舶图例</el-checkbox>
            </div>
          </div>
        </div>
        <div style="display: flex; align-items: center; justify-content: flex-end">
          <el-button class="el-button-primary btn-style" type="primary" plain size="small"
            @click="resetLayers">重置图层</el-button>
          <el-button class="el-button-primary btn-style" plain size="small"
            @click="toggleLayersPanel">关闭</el-button>
        </div>
      </div>
    </div>
    
    <!-- 距离量算 -->
    <div class="content-item">
      <div class="tooltip" title="距离量算">
        <el-button type="primary" :icon="Notebook" size="small" @click="startMeasureDistance" circle></el-button>
      </div>
    </div>
    
    <!-- 面积量算 -->
    <div class="content-item">
      <div class="tooltip" title="面积量算">
        <el-button type="primary" :icon="DataAnalysis" size="small" @click="startMeasureArea" circle></el-button>
      </div>
    </div>
    

    
    <!-- 船舶图例 -->
    <div class="content-item">
      <div class="tooltip" title="船舶图例">
        <el-button type="primary" :icon="Ship" size="small" @click="toggleShipLegendPanel" circle></el-button>
      </div>

      <div class="pointBox ship-legend-panel" v-if="shipLegendPanelOpen">
        <div class="pointBox_head">
          <div class="pointBox_head_left">
            <img src="@/assets/images/wenjian.png" alt="" />
            <span>船舶图例</span>
          </div>
          <img src="@/assets/images/guanbi.png" alt="" @click="toggleShipLegendPanel" />
        </div>
        <div class="pointBox_body">
          <div class="padding-style">
            <div class="ship-legend-title">船舶类型颜色说明</div>
            <div class="ship-legend-items">
              <div class="ship-legend-item">
                <div class="ship-legend-color" style="background-color: #FFA500;"></div>
                <span>客船</span>
              </div>
              <div class="ship-legend-item">
                <div class="ship-legend-color" style="background-color: #FFFF00;"></div>
                <span>货船</span>
              </div>
              <div class="ship-legend-item">
                <div class="ship-legend-color" style="background-color: #8B4513;"></div>
                <span>作业船</span>
              </div>
              <div class="ship-legend-item">
                <div class="ship-legend-color" style="background-color: #800080;"></div>
                <span>拖船</span>
              </div>
              <div class="ship-legend-item">
                <div class="ship-legend-color" style="background-color: #0000FF;"></div>
                <span>渔船</span>
              </div>
              <div class="ship-legend-item">
                <div class="ship-legend-color" style="background-color: #D2B48C;"></div>
                <span>其他类型</span>
              </div>
            </div>
          </div>
        </div>
        <div style="display: flex; align-items: center; justify-content: flex-end">
          <el-button class="el-button-primary btn-style" plain size="small"
            @click="toggleShipLegendPanel">关闭</el-button>
        </div>
      </div>
    </div>

    <!-- 地图重置 -->
    <div class="content-item">
      <div class="tooltip" title="重置地图并清除所有测量">
        <el-button type="primary" :icon="RefreshRight" size="small" @click="resetMap" circle></el-button>
      </div>
    </div>

    <!-- 预警发送记录 -->
    <!-- <div class="content-item">
      <div class="tooltip" title="预警发送记录">
        <el-button type="primary" :icon="Calendar" size="small" @click="showAlertLogs" circle></el-button>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onUnmounted, onMounted, computed } from 'vue'
import {
  Location, Calendar, Delete, RefreshRight, DataAnalysis, Notebook,
  MapLocation, Files, Picture, Histogram, Ship
} from '@element-plus/icons-vue'
import type { Map as OlMap } from 'ol'
import { fromLonLat } from 'ol/proj'
import { MeasureUtils, addMeasureStyles } from '@/utils/measureUtils';
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus';
import VectorLayer from 'ol/layer/Vector';
import VectorSource from 'ol/source/Vector';
import { Style, Fill, Stroke } from 'ol/style.js';
import { Feature } from 'ol';
import * as olGeom from 'ol/geom.js';
import mapUtils from '@/utils/mapUtils';
import emitter from '@/utils/bus';
import {
  envList
} from "@/api/map/map.js";
import { Place } from "@/utils/request.js";
import TileLayer from 'ol/layer/Tile.js';
import XYZ from 'ol/source/XYZ.js';
import Layer from 'ol/layer/Layer.js';

// 定义Props类型
interface Props {
  isPopup: boolean
  map: OlMap | null
}

const props = defineProps<Props>()

// 定义事件
const emit = defineEmits<{
  (e: 'fun', value: number): void
  (e: 'clearMeasurements'): void
  (e: 'resetMap'): void
  (e: 'disposeLogShow'): void
  (e: 'changeLegendType', value: number): void
  (e: 'resetLayers'): void
  (e: 'reloadShipData'): void
}>()

// 状态变量
const contentWidth = ref('0')
const locationPanelOpen = ref(false)
const activeTab = ref('location')
const isPickingLocation = ref(false)
const coordinates = reactive({
  longitude: '',
  latitude: ''
})
const cursorPosition = reactive({
  x: 0,
  y: 0
})
const isMeasuring = ref(false)

// 新增 - 地图类型控制
const mapTypePanelOpen = ref(false)
const currentMapType = ref('default')
const mapLayers = reactive<Record<string, TileLayer<any> | null>>({
  default: null,
  satellite: null,
  terrain: null,
  sea: null,
  labels: null
})

// 新增 - 图层控制
const layersPanelOpen = ref(false)
const layerControls = reactive({
  labels: true,
  ships: true,
  shipLegend: false  // 船舶图例，默认不选中
})

// 新增 - 船舶图例控制
const shipLegendPanelOpen = ref(false)

// 存储事件监听器
let mouseMoveListener: any = null
let mapClickListener: any = null

// 在代码中适当位置（可能是setup或者onMounted钩子内）添加如下代码
let measureUtils: MeasureUtils | null = null;
let measureCleanupFn: (() => void) | null = null;

// 添加环境相关的状态变量
const earlyMakers = ref<any[]>([]);
const envs = ref<any[]>([]);
const place = ref<number>(Place);

// 监听props.isPopup变化
watch(() => props.isPopup, (val) => {
  if (document.getElementById('contentWidth')) {
    contentWidth.value = window.getComputedStyle(document.getElementById('contentWidth')).width
  } else {
    contentWidth.value = '0'
  }
})

// 计算工具栏是否展开（任何面板展开都算展开）
const isAnyPanelOpen = computed(() => {
  return locationPanelOpen.value ||
         mapTypePanelOpen.value ||
         layersPanelOpen.value ||
         shipLegendPanelOpen.value
})

// 监听工具栏状态变化，发送事件给父组件
watch(isAnyPanelOpen, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    console.log(`工具栏状态变化: ${oldValue} -> ${newValue}`)
    // 通过事件总线发送工具栏状态变化事件
    if (window.emitter) {
      window.emitter.emit('toolbar-state-change', newValue)
    }
  }
}, { immediate: true })

// 新增 - 切换地图类型面板
const toggleMapTypePanel = () => {
  mapTypePanelOpen.value = !mapTypePanelOpen.value

  // 关闭其他面板
  if (mapTypePanelOpen.value) {
    locationPanelOpen.value = false
    layersPanelOpen.value = false
    shipLegendPanelOpen.value = false
  }
}

// 新增 - 切换图层控制面板
const toggleLayersPanel = () => {
  layersPanelOpen.value = !layersPanelOpen.value

  // 关闭其他面板
  if (layersPanelOpen.value) {
    locationPanelOpen.value = false
    mapTypePanelOpen.value = false
    shipLegendPanelOpen.value = false
  }
}

// 新增 - 切换船舶图例面板
const toggleShipLegendPanel = () => {
  shipLegendPanelOpen.value = !shipLegendPanelOpen.value

  // 关闭其他面板
  if (shipLegendPanelOpen.value) {
    locationPanelOpen.value = false
    mapTypePanelOpen.value = false
    layersPanelOpen.value = false
  }
}

// 新增 - 创建地图图层
const createMapLayers = () => {
  if (!props.map) {
    console.error('地图未初始化')
    return
  }
  
  // 天地图密钥
  const tiandituKey = '9dc4d6ee856d047e1a0caf2eec1c17f8'
  
  // 创建自定义瓦片加载函数
  const createTileLoadFunction = () => {
    return function(tile: any, src: string) {
      const img = tile.getImage();
      
      fetch(src, { 
        credentials: 'same-origin',
        cache: 'force-cache'
      })
      .then(response => response.blob())
      .then(blob => {
        const blobUrl = URL.createObjectURL(blob);
        img.src = blobUrl;
        
        img.onload = () => {
          URL.revokeObjectURL(blobUrl);
        };
      })
      .catch(() => {
        img.src = src;
      });
    };
  };
  
  // 标准地图
  mapLayers.default = new TileLayer({
    source: new XYZ({
      url: `http://t4.tianditu.com/DataServer?T=vec_w&tk=${tiandituKey}&x={x}&y={y}&l={z}`,
      cacheSize: 512,
      crossOrigin: 'anonymous',
      wrapX: true,
      tileLoadFunction: createTileLoadFunction()
    }),
    className: 'light-layer',
    preload: 4,
    visible: currentMapType.value === 'default'
  })
  
  // 卫星图
  mapLayers.satellite = new TileLayer({
    source: new XYZ({
      url: `http://t4.tianditu.com/DataServer?T=img_w&tk=${tiandituKey}&x={x}&y={y}&l={z}`,
      cacheSize: 512,
      crossOrigin: 'anonymous',
      wrapX: true,
      tileLoadFunction: createTileLoadFunction()
    }),
    className: 'satellite-layer',
    preload: 4,
    visible: currentMapType.value === 'satellite'
  })
  
  // 地形图
  mapLayers.terrain = new TileLayer({
    source: new XYZ({
      url: `http://t4.tianditu.com/DataServer?T=ter_w&tk=${tiandituKey}&x={x}&y={y}&l={z}`,
      cacheSize: 512,
      crossOrigin: 'anonymous',
      wrapX: true,
      tileLoadFunction: createTileLoadFunction()
    }),
    className: 'terrain-layer',
    preload: 4,
    visible: currentMapType.value === 'terrain'
  })
  
  // 海图 (使用OpenStreetMap的地图作为示例)
  mapLayers.sea = new TileLayer({
    source: new XYZ({
      url: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
      cacheSize: 512,
      crossOrigin: 'anonymous',
      wrapX: true,
      tileLoadFunction: createTileLoadFunction()
    }),
    className: 'sea-layer',
    preload: 4,
    visible: currentMapType.value === 'sea'
  })
  
  // 标注图层
  mapLayers.labels = new TileLayer({
    source: new XYZ({
      url: `http://t4.tianditu.com/DataServer?T=cva_w&tk=${tiandituKey}&x={x}&y={y}&l={z}`,
      cacheSize: 512,
      crossOrigin: 'anonymous',
      wrapX: true,
      tileLoadFunction: createTileLoadFunction()
    }),
    className: 'label-layer',
    preload: 4,
    visible: layerControls.labels
  })
  
  // 将图层添加到地图
  Object.values(mapLayers).forEach(layer => {
    if (layer) {
      props.map!.addLayer(layer)
    }
  })
}

// 新增 - 切换地图类型
const changeMapType = (type: string) => {
  if (!props.map) return
  
  currentMapType.value = type
  
  // 更新图层可见性
  if (mapLayers.default) mapLayers.default.setVisible(type === 'default')
  if (mapLayers.satellite) mapLayers.satellite.setVisible(type === 'satellite')
  if (mapLayers.terrain) mapLayers.terrain.setVisible(type === 'terrain')
  if (mapLayers.sea) mapLayers.sea.setVisible(type === 'sea')
  
  // 根据地图类型发送事件
  let legendType = 1
  switch (type) {
    case 'default':
      legendType = 1
      break
    case 'satellite':
      legendType = 2
      break
    case 'terrain':
      legendType = 3
      break
    case 'sea':
      legendType = 4
      break
  }
  
  emit('changeLegendType', legendType)
  
  // 显示提示信息
  const mapTypeNames = {
    default: '标准地图',
    satellite: '卫星影像',
    terrain: '地形图',
    sea: '海图'
  }
  
  ElMessage.success(`已切换到${mapTypeNames[type as keyof typeof mapTypeNames]}`)
}

// 新增 - 更新图层
const updateLayers = (value : String) => {
  if (!props.map) return

  // 更新标注图层可见性
  if (value==="locationName" && mapLayers.labels) {
    mapLayers.labels.setVisible(layerControls.labels)
  }

  // 向父组件发送图层变更事件
  // 发送船舶图层变更事件 (10: 船舶图层)
  if (layerControls.ships !== undefined && value==="ship") {
    emit('changeLegendType', 10)
  }

  // 发送船舶图例变更事件 (12: 船舶图例)
  if (layerControls.shipLegend !== undefined && value==="shipLegend") {
    emit('changeLegendType', 12)
  }
}

// 新增 - 重置图层设置
const resetLayers = () => {
  // 重置所有图层控制选项，但船舶图例保持默认的false状态
  Object.keys(layerControls).forEach(key => {
    if (key === 'shipLegend') {
      layerControls[key as keyof typeof layerControls] = false
    } else {
      layerControls[key as keyof typeof layerControls] = true
    }
  })

  // 更新图层
  updateLayers(undefined)

  // 向父组件发送图层重置消息
  // 触发船舶图层更新
  emit('changeLegendType', 10)
  // 触发船舶图例重置
  emit('changeLegendType', 12)
  // 发送重置图层事件
  emit('resetLayers')

  ElMessage.success('图层设置已重置')
}

// 地图截图功能已移除 - 避免canvas冲突导致的性能问题
const captureMap = () => {
  ElMessage.warning('地图截图功能已禁用，请使用浏览器自带的截图功能');
};



// 显示预警发送记录
const showAlertLogs = () => {
  emit('disposeLogShow')
}

// 打开/关闭定位面板
const toggleLocationPanel = () => {
  locationPanelOpen.value = !locationPanelOpen.value
  
  // 关闭其他面板
  if (locationPanelOpen.value) {
    mapTypePanelOpen.value = false
    layersPanelOpen.value = false
  }
  
  if (!locationPanelOpen.value) {
    // 如果面板关闭，确保停止拾取
    stopPickingCoordinates()
  }
}

// 拾取坐标
const pickCoordinates = () => {
  if (!props.map) {
    console.error('地图未初始化')
    return
  }
  
  // 如果已经在拾取，则停止拾取
  if (isPickingLocation.value) {
    stopPickingCoordinates()
    return
  }
  
  isPickingLocation.value = true
  
  // 先确保之前的监听器被清理
  if (mouseMoveListener) {
    document.removeEventListener('mousemove', mouseMoveListener)
  }
  
  if (mapClickListener && props.map) {
    props.map.un('click', mapClickListener)
  }
  
  // 重新创建鼠标移动监听，更新光标提示位置
  mouseMoveListener = (e: MouseEvent) => {
    cursorPosition.x = e.clientX - 50
    cursorPosition.y = e.clientY - 50
  }
  
  document.addEventListener('mousemove', mouseMoveListener)
  
  // 重新创建地图点击监听
  mapClickListener = (e: any) => {
    try {
      if (!props.map || !isPickingLocation.value) return
      
      // 尝试获取点击坐标 - 简化版本，只获取mapCoordinate
      const pixel = props.map.getEventPixel(e.originalEvent)
      const mapCoordinate = props.map.getCoordinateFromPixel(pixel)
      
      if (!mapCoordinate || mapCoordinate.length < 2) {
        console.error('获取坐标失败')
        return
      }
      
      // 设置坐标值
      coordinates.longitude = mapCoordinate[0].toFixed(6)
      coordinates.latitude = mapCoordinate[1].toFixed(6)
      
      // 停止拾取
      stopPickingCoordinates()
    } catch (error) {
      console.error('拾取坐标失败:', error)
      stopPickingCoordinates()
    }
  }
  
  // 添加地图点击监听
  props.map.on('click', mapClickListener)
}

// 停止拾取坐标
const stopPickingCoordinates = () => {
  isPickingLocation.value = false
  
  // 移除鼠标移动监听
  if (mouseMoveListener) {
    document.removeEventListener('mousemove', mouseMoveListener)
    mouseMoveListener = null
  }
  
  // 移除地图点击监听
  if (mapClickListener && props.map) {
    props.map.un('click', mapClickListener)
    mapClickListener = null
  }
}

// 前往定位点
const goToLocation = () => {
  if (!props.map) {
    console.error('地图未初始化')
    return
  }
  
  if (!coordinates.longitude || !coordinates.latitude) {
    console.error('请输入有效的坐标')
    return
  }
  
  try {
    const lon = parseFloat(coordinates.longitude)
    const lat = parseFloat(coordinates.latitude)
    
    if (isNaN(lon) || isNaN(lat)) {
      console.error('坐标格式不正确')
      return
    }
    
    // 创建坐标对象
    const position = fromLonLat([lon, lat], props.map.getView().getProjection())
    
    // 设置地图中心并缩放
    props.map.getView().animate({
      center: position,
      zoom: 12,
      duration: 500
    })
  } catch (error) {
    console.error('定位失败:', error)
  }
}

// 开始距离测量
const startMeasureDistance = () => {
  isMeasuring.value = true
  emit('fun', 1) // 1 代表距离测量
}

// 开始面积测量
const startMeasureArea = () => {
  isMeasuring.value = true
  emit('fun', 2) // 2 代表面积测量
}

// 清除测量
const clearMeasurements = () => {
  isMeasuring.value = false
  emit('clearMeasurements')
}

// 重置地图 - 优化重置方式，参考电子围栏刷新模式
const resetMap = () => {
  // 显示加载状态
  const loading = ElLoading.service({
    lock: true,
    text: '正在重置地图...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 1. 重置本地测量状态
    isMeasuring.value = false;

    // 2. 清除测量工具
    clearMeasurements();

    // 3. 停止位置拾取（如果正在进行）
    stopPickingCoordinates();

    // 4. 只清除围栏相关的图形元素，保护船舶图标
    if (earlyMakers.value.length > 0) {
      earlyMakers.value.forEach((ele) => {
        try {
          if (ele instanceof Feature) {
            // 如果是OpenLayers Feature
            const source = mapUtils.getVectorSource();
            if (source) {
              source.removeFeature(ele);
            }
          } else if (props.map) {
            // 任何其他类型的覆盖物，统一使用removeOverlay方法
            try {
              props.map.removeOverlay(ele);
            } catch (e) {
              // 如果removeOverlay方法不存在或失败，尝试使用备用方法
              try {
                (props.map as any).removeOverLay?.(ele);
              } catch (innerError) {
                console.error('备用移除方法也失败:', innerError);
              }
            }
          }
        } catch (error) {
          console.error('移除图形失败:', error);
        }
      });
      earlyMakers.value = [];
    }

    // 5. 发送重置事件
    emit('resetMap');

    // 6. 重新获取并绘制围栏数据，参考电子围栏刷新模式
    getwholeEnv();

    // 7. 延迟关闭加载状态并重新加载船舶数据
    setTimeout(() => {
      // 发送重新加载船舶数据的事件
      emit('reloadShipData');

      loading.close();
      // 显示成功提示
      ElMessage.success('地图重置成功，围栏和船舶数据已重新加载');
    }, 500);

  } catch (error) {
    console.error('重置地图时发生错误:', error);
    loading.close();
    ElMessage.error('重置地图失败，请重试');
  }
};

// 获取全部通航环境列表
const getwholeEnv = () => {
  envList({
    place: place.value,
  }).then((res: any) => {
    if (res.code == 200) {
      envs.value = res.rows;
      setEarlyWarning(envs.value);
    }
  }).catch(error => {
    console.error("获取全部环境列表失败:", error);
  });
};

// 绘制通航环境
const setEarlyWarning = (warning: any[]) => {
  if (!props.map) {
    console.error("地图实例未加载，无法绘制环境");
    return;
  }
  
  if (!warning || warning.length === 0) {
    console.warn("没有可绘制的环境数据");
    return;
  }
  
  // 清除已有的环境围栏
  if (earlyMakers.value.length > 0) {
    earlyMakers.value.forEach((ele) => {
      try {
        if (ele instanceof Feature) {
          // 如果是OpenLayers Feature
          const source = mapUtils.getVectorSource();
          if (source) {
            source.removeFeature(ele);
          }
        } else if (props.map) {
          // 任何其他类型的覆盖物，统一使用removeOverlay方法
          try {
            props.map.removeOverlay(ele);
          } catch (e) {
            // 如果removeOverlay方法不存在或失败，尝试使用备用方法
            try {
              (props.map as any).removeOverLay?.(ele);
            } catch (innerError) {
              console.error('备用移除方法也失败:', innerError);
            }
          }
        }
      } catch (error) {
        console.error('移除早期标记失败:', error);
      }
    });
    earlyMakers.value = [];
  }
  
  const T = window.T;
  
  // 如果T不存在，则使用OpenLayers绘制
  if (!T) {
    try {
      for (let i = 0; i < warning.length; i++) {
        if (warning[i].display && warning[i].regions) {
          try {
            const points = JSON.parse(warning[i].regions);
            
            for (let j = 0; j < points.length; j++) {
              const coordinates = points[j].map((p: any) => [p.lng, p.lat]);
              
              // 创建多边形样式
              const polygonStyle = new Style({
                fill: new Fill({
                  color: ensureColorWithOpacity(warning[i].fillColor)
                }),
                stroke: new Stroke({
                  color: ensureColorWithOpacity(warning[i].borderColor),
                  width: warning[i].borderWidth || 2,
                  lineDash: warning[i].borderType === 1 ? undefined : [5, 5]
                })
              });
              
              // 创建多边形要素
              const polygonFeature = new Feature({
                geometry: new olGeom.Polygon([coordinates])
              });
              polygonFeature.setStyle(polygonStyle);
              
              // 将当前项的数据存储在要素的属性中
              polygonFeature.set('envData', {
                title: warning[i].name,
                type: warning[i].envType ? warning[i].envType : '',
                remark: warning[i].remark
              });
              
              // 添加到图层
              mapUtils.addFeature(polygonFeature);
              earlyMakers.value.push(polygonFeature);
            }
          } catch (error) {
            console.error("解析区域数据失败", error);
          }
        } else if (warning[i].regions2) {
          try {
            const points = JSON.parse(warning[i].regions2);
            
            for (let j = 0; j < points.length; j++) {
              const coordinates = points[j].map((p: any) => [p.lng, p.lat]);
              
              // 创建线条样式
              const lineStyle = new Style({
                stroke: new Stroke({
                  color: ensureColorWithOpacity(warning[i].borderColor),
                  width: warning[i].borderWidth || 2,
                  lineDash: warning[i].borderType === 1 ? undefined : [5, 5]
                })
              });
              
              // 创建线条要素
              const lineFeature = new Feature({
                geometry: new olGeom.LineString(coordinates)
              });
              lineFeature.setStyle(lineStyle);
              
              // 将当前项的数据存储在要素的属性中
              lineFeature.set('envData', {
                title: warning[i].name,
                type: warning[i].envType ? warning[i].envType : '',
                remark: warning[i].remark
              });
              
              // 添加到图层
              mapUtils.addFeature(lineFeature);
              earlyMakers.value.push(lineFeature);
            }
          } catch (error) {
            console.error("解析regions2数据失败", error);
          }
        }
      }
      
      // 为地图添加鼠标悬停事件
      if (props.map && typeof props.map.on === 'function') {
        // 移除之前的事件监听器
        props.map.un('pointermove', handleMapPointerMove);
        // 添加新的事件监听器
        props.map.on('pointermove', handleMapPointerMove);
      }
    } catch (error) {
      console.error("使用OpenLayers绘制环境失败:", error);
    }
    return;
  }
  
  // 天地图绘制方式
  for (let i = 0; i < warning.length; i++) {
    if (warning[i].display) {
      try {
        const points = JSON.parse(warning[i].regions);
        const currentItemData = warning[i]; // 保存当前条目数据，避免闭包问题
        
        for (let j = 0; j < points.length; j++) {
          const polygon = new T.Polygon(points, {
            color: ensureColorWithOpacity(currentItemData.borderColor),
            weight: currentItemData.borderWidth,
            fillColor: ensureColorWithOpacity(currentItemData.fillColor),
          });
          
          // 使用try-catch处理可能不兼容的方法
          try {
            props.map.addOverlay(polygon);
          } catch (e) {
            // 如果addOverlay不可用，尝试使用备用方法
            try {
              (props.map as any).addOverLay?.(polygon);
            } catch (innerError) {
              console.error('备用添加方法也失败:', innerError);
            }
          }
          
          earlyMakers.value.push(polygon);
          
          // 鼠标悬停事件 - 使用全局InfoWindow
          polygon.addEventListener("mouseover", (e: any) => {
            emitter.emit('show-info-window', {
              title: currentItemData.name,
              type: currentItemData.envType ? currentItemData.envType : '',
              remark: currentItemData.remark,
              x: e.containerPoint.x,
              y: e.containerPoint.y - 10, // 稍微上移避免鼠标遮挡
              autoClose: false
            });
          });
          
          // 鼠标移出事件
          polygon.addEventListener("mouseout", () => {
            emitter.emit('hide-info-window');
          });
        }
      } catch (error) {
        console.error("解析区域数据失败", error);
      }
    } else if (warning[i].regions2) {
      try {
        const points = JSON.parse(warning[i].regions2);
        const currentItemData = warning[i]; // 保存当前条目数据，避免闭包问题
        
        for (let j = 0; j < points.length; j++) {
          const polyline = new T.Polyline(points[j], {
            color: "blue",
            weight: currentItemData.borderWidth,
          });
          
          // 使用try-catch处理可能不兼容的方法
          try {
            props.map.addOverlay(polyline);
          } catch (e) {
            // 如果addOverlay不可用，尝试使用备用方法
            try {
              (props.map as any).addOverLay?.(polyline);
            } catch (innerError) {
              console.error('备用添加方法也失败:', innerError);
            }
          }
          
          earlyMakers.value.push(polyline);
          
          // 鼠标悬停事件 - 使用全局InfoWindow
          polyline.addEventListener("mouseover", (e: any) => {
            emitter.emit('show-info-window', {
              title: currentItemData.name,
              type: currentItemData.envType ? currentItemData.envType : '',
              remark: currentItemData.remark,
              x: e.containerPoint.x,
              y: e.containerPoint.y - 10, // 稍微上移避免鼠标遮挡
              autoClose: false
            });
          });
          
          // 鼠标移出事件 - 修复mouseout事件绑定方式
          polyline.addEventListener("mouseout", () => {
            emitter.emit('hide-info-window');
          });
        }
      } catch (error) {
        console.error("解析regions2数据失败", error);
      }
    }
  }
};

// OpenLayers鼠标悬停处理函数
const handleMapPointerMove = (e: any) => {
  if (!props.map) return;
  
  // 检查当前指针位置是否有Feature
  const feature = props.map.forEachFeatureAtPixel?.(e.pixel, (feature: any) => feature);
  
  if (feature) {
    // 获取Feature上存储的环境数据
    const envData = feature.get('envData');
    if (envData) {
      // 使用事件总线显示信息窗口
      emitter.emit('show-info-window', {
        ...envData,
        x: e.originalEvent.clientX,
        y: e.originalEvent.clientY - 10, // 稍微上移避免鼠标遮挡
        autoClose: false
      });
    }
  } else {
    // 隐藏信息窗口
    emitter.emit('hide-info-window');
  }
};

/**
 * 颜色工具函数 - 确保填充颜色有50%的透明度
 * @param color 输入的颜色 (hex, rgb, rgba)
 * @returns 带50%透明度的rgba颜色
 */
function ensureColorWithOpacity(color: string | null): string {
  if (!color) return 'rgba(64, 158, 255, 0.5)'; // 默认蓝色，50%透明
  
  // 已经是rgba格式，修改透明度为0.5
  if (color.startsWith('rgba')) {
    // 提取 rgba 中的 r, g, b 值
    const match = color.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]+\s*\)/);
    if (match) {
      const [, r, g, b] = match;
      return `rgba(${r}, ${g}, ${b}, 0.5)`;
    }
  }
  
  // rgb格式转rgba
  if (color.startsWith('rgb(')) {
    // 提取 rgb 中的 r, g, b 值
    const match = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
    if (match) {
      const [, r, g, b] = match;
      return `rgba(${r}, ${g}, ${b}, 0.5)`;
    }
  }
  
  // 十六进制格式转rgba
  if (color.startsWith('#')) {
    let r = 0, g = 0, b = 0;
    // #RGB 格式
    if (color.length === 4) {
      r = parseInt(color[1] + color[1], 16);
      g = parseInt(color[2] + color[2], 16);
      b = parseInt(color[3] + color[3], 16);
    } 
    // #RRGGBB 格式
    else if (color.length === 7) {
      r = parseInt(color.substring(1, 3), 16);
      g = parseInt(color.substring(3, 5), 16);
      b = parseInt(color.substring(5, 7), 16);
    }
    return `rgba(${r}, ${g}, ${b}, 0.5)`;
  }
  
  // 其他情况，返回默认颜色
  return 'rgba(64, 158, 255, 0.5)';
}

// 在组件挂载时初始化数据
onMounted(() => {
  place.value = Place;
  
  // 如果地图已加载，获取全部通航环境列表
  if (props.map) {
    getwholeEnv();
  }
  
  // 初始化图层控制
  if (props.map) {
    createMapLayers()
  }
})

// 监听地图实例变化
watch(() => props.map, (newMap) => {
  // 当地图实例加载完成后，获取环境数据
  if (newMap) {
    getwholeEnv();
  }
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  stopPickingCoordinates()

  // 清理图层和相关资源
  if (props.map) {
    Object.values(mapLayers).forEach(layer => {
      if (layer) {
        // 清理图层的源数据
        const source = layer.getSource && layer.getSource();
        if (source) {
          // 清理矢量源的要素
          if (typeof source.clear === 'function') {
            source.clear();
          }
          // 清理瓦片源的缓存
          if (typeof source.refresh === 'function') {
            source.refresh();
          }
        }

        // 从地图中移除图层
        props.map!.removeLayer(layer);
      }
    });

    // 清空图层对象
    Object.keys(mapLayers).forEach(key => {
      delete mapLayers[key];
    });
  }

  // 清理emitter事件监听器
  if (window.emitter) {
    window.emitter.off('layer-visibility-change');
    window.emitter.off('map-type-change');
    window.emitter.off('ship-legend-toggle');
  }

  console.log('Right-content组件清理完成');
})
</script>

<style scoped>
.btn-style {
  width: 80px !important;
}

.padding-style {
  padding: 15px;
}

/* 更改tabs切换标签下的蓝色下划线 */
:deep(.el-tabs__active-bar) {
  background-color: #5fd3fc !important;
}

/* 改变 element的el-tabs默认选中项 文字颜色 */
:deep(.el-tabs__item.is-active) {
  color: #5fd3fc !important;
}

:deep(.el-tabs .el-tabs__nav-scroll) {
  width: 300px !important;
  background-color: transparent !important;
  color: #ffffff;
}

:deep(.el-button--primary:hover),
:deep(.el-button--primary:focus) {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  background: rgb(103, 113, 183) !important;
  border: 0;
  box-shadow: 0 0 3px gray;
}

:deep(.el-button--primary) {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  background: rgb(60, 65, 103) !important;
  border: 0;
  box-shadow: 0 0 3px gray;
}

:deep(.el-checkbox) {
  color: #ffffff;
}

.content-right-btn {
  position: absolute;
  right: 20px;
  top: 100px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-item {
  margin: 5px 20px;
  position: relative;
}

.pointBox {
  position: absolute;
  padding: 10px;
  width: 298px;
  background: rgba(60, 65, 103, 0.95);
  color: white;
  box-shadow: 0 0 7px 1px rgba(0, 0, 0, 0.55);
  overflow: hidden;
  top: 0px;
  left: -300px;
  z-index: 1000;
}

.pointBox_head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.pointBox_head img {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.pointBox_head_left {
  display: flex;
  align-items: center;
  color: #ffffff;
}

.pointBox_head_left>img {
  margin-right: 10px;
}

.pointBox_inpBox {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #ffffff;
  margin-bottom: 14px;
}

.pointBox_inpBox>span {
  flex-shrink: 0;
}

.pointBox_inpBox_inp {
  width: 214px;
  margin-left: 14px;
}

.inp-right {
  margin-left: 5px;
  padding: 0 5px;
  width: 68px;
  height: 24px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  line-height: 24px;
  text-align: center;
}

.btns {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 10px;
}

.btns button {
  background: #5fd3fc;
  border: none;
  color: white;
  padding: 5px 15px;
  cursor: pointer;
  margin-left: 10px;
  border-radius: 2px;
}

.btns button:hover {
  background: #4fc3ee;
}

/* 新增 - 地图类型面板样式 */
.map-types-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 10px;
}

.map-type-item {
  width: 48%;
  background-color: rgba(60, 65, 103, 0.7);
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 2px solid transparent;
}

.map-type-item:hover {
  background-color: rgba(60, 65, 103, 0.9);
}

.map-type-item.active {
  border-color: #5fd3fc;
  background-color: rgba(95, 211, 252, 0.2);
}

.map-type-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #fff;
}

.map-type-label {
  font-size: 14px;
  color: #fff;
}

/* 新增 - 图层控制面板样式 */
.layers-panel {
  width: 280px;
}

.layer-control-title {
  font-size: 14px;
  font-weight: bold;
  color: #5fd3fc;
  margin: 10px 0;
  border-bottom: 1px solid rgba(95, 211, 252, 0.3);
  padding-bottom: 5px;
}

.layer-control-item {
  padding: 8px 0;
}

/* 新增 - 船舶图例面板样式 */
.ship-legend-panel {
  width: 300px;
  max-height: 400px;
  overflow-y: auto;
}

.ship-legend-title {
  font-size: 14px;
  font-weight: bold;
  color: #5fd3fc;
  margin: 10px 0 15px 0;
  border-bottom: 1px solid rgba(95, 211, 252, 0.3);
  padding-bottom: 8px;
  text-align: center;
}

.ship-legend-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px 16px;
  padding: 0 8px;
}

.ship-legend-item {
  display: flex;
  align-items: center;
  padding: 8px 6px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  cursor: default;
}

.ship-legend-item:hover {
  background-color: rgba(95, 211, 252, 0.1);
}

.ship-legend-color {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.ship-legend-item span {
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 - 小屏幕时单列显示 */
@media (max-width: 1200px) {
  .ship-legend-items {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .ship-legend-panel {
    width: 260px;
  }
}

/* 新增 - 点选提示样式 */
.pointTips {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 10000;
}

.pointTips > div:first-child {
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 5px;
}

.round {
  width: 10px;
  height: 10px;
  background-color: #f00;
  border-radius: 50%;
  position: relative;
}

.round::before {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.3);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip::after {
  position: absolute;
  z-index: 1000;
  display: none;
  padding: 5px 8px;
  font-size: 12px;
  color: #fff;
  text-align: center;
  white-space: pre;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 3px;
  content: attr(title);
}

.tooltip:hover::after {
  display: block;
  transform: translateX(-50%);
  bottom: -30px;
  left: 50%;
}
</style>



