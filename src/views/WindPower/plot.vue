<template>
  <div style="position: relative">
    <div class="content">
      <!-- <el-tabs type="border-card" class="eltabs">
        <el-tab-pane label="标绘"> -->
      <div class="flex-column">
        <!-- <div class="flex-row" style="justify-content: space-between">
          <div class="content-txt1">标绘</div>
          <div @click="handToParentClose()">
            <i class="el-icon-circle-close" style="color: #409eff"></i>
          </div>
        </div>
        <div class="line"></div> -->
        <div class="btns">
          <el-button type="primary" :icon="Plus" @click="addGroup"
            >新增分组</el-button
          >
          <!-- <el-button type="primary" style="width: 100px" @click="add"
                >新增标绘</el-button
              > -->

          <el-dropdown @command="handleCommand">
            <el-button type="primary">
              新增标绘<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="1">点</el-dropdown-item>
                <el-dropdown-item :command="2">圆</el-dropdown-item>
                <el-dropdown-item :command="3">线</el-dropdown-item>
                <el-dropdown-item :command="4">矩形</el-dropdown-item>
                <el-dropdown-item :command="5">多边形</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <!-- <div class="line"></div> -->
        <div class="shipContent">
          <el-collapse>
            <el-collapse-item
              v-for="item in list"
              :key="item.id"
              :name="item.id"
            >
              <template #title>
                <div style="display: flex; align-items: center;width: 100%;">
                  <div
                    style="
                      width: 100%;
                      height: 100%;
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    "
                    @click="changeCollapse(item)"
                  >
                    <!-- <div class="envName">{{ item.name }}</div> -->
                    <div style="display: flex; align-items: center">
                      <el-icon class="iconcolor" :class="{ active: item.isActive }" style="margin: 0 5px">
                        <ArrowRight />
                      </el-icon>
                      <div class="envName">{{ item.name }}</div>
                    </div>
                    <div class="el-collapse-right">
                      <el-icon class="iconfont" @click.stop="editGroup(item)"><Edit /></el-icon>
                      <el-icon class="iconfont" @click.stop="delGroup(item)"><Delete /></el-icon>
                    </div>
                    <!-- <div class="envFun">
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="修改"
                        placement="bottom"
                      >
                        <i
                          class="el-icon-edit"
                          @click.stop="editGroup(item)"
                        ></i>
                      </el-tooltip>
                    </div>
                    <div class="envFun">
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="删除"
                        placement="bottom"
                      >
                        <i
                          class="el-icon-delete"
                          @click.stop="delGroup(item)"
                        ></i>
                      </el-tooltip>
                    </div> -->
                  </div>
                </div>

                <!-- <el-button type="text" @click="delGroup(index)"
                    >删除</el-button
                  > -->
              </template>

              <div v-for="ite in item.list" :key="ite.id" class="envItem">
                <div>{{ ite.name }}</div>
                <div style="display: flex; align-items: center">
                  <div class="envFun">
                    <el-tooltip
                      effect="dark"
                      content="修改"
                      placement="bottom"
                    >
                      <el-icon @click.stop="handToEdit(ite)"><Edit /></el-icon>
                    </el-tooltip>
                  </div>
                  <div class="envFun">
                    <el-tooltip
                      effect="dark"
                      content="删除"
                      placement="bottom"
                    >
                      <el-icon @click.stop="handToDelete(item, ite)"><Delete /></el-icon>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <div
                class="envloading"
                @click="nextPage(item)"
                v-if="item.list.length < item.envtotal"
              >
                点击加载更多
              </div>
              <!-- <div
                    v-for="(ite, idx) in item.list"
                    :key="idx"
                    @click="setCenter(ite)"
                    class="listItem"
                  >
                    <div>{{ ite.name }}</div>
                    <el-button type="text" @click="delItem(index, ite, idx)"
                      >删除</el-button
                    >
                  </div> -->
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
      <!-- </el-tab-pane>
      </el-tabs> -->
      <div class="addPlotting" v-if="isAdd">
        <div class="addPlotting_head">
          <div style="display: flex; align-items: center">
            <img src="@/assets/images/wenjian.png" alt="" />
            <div class="addPlotting_head_title">创建标绘</div>
          </div>
          <div class="close" @click="cancelDraw">
            <img src="@/assets/images/guanbi.png" alt="" />
          </div>
        </div>
        <div class="addPlotting_item">
          名称
          <el-input
            class="elWidth"
            v-model="form.name"
            placeholder="请输入内容"
          ></el-input>
        </div>
        <div class="addPlotting_item">
          分组
          <el-select
            v-model="form.plotType"
            placeholder="请选择"
            class="elWidth"
          >
            <el-option
              v-for="item in list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div style="display: flex">
          <el-button type="primary" size="small" @click="savePlot"
            >保存</el-button
          >
          <el-button type="info" plain size="small" @click="cancelDraw"
            >取消</el-button
          >
        </div>
      </div>
    </div>

    <el-dialog
      title="分组"
      v-model="isGroup"
      width="30%"
      :before-close="cancelGroup"
      :modal="false"
    >
      <div class="typeBox">
        <div>
          <div class="typeBox_title">名称：</div>
          <el-input
            class="elinp"
            placeholder="请输入内容"
            v-model="groupForm.name"
          ></el-input>
        </div>
        <div>
          <div class="typeBox_title">key：</div>
          <el-input
            class="elinp"
            placeholder="请输入内容（唯一）"
            v-model="groupForm.plotKey"
          ></el-input>
        </div>
        <div>
          <div class="typeBox_title">备注：</div>
          <el-input
            class="elinp"
            placeholder="请输入内容"
            v-model="groupForm.remark"
          ></el-input>
        </div>
        <div>
          <div class="typeBox_title">排序：</div>
          <el-input
            class="elinp"
            type="Number"
            placeholder="请输入内容"
            v-model="groupForm.sort"
          ></el-input>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelGroup">取 消</el-button>
          <el-button type="primary" @click="subGroup">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <div v-if="isAdd">
      <drawTools @Tools="setTools" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, defineProps, defineEmits } from 'vue'
import { Plus, ArrowDown, ArrowRight, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import drawTools from "./drawingTools.vue"
import {
  plotType,
  plotTypeList,
  editPlotType,
  addplot,
  plotList,
  delPlot,
  delPlotType,
  editPlot,
} from "@/api/map/map"

// 全局声明天地图对象
declare const window: Window & {
  T: any
}
const T = window.T

// TypeScript 接口定义
interface PlotForm {
  id?: number
  name: string | null
  plotType: number | null
  borderColor: string | null
  borderType: number | null
  borderWidth: number | null
  fillColor: string | null
  regions: string | null
  remark: string | null
  graphType: string | null
  icon: number | null
}

interface GroupForm {
  id?: number
  name: string | null
  plotKey: string | null
  remark: string | null
  sort: number | null
}

interface PlotParams {
  plotType: number | null
  pageNum: number
  pageSize: number
}

interface ToolsOptions {
  imgUrl: number
  borderColor: string
  borderWidth: number
  isSolid: boolean
  paddColor: string | null
}

interface ListItem {
  id: number
  name: string
  list: any[]
  envtotal: number
  isShow: boolean
  isActive: boolean
  plotTotal?: number
}

// 定义props
const props = defineProps<{
  map: any
  status: any
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'handToClose', value: string): void
  (e: 'renewData', value: string): void
}>()

// 响应式状态
const isGroup = ref(false) // 是否打开新增分组弹窗
const isAdd = ref(false) // 是否打开新增标绘弹窗
const isTools = ref(true)
const isSub = ref(true) // 当新增一个标绘，没有保存时，不能继续新增
const activeName = ref("first")
const isShow = ref(true)
const total = ref<number | null>(null)
const type = ref<number | null>(null)
const onGroupName = ref("")
const tabName = ref("")

// 标绘相关
const marker = ref<any>(null)
const markers = ref<any[]>([])
const circle = ref<any>(null)
const line = ref<any>(null)
const rect = ref<any>(null)
const polygon = ref<any>(null)
let circleTool = ref<any>(null) // 画圆工具
let handler = ref<any>(null) // 画线工具
let rectTool = ref<any>(null) // 画矩形工具

// 表单数据
const form = reactive<PlotForm>({
  name: null,
  plotType: null,
  borderColor: null,
  borderType: null,
  borderWidth: null,
  fillColor: null,
  regions: null,
  remark: null,
  graphType: null,
  icon: null,
})

const groupForm = reactive<GroupForm>({
  name: null,
  plotKey: null,
  remark: null,
  sort: null,
})

// 绘制工具配置
const tools = reactive<ToolsOptions>({
  imgUrl: 1,
  borderColor: "#409EFF", // 线框颜色
  borderWidth: 3,
  isSolid: true,
  paddColor: null,
})

// 查询参数
const plotParams = reactive<PlotParams>({
  plotType: null,
  pageNum: 1,
  pageSize: 500,
})

// 列表数据
const list = ref<ListItem[]>([])
const plots = ref<any[]>([]) // 全部标绘
const plotMarkers = ref<any[]>([]) // 地图上的标绘标记
const onPlot = reactive<any>({})

// 默认属性
const defaultProps = {
  children: "children",
  label: "label",
}

// 监听工具变化
watch(() => isTools.value, (val) => {
  if (type.value == 3) {
    setLineTool()
  } else if (type.value == 2) {
    setCircleTool()
  } else if (type.value == 4) {
    setRectangleTool()
  } else if (type.value == 5) {
    setPolygonTool()
  }
})

// 获取标绘类型列表
const getplotTypeList = () => {
  plotTypeList().then((res: any) => {
    if (res.code == 200) {
      res.rows.map((ele: any) => {
        ele.list = []
        ele.envtotal = 0
        ele.isShow = false
        ele.isActive = false
      })
      list.value = res.rows
      total.value = res.total
    }
  })
}

// 组件加载时
onMounted(() => {
  getplotTypeList()
})

// 设置线工具
const setLineTool = () => {
  handler.value.close()
  handler.value = new T.PolylineTool(props.map, {
    color: tools.borderColor, // 画线颜色
    weight: tools.borderWidth,
    lineStyle: tools.isSolid ? "solid" : "dashed",
  })
  handler.value.open()
}

// 设置圆工具
const setCircleTool = () => {
  circleTool.value.close()
  circleTool.value = new T.CircleTool(props.map, {
    color: tools.borderColor,
    weight: tools.borderWidth,
    opacity: 0.5,
    fillColor: tools.paddColor,
    fillOpacity: 0.5,
    lineStyle: tools.isSolid ? "solid" : "dashed",
  })
  circleTool.value.open()
}

// 设置矩形工具
const setRectangleTool = () => {
  rectTool.value.close()
  rectTool.value = new T.RectangleTool(props.map, {
    color: tools.borderColor,
    weight: tools.borderWidth,
    opacity: 0.5,
    fillColor: tools.paddColor,
    fillOpacity: 0.5,
    lineStyle: tools.isSolid ? "solid" : "dashed",
  })
  rectTool.value.open()
}

// 设置多边形
const setPolygonTool = () => {
  polygon.value.close()
  polygon.value = new T.PolygonTool(props.map, {
    color: tools.borderColor,
    weight: tools.borderWidth,
    opacity: 0.5,
    fillColor: tools.paddColor,
    fillOpacity: 0.5,
    lineStyle: tools.isSolid ? "solid" : "dashed",
  })
  polygon.value.open()
}

// 关闭弹窗
const handToParentClose = () => {
  emit("handToClose", "plot")
}

// 新增分组
const addGroup = () => {
  groupForm.name = null
  groupForm.plotKey = null
  groupForm.remark = null
  groupForm.sort = null
  delete groupForm.id
  isGroup.value = true
}

// 取消新增分组
const cancelGroup = () => {
  groupForm.name = null
  groupForm.plotKey = null
  groupForm.remark = null
  groupForm.sort = null
  delete groupForm.id
  isGroup.value = false
}

// 提交分组
const subGroup = () => {
  if (!groupForm.id && groupForm.id != 0) {
    plotType(groupForm).then((res: any) => {
      if (res.code == 200) {
        ElMessage({
          message: "操作成功",
          type: "success",
        })
        isGroup.value = false
        getplotTypeList()
      }
    })
  } else {
    editPlotType(groupForm).then((res: any) => {
      if (res.code == 200) {
        ElMessage({
          message: "操作成功",
          type: "success",
        })
        isGroup.value = false
        getplotTypeList()
      }
    })
  }
}

// 修改分组
const editGroup = (val: any) => {
  Object.assign(groupForm, val)
  isGroup.value = true
}

// 删除分组
const delGroup = (val: any) => {
  ElMessageBox.confirm(`是否确认删除"${val.name}"？`).then(() => {
    return delPlotType(val.id)
  }).then(() => {
    ElMessage.success("删除成功")
    getplotTypeList()
  })
}

// 处理下拉命令
const handleCommand = (command: number | string) => {
  if (isSub.value) {
    form.graphType = command as string
    isAdd.value = true
    isSub.value = false
    draw()
  } else {
    ElMessage({
      message: "请先保存或取消正在编辑的标绘",
      type: "warning",
    })
  }
}

// 取消绘制
const cancelDraw = () => {
  if (handler.value) {
    handler.value.close()
  }
  if (circleTool.value) {
    circleTool.value.close()
  }
  if (rectTool.value) {
    rectTool.value.close()
  }
  if (polygon.value) {
    polygon.value.close()
  }
  resetting()
}

/**
 * 颜色工具函数 - 确保填充颜色有50%的透明度
 * @param color 输入的颜色 (hex, rgb, rgba)
 * @returns 带50%透明度的rgba颜色
 */
function ensureColorWithOpacity(color: string | null): string {
  if (!color) return 'rgba(64, 158, 255, 0.5)'; // 默认蓝色，50%透明
  
  // 已经是rgba格式，修改透明度为0.5
  if (color.startsWith('rgba')) {
    // 提取 rgba 中的 r, g, b 值
    const match = color.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]+\s*\)/);
    if (match) {
      const [, r, g, b] = match;
      return `rgba(${r}, ${g}, ${b}, 0.5)`;
    }
  }
  
  // rgb格式转rgba
  if (color.startsWith('rgb(')) {
    // 提取 rgb 中的 r, g, b 值
    const match = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
    if (match) {
      const [, r, g, b] = match;
      return `rgba(${r}, ${g}, ${b}, 0.5)`;
    }
  }
  
  // 十六进制格式转rgba
  if (color.startsWith('#')) {
    let r = 0, g = 0, b = 0;
    // #RGB 格式
    if (color.length === 4) {
      r = parseInt(color[1] + color[1], 16);
      g = parseInt(color[2] + color[2], 16);
      b = parseInt(color[3] + color[3], 16);
    } 
    // #RRGGBB 格式
    else if (color.length === 7) {
      r = parseInt(color.substring(1, 3), 16);
      g = parseInt(color.substring(3, 5), 16);
      b = parseInt(color.substring(5, 7), 16);
    }
    return `rgba(${r}, ${g}, ${b}, 0.5)`;
  }
  
  // 其他情况，返回默认颜色
  return 'rgba(64, 158, 255, 0.5)';
}

// 开始绘制
const draw = () => {
  if (form.graphType == 1) {
    // 点
    props.map.addEventListener("click", (e: any) => {
      setMark(e.lnglat)
    })
  } else if (form.graphType == 2) {
    // 圆
    circleTool.value = new T.CircleTool(props.map, {
      color: tools.borderColor,
      weight: tools.borderWidth,
      opacity: 0.5,
      fillColor: ensureColorWithOpacity(tools.paddColor),
      fillOpacity: 0.5,
      lineStyle: tools.isSolid ? "solid" : "dashed",
    })
    circleTool.value.open()
    circleTool.value.addEventListener("drawend", (e: any) => {
      circleTool.value.clear()
      addCircle(e.currentCenter, e.currentRadius)
    })
  } else if (form.graphType == 3) {
    // 线
    handler.value = new T.PolylineTool(props.map, {
      color: tools.borderColor, // 画线颜色
      weight: tools.borderWidth,
      lineStyle: tools.isSolid ? "solid" : "dashed",
    })
    handler.value.open() // 开启
    handler.value.addEventListener("draw", (e: any) => {
      handler.value.clear()
      addLine(e.currentLnglats)
    })
  } else if (form.graphType == 4) {
    // 矩形
    rectTool.value = new T.RectangleTool(props.map, {
      color: tools.borderColor,
      weight: tools.borderWidth,
      opacity: 0.5,
      fillColor: ensureColorWithOpacity(tools.paddColor),
      fillOpacity: 0.5,
      lineStyle: tools.isSolid ? "solid" : "dashed",
    })
    rectTool.value.open()
    rectTool.value.addEventListener("draw", (e: any) => {
      rectTool.value.clear()
      console.log(e.currentBounds)
      addRect(e.currentBounds)
    })
  } else if (form.graphType == 5) {
    // 多边形
    polygon.value = new T.PolygonTool(props.map, {
      color: tools.borderColor,
      weight: tools.borderWidth,
      opacity: 0.5,
      fillColor: ensureColorWithOpacity(tools.paddColor),
      fillOpacity: 0.5,
      lineStyle: tools.isSolid ? "solid" : "dashed",
    })
    polygon.value.open()
    polygon.value.addEventListener("draw", (e: any) => {
      polygon.value.clear()
      addPoly(e.currentLnglats)
    })
  }
}

// 添加点
const setMark = (points: any) => {
  props.map.removeEventListener("click")
  // 创建图片对象
  const icon = new T.Icon({
    iconUrl: setimg(tools.imgUrl),
    iconSize: new T.Point(20, 20),
  })
  // 向地图上添加自定义标注
  marker.value = new T.Marker(points, {
    icon: icon,
  })
  props.map.addOverLay(marker.value)
  form.regions = JSON.stringify(points)
}

// 添加圆
const addCircle = (center: any, radius: number) => {
  circle.value = new T.Circle(new T.LngLat(center.lng, center.lat), radius, {
    color: tools.borderColor,
    weight: tools.borderWidth,
    opacity: 0.5,
    fillColor: tools.paddColor,
    fillOpacity: 0.5,
    lineStyle: tools.isSolid ? "solid" : "dashed",
  })
  // 向地图上添加圆
  props.map.addOverLay(circle.value)
  const obj = {
    p: center,
    r: radius,
  }
  form.regions = JSON.stringify(obj)
}

// 添加线
const addLine = (points: any) => {
  // 创建线对象
  line.value = new T.Polyline(points, {
    color: tools.borderColor,
    weight: tools.borderWidth,
    lineStyle: tools.isSolid ? "solid" : "dashed",
  })
  form.regions = JSON.stringify(points)
  props.map.addOverLay(line.value)
}

// 添加矩形
const addRect = (points: any) => {
  const obj = {
    s: {
      lat: points.Lq.lat,
      lng: points.Lq.lng,
    },
    e: {
      lat: points.kq.lat,
      lng: points.kq.lng,
    },
  }
  const bounds = new T.LngLatBounds(points)
  rect.value = new T.Rectangle(bounds, {
    color: tools.borderColor,
    weight: tools.borderWidth,
    opacity: 0.5,
    fillColor: tools.paddColor,
    fillOpacity: 0.5,
    lineStyle: tools.isSolid ? "solid" : "dashed",
  })
  form.regions = JSON.stringify(obj)
  props.map.addOverLay(rect.value)
}

// 添加多边形
const addPoly = (points: any) => {
  polygon.value = new T.Polygon(points, {
    color: tools.borderColor,
    weight: tools.borderWidth,
    opacity: 0.5,
    fillColor: tools.paddColor,
    fillOpacity: 0.5,
    lineStyle: tools.isSolid ? "solid" : "dashed",
  })
  // 向地图上添加面
  props.map.addOverLay(polygon.value)
  form.regions = JSON.stringify(points)
}

// 设置中心点
const setCenter = (item: any) => {
  if (item.type == 1) {
    props.map.centerAndZoom(item.point)
  } else if (item.type == 2) {
    props.map.centerAndZoom(item.point[0])
  } else if (item.type == 3) {
    props.map.centerAndZoom(item.point.Lq)
  }
}

// 删除标绘
const delItem = (index: number, item: any, idx: number) => {
  props.map.removeOverLay(item.origin)
  list.value[index].list.splice(idx, 1)
}

// 重置
const resetting = () => {
  onGroupName.value = ""
  type.value = null
  tabName.value = ""
  isAdd.value = false
  isSub.value = true
  Object.assign(tools, {
    imgUrl: 1,
    borderColor: "#409EFF", // 线框颜色
    borderWidth: 3,
    isSolid: true,
    paddColor: null,
  })
}

// 接收设置的绘制工具选项值
const setTools = (val: any) => {
  Object.assign(tools, val)
  isTools.value = !isTools.value
}

// 保存
const savePlot = () => {
  if (!form.id && form.id != 0) {
    if (form.regions && form.name && form.plotType) {
      form.borderColor = tools.borderColor
      form.borderWidth = tools.borderWidth
      form.fillColor = tools.paddColor
      form.borderType = tools.isSolid ? 1 : 0
      if (form.graphType == "1") {
        form.icon = tools.imgUrl
      }

      addplot(form).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success("操作成功")
          isAdd.value = false
          isSub.value = true
          cleanMap() // 提交成功后清理地图
          emit('renewData', 'plot')
        }
      })
    } else {
      if (!form.regions) {
        ElMessage.warning("请完成绘制")
        return
      }
      if (!form.name) {
        ElMessage.warning("请输入名称")
        return
      }
      if (!form.plotType) {
        ElMessage.warning("请选择分组")
        return
      }
    }
  } else {
    editPlot(form).then((res: any) => {
      ElMessage.success("操作成功")
      isAdd.value = false
      isSub.value = true
      emit('renewData', 'plot')
    })
  }
}

// 切换折叠面板
const changeCollapse = (val: any) => {
  val.isShow = !val.isShow
  val.isActive = !val.isActive
  if (val.isShow) {
    console.log(val)
    plotParams.plotType = val.id
    getplotList(val)
  }
}

// 获取标绘列表
const getplotList = (obj: any) => {
  plotList(plotParams).then((res: any) => {
    if (res.code == 200) {
      if (plotParams.pageNum == 1) {
        obj.list = res.rows
      } else {
        obj.list.push(...res.rows)
      }
      obj.plotTotal = res.total
    }
  })
}

// 加载更多
const nextPage = (item: any) => {
  plotParams.pageNum += 1
  getplotList(item)
}

// 获取全部标绘
const getwholePlot = () => {
  plotList().then((res: any) => {
    if (res.code == 200) {
      plots.value = res.rows
      setplotMap()
    }
  })
}

// 删除标绘
const handToDelete = (item: any, ite: any) => {
  ElMessageBox.confirm(`是否确认删除"${ite.name}"？`).then(() => {
    return delPlot(ite.id)
  }).then(() => {
    ElMessage.success("删除成功")
    plotParams.pageNum = 1
    plotParams.plotType = item.id
    getplotList(item)
    emit('renewData', 'plot')
  })
}

// 修改标绘
const handToEdit = (val: any) => {
  isAdd.value = true
  Object.assign(form, val)
}

// 绘图
const setplotMap = () => {
  for (let i = 0; i < plots.value.length; i++) {
    if (plots.value[i].graphType == 1) {
      // 点
      const points = JSON.parse(plots.value[i].regions)
      // 创建图片对象
      const icon = new T.Icon({
        iconUrl: setimg(plots.value[i].icon),
        iconSize: new T.Point(20, 20),
      })
      // 向地图上添加自定义标注
      const marker = new T.Marker(points, {
        icon: icon,
      })
      props.map.addOverLay(marker)
      plotMarkers.value.push(marker)
    } else if (plots.value[i].graphType == 2) {
      // 圆
      const obj = JSON.parse(plots.value[i].regions)
      const circle = new T.Circle(new T.LngLat(obj.p.lng, obj.p.lat), obj.r, {
        color: plots.value[i].borderColor,
        weight: plots.value[i].borderWidth,
        opacity: 0.5,
        fillColor: plots.value[i].paddColor,
        fillOpacity: 0.5,
        lineStyle: plots.value[i].isSolid ? "solid" : "dashed",
      })
      // 向地图上添加圆
      props.map.addOverLay(circle)
      plotMarkers.value.push(circle)
    } else if (plots.value[i].graphType == 3) {
      // 线
      const points = JSON.parse(plots.value[i].regions)
      const line = new T.Polyline(points, {
        color: plots.value[i].borderColor,
        weight: plots.value[i].borderWidth,
        lineStyle: plots.value[i].isSolid ? "solid" : "dashed",
      })
      props.map.addOverLay(line)
      plotMarkers.value.push(line)
    } else if (plots.value[i].graphType == 4) {
      // 矩形
      const points = JSON.parse(plots.value[i].regions)

      const bounds = new T.LngLatBounds(
        new T.LngLat(points.s.lng, points.s.lat),
        new T.LngLat(points.e.lng, points.e.lat)
      )

      const rect = new T.Rectangle(bounds, {
        color: plots.value[i].borderColor,
        weight: plots.value[i].borderWidth,
        opacity: 0.5,
        fillColor: plots.value[i].paddColor,
        fillOpacity: 0.5,
        lineStyle: plots.value[i].isSolid ? "solid" : "dashed",
      })
      plotMarkers.value.push(rect)
      props.map.addOverLay(rect)
    } else if (plots.value[i].graphType == 5) {
      // 多边形
      const points = JSON.parse(plots.value[i].regions)
      const polygon = new T.Polygon(points, {
        color: plots.value[i].borderColor,
        weight: plots.value[i].borderWidth,
        opacity: 0.5,
        fillColor: plots.value[i].paddColor,
        fillOpacity: 0.5,
        lineStyle: plots.value[i].isSolid ? "solid" : "dashed",
      })
      // 向地图上添加面
      props.map.addOverLay(polygon)
      plotMarkers.value.push(polygon)
    }
  }
}

// 设置图标
const setimg = (val: number) => {
  let str = ""
  if (val == 1) {
    str = require("@/assets/plotting/pl1.png")
  }
  return str
}

// 清理绘图工具
const cleanMap = () => {
  if (marker.value) {
    props.map.removeOverLay(marker.value)
  }
  if (circle.value) {
    props.map.removeOverLay(circle.value)
  }
  if (line.value) {
    props.map.removeOverLay(line.value)
  }
  if (rect.value) {
    props.map.removeOverLay(rect.value)
  }
  if (polygon.value) {
    props.map.removeOverLay(polygon.value)
  }
}

// 清理地图标绘
const cleanMapPlot = () => {
  plotMarkers.value.forEach((ele) => {
    props.map.removeOverLay(ele)
  })
}
</script>

<style scoped>
.content {
  width: 260px;
  height: 100%;
  display: flex;
  flex-direction: row;
}

.flex-column {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 0 10px 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #cccccc;
}

.content-txt1 {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: rgb(203, 208, 246);
}

.el-icon-circle-close {
  font-size: 22px;
}

.line {
  width: 100%;
  height: 1px;
  margin: 10px auto;
  background: #d5d5d5;
}

.btns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.iconfont {
  font-size: 20px;
  margin-left: 5px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  color: #4386c6;
}

.custom-tree-node-label {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.shipNum {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.collItem:hover {
  cursor: pointer;
}

.collItem {
  color: #409eff;
}

.addBox_list_inp {
  width: 240px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
}

input {
  width: 230px;
  border: none;
  outline: none;
}

.listItem {
  padding: 0 10px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.active {
  transform: rotate(90deg);
}
.listItem:hover {
  cursor: pointer;
}

.addPlotting {
  position: fixed;
  top: 60px;
  left: 380px;
  background-color: rgb(60 65 103) !important;
  width: 300px;
  height: 200px;
  z-index: 999;
  padding: 20px;
  color: #ffffff;
}

.addPlotting_head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.addPlotting_head img {
  width: 20px;
  height: 20px;
}

.addPlotting_head_title {
  margin-left: 20px;
  color: #ffffff;
}

.close {
  display: flex;
  align-items: center;
  justify-content: center;
}

.close:hover {
  cursor: pointer;
}

.addPlotting_item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.elWidth {
  width: 200px;
  margin-left: 10px;
}

.edit-tool {
  width: 500px;
  background: #fff;
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 9999;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 5px 10px;
}

.edit-icon {
  color: #fff;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: #409eff;
  cursor: pointer;
}

.typeBox {
  color: #ffffff;
}

.typeBox_title {
  width: 80px;
}

.typeBox > div {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.elinp {
  width: 200px;
}

.envFun {
  margin-right: 10px;
  font-size: 16px;
}

.envFun:hover {
  cursor: pointer;
}

.envItem {
  color: #ffffff;
  margin-bottom: 10px;
  padding-left: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.envloading {
  width: 100%;
  text-align: center;
  color: #ffffff;
}

.envloading:hover {
  cursor: pointer;
}

.shipContent {
  height: 700px;
  overflow: auto;
}

:deep(.el-collapse-item__arrow) {
  display: none;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
  -webkit-appearance: none;
}

/* 滚动条设置 */
/* 滚动条凹槽的颜色，还可以设置边框属性  */
::-webkit-scrollbar-track-piece { 
  background-color:#022044; 
}

/* 滚动条的宽度  */
::-webkit-scrollbar {
  width: 10px;
}

/* 滚动条的设置  */
::-webkit-scrollbar-thumb {
  background-color:#8e97d9;
  background-clip:padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color:#022044; 
}
</style>

<style src="@/assets/datePark.css" scoped></style>

