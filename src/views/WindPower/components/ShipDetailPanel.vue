<template>
  <div class="ship-detail-panel" v-if="visible">
    <div class="panel-header" :class="{ 'police-ship': isPoliceShip }">
      <h3>
        {{ isPoliceShip ? '执法船' : '船舶' }}详情
        <el-tag v-if="isPoliceShip" type="danger" size="small" class="police-tag">执法船</el-tag>
      </h3>
      <el-button type="text" @click="close">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    <div class="panel-content" v-if="shipData">
      <!-- 加载状态指示 -->
      <div v-if="isLoading" class="loading-indicator">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载船舶详情...</span>
      </div>

      <div v-if="debugMode" class="debug-info">
        {{ JSON.stringify(shipData, null, 2) }}
      </div>
      <div class="info-item">
        <span class="label">船名：</span>
        <span class="value">{{ shipName }}</span>
      </div>
      <div class="info-item">
        <span class="label">MMSI：</span>
        <span class="value">{{ shipData.mmsi || '未知' }}</span>
      </div>
      <div class="info-item">
        <span class="label">船舶类型：</span>
        <span class="value">{{ formatShipType(shipData.shipType) }}</span>
      </div>
      <div class="info-item">
        <span class="label">经度：</span>
        <span class="value">{{ formatCoordinate(shipData.lon) }}</span>
      </div>
      <div class="info-item">
        <span class="label">纬度：</span>
        <span class="value">{{ formatCoordinate(shipData.lat) }}</span>
      </div>
      <div class="info-item">
        <span class="label">航速：</span>
        <span class="value">{{ formatSpeed(shipData.sog) }}</span>
      </div>
      <div class="info-item">
        <span class="label">航向：</span>
        <span class="value">{{ formatHeading(shipData.cog) }}</span>
      </div>
      <div class="info-item">
        <span class="label">船舶状态：</span>
        <span class="value">{{ formatShipStatus(shipData.status) }}</span>
      </div>
      <div class="info-item">
        <span class="label">更新时间：</span>
        <span class="value">{{ formatTime(shipData.time) }}</span>
      </div>
      <div class="info-item">
        <span class="label">船舶类别：</span>
        <span class="value" :class="{ 'police-text': isPoliceShip }">
          {{ isPoliceShip ? '执法船' : '普通船舶' }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { Close, Loading } from '@element-plus/icons-vue';

// 调试模式
const debugMode = ref(false);

// 加载状态
const isLoading = ref(false);

// 定义props
const props = defineProps<{
  visible: boolean;
  shipData: any;
}>();

// 定义事件
const emit = defineEmits(['update:visible', 'close']);

// 监听属性变化
watch(() => props.visible, (newVal) => {
  console.log('ShipDetailPanel visible changed:', newVal);
  if (newVal) {
    // 显示面板时启动加载状态
    isLoading.value = true;

    // 异步处理数据，模拟加载过程
    nextTick(() => {
      setTimeout(() => {
        isLoading.value = false;
      }, 100); // 短暂的加载指示，提升用户体验
    });

    console.log('ShipDetailPanel data:', props.shipData);
  } else {
    isLoading.value = false;
  }
});

watch(() => props.shipData, (newVal) => {
  console.log('ShipDetailPanel shipData changed:', newVal);
  // 数据变化时重置加载状态
  if (newVal && props.visible) {
    isLoading.value = true;
    nextTick(() => {
      setTimeout(() => {
        isLoading.value = false;
      }, 100);
    });
  }
}, { deep: false }); // 使用浅监听，避免深度监听的性能开销

// 计算属性：是否为执法船
const isPoliceShip = computed(() => {
  return props.shipData?.isPolice || false;
});

// 计算船舶名称
const shipName = computed(() => {
  const data = props.shipData || {};
  return data.name || data.chineseName || data.englishName || '未知';
});

// 初始化
onMounted(() => {
  console.log('ShipDetailPanel mounted, props:', props);
});

// 格式化坐标
function formatCoordinate(value: number | undefined): string {
  if (value === undefined || value === null) return '未知';
  return typeof value === 'number' ? value.toFixed(6) : String(value);
}

// 格式化速度
function formatSpeed(speed: number | undefined): string {
  if (speed === undefined || speed === null) return '未知';
  return typeof speed === 'number' ? `${speed.toFixed(1)}节` : String(speed);
}

// 格式化航向
function formatHeading(heading: number | undefined): string {
  if (heading === undefined || heading === null) return '未知';
  return typeof heading === 'number' ? `${heading.toFixed(1)}°` : String(heading);
}

// 格式化船舶类型
function formatShipType(type: number | undefined): string {
  if (type === undefined) return '未知';
  
  if (type >= 60 && type <= 69) {
    return "客船";
  } else if (type >= 70 && type <= 74) {
    return "货船";
  } else if (type === 33) {
    return "作业船";
  } else if (type === 52) {
    return "拖船";
  } else if (type === 30) {
    return "渔船";
  } else {
    return "其他";
  }
}

// 格式化船舶状态
function formatShipStatus(status: number | undefined): string {
  if (status === undefined) return '未知';
  
  if (status === 8 || status === 0) {
    return "航行";
  } else if (status === 5 || status === 1) {
    return "停泊";
  } else {
    return "未知";
  }
}

// 格式化时间
function formatTime(timestamp: number | undefined): string {
  if (!timestamp) return '未知';
  try {
    return new Date(timestamp * 1000).toLocaleString();
  } catch (e) {
    return '时间格式错误';
  }
}

// 关闭面板
function close() {
  emit('update:visible', false);
  emit('close');
}
</script>

<style scoped lang="less">
.ship-detail-panel {
  position: absolute;
  top: 80px;
  left: 20px;
  width: 320px;
  max-width: calc(100vw - 40px);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(10px);

  /* 响应式设计 */
  @media (max-width: 768px) {
    position: fixed;
    top: 60px;
    left: 10px;
    right: 10px;
    width: auto;
    max-width: none;
  }

  @media (max-width: 480px) {
    top: 50px;
    left: 5px;
    right: 5px;
    padding: 15px;
  }
  
  .loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #409eff;
    font-size: 14px;

    .el-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }

  .debug-info {
    background: #f8f8f8;
    padding: 10px;
    margin-bottom: 15px;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    overflow-x: auto;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
    
    &.police-ship {
      background-color: rgba(245, 108, 108, 0.1);
      margin: -15px -15px 15px -15px;
      padding: 15px;
      border-bottom: 2px solid #f56c6c;
      border-radius: 4px 4px 0 0;
    }
    
    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
      display: flex;
      align-items: center;
      
      .police-tag {
        margin-left: 8px;
      }
    }
  }
  
  .panel-content {
    .info-item {
      display: flex;
      margin-bottom: 10px;
      font-size: 14px;
      
      .label {
        color: #606266;
        width: 80px;
        flex-shrink: 0;
      }
      
      .value {
        color: #303133;
        flex: 1;
        
        &.police-text {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }
  }
}
</style> 