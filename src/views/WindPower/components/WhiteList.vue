<template>
  <div class="white-list-container">
    <div class="header">
      <div class="title">白名单列表</div>
      <div class="close-btn" @click="handleClose">
        <el-icon><Close /></el-icon>
      </div>
    </div>
    
    <div class="content">
      <el-table :data="whiteListData" border stripe height="calc(100% - 70px)" v-loading="loading">
        <el-table-column prop="mmsi" label="MMSI" min-width="120" />
        <el-table-column prop="ship_name" label="船舶名称" min-width="120" />
        <el-table-column prop="sea_area" label="海域区域" min-width="130" />
        <el-table-column prop="start_time" label="开始时间" min-width="180" />
        <el-table-column prop="end_time" label="结束时间" min-width="180" />
        <el-table-column prop="type" label="类型" min-width="100">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)" size="small" effect="plain">
              {{ formatType(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="坐标" min-width="160">
          <template #default="scope">
            <span v-if="scope.row.lon && scope.row.lat">
              {{ scope.row.lon.toFixed(4) }}, {{ scope.row.lat.toFixed(4) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 15, 20, 30]"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { getWhiteListList } from '@/api/wan/GaShip';
import { Close } from '@element-plus/icons-vue';

// 定义事件
const emit = defineEmits(['close']);

// 表格数据
const whiteListData = ref([]);
const loading = ref(false);
const total = ref(0);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
});

// 格式化类型
const formatType = (type: number) => {
  switch (type) {
    case 1:
      return '渔船';
    case 2:
      return '执法船';
    case 3:
      return '货船';
    case 4:
      return '客船';
    default:
      return '其他';
  }
};

// 获取标签类型
const getTypeTagType = (type: number) => {
  switch (type) {
    case 1:
      return 'success';
    case 2:
      return 'primary';
    case 3:
      return 'warning';
    case 4:
      return 'danger';
    default:
      return 'info';
  }
};

// 获取白名单列表数据
const getList = async () => {
  try {
    loading.value = true;
    const res = await getWhiteListList(queryParams);
    if (res.code === 200) {
      // 如果返回的是完整的分页结构
      if (res.data && Array.isArray(res.data.rows)) {
        whiteListData.value = res.data.rows;
        total.value = res.data.total || 0;
      } 
      // 如果直接返回数组
      else if (Array.isArray(res.data)) {
        whiteListData.value = res.data;
        total.value = res.data.length;
      }
    }
  } catch (error) {
    console.error('获取白名单列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size;
  getList();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page;
  getList();
};

// 关闭白名单面板
const handleClose = () => {
  emit('close');
};

// 组件挂载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.white-list-container {
  position: absolute;
  top: 70px;
  right: 15px;
  width: 800px;
  height: 500px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1003;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.close-btn {
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;
}

.close-btn:hover {
  color: #409eff;
}

.content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table) {
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-pagination) {
  font-weight: normal;
}
</style> 