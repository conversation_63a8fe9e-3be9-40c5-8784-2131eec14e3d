<template>
  <div class="ga-ship-list" v-if="visible">
    <div class="ga-ship-header">
      <h3>执法船列表</h3>
      <el-icon class="close-icon" @click="close"><Close /></el-icon>
    </div>
    <div class="ga-ship-content">
      <el-table :data="shipList" style="width: 100%" size="small" :max-height="400">
        <el-table-column prop="shipName" label="船名" min-width="100" show-overflow-tooltip />
        <el-table-column prop="mmsi" label="MMSI" min-width="120" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" min-width="100">
          <template #default="{ row }">
            {{ getShipType(row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="生效时间" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            {{ formatTime(row.startTime) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getGaShipList } from '@/api/wan/GaShip'
import { Close } from '@element-plus/icons-vue'

const visible = ref(true)
const shipList = ref([])

// 获取执法船列表
const getShipList = async () => {
  try {
    const res = await getGaShipList({})
    if (res.code === 200) {
      shipList.value = res.rows || []
    }
  } catch (error) {
    console.error('获取执法船列表失败:', error)
  }
}

// 格式化时间
const formatTime = (timestamp: string | number) => {
  if (!timestamp) return '-'
  if (typeof timestamp === 'string' && timestamp.includes('-')) {
    return timestamp
  }
  return new Date(timestamp).toLocaleString()
}

// 获取船舶类型
const getShipType = (type: number) => {
  const types = {
    0: '其他',
    1: '自有船只',
    2: '工作船只'
  }
  return types[type] || '未知'
}

// 关闭弹窗
const close = () => {
  visible.value = false
  emit('close')
}

// 定义事件
const emit = defineEmits(['close'])

// 组件挂载时获取数据
onMounted(() => {
  getShipList()
})
</script>

<style scoped>
.ga-ship-list {
  position: absolute;
  top: 70px;
  right: 15px;
  width: 800px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1002;
  animation: slideIn 0.3s ease-in-out;
}

.ga-ship-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.ga-ship-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.close-icon {
  cursor: pointer;
  font-size: 20px;
  color: #909399;
  transition: color 0.3s;
}

.close-icon:hover {
  color: #409eff;
}

.ga-ship-content {
  padding: 20px;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.el-table) {
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-table th) {
  background-color: var(--el-table-header-bg-color);
  font-weight: bold;
}
</style> 