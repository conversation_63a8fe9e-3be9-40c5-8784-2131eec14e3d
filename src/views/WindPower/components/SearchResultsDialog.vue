<template>
  <el-dialog
    v-model="dialogVisible"
    title="搜索结果"
    width="500px"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @close="handleClose"
  >
    <div class="search-results-container">
      <div class="results-header">
        <span class="results-count">找到 {{ searchResults.length }} 艘船舶</span>
      </div>
      
      <div class="results-list">
        <div
          v-for="(ship, index) in searchResults"
          :key="ship.mmsi || index"
          class="result-item"
          @click="handleShipClick(ship)"
        >
          <div class="ship-info">
            <div class="ship-name">
              {{ ship.chineseName || ship.englishName || ship.name || '未知船舶' }}
            </div>
            <div class="ship-details">
              <span class="mmsi">MMSI: {{ ship.mmsi || '未知' }}</span>
              <span class="ship-type">类型: {{ getShipTypeName(ship.shipType) }}</span>
            </div>
            <div class="ship-location" v-if="ship.lat && ship.lon">
              <span>位置: {{ ship.lat.toFixed(4) }}, {{ ship.lon.toFixed(4) }}</span>
              <span class="update-time" v-if="ship.time">
                更新: {{ formatTime(ship.time) }}
              </span>
            </div>
          </div>
          <div class="action-buttons">
            <el-button type="primary" size="small" @click.stop="handleViewDetails(ship)">
              查看详情
            </el-button>
            <el-button size="small" @click.stop="handleLocateShip(ship)">
              定位
            </el-button>
          </div>
        </div>
      </div>
      
      <div v-if="searchResults.length === 0" class="no-results">
        <el-empty description="未找到相关船舶" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElDialog, ElButton, ElEmpty } from 'element-plus';

// 定义类型
interface ShipInfo {
  id?: number;
  mmsi: string;
  name?: string;
  chineseName?: string;
  englishName?: string;
  lat?: number;
  lon?: number;
  sog?: number;
  cog?: number;
  shipType?: number;
  status?: number;
  time?: number;
  destination?: string;
  eta?: number;
  draught?: number;
  region?: string;
  callsign?: string;
}

// 定义 props
const props = defineProps<{
  visible: boolean;
  searchResults: ShipInfo[];
  map?: any;
}>();

// 定义 emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'ship-selected': [ship: ShipInfo];
  'close': [];
}>();

// 响应式数据
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 方法
const handleClose = () => {
  emit('close');
};

const handleShipClick = (ship: ShipInfo) => {
  emit('ship-selected', ship);
};

const handleViewDetails = (ship: ShipInfo) => {
  emit('ship-selected', ship);
};

const handleLocateShip = (ship: ShipInfo) => {
  if (ship.lat && ship.lon && props.map) {
    // 移动地图到船舶位置
    props.map.getView().animate({
      center: [ship.lon, ship.lat],
      zoom: 12,
      duration: 1000
    });
    
    // 触发船舶选中事件
    emit('ship-selected', ship);
  }
};

// 获取船舶类型名称
const getShipTypeName = (shipType?: number): string => {
  if (!shipType) return '未知';
  
  if (shipType >= 60 && shipType <= 69) {
    return "客船";
  } else if (shipType >= 70 && shipType <= 74) {
    return "货船";
  } else if (shipType === 33) {
    return "作业船";
  } else if (shipType === 52) {
    return "拖船";
  } else if (shipType === 30) {
    return "渔船";
  } else {
    return "其他";
  }
};

// 格式化时间
const formatTime = (timestamp: number): string => {
  if (!timestamp) return '未知';
  
  const now = Date.now();
  const time = timestamp * 1000; // 转换为毫秒
  const diff = now - time;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else {
    return '刚刚';
  }
};
</script>

<style scoped>
.search-results-container {
  max-height: 500px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.results-header {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 15px;
}

.results-count {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-item:hover {
  border-color: #409eff;
  background-color: #f5f7fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.ship-info {
  flex: 1;
  min-width: 0;
}

.ship-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ship-details {
  display: flex;
  gap: 15px;
  margin-bottom: 5px;
}

.mmsi, .ship-type {
  font-size: 12px;
  color: #909399;
}

.ship-location {
  font-size: 12px;
  color: #606266;
  display: flex;
  gap: 15px;
}

.update-time {
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.dialog-footer {
  text-align: right;
}

/* 滚动条样式 */
.results-list::-webkit-scrollbar {
  width: 6px;
}

.results-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.results-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
