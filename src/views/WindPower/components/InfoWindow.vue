<template>
    <!-- <teleport to="body">
        <div 
            v-if="visible" 
            class="global-infoWindow" 
            :style="windowStyle"
            @click="handleWindowClick"
        >
            <div class="global-infoWindow-title">
                <span>{{ title }}</span>
                <div class="global-infoWindow-close" @click.stop="forceHide">
                    <el-icon><Close /></el-icon>
                </div>
            </div>
            <div v-if="type" class="global-infoWindow-type">
                <span>类型:</span>
                <span>{{ type }}</span>
            </div>
            <div v-if="remark" class="global-infoWindow-remark">
                <span>备注:</span>
                <span>{{ remark }}</span>
            </div>
        </div>
    </teleport> -->
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { Close } from '@element-plus/icons-vue';
import emitter from '@/utils/bus';

// 状态变量
const visible = ref(false);
const title = ref('');
const type = ref('');
const remark = ref('');
const position = ref({ x: 0, y: 0 });
const closeTimer = ref<number | null>(null);

// 窗口样式计算属性
const windowStyle = computed(() => {
    return {
        top: position.value.y ? `${position.value.y}px` : '50%',
        left: position.value.x ? `${position.value.x}px` : '50%',
        transform: position.value.x && position.value.y ? 'translate(-50%, -50%)' : 'translate(-50%, -50%)',
        zIndex: 2147483647 // 最大z-index值
    };
});

// 清除定时器
function clearCloseTimer() {
    if (closeTimer.value !== null) {
        window.clearTimeout(closeTimer.value);
        closeTimer.value = null;
    }
}

// 显示信息窗口
function show(payload: any) {
    // 清除可能存在的关闭定时器
    clearCloseTimer();
    
    title.value = payload.title || '';
    type.value = payload.type || '';
    remark.value = payload.remark || '';
    
    // 如果提供了坐标，则使用坐标
    if (payload.x !== undefined && payload.y !== undefined) {
        position.value.x = payload.x;
        position.value.y = payload.y;
    } else {
        // 默认居中显示
        position.value.x = 0;
        position.value.y = 0;
    }
    
    visible.value = true;
    
    // 如果设置了持续时间，则自动关闭
    if (payload.duration) {
        closeTimer.value = window.setTimeout(() => {
            forceHide();
        }, payload.duration);
    }
}

// 隐藏信息窗口
function hide() {
    visible.value = false;
}

// 强制隐藏 - 用于关闭按钮
function forceHide(event?: MouseEvent) {
    if (event) {
        event.stopPropagation();
    }
    clearCloseTimer();
    visible.value = false;
}

// 处理窗口点击事件
function handleWindowClick(event: MouseEvent) {
    // 阻止事件冒泡，防止点击窗口时触发下层元素的事件
    event.stopPropagation();
}

// 确保组件卸载时清除定时器
onUnmounted(() => {
    clearCloseTimer();
});

// 注册事件监听
onMounted(() => {
    // 添加全局点击事件监听，点击其他区域时关闭窗口
    document.addEventListener('click', () => {
        if (visible.value) {
            forceHide();
        }
    });
    
    emitter.on('show-info-window', show);
    emitter.on('hide-info-window', hide);
});

// 清理事件监听
onUnmounted(() => {
    document.removeEventListener('click', forceHide);
    emitter.off('show-info-window', show);
    emitter.off('hide-info-window', hide);
});
</script>

<style>
/* 信息窗口样式 - 全局样式，使用独特的前缀防止冲突 */
.global-infoWindow {
    padding: 15px;
    position: fixed; /* 固定定位 */
    min-width: 280px;
    max-width: 400px;
    background-color: rgba(31, 63, 130, 0.9);
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(95, 211, 252, 0.5);
    backdrop-filter: blur(5px);
    color: #ffffff;
    font-size: 14px;
    pointer-events: auto;
    z-index: 2147483647; /* 最大z-index值 */
    transition: opacity 0.2s ease;
}

.global-infoWindow-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 12px;
    border-bottom: 2px solid #5fd3fc;
    padding-bottom: 8px;
}

.global-infoWindow-close {
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: #ffffff;
    transition: all 0.2s ease;
}

.global-infoWindow-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.global-infoWindow-type,
.global-infoWindow-remark {
    margin-top: 8px;
    line-height: 1.5;
    display: flex;
}

.global-infoWindow-type span:first-child,
.global-infoWindow-remark span:first-child {
    font-weight: bold;
    margin-right: 8px;
    color: #5fd3fc;
    min-width: 40px;
}

.global-infoWindow-type span:last-child,
.global-infoWindow-remark span:last-child {
    flex: 1;
    word-break: break-word;
}
</style>