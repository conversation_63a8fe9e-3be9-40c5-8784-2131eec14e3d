<template>
  <!-- 外层容器 -->
  <div class="layout" :class="isAdmin ? 'admT' : 'admF'">
    <!-- 顶部容器 -->
    <div class="head">
      <div class="headleft" style="display: flex; align-items: center">
        <img src="@/assets/logo/logo_white.png" style="width: 38px; height: 32px; margin-left: 15px" />
        <div class="head-txt">海上风电项目电子围栏系统</div>
      </div>

      <div style="
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
        ">
        <div class="head-inp">
          <img class="head-inp-img" src="@/assets/images/fdj.png" alt="" />
          <input type="text" style="
              width: 100%;
              height: 100%;
              outline: none;
              border: none;
              background-color: transparent;
              color: #ffffff;
            " placeholder="请输入船名、MMSI、呼号等" v-model="searchVal" @keyup.enter="searchSub" />
        </div>
        <div style="
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-left: 50px;
          ">
          <div style="
              color: #c4c4c4;
              font-size: 14px;
              text-shadow: 0 0 20px rgb(159, 170, 255);
            ">
            {{ timeYMD }}
          </div>
          <div style="
              color: #c4c4c4;
              font-size: 20px;
              text-shadow: 0 0 20px rgb(159, 170, 255);
            ">
            {{ timeHMS }}
          </div>
        </div>
      </div>

      <div class="head-right">
        <div class="head-right-flex">
          <div class="head-right-txt1" style="margin-right: 0">
            {{ username }}
          </div>
          <i class="el-icon-arrow-down head-right-txt1" style="font-size: 12px"></i>
          <i class="el-icon-s-custom head-right-txt1" style="font-size: 24px"></i>
        </div>
      </div>
    </div>
    <!-- 地图坐标层级信息 -->
    <div class="last-message-box">
      <div class="last-message">
        {{ mousePointlat }}，{{ mousePointlng }}，层级：{{ zoom }}
      </div>
    </div>
    
    <!-- 地图容器 -->
    <div id="map" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import { useUserStore } from '@/store/modules/user';

// Global T object from window
declare const window: Window & {
  T: any;
};

// 使用Pinia store
const userStore = useUserStore();
const router = useRouter();

// Define data properties
const map = ref(null);
const isAdmin = ref(false);
const timeYMD = ref("");
const timeHMS = ref("");
const mousePointlat = ref("");
const mousePointlng = ref("");
const zoom = ref("");
const searchVal = ref("");

// Create computed properties
const username = computed(() => userStore.name || '');

// Methods
const searchSub = () => {
  if (!searchVal.value) {
    ElMessageBox.alert('请输入搜索内容', '提示');
    return;
  }
  
  // 执行搜索
  console.log('搜索:', searchVal.value);
};

// Lifecycle hooks
onMounted(() => {
  // 设置时间显示
  const updateTime = () => {
    const now = new Date();
    timeYMD.value = now.toLocaleDateString();
    timeHMS.value = now.toLocaleTimeString();
  };
  updateTime();
  const timer = setInterval(updateTime, 1000);

  // 使用nextTick确保DOM已经渲染
  nextTick(() => {
    initMap();
  });

  onBeforeUnmount(() => {
    // 清除计时器
    clearInterval(timer);
    
    // 清除地图实例
    if (map.value) {
      // 移除事件监听
      map.value.removeEventListener('mousemove');
      map.value.removeEventListener('zoomend');
      
      // 销毁地图
      map.value = null;
    }
  });
});

// 地图初始化函数
function initMap() {
  const mapContainer = document.getElementById('map');
  if (!mapContainer) {
    console.error('地图容器不存在');
    return;
  }

  // 确保T对象存在
  if (window.T) {
    try {
      // 创建地图实例，初始化中心点和缩放级别
      map.value = new window.T.Map('map', {
        center: new window.T.LngLat(121.9, 30.5), // 默认中心点坐标
        zoom: 12 // 默认缩放级别
      });

      // 添加地图事件监听
      if (map.value) {
        // 鼠标移动事件 - 更新坐标显示
        map.value.addEventListener('mousemove', (e) => {
          if (e.lnglat) {
            mousePointlng.value = e.lnglat.lng.toFixed(6);
            mousePointlat.value = e.lnglat.lat.toFixed(6);
          }
        });

        // 缩放事件 - 更新缩放级别显示
        map.value.addEventListener('zoomend', () => {
          zoom.value = map.value.getZoom().toString();
        });

        // 初始化缩放级别显示
        zoom.value = map.value.getZoom().toString();
      }
    } catch (error) {
      console.error('地图初始化失败:', error);
    }
  } else {
    console.error('天地图API未加载');
  }
}
</script>

<style scoped lang="less">
.head-txt {
  font-size: 20px;
  color: #fff;
  margin-left: 10px;
  white-space: nowrap;
}

.head-right {
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  justify-content: flex-end;

  span:nth-of-type(1) {
    height: 100%;
  }
}

.head-right-flex {
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  span:nth-of-type(1) {
    height: 100%;
  }
}

.head-right-txt {
  line-height: 100%;
  font-size: 16px;
  margin-right: 10px;
  color: #c4c4c4;
  cursor: pointer;
}

.head-right-txt1 {
  line-height: 100%;
  font-size: 16px;
  margin-right: 15px;
  color: #fff;
  cursor: pointer;
}

.head {
  width: 100%;
  height: 60px;
  background: linear-gradient(to right, #2d3a7b, #4b5d92);
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  padding: 0 20px;
}

.head-inp {
  width: 260px;
  height: 36px;
  background: rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 20px rgba(146, 157, 242, 0.35);
  border-radius: 8px;
  padding: 0 10px;
  position: relative;
  display: flex;
  align-items: center;
}

.head-inp-img {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.layout {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.last-message-box {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 5px 10px;
  font-size: 12px;
}

.last-message {
  width: 100%;
  height: 100%;
}

#map {
  flex: 1;
  position: relative;
}
</style> 