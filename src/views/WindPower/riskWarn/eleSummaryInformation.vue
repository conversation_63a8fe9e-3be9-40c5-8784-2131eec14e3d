<template>
  <div>
    <div class="info-content" v-show="isExpandPop">
      <!-- 头部 -->
      <div class="info-head">
        <el-icon><Document /></el-icon>
        <h5>通航要素概要信息</h5>
        <span>
          <el-icon @click="retract"><Minus /></el-icon>
          <el-icon class="el-icon" @click="cancel"><Close /></el-icon>
        </span>
      </div>
      <!-- body -->
      <div class="info-body">
        <div class="info-form">
          <el-form :inline="true" :model="formInline" class="demo-form-inline">
            <el-form-item label="类型:">
              <el-select v-model="formInline.region" placeholder="请选择">
                <el-option label="危险品锚地" value="shanghai"></el-option>
                <el-option label="军事禁区" value="beijing"></el-option>
                <el-option label="海底管道区" value="beijing"></el-option>
                <el-option label="养殖区" value="beijing"></el-option>
                <el-option label="禁止采砂区" value="beijing"></el-option>
                <el-option label="自定义围栏" value="beijing"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="关键字:">
              <el-input
                v-model="formInline.user"
                placeholder="关键字"
              ></el-input>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                @click="onSubmit"
                size="default"
                class="query"
                >查询</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <!-- 表格 -->
        <div class="info-table">
          <el-table :data="tableData" border height="100%" width="100%">
            <el-table-column
              :width="width"
              min-width="80"
              prop="date"
              label="名称"
            >
            </el-table-column>
            <el-table-column
              :width="width"
              min-width="80"
              prop="name"
              label="类型"
            >
            </el-table-column>
            <el-table-column
              :width="width"
              min-width="80"
              prop="address"
              label="创建人"
            >
            </el-table-column>
            <el-table-column
              :width="width"
              min-width="80"
              prop="institution"
              label="所属组织机构"
            >
            </el-table-column>
            <el-table-column
              width="80"
              min-width="80"
              prop="operate"
              label="操作"
            >
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 收起 -->
    <RetractComponent
      v-if="isRetract"
      @expand="expand"
      @cancel="cancel"
      :title="componentTitle"
    ></RetractComponent>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Document, Minus, Close } from '@element-plus/icons-vue';
import RetractComponent from "./retract.vue";

// 定义接口
interface FormInline {
  user: string;
  region: string;
}

interface TableItem {
  date: string;
  name: string;
  address: string;
  institution: string;
  operate: string;
}

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'closeInfoPop'): void
}>();

// 响应式数据
const isRetract = ref(false); // 是否收起弹窗
const isExpandPop = ref(true); // 是否展示弹窗
const componentTitle = ref("通航要素概要信息");
const width = ref("180%");

const formInline = reactive<FormInline>({
  user: "",
  region: "",
});

const tableData = ref<TableItem[]>([
  {
    date: "二级警戒区1",
    name: "自定义围栏",
    address: "超级管理员",
    institution: "-",
    operate: "",
  },
  {
    date: "二级警戒区2",
    name: "自定义围栏",
    address: "超级管理员",
    institution: "-",
    operate: "",
  },
  {
    date: "二级警戒区3",
    name: "自定义围栏",
    address: "超级管理员",
    institution: "-",
    operate: "",
  },
  {
    date: "二级警戒区4",
    name: "自定义围栏",
    address: "超级管理员",
    institution: "-",
    operate: "",
  }, 
  {
    date: "航道",
    name: "自定义围栏",
    address: "超级管理员",
    institution: "-",
    operate: "",
  },
  {
    date: "三级警戒区3",
    name: "自定义围栏",
    address: "超级管理员",
    institution: "-",
    operate: "",
  },
  {
    date: "一级警戒区",
    name: "自定义围栏",
    address: "超级管理员",
    institution: "-",
    operate: "",
  },
  {
    date: "一级警戒区1",
    name: "自定义围栏",
    address: "超级管理员",
    institution: "-",
    operate: "",
  },
]);

// 方法
function cancel() {
  emit("closeInfoPop");
}

function onSubmit() {
  console.log("submit!");
}

// 收起
function retract() {
  isExpandPop.value = false;
  isRetract.value = true;
}

// 展开
function expand() {
  isExpandPop.value = true;
  isRetract.value = false;
}
</script>

<style scoped>
.info-content {
  width: 780px;
  height: 500px;
  background: white;
  color: var(--el-text-color-primary, #303133);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light, #e4e7ed);
  position: fixed;
  right: 320px;
  top: 80px;
  z-index: 10;
  display: flex;
  flex-direction: column;
}

.info-head {
  display: flex;
  font-size: 16px;
  align-items: center;
  height: 45px;
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);
  background-color: var(--el-bg-color-page, #f5f7fa);
  border-radius: 8px 8px 0 0;
  padding: 0 20px;
}

.info-head > h5 {
  margin: 0;
  flex: 1;
  font-size: 1em;
  padding: 5px 8px;
  cursor: default;
  font-weight: 600;
  color: var(--el-text-color-primary, #303133);
}

.info-body {
  width: 100%;
  height: calc(100% - 45px);
  padding: 10px 20px;
  overflow: hidden;
  box-sizing: border-box;
}

.info-form {
  margin: 0;
  padding: 0;
  height: 12%;
}

.info-table {
  margin: 0;
  padding: 0;
  height: 88%;
  width: 100%;
}

.label-style {
  width: 80px;
  text-align: right;
  margin-left: 10px;
  font-size: 14px;
  padding: 0 12px 0 0;
  color: var(--el-text-color-regular, #606266);
  font-weight: 500;
}

.elWidth {
  width: 180px;
}

.query {
  color: #fff;
  font-size: 12px;
  padding: 7px 14px;
  background-color: var(--el-color-primary, #409EFF);
  border-color: var(--el-color-primary, #409EFF);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.query:hover,
.query:focus {
  background: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

.el-icon {
  margin-left: 5px;
  cursor: pointer;
  transition: all 0.3s;
  color: var(--el-text-color-secondary, #909399);
}

.el-icon:hover {
  color: var(--el-color-primary, #409EFF);
  transform: scale(1.1);
}

:deep(.el-form-item__label) {
  color: var(--el-text-color-regular, #606266);
  font-size: 14px;
  font-weight: 500;
  padding: 0 12px 0 0;
  width: 80px;
}

:deep(.el-input__inner) {
  height: 28px;
  line-height: 28px;
  background-color: white;
  border-color: var(--el-border-color, #dcdfe6);
  color: var(--el-text-color-primary, #303133);
}

:deep(.el-select) {
  background: white;
  height: 28px;
  line-height: 28px;
  border-radius: 4px;
}

:deep(.el-table--border) {
  border: 1px solid var(--el-border-color-light, #e4e7ed) !important;
  border-right: 1px solid var(--el-border-color-light, #e4e7ed) !important;
}

:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-light, #e4e7ed);
  --el-table-header-bg-color: var(--el-bg-color-page, #f5f7fa);
  --el-table-tr-bg-color: white;
  --el-table-text-color: var(--el-text-color-primary, #303133);
  --el-table-header-text-color: var(--el-text-color-primary, #303133);
}

:deep(.el-table th.el-table__cell),
:deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);
}

@media screen and (max-width: 768px) {
  .info-content {
    width: 90%;
    max-width: 780px;
    left: 5%;
    right: 5%;
  }
}
</style>

<style scoped src="@/assets/datePark.css"></style>