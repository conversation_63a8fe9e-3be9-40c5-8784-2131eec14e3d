<template>
  <div>
    <div class="flex-column">
      <!-- <div
        class="flex-row"
        style="justify-content: space-between; padding: 5px 0"
      >
        <div class="content-txt1">
          <i class="el-icon-guide"></i><span class="el-icon">电子围栏</span>
        </div>
        <div @click="handToParentClose()">
          <i class="el-icon-close" style="color: #fefeff; opacity: 0.5"></i>
        </div>
      </div> -->
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-col :span="18">
          <el-input
            placeholder="输入名称查找"
            v-model="input"
            clearable
          ></el-input>
        </el-col>
        <el-button title="统计" class="button-style" @click="popupStatistics">
          <el-icon><Help /></el-icon>
        </el-button>
        <el-button title="概要信息" class="button-style" @click="popupInfo">
          <el-icon><Calendar /></el-icon>
        </el-button>
      </div>
      <!-- 折叠式面板 -->
      <div class="fold-panel">
        <el-collapse>
          <el-collapse-item
            v-for="(item, index) in collapseList"
            :key="index"
            :name="index"
            @click.native="showCollapse(item)"
          >
            <template #title>
              <div class="el-collapse-fbox">
                <div class="el-collapse-left">
                  <el-icon class="iconcolor" :class="{ active: item.isActive }" style="margin: 0 5px">
                    <ArrowRight />
                  </el-icon>
                  <span>{{ item.name }}</span>
                </div>
                <div class="el-collapse-right">
                  <el-icon class="view-icon"><View /></el-icon>
                  <el-icon class="add-icon" @click.stop="addPop(item)"><Plus /></el-icon>
                </div>
              </div>
            </template>
            <div
              v-for="(i, idx) in item.title"
              :key="idx"
              class="fence-item"
            >
              <div>{{ i.tip }}</div>
              <div class="fence-item-actions">
                <el-tooltip effect="dark" content="区域船舶" placement="top">
                  <el-icon @click="toRegionVessel(i)"><Search /></el-icon>
                </el-tooltip>
                <el-tooltip effect="dark" content="规则设置" placement="top">
                  <el-icon @click="setRule(i)"><Setting /></el-icon>
                </el-tooltip>
                <el-tooltip effect="dark" content="编辑" placement="top">
                  <el-icon @click="addPop(item, i)"><Edit /></el-icon>
                </el-tooltip>
                <el-tooltip effect="dark" content="删除" placement="top">
                  <el-icon @click="onDelete(i.tip)"><Delete /></el-icon>
                </el-tooltip>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <!-- 通航环境统计弹窗 -->
    <environmentalStatistics
      v-if="isEnvPop"
      @closeEnvPop="closeEnvPop"
    ></environmentalStatistics>
    <!-- 通航要素概要信息弹窗 -->
    <eleSummaryInformation
      v-if="isElEInfo"
      @closeInfoPop="closeInfoPop"
    ></eleSummaryInformation>
    <!-- 新增弹窗 -->
    <addPopTemplate
      v-if="isAdd"
      @closeAddPop="closeAddPop"
      :title="title"
    ></addPopTemplate>
    <el-dialog
      v-model="isrule"
      width="30%"
      :modal="false"
      :show-close="false"
    >
      <template #header>
        <div class="dialog-title">
          <div class="static-head">
            <el-icon><Document /></el-icon>
            <h5>{{ ruleTitle }}</h5>
            <span>
              <el-icon><Minus /></el-icon>
              <el-icon class="close-icon" @click="closeRuleDialog"><Close /></el-icon>
            </span>
          </div>
        </div>
      </template>
      <ruleVue :rule-data="ruleData" :rule-title="ruleTitle" />
    </el-dialog>
  </div>
</template>
  
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { 
  ArrowRight, 
  Help, 
  Calendar, 
  View, 
  Plus, 
  Search, 
  Setting, 
  Edit, 
  Delete, 
  Document, 
  Minus, 
  Close 
} from '@element-plus/icons-vue'
import { getShipList } from "@/api/map/map"
import environmentalStatistics from "./environmentalStatistics.vue"
import eleSummaryInformation from "./eleSummaryInformation.vue"
import addPopTemplate from "./addPopTemplate.vue"
import ruleVue from "@/views/WindPower/rule.vue"
import { ElMessage, ElMessageBox } from 'element-plus'

interface TitleItem {
  tip: string
  [key: string]: any
}

interface CollapseItem {
  name: string
  isActive: boolean
  title?: TitleItem[]
}

interface ShipParams {
  [key: string]: any
}

// 声明天地图API的全局变量
declare global {
  interface Window {
    T: any
  }
}

// 定义 props
const props = defineProps<{
  map: any
}>()

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'closeList', val: number): void
  (e: 'popup-right-val', val: string): void
}>()

// 响应式数据
const isPopupRight = ref(false)
const PopupRight = ref("")
const isEnvPop = ref(false)
const isElEInfo = ref(false)
const isAdd = ref(false)
const input = ref("")
const marker = ref<any>(null)
const threeNone = ref<any[]>([])
const fishing = ref<any[]>([])
const enforce = ref<any[]>([])
const interest = ref<any[]>([])
const list = ref<any[]>([])
const ruleData = ref<any>(null)
const isrule = ref(false)
const ruleTitle = ref("")
const tableData = ref<any[]>([])
const shipTotal = ref(0)
const shipParams = reactive<ShipParams>({})
const title = ref("")

const collapseList = reactive<CollapseItem[]>([
  {
    name: "危险品锚地",
    isActive: false,
    title: [],
  },
  {
    name: "军事禁区",
    isActive: false,
  },
  {
    name: "海底管道区",
    isActive: false,
  },
  {
    name: "养殖区",
    isActive: false,
  },
  {
    name: "禁止采砂区",
    isActive: false,
  },
  {
    name: "自定义围栏",
    isActive: false,
    title: [
      { tip: "二级警戒区1" },
      { tip: "二级警戒区2" },
      { tip: "二级警戒区3" },
      { tip: "二级警戒区4" },
      { tip: "航道" },
      { tip: "三级警戒区3" },
      { tip: "一级警戒区" },
      { tip: "一级警戒区1" },
    ],
  },
])

// 生命周期钩子
onMounted(() => {
  // 注释掉的代码保留
  // list.value.forEach((ele, index) => {
  //   if (index % 4 == 1) {
  //     threeNone.value.push(ele);
  //   } else if (index % 4 == 2) {
  //     fishing.value.push(ele);
  //   } else if (index % 4 == 3) {
  //     enforce.value.push(ele);
  //   } else {
  //     interest.value.push(ele);
  //   }
  // });
})

// 方法
// 获取船信息
function getShipInfo() {
  // getShipList(shipParams).then((res: any) => {
  //   if (res.code == 200) {
  //     tableData.value = res.rows
  //     shipTotal.value = res.total
  //   }
  // })
}

// 关闭规则对话框
function closeRuleDialog() {
  isrule.value = false
}

// 显示折叠面板
function showCollapse(item: CollapseItem) {
  item.isActive = !item.isActive
}

// 区域船舶
function toRegionVessel(item: TitleItem) {
  emit('popup-right-val', 'regionVessel')
}

// 设置规则
function setRule(item: TitleItem) {
  ruleTitle.value = item.tip + "规则设置"
  ruleData.value = item
  isrule.value = true
}

// 删除确认
function onDelete(name: string) {
  ElMessageBox.confirm(`确认删除 "${name}" 吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
  }).catch(() => {
    // 取消删除
  })
}

// 显示统计信息弹窗
function popupStatistics() {
  isEnvPop.value = true
}

// 关闭统计信息弹窗
function closeEnvPop() {
  isEnvPop.value = false
}

// 显示概要信息弹窗
function popupInfo() {
  isElEInfo.value = true
}

// 关闭概要信息弹窗
function closeInfoPop() {
  isElEInfo.value = false
}

// 显示添加/编辑弹窗
function addPop(item: CollapseItem, subItem?: TitleItem) {
  title.value = subItem ? "编辑" + item.name : "添加" + item.name
  isAdd.value = true
}

// 关闭添加/编辑弹窗
function closeAddPop() {
  isAdd.value = false
}

// 关闭列表
function handToParentClose() {
  emit('closeList', 10)
}
</script>
  
<style scoped>
.static-head {
  padding: 0px;
  display: flex;
  font-size: 16px;
  align-items: center;
}
.static-head > h5 {
  margin: 0;
  flex: 1;
  font-size: 1em;
  padding: 5px 8px;
  cursor: default;
}
.button-style {
  margin-left: 0px;
  padding: 0;
  color: var(--el-color-primary, #409EFF);
  background: 0 0;
  border-color: transparent;
  transition: all 0.3s;
}
.button-style:hover {
  transform: translateY(-2px);
  opacity: 0.8;
}
.search-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 5px;
  width: 90%;
  padding: 10px 0;
}
.fold-panel {
  width: 90%;
}
.el-collapse-left {
  display: flex;
  align-items: center;
}
.el-collapse-right {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.el-collapse-fbox {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 5px;
}
.iconcolor {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-color-primary, #409EFF);
}
.active {
  transform: rotate(90deg);
  transition: all 0.4s;
  color: var(--el-color-primary, #409EFF);
}
.no-active {
  transform: rotate(0deg);
  /* 过渡 */
  transition: all 0.4s;
}

div >>> .el-collapse-item__arrow {
  display: none;
}

.el-icon {
  margin-left: 6px;
}

.content {
  /* width: 350px; */
  height: 100%;
  /* background: rgba(0, 0, 0, 0.5); */
  position: fixed;
  right: 0;
  top: 60px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.flex-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.el-icon-circle-close {
  font-size: 22px;
}
.content-txt1 {
  font-size: 16px;
  color: #cbd0f6;
}

.line {
  width: 100%;
  height: 1px;
  margin: 1px auto;
  background: #d5d5d5;
}
.area {
  margin: 20px 0;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  color: #4386c6;
}

.custom-tree-node-label {
  display: block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.shipNum {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.collItem:hover {
  cursor: pointer;
}
.collItem {
  color: #409eff;
  margin-bottom: 10px;
}
.dialog-header{
  border: 1px solid red !important;
}

.view-icon, .add-icon {
  margin-left: 10px;
  font-size: 16px;
  cursor: pointer;
  color: var(--el-text-color-secondary, #909399);
  transition: all 0.3s;
}

.view-icon:hover, .add-icon:hover {
  color: var(--el-color-primary, #409EFF);
  transform: scale(1.1);
}

.fence-item {
  display: flex;
  justify-content: space-between;
  width: 80%;
  margin: 10px auto;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
  color: var(--el-text-color-regular, #606266);
  background-color: var(--el-bg-color-overlay, white);
  border-left: 2px solid var(--el-color-primary, #409EFF);
}

.fence-item:hover {
  background-color: var(--el-fill-color-light, #f5f7fa);
  color: var(--el-text-color-primary, #303133);
}

.fence-item-actions {
  display: flex;
  gap: 8px;
}

.fence-item-actions .el-icon {
  cursor: pointer;
  transition: all 0.3s;
  color: var(--el-text-color-secondary, #909399);
}

.fence-item-actions .el-icon:hover {
  color: var(--el-color-primary, #409EFF);
  transform: scale(1.1);
}

.dialog-title {
  width: 100%;
}

.static-head {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 5px 0;
}

.static-head h5 {
  margin: 0 10px;
  flex: 1;
  color: var(--el-text-color-primary, #303133);
  font-weight: 600;
}

.static-head span {
  display: flex;
  gap: 8px;
}

.close-icon {
  cursor: pointer;
  color: var(--el-text-color-secondary, #909399);
  transition: all 0.3s;
}

.close-icon:hover {
  color: var(--el-color-primary, #409EFF);
  transform: scale(1.1);
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 16px 20px;
  background-color: var(--el-bg-color-page, #f5f7fa);
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);
}

:deep(.el-dialog__body) {
  padding: 20px;
  background-color: white;
}

:deep(.el-collapse-item__header) {
  background-color: var(--el-bg-color-overlay, white);
  color: var(--el-text-color-primary, #303133);
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);
  font-weight: 500;
}

:deep(.el-collapse-item__header:hover) {
  background-color: var(--el-fill-color-light, #f5f7fa);
}

:deep(.el-collapse-item__content) {
  background-color: var(--el-bg-color-overlay, white);
  color: var(--el-text-color-regular, #606266);
  padding: 16px;
}

:deep(.el-collapse) {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}
</style>