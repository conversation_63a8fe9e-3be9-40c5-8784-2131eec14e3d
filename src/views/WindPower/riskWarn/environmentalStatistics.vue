<template>
  <div>
    <!-- 统计弹窗 -->
    <div class="static-content" v-show="isExpandPop">
      <!-- 头部 -->
      <div class="static-head">
        <el-icon><Document /></el-icon>
        <h5>{{ componentTitle }}</h5>
        <span>
          <el-icon @click="retract"><Minus /></el-icon>
          <el-icon class="el-icon" @click="cancel"><Close /></el-icon>
        </span>
      </div>
      <!-- 图表 -->
      <div class="chart-container">
        <div class="chart-wrapper">
          <div class="chart-inner">
            <div id="echartsType" ref="chartRef"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- 收起 -->
    <RetractComponent
      v-if="isRetract"
      @expand="expand"
      @cancel="cancel"
      :title="componentTitle"
    ></RetractComponent>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { Document, Minus, Close } from '@element-plus/icons-vue'
import RetractComponent from "./retract.vue"
import * as echarts from 'echarts'

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'closeEnvPop'): void
}>()

// 响应式数据
const isRetract = ref(false) //是否收起弹窗
const isExpandPop = ref(true) //是否展示弹窗
const componentTitle = ref("通航环境统计")
const chartRef = ref<HTMLElement | null>(null)
const chart = ref<echarts.ECharts | null>(null)

// 图表数据
const chartData = {
  categories: [
    "危险品锚地",
    "军事禁区",
    "海底管道区",
    "养殖区",
    "禁止采砂区",
    "自定义围栏",
  ],
  values: [0, 0, 0, 0, 0, 8]
}

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onBeforeUnmount(() => {
  disposeChart()
})

// 方法
function initChart() {
  const chartDom = document.getElementById("echartsType")
  if (!chartDom) return
  
  chart.value = echarts.init(chartDom)
  const option: echarts.EChartsOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      backgroundColor: "white",
      borderColor: "#e4e7ed",
      textStyle: {
        color: "#303133",
      },
      borderWidth: 1,
    },
    grid: {
      top: "15%",
      right: "5%",
      left: "15%",
      bottom: "12%",
    },
    xAxis: [
      {
        type: "category",
        data: chartData.categories,
        axisLine: {
          lineStyle: {
            color: "#909399",
          },
        },
        axisLabel: {
          margin: 10,
          color: "#606266",
          fontSize: 10
        },
        axisTick: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        axisLabel: {
          formatter: "{value}",
          color: "#606266",
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#909399",
          },
        },
        splitLine: {
          lineStyle: {
            color: "#e4e7ed",
          },
        },
      },
    ],
    series: [
      {
        type: "bar",
        data: chartData.values,
        barWidth: "50%",
        itemStyle: {
          color: "#409EFF",
        },
        label: {
          show: true,
          lineHeight: 10,
          formatter: "{c}",
          position: "top",
          color: "#409EFF",
          fontSize: 12
        },
      },
    ],
  }
  chart.value.setOption(option)
  
  // 监听窗口变化，调整图表大小
  window.addEventListener('resize', handleResize)
}

function handleResize() {
  if (chart.value) {
    chart.value.resize()
  }
}

function disposeChart() {
  if (chart.value) {
    chart.value.dispose()
    window.removeEventListener('resize', handleResize)
  }
}

function updateChartData(categories: string[], values: number[]) {
  if (chart.value) {
    chart.value.setOption({
      xAxis: [{
        data: categories
      }],
      series: [{
        data: values
      }]
    })
  }
}

function cancel() {
  emit('closeEnvPop')
}

// 收起
function retract() {
  isExpandPop.value = false
  isRetract.value = true
}

// 展开
function expand() {
  isExpandPop.value = true
  isRetract.value = false
  nextTick(() => {
    if (!chart.value) {
      initChart()
    } else {
      chart.value.resize()
    }
  })
}
</script>

<style scoped>
.static-content {
  width: 530px;
  height: 360px;
  background: white;
  color: var(--el-text-color-primary, #303133);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light, #e4e7ed);
  padding: 10px;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 60px;
  right: 350px;
  z-index: 10;
}

.static-head {
  display: flex;
  font-size: 16px;
  align-items: center;
  height: 40px;
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);
  margin-bottom: 10px;
  background-color: var(--el-bg-color-page, #f5f7fa);
  border-radius: 4px 4px 0 0;
  padding: 0 10px;
}

.static-head > h5 {
  margin: 0;
  flex: 1;
  font-size: 1em;
  padding: 5px 8px;
  cursor: default;
  font-weight: 600;
  color: var(--el-text-color-primary, #303133);
}

.static-head .el-icon {
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.3s;
  color: var(--el-text-color-secondary, #909399);
}

.static-head .el-icon:hover {
  color: var(--el-color-primary, #409EFF);
  transform: scale(1.1);
}

.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

#echartsType {
  width: 100%;
  height: 100%;
}

@media screen and (max-width: 768px) {
  .static-content {
    width: 90%;
    right: 5%;
    left: 5%;
  }
}
</style>

<style scoped src="@/assets/datePark.css"></style>