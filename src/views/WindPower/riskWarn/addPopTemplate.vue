<template>
  <div>
    <div
      :class="title == '危险品锚地' ? 'add-content-dangerous' : 'add-content'"
    >
      <!-- 头部 -->
      <div class="add-head">
        <el-icon><Document /></el-icon>
        <h5>新增{{ title }}</h5>
        <span style="color: #a0a2b5">
          <el-icon><Minus /></el-icon>
          <el-icon class="el-icon" @click="cancel"><Close /></el-icon>
        </span>
      </div>
      <!-- body -->
      <div class="add-body">
        <div style="display: inline">
          <div style="width: 100%; padding: 2px 0 0 15px">
            <!-- form -->
            <el-form
              style="
                min-width: 400px;
                max-width: 600px;
                max-height: 912px;
                padding: 0px 10px 10px;
              "
              :inline="true"
              :model="formInline"
              class="demo-form-inline"
            >
              <el-row>
                <el-col :span="24">
                  <el-form-item label="名称">
                    <el-input
                      :class="
                        title == '危险品锚地'
                          ? 'input-style-dangerous'
                          : 'input-style'
                      "
                      v-model="formInline.user"
                    ></el-input> </el-form-item
                ></el-col>
              </el-row>
              <el-row v-if="title == '危险品锚地'" style="padding: 10px 0">
                <el-col :span="24">
                  <el-form-item label="功能">
                    <el-checkbox-group
                      v-model="checkList"
                      style="
                        width: 400px;
                        display: flex;
                        flex-wrap: wrap;
                        padding: 0;
                      "
                    >
                      <el-checkbox label="待泊"></el-checkbox>
                      <el-checkbox label="临时待泊"></el-checkbox>
                      <el-checkbox label="避风"></el-checkbox>
                      <el-checkbox label="联检"></el-checkbox>
                      <el-checkbox label="引航"></el-checkbox>
                      <el-checkbox label="候潮"></el-checkbox>
                      <el-checkbox label="过泊"></el-checkbox>
                      <el-checkbox label="锚泊"></el-checkbox>
                      <el-checkbox label="临时锚泊"></el-checkbox>
                    </el-checkbox-group> </el-form-item
                ></el-col>
              </el-row>
              <el-row v-if="title == '危险品锚地'">
                <el-col :span="24">
                  <div
                    style="
                      display: flex;
                      align-items: start;
                      margin-bottom: 10px;
                    "
                  >
                    <div class="label-style">面积(km²)</div>
                    <div style="flex: 1 1 0%">
                      <el-input-number
                        size="small"
                        v-model="num"
                        controls-position="right"
                        @change="handleChange"
                        :min="1"
                        :max="10"
                      ></el-input-number>
                    </div>
                    <div class="label-style">水深-低(m)</div>
                    <div style="flex: 1 1 0%">
                      <el-input-number
                        size="small"
                        v-model="num"
                        controls-position="right"
                        @change="handleChange"
                        :min="1"
                        :max="10"
                      ></el-input-number>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row v-if="title == '危险品锚地'">
                <el-col :span="24">
                  <div
                    style="
                      display: flex;
                      align-items: start;
                      margin-bottom: 10px;
                    "
                  >
                    <div class="label-style">规模(&lt;=万吨)</div>
                    <div style="flex: 1 1 0%">
                      <el-input-number
                        size="small"
                        v-model="num"
                        controls-position="right"
                        @change="handleChange"
                        :min="1"
                        :max="10"
                      ></el-input-number>
                    </div>
                    <div class="label-style">水深-高(m)</div>
                    <div style="flex: 1 1 0%">
                      <el-input-number
                        size="small"
                        v-model="num"
                        controls-position="right"
                        @change="handleChange"
                        :min="1"
                        :max="10"
                      ></el-input-number>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row v-if="title == '危险品锚地'">
                <el-col :span="24">
                  <div
                    style="
                      display: flex;
                      align-items: start;
                      margin-bottom: 10px;
                    "
                  >
                    <div class="label-style">锚地容量(艘)</div>
                    <div style="flex: 1 1 0%">
                      <el-input-number
                        style="width: 95%"
                        size="small"
                        v-model="num"
                        controls-position="right"
                        @change="handleChange"
                        :min="1"
                        :max="10"
                      ></el-input-number>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="区域">
                    <div
                      :class="
                        title == '危险品锚地'
                          ? 'text-style-dangerous'
                          : 'text-style'
                      "
                    ></div>
                    <el-button
                      class="button-style"
                      @click="clickToParentEditLine()"
                      ><span>编辑</span>
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-button class="button-style"
                      ><span>清除</span>
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row style="text-align: right">
                <el-col :span="24">
                  <el-form-item>
                    <el-button type="primary" size="small">保存</el-button>
                    <el-button size="small">取消</el-button>
                  </el-form-item></el-col
                ></el-row
              >
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isToolShow" class="toolsBox">
      <drawTools
        @Tools="setTools"
        @closeTools="clickToToolClear"
        @subTools="clickToToolSubmit"
        :isBtn="true"
      />
      <div class="btns"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits } from 'vue';
import { Document, Minus, Close, Edit } from '@element-plus/icons-vue';
import drawTools from "@/views/WindPower/drawingTools.vue";

// 定义接口
interface FormInline {
  user: string;
  region: string;
  [key: string]: any;
}

// 定义 props
const props = defineProps<{
  title: string;
}>();

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'closeAddPop'): void;
}>();

// 响应式数据
const isToolShow = ref(false); // 工具弹窗
const checkList = ref(['选中且禁用', '复选框 A']);
const formInline = reactive<FormInline>({
  user: '',
  region: '',
});
const num = ref(1);

// 方法
function cancel() {
  emit('closeAddPop');
}

// 分界线编辑
function clickToParentEditLine() {
  console.log(11);
  isToolShow.value = true;
  console.log(isToolShow.value);
}

function setTools() {
  // 实现设置工具的逻辑
}

function clickToToolClear() {
  // 实现清除工具的逻辑
}

function clickToToolSubmit() {
  // 实现提交工具的逻辑
}

function handleChange() {
  // 实现值变化的逻辑
}
</script>

<style scoped>
:deep(.el-form-item) {
  margin-bottom: 0px;
}

.label-style {
  width: 100px;
  margin-top: 10px;
  line-height: 20px;
}

:deep(.el-checkbox) {
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  user-select: none;
  margin-right: 15px;
  margin-bottom: 10px;
  min-width: 20px !important;
  padding: 0px !important;
}

:deep(.el-form-item__label) {
  margin-top: 1px !important;
}

.el-icon {
  margin-left: 5px;
  cursor: pointer;
  transition: color 0.3s;
}

.el-icon:hover {
  color: #409EFF;
}

.add-content,
.add-content-dangerous {
  width: 580px;
  min-height: 400px;
  max-height: 80vh;
  background: white;
  color: var(--el-text-color-primary, #303133);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light, #e4e7ed);
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: auto;
}

.add-head {
  display: flex;
  font-size: 16px;
  align-items: center;
  height: 50px;
  border-bottom: 1px solid var(--el-border-color-light, #e4e7ed);
  background-color: var(--el-bg-color-page, #f5f7fa);
  border-radius: 8px 8px 0 0;
  padding: 0 20px;
}

.add-head > h5 {
  margin: 0;
  flex: 1;
  font-size: 16px;
  padding: 5px 8px;
  cursor: default;
  font-weight: 600;
  color: var(--el-text-color-primary, #303133);
}

.add-body {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.button-style {
  color: var(--el-color-primary, #409EFF);
  background: transparent;
  padding-left: 0;
  padding-right: 0;
  border: 0px;
  transition: all 0.3s;
}

.button-style:hover {
  transform: translateY(-2px);
  opacity: 0.8;
}

:deep(.el-form-item__label) {
  color: var(--el-text-color-regular, #606266);
  width: 100px;
  margin-top: 10px;
  text-align: left;
}

.input-style,
.input-style-dangerous {
  width: 280px;
  background-color: white;
  border: 1px solid var(--el-border-color, #dcdfe6);
  border-radius: 4px;
}

.input-style-dangerous {
  width: 392px;
}

.text-style,
.text-style-dangerous {
  height: 100px;
  width: 280px;
  font-size: 14px;
  overflow-x: auto;
  background-color: white;
  border: 1px solid var(--el-border-color, #dcdfe6);
  outline: none;
  border-radius: 4px;
  padding: 5px 15px;
  color: var(--el-text-color-primary, #303133);
}

.text-style-dangerous {
  width: 392px;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: var(--el-color-primary, #409EFF);
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: var(--el-color-primary, #409EFF);
  border-color: var(--el-color-primary, #409EFF);
}

:deep(.el-checkbox__label) {
  color: var(--el-text-color-regular, #606266);
}

:deep(.el-button--primary) {
  background-color: var(--el-color-primary, #409EFF);
  border-color: var(--el-color-primary, #409EFF);
}

:deep(.el-button--primary:hover) {
  background-color: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-3);
}

@media screen and (max-width: 768px) {
  .add-content,
  .add-content-dangerous {
    width: 90%;
    left: 5%;
    right: 5%;
  }
}
</style>

<style scoped src="@/assets/datePark.css"></style>