<template>
  <div class="box" style="width: 1430px">
    <div class="box_title">
      <div style="display: flex; align-items: center">
        <span class="iconfont icon-yichangshujuchaxun"></span>
        AIS异常
      </div>
      <div>
        <i class="el-icon-minus el-icon"></i>
        <i class="el-icon-close el-icon" @click="cancel"></i>
        <!-- <img src="@/static/guanbi.png" @click="cancel" alt="" /> -->
      </div>
    </div>
    <div class="condition">
      <div class="condition_item">
        <label class="label-style">异常类型</label>
        <el-select clearable v-model="form.rule" placeholder="请选择" class="elWidth" :popper-append-to-body="false">
          <el-option :key="1" :value="1" label="大角度转向"></el-option>
          <el-option :key="2" :value="2" label="AIS信号丢失"></el-option>
          <el-option :key="3" :value="3" label="船舶失速"></el-option>
          <el-option :key="4" :value="4" label="套牌船"></el-option>
        </el-select>
      </div>

      <el-button type="primary" size="medium" class="screen">筛选</el-button>
    </div>
    <div style="display: flex; align-items: flex-start">
      <div style="width: 100%">
        <el-table :data="tableData" style="width: 100%" height="500">
          <el-table-column header-align="center" prop="location" label="定位" width="80">
          </el-table-column>
          <el-table-column header-align="center" prop="name" label="英文船名" width="150">
          </el-table-column>
          <el-table-column header-align="center" prop="name" label="中文船名" width="130">
          </el-table-column>
          <el-table-column header-align="center" prop="MMSI" label="mmsi" width="100">
          </el-table-column>
          <el-table-column header-align="center" prop="type" label="异常类型" width="80">
          </el-table-column>
          <el-table-column header-align="center" prop="length" label="异常发生时间" width="160">
          </el-table-column>
          <el-table-column header-align="center" prop="weigth" label="经度" width="140">
          </el-table-column>
          <el-table-column header-align="center" prop="direction" label="纬度" width="140">
          </el-table-column>
          <el-table-column header-align="center" prop="time" label="航速" width="70">
          </el-table-column>
          <el-table-column header-align="center" prop="time" label="航向" width="70">
          </el-table-column><el-table-column header-align="center" prop="time" label="船舶类型" width="100">
          </el-table-column><el-table-column header-align="center" prop="time" label="船宽" width="80">
          </el-table-column><el-table-column header-align="center" prop="time" label="船长" width="80">
          </el-table-column><el-table-column header-align="center" prop="time" label="异常发生时间" width="160">
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="partition"></div>
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage4"
      :page-sizes="[30, 50, 100, 200]" :page-size="30" layout="->,total, sizes, prev, pager, next, jumper" :total="0">
    </el-pagination>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

interface FormState {
  time: string
  type: string
  region: string
  rule: number | string
  keyword: string
}

// 定义组件需要触发的事件
const emit = defineEmits<{
  (e: 'closeAnomaly'): void
}>()

// 响应式数据
const form = reactive<FormState>({
  time: "",
  type: "",
  region: "",
  rule: "",
  keyword: "",
})

const tableData = ref<any[]>([])
const currentPage4 = ref(4)

// 方法
function cancel() {
  emit('closeAnomaly')
}

function handleSizeChange(val: number) {
  console.log(`每页 ${val} 条`)
}

function handleCurrentChange(val: number) {
  console.log(`当前页: ${val}`)
}
</script>

<style scoped>
.label-style {
  width: 80px;
  text-align: right;
  margin-left: 10px;
  font-size: 14px;
  padding: 0 12px 0 0;
  color: rgb(253, 253, 253);
  font-weight: 500;
}

.partition {
  padding: 5px;
}

.screen {
  color: #fff;
  background-color: rgb(103, 113, 183);
  border-color: rgb(103, 113, 183);
}

.screen:hover,
.screen:focus {
  background: rgb(142, 151, 217);
  border-color: rgb(142, 151, 217);
  color: var(--el-button-font-color);
}

.el-icon {
  margin-left: 5px;
  cursor: pointer;
}

.box {
  display: flex;
  flex-direction: column;
  background: rgba(60, 65, 103, 0.95);
  color: white;
  box-shadow: 0 0 7px 1px rgba(0, 0, 0, 0.55);
  overflow: hidden;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  z-index: 100000;
  min-width: 1300px;
  /* color: #ffffff; */
  /* background-color: rgb(60 65 103 / 95%); */
  /* box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5); */
}

.box_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.box_title img {
  width: 20px;
  height: 20px;
}

.box_title>img:hover {
  cursor: pointer;
}

.condition {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}

.condition_item {
  margin-right: 20px;
}

.addBox_list_inp {
  width: 180px;
  height: 36px;
  margin-left: 20px;
  border: 1px solid #4376ec;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.elWidth {
  width: 180px;
}

input {
  border: none;
  outline: none;

  background: #000000;
  color: #ffffff;
}

#echartsType {
  width: 100%;
  height: 250px;
}

#echartsLength {
  width: 100%;
  height: 250px;
}

#echartsFlow {
  width: 100%;
  height: 250px;
}

.echartsName {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
</style>