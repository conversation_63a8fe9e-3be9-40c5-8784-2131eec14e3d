<template>
  <div>
    <div class="flex-column;">
      <div class="scrollbar-fbox">
        <el-scrollbar style="height: 100%">
          <!-- 滚动条要包裹的内容 -->
          <div style="width: 92%; margin: 0 auto">
            <!-- 系统主题 -->
            <!-- <div class="system-theme">
              <div class="system-setup-label">系统主题</div>
              <div class="system-setup-radio">
                <el-radio-group v-model="radio">
                  <el-radio :label="3">蓝色天空</el-radio>
                  <el-radio :label="6">深色主题</el-radio>
                  <el-radio :label="9">默认主题</el-radio>
                </el-radio-group>
              </div>
            </div> -->
            <!-- 状态配置 -->
            <!-- <div class="status-configuration">
              <div class="system-setup-label">状态配置</div>
              <div class="system-setup-radio">
                <el-checkbox>船舶国籍标签</el-checkbox>
              </div>
            </div> -->
            <!-- 静音时长 -->
            <!-- <div class="status-configuration">
              <div class="system-setup-label">静音时长</div>
              <div class="mute-duration">
                <el-radio v-model="radio" label="1">4小时</el-radio>
                <el-radio v-model="radio" label="2">8小时</el-radio>
                <el-radio v-model="radio" label="2">12小时</el-radio>
              </div>
              <div class="mute-duration">
                <el-radio-group v-model="radio">
                  <el-radio label="24小时"></el-radio>
                  <el-radio label="48小时"></el-radio>
                  <el-radio label="不静音"></el-radio>
                </el-radio-group>
              </div>
            </div> -->
            <!-- 底图设置 -->
            <div class="status-configuration">
              <div class="system-setup-label">底图设置</div>
              <div class="system-setup-radio">
                <div>
                  <div>
                    <el-radio v-model="form.oceanMap" label="海图" @change="changeOcean">海图</el-radio>
                    <!-- <div class="padd-left">
                      <div class="small-title">-模式-</div>
                      <div>
                        <el-radio-group v-model="radio">
                          <el-radio :label="3">仅海图</el-radio>
                          <el-radio :label="6">海图+陆图</el-radio>
                          <el-radio :label="9">海图+卫星图</el-radio>
                        </el-radio-group>
                      </div>
                    </div> -->
                    <div class="padd-left">
                      <div class="small-title">-海图色调-</div>
                      <div>
                        <el-radio-group v-model="form.time" @change="changeTime">
                          <el-radio :label="1" >白天</el-radio>
                          <el-radio :label="2" >黄昏</el-radio>
                          <el-radio :label="3" >夜晚</el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                    <div class="padd-left">
                      <!-- <el-checkbox v-model="checked">水深点</el-checkbox> -->
                    </div>
                  </div>
                  <div>
                    <!-- <el-radio v-model="radio" label="2">道路图</el-radio> -->
                  </div>
                  <div>
                    <el-radio v-model="form.satelliteMap" @change="changeSatellite" label="卫星图">卫星图</el-radio>
                  </div>
                </div>
              </div>
            </div>
            <!-- 图层控制 -->
            <!-- <div class="status-configuration">
              <div class="system-setup-label">图层控制</div>
              <div class="system-setup-radio">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    width: 100%;
                  "
                >
                  <el-checkbox-group v-model="form.type">
                    <el-checkbox label="船舶" name="type"></el-checkbox>
                    <el-checkbox label="标绘" name="type"></el-checkbox>
                    <el-checkbox label="观测截面" name="type"></el-checkbox>
                    <el-checkbox label="危险品锚地" name="type"></el-checkbox>
                    <el-checkbox label="军事禁区" name="type"></el-checkbox>
                    <el-checkbox label="海底管道区" name="type"></el-checkbox>
                    <el-checkbox label="辖区" name="type"></el-checkbox>
                    <el-checkbox label="锚地" name="type"></el-checkbox>
                    <el-checkbox label="码头" name="type"></el-checkbox>
                    <el-checkbox label="养殖区" name="type"></el-checkbox>
                    <el-checkbox label="禁止采砂区" name="type"></el-checkbox>
                    <el-checkbox label="水道" name="type"></el-checkbox>
                    <el-checkbox label="VTS服务区" name="type"></el-checkbox>
                    <el-checkbox label="自定义围栏" name="type"></el-checkbox>
                    <el-checkbox label="港区" name="type"></el-checkbox>
                    <el-checkbox label="停泊点" name="type"></el-checkbox>
                    <el-checkbox label="施工作业区" name="type"></el-checkbox>
                    <el-checkbox label="自定义要素" name="type"></el-checkbox>
                    <el-checkbox label="视频" name="type"></el-checkbox>
                    <el-checkbox label="视频监控范围" name="type"></el-checkbox>
                    <el-checkbox label="应急资源" name="type"></el-checkbox>
                    <el-checkbox label="船舶热力图" name="type"></el-checkbox
                  ></el-checkbox-group>
                </div>
              </div>
            </div> -->
            <!-- 船舶筛选 -->
            <div class="status-configuration">
              <div class="system-setup-label">船舶筛选</div>
              <div class="system-setup-radio">
                <!-- 船舶类型 -->
                <div>
                  <div class="ship-type-label">
                    <span>船舶类型</span>
                    <el-button class="button-style" @click="wholeSelect">全选</el-button>
                    <el-button class="button-style" @click="reverSelect">反选</el-button>
                  </div>
                  <div class="ship-type-checkbox">
                    <el-checkbox label="客船" name="type" v-model="shipType.passenger" @change="changeShipType"></el-checkbox>
                    <el-checkbox label="货船" name="type" v-model="shipType.goods" @change="changeShipType"></el-checkbox>
                    
                    <el-checkbox label="作业船" name="type" v-model="shipType.assignment" @change="changeShipType"></el-checkbox>
                    <el-checkbox label="拖船" name="type" v-model="shipType.tug" @change="changeShipType"></el-checkbox>
                    <el-checkbox label="渔船" name="type" v-model="shipType.fishing" @change="changeShipType"></el-checkbox>
                    <el-checkbox label="其他" name="type" v-model="shipType.other" @change="changeShipType"></el-checkbox>
                   
                  </div>
                </div>
                <!-- 国内外 -->
                <!-- <div>
                  <div class="ship-type-label">
                    <span>国内外</span>
                  </div>
                  <div class="ship-type-checkbox">
                    <el-checkbox label="国内" name="type"></el-checkbox>
                    <el-checkbox label="国外" name="type"></el-checkbox>
                    <el-checkbox label="未知" name="type"></el-checkbox>
                  </div>
                </div> -->
                <!-- 船舶国籍 -->
                <!-- <div>
                  <div class="ship-type-label">
                    <span>船舶国籍</span>
                  </div>
                  <div class="ship-type-checkbox">
                    <el-select v-model="value1" multiple placeholder="请选择">
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div> -->
                <!-- 船舶尺寸(米) -->
                <!-- <div>
                  <div class="ship-type-label">
                    <span>船舶尺寸(米)</span>
                    <el-button
                      class="button-style"
                      @click="onCustomLength"
                      v-if="isCustomLengthFont"
                      >自定义长度</el-button
                    >
                    <el-button
                      class="button-style"
                      @click="onCancelCustomLength"
                      v-if="isCustomLengthAfter"
                      >取消自定义</el-button
                    >
                  </div>
                  <div class="ship-type-checkbox" v-if="isCustomLengthFont">
                    <el-checkbox label="0-50" name="type"></el-checkbox>
                    <el-checkbox label="50-100" name="type"></el-checkbox>
                    <el-checkbox label="100-200" name="type"></el-checkbox>
                    <el-checkbox label="大于200" name="type"></el-checkbox>
                  </div>
                  <div style="width: 80%" v-if="isCustomLengthAfter">
                    <el-input style="width: 55px" size="mini"></el-input>
                    <span style="margin-right: 5px; margin-left: 5px">-</span>
                    <el-input style="width: 55px" size="mini"></el-input>
                    <el-button
                      size="mini"
                      style="margin-left: 37px"
                      type="primary"
                      >确定</el-button
                    >
                  </div>
                </div> -->
                <!-- 船舶航速(节)  -->
                <!-- <div>
                  <div class="ship-type-label">
                    <span>船舶航速(节)</span>
                    <el-button
                      class="button-style"
                      @click="onCustomSpeed"
                      v-if="isCustomSpeedFont"
                      >自定义航速</el-button
                    >
                    <el-button
                      class="button-style"
                      @click="onCancelCustomSpeed"
                      v-if="isCustomAfter"
                      >取消自定义</el-button
                    >
                  </div>
                  <div class="ship-type-checkbox" v-if="isCustomSpeedFont">
                    <el-checkbox label="0-1" name="type"></el-checkbox>
                    <el-checkbox label="1-5" name="type"></el-checkbox>
                    <el-checkbox label="5-10" name="type"></el-checkbox>
                    <el-checkbox label="10-20" name="type"></el-checkbox>
                    <el-checkbox label="20以上" name="type"></el-checkbox>
                  </div>
                  <div style="width: 80%" v-if="isCustomAfter">
                    <el-input style="width: 55px" size="mini"></el-input>
                    <span style="margin-right: 5px; margin-left: 5px">-</span>
                    <el-input style="width: 55px" size="mini"></el-input>
                    <el-button
                      size="mini"
                      style="margin-left: 37px"
                      type="primary"
                      >确定</el-button
                    >
                  </div>
                </div> -->
                <!-- 船舶行驶状态  -->
                <div>
                  <div class="ship-type-label">
                    <span>船舶行驶状态</span>
                  </div>
                  <div class="ship-type-checkbox">
                    <el-checkbox label="在航" name="type" v-model="shipType.sail" @change="changeShipType"></el-checkbox>
                    <el-checkbox label="停泊" name="type" v-model="shipType.berth" @change="changeShipType"></el-checkbox>
                    <el-checkbox label="未知" name="type" v-model="shipType.unknown" @change="changeShipType"></el-checkbox>
                  </div>
                </div>
                <!-- 船舶信号源  -->
                <!-- <div>
                  <div class="ship-type-label">
                    <span>船舶信号源</span>
                  </div>
                  <div class="ship-type-checkbox">
                    <el-checkbox label="AIS信号" name="type"></el-checkbox>
                    <el-checkbox label="雷达信号" name="type"></el-checkbox>
                    <el-checkbox label="北斗信号" name="type"></el-checkbox>
                  </div>
                </div> -->
              </div>
            </div>
            <!-- 船舶色彩设置 -->
            <!-- <div class="system-theme">
              <div class="system-setup-label">船舶色彩设置</div>
              <div class="system-setup-radio">
                <el-radio-group v-model="radio">
                  <el-radio :label="3">船舶类型</el-radio>
                  <el-radio :label="6">国内外</el-radio>
                  <el-radio :label="6">单色船舶</el-radio>
                  <el-radio :label="9">商渔船</el-radio>
                </el-radio-group>
              </div>
            </div> -->
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>
  
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { getShipList } from "@/api/map/map.js";

// 声明全局类型
declare global {
  interface Window {
    T: any;
  }
}

// 定义类型接口
interface FormState {
  oceanMap: boolean | string;
  satelliteMap: boolean | string;
  time: number;
  sail: boolean;
  berth: boolean;
}

interface ShipTypeState {
  passenger: boolean; // 客船
  goods: boolean;     // 货船
  Liquefied: boolean; // 液化船
  work: boolean;      // 工作船
  assignment: boolean;// 作业船
  tug: boolean;       // 拖船
  fishing: boolean;   // 渔船
  other: boolean;     // 其他
  sail: boolean;      // 在航
  berth: boolean;     // 停泊
  unknown: boolean;   // 未知
}

interface MapObject {
  panTo: (lngLat: any) => void;
  removeOverLay: (overlay: any) => void;
  addOverLay: (overlay: any) => void;
}

// Props定义
const props = defineProps<{
  map?: MapObject;
}>();

// Emits定义
const emit = defineEmits(['closeList']);

// 响应式数据
const checked = ref('');
const radio = ref(3);
const checkList = ref<string[]>([]);
const isCustomLengthFont = ref(true);
const isCustomLengthAfter = ref(false);
const isCustomSpeedFont = ref(true);
const isCustomAfter = ref(false);
const options = reactive([
  { value: "选项1", label: "中国" },
  { value: "选项2", label: "中国台湾" },
  { value: "选项3", label: "中国澳门" },
  { value: "选项4", label: "中国香港" },
  { value: "选项5", label: "不丹" },
]);
const value1 = ref<string[]>([]);
const activeName = ref("first"); // Tabs 标签页
const input = ref("");
const marker = ref<any>(null);
const selectVal = ref(1);
const threeNone = ref<any[]>([]);
const fishing = ref<any[]>([]);
const enforce = ref<any[]>([]);
const interest = ref<any[]>([]);
const list = ref<any[]>([]);

// 表单数据
const form = reactive<FormState>({
  oceanMap: true,
  satelliteMap: false,
  time: 1,
  sail: true,
  berth: true,
});

// 船舶类型数据
const shipType = reactive<ShipTypeState>({
  passenger: true,  // 客船
  goods: true,      // 货船
  Liquefied: true,  // 液化船
  work: true,       // 工作船
  assignment: true, // 作业船
  tug: true,        // 拖船
  fishing: true,    // 渔船
  other: true,      // 其他
  sail: true,       // 在航
  berth: true,      // 停泊
  unknown: true,    // 未知
});

// 卫星图切换
const changeSatellite = () => {
  form.oceanMap = false;
  window.$eventBus.$emit('satellite');
};

// 海图切换
const changeOcean = () => {
  form.satelliteMap = false;
  window.$eventBus.$emit('oceanMap');
};

// 时间变化
const changeTime = (e: number) => {
  form.oceanMap = true;
  changeOcean();
  window.$eventBus.$emit('time', e);
};

// 全选
const wholeSelect = () => {
  for (let key in shipType) {
    shipType[key as keyof ShipTypeState] = true;
  }
};

// 反选
const reverSelect = () => {
  for (let key in shipType) {
    shipType[key as keyof ShipTypeState] = !shipType[key as keyof ShipTypeState];
  }
};

// 船舶类型变化
const changeShipType = () => {
  window.$eventBus.$emit('showShipType', shipType);
};

// 自定义长度
const onCustomLength = () => {
  isCustomLengthFont.value = false;
  isCustomLengthAfter.value = true;
};

// 自定义航速
const onCustomSpeed = () => {
  isCustomSpeedFont.value = false;
  isCustomAfter.value = true;
};

// 取消自定义长度
const onCancelCustomLength = () => {
  isCustomLengthFont.value = true;
  isCustomLengthAfter.value = false;
};

// 取消自定义航速
const onCancelCustomSpeed = () => {
  isCustomSpeedFont.value = true;
  isCustomAfter.value = false;
};

// 获取船信息
const getShipInfo = () => {
  // getShipList({}).then((res: any) => {
  //   if (res.code == 200) {
  //     // tableData = res.rows;
  //     // shipTotal = res.total;
  //   }
  // });
};

// 关闭父层
const handToParentClose = () => {
  if (marker.value && props.map) {
    props.map.removeOverLay(marker.value);
  }

  // 关闭弹窗
  emit("closeList", 1);
};

// 点击船，查看位置
const getShip = (val: { lon: number; lat: number }) => {
  if (!props.map) return;
  
  if (marker.value) {
    props.map.removeOverLay(marker.value);
  }

  const lng = val.lon;
  const lat = val.lat;
  props.map.panTo(new window.T.LngLat(lng, lat));
  
  // 设置标记点
  const icon = new window.T.Icon({
    iconUrl: require("@/assets/images/jingzhun.png"),
    iconSize: new window.T.Point(25, 25),
    iconAnchor: new window.T.Point(12, 25),
  });
  
  // 创建标注对象
  marker.value = new window.T.Marker(new window.T.LngLat(lng, lat), {
    icon: icon,
  });
  
  // 向地图上添加标注
  props.map.addOverLay(marker.value);
};

// 切换Tabs 标签页
const handleClick = (tab: any, event: Event) => {
  console.log(tab, event);
};
</script>
  
<style scoped>
.small-title {
  margin-bottom: 8px;
  color: #a0a0a0;
}
/* 左边内边距样式 */
.padd-left {
  padding-left: 20px;
}
/* 滚动条父盒子样式 */
.scrollbar-fbox {
  height: calc(100vh - 90px);
  overflow: auto;
  margin-top: -16px;
}
/*  默认展示滚动条 */
:deep(.el-scrollbar__bar) {
  opacity: 1;
  width: 8px;
  border-radius: 0;
  background: #303452;
}
/* 改变滚动条颜色 */
:deep(.el-scrollbar__thumb) {
  background: #8e97d9;
}
* {
  box-sizing: border-box;
}
.ship-type-label {
  margin-bottom: 8px;
}
.button-style {
  margin-left: 20px;
  padding: 0;
  color: #409eff;
  background: 0 0;
  text-decoration: underline;
  border-color: transparent;
}
.mute-duration {
  display: flex;
  justify-content: space-between;
  width: 95%;
  margin: 0 auto;
}
/* 系统主题 */
.system-setup-label {
  margin-bottom: 0.1rem;
  font-weight: bold;
  color: #aee8ff;
  padding: 5px 15px 5px 15px;
  margin-left: -15px;
  margin-right: -15px;
  width: calc(100% + 30px);
  max-width: none;
}
.system-setup-radio {
  padding-left: 15px;
}
:deep(.el-radio) {
  color: #ffffff !important;
  margin-right: 8px;
  padding: 5px;
  display: inline-flex;
}

:deep(.is-active) {
  background-color: #3c4167 !important;
  color: #5fd3fc !important;
}
.el-tabs__nav-wrap::after {
  width: 0;
}
:deep(.el-tabs .el-tabs__nav-scroll) {
  width: 260px !important;
  background-color: rgb(42 47 83);
  color: #ffffff;
}
:deep(.el-tabs__item) {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff !important;
  cursor: default;
  user-select: none;
  color: #b0b0b0;
  padding: 20px 5px 20px 5px;
  height: 40px !important;
  writing-mode: horizontal-tb !important;
  padding: 0 !important;
  border-top: 0;
}
.el-button-primary:hover,
.el-button-primary:focus {
  background: rgb(142, 151, 217);
  border-color: rgb(142, 151, 217);
  color: var(--el-button-font-color);
}

.el-button-primary {
  background-color: rgb(103, 113, 183);
  border-color: rgb(103, 113, 183);
  color: #fff;
}
.el-icon {
  margin-left: 6px;
}

.icon-anchor-full {
  font-size: 14px;
}
.content {
  height: 100%;
  position: fixed;
  right: 0;
  top: 60px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.flex-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.el-icon-circle-close {
  font-size: 22px;
}
.content-txt1 {
  font-size: 16px;
  color: #cbd0f6;
}

.line {
  width: 100%;
  height: 1px;
  margin: 10px auto;
  background: #d5d5d5;
}
.area {
  margin: 20px 0;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  color: #ffffff;
}
.custom-tree-node-label {
  display: block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.tree {
  margin: 20px 0;
}
:deep(.el-input__inner) {
  background-color: #3c4167;
  border: 1px solid #3c4167;
  color: #ffffff;
}
:deep(.el-tree) {
  height: 100%;
  color: #ffffff;
  background-color: transparent;
  border: 1px solid #5e66a0;
}
:deep(.el-tree-node__content:hover) {
  background-color: rgb(32, 38, 70);
}
:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: rgb(32, 38, 70);
}
.ship-type-checkbox {
  display: flex;
  flex-wrap: wrap;
}
</style>