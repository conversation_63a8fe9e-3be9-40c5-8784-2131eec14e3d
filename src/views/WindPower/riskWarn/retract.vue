<template>
  <!-- 收起 -->
  <div class="static-retract">
    <button class="button-retract" @click="expandPop">
      <el-icon><Document /></el-icon>
      <span>{{ title }}</span>
    </button>
    <a href="javascript:void(0)" class="close-button" @click.prevent="cancel">
      <el-icon><Close /></el-icon>
    </a>
  </div>
</template>

<script setup lang="ts">
import { Document, Close } from '@element-plus/icons-vue'

// 定义 props
const props = defineProps<{
  title: string
}>()

// 定义需要触发的事件
const emit = defineEmits<{
  (e: 'cancel'): void
  (e: 'expand'): void
}>()

// 方法
function cancel() {
  emit('cancel')
}

function expandPop() {
  emit('expand')
}
</script>

<style scoped>
.static-retract {
  position: fixed;
  bottom: 10px;
  right: 600px;
  pointer-events: all;
  display: inline-block;
  z-index: 10;
}

.button-retract {
  padding-right: 28px;
  background: white;
  border: 1px solid var(--el-border-color-light, #e4e7ed);
  color: var(--el-text-color-primary, #303133);
  padding: 7px 28px 7px 15px;
  font-size: inherit;
  line-height: inherit;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-retract:hover {
  background: var(--el-fill-color-light, #f5f7fa);
  color: var(--el-color-primary, #409EFF);
  transform: translateY(-2px);
}

.button-retract .el-icon {
  margin-right: 5px;
  color: var(--el-color-primary, #409EFF);
}

.close-button {
  text-decoration: none;
  background-color: transparent;
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  top: 50%;
  right: 7px;
  margin-top: -8px;
  color: var(--el-text-color-secondary, #909399);
  opacity: 0.5;
  transition: all 0.3s ease;
}

.close-button:hover {
  opacity: 1;
  color: var(--el-color-primary, #409EFF);
  transform: scale(1.1);
}
</style>