<template>
  <div>
    <div style="display: inline">
      <div style="height: 100%">
        <div class="list-head">
          <div>
            <div>
              <span> 区域船舶数:</span>
              <span>0</span>
            </div>
            <div style="display: flex">
              <el-button
                class="el-button-primary"
                type="primary"
                plain
                size="small"
                @click="detailList"
              >
                <i class="el-icon-bank-card"></i>
                详细列表</el-button
              ><el-button
                class="el-button-primary"
                type="primary"
                plain
                size="small"
                @click="onScreen"
              >
                <i class="el-icon-table-lamp"></i>
                筛选</el-button
              ><el-button
                class="el-button-primary"
                type="primary"
                plain
                size="small"
              >
                <i class="el-icon-position"></i>
                导出</el-button
              >
            </div>
          </div>
        </div>
        <div class="tabs-box">
          <div class="list-title">
            <div
              :class="listIndex === 0 ? 'title-choose' : 'title-unchoose'"
              @click="changeList(0)"
            >
              在航
              <div class="list-num">0</div>
            </div>
            <div
              :class="listIndex === 1 ? 'title-choose' : 'title-unchoose'"
              @click="changeList(1)"
            >
              锚泊
              <div class="list-num">0</div>
            </div>
            <div
              :class="listIndex === 2 ? 'title-choose' : 'title-unchoose'"
              @click="changeList(2)"
            >
              靠泊
              <div class="list-num">0</div>
            </div>
          </div>
        </div>
        <div class="static-view" v-if="isStatic">
          <div class="static-head">
            <i class="el-icon-document"></i>
            <h5>区域船舶统计</h5>
            <span>
              <i class="el-icon-minus"></i>
              <i class="el-icon-close el-icon" @click="cancel"></i
            ></span>
          </div>
          <div class="static-body">
            <div style="display: inline">
              <div style="width: 830px; height: 400px; display: flex">
                <div style="width: 100%">
                  <el-tabs
                    v-model="activeName"
                    @tab-click="handleClick"
                    :stretch="true"
                    class="eTabs"
                  >
                    <el-tab-pane label="在航" name="first" class="el-tab-pane">
                      <div style="display: flex; align-items: flex-start">
                        <div style="width: 100%">
                          <el-table
                            size="small"
                            :data="tableData"
                            style="width: 100%; border: 1px solid #50577f"
                            height="300"
                          >
                            <el-table-column
                              header-align="center"
                              prop="location"
                              label="操作"
                              width="80"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="name"
                              label="中文船名"
                              width="130"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="name"
                              label="英文船名"
                              width="150"
                            >
                            </el-table-column>

                            <el-table-column
                              header-align="center"
                              prop="MMSI"
                              label="MMSI"
                              width="100"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="type"
                              label="船舶类型"
                              width="80"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="length"
                              label="国籍"
                              width="160"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="weigth"
                              label="下一港"
                              width="140"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="direction"
                              label="纬度"
                              width="140"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="time"
                              label="经度"
                              width="70"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="time"
                              label="航速(节)"
                              width="70"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="船长(米)"
                              width="100"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="船宽(米)"
                              width="80"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="更新时间"
                              width="160"
                            >
                            </el-table-column>
                          </el-table>
                          <div class="partition"></div>

                          <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage4"
                            :page-sizes="[30, 50, 100, 200]"
                            :page-size="30"
                            layout="->,total, sizes, prev, pager, next, jumper"
                            :total="0"
                          >
                          </el-pagination>
                        </div></div
                    ></el-tab-pane>
                    <el-tab-pane label="锚泊" name="second">
                      <div style="display: flex; align-items: flex-start">
                        <div style="width: 100%">
                          <el-table
                            size="small"
                            :data="tableData"
                            style="width: 100%; border: 1px solid #50577f"
                            height="300"
                          >
                            <el-table-column
                              header-align="center"
                              prop="location"
                              label="操作"
                              width="80"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="name"
                              label="中文船名"
                              width="130"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="name"
                              label="英文船名"
                              width="150"
                            >
                            </el-table-column>

                            <el-table-column
                              header-align="center"
                              prop="MMSI"
                              label="MMSI"
                              width="100"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="type"
                              label="船舶类型"
                              width="80"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="length"
                              label="国籍"
                              width="160"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="weigth"
                              label="下一港"
                              width="140"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="direction"
                              label="纬度"
                              width="140"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="time"
                              label="经度"
                              width="70"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="time"
                              label="航速(节)"
                              width="70"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="船长(米)"
                              width="100"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="船宽(米)"
                              width="80"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="更新时间"
                              width="160"
                            >
                            </el-table-column>
                          </el-table>
                          <div class="partition"></div>

                          <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage4"
                            :page-sizes="[30, 50, 100, 200]"
                            :page-size="30"
                            layout="->,total, sizes, prev, pager, next, jumper"
                            :total="0"
                          >
                          </el-pagination>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="靠泊" name="three">
                      <div style="display: flex; align-items: flex-start">
                        <div style="width: 100%">
                          <el-table
                            size="small"
                            :data="tableData"
                            style="width: 100%; border: 1px solid #50577f"
                            height="300"
                          >
                            <el-table-column
                              header-align="center"
                              prop="location"
                              label="操作"
                              width="80"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="name"
                              label="中文船名"
                              width="130"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="name"
                              label="英文船名"
                              width="150"
                            >
                            </el-table-column>

                            <el-table-column
                              header-align="center"
                              prop="MMSI"
                              label="MMSI"
                              width="100"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="type"
                              label="船舶类型"
                              width="80"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="length"
                              label="国籍"
                              width="160"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="weigth"
                              label="下一港"
                              width="140"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="direction"
                              label="纬度"
                              width="140"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="time"
                              label="经度"
                              width="70"
                            >
                            </el-table-column>
                            <el-table-column
                              header-align="center"
                              prop="time"
                              label="航速(节)"
                              width="70"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="船长(米)"
                              width="100"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="船宽(米)"
                              width="80"
                            >
                            </el-table-column
                            ><el-table-column
                              header-align="center"
                              prop="time"
                              label="更新时间"
                              width="160"
                            >
                            </el-table-column>
                          </el-table>
                          <div class="partition"></div>

                          <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage4"
                            :page-sizes="[30, 50, 100, 200]"
                            :page-size="30"
                            layout="->,total, sizes, prev, pager, next, jumper"
                            :total="0"
                          >
                          </el-pagination>
                        </div></div
                    ></el-tab-pane>
                  </el-tabs>
                  <el-button
                    class="button-chart"
                    type="primary"
                    plain
                    size="mini"
                    @click="onStaticChart"
                    >图表</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="stctic-chart" v-if="isChart">
          <div class="static-head">
            <i class="el-icon-document"></i>
            <h5>船舶: 0 艘</h5>
            <span>
              <i class="el-icon-minus"></i>
              <i class="el-icon-close el-icon" @click="cancelChart"></i
            ></span>
          </div>
        </div>
        <div class="static-screen" v-if="isScreen">
          <div class="static-head">
            <i class="el-icon-document"></i>
            <h5>船舶筛选</h5>
            <span>
              <i class="el-icon-minus"></i>
              <i class="el-icon-close el-icon" @click="cancelScreen"></i
            ></span>
          </div>
          <div class="static-screen-body">
            <!-- 船舶筛选 -->
            <div class="system-setup-radio">
              <!-- 船舶类型 -->
              <div>
                <div class="ship-type-label">
                  <span>船舶类型</span>
                  <el-button class="button-style">全选</el-button>
                  <el-button class="button-style">反选</el-button>
                </div>
                <div class="ship-type-checkbox">
                  <el-checkbox label="客船" name="type"></el-checkbox>
                  <el-checkbox label="货船" name="type"></el-checkbox>
                  <el-checkbox label="液化船" name="type"></el-checkbox>
                  <el-checkbox label="工作船" name="type"></el-checkbox>
                  <el-checkbox label="作业船" name="type"></el-checkbox>
                  <el-checkbox label="拖船" name="type"></el-checkbox>
                  <el-checkbox label="渔船" name="type"></el-checkbox>
                  <el-checkbox label="其他" name="type"></el-checkbox>
                  <el-checkbox label="北斗渔船" name="type"></el-checkbox>
                  <el-checkbox label="北斗游艇" name="type"></el-checkbox>
                  <el-checkbox label="北斗小艇" name="type"></el-checkbox>
                </div>
              </div>
              <!-- 国内外 -->
              <div>
                <div class="ship-type-label">
                  <span>国内外</span>
                </div>
                <div class="ship-type-checkbox">
                  <el-checkbox label="国内" name="type"></el-checkbox>
                  <el-checkbox label="国外" name="type"></el-checkbox>
                  <el-checkbox label="未知" name="type"></el-checkbox>
                </div>
              </div>
              <!-- 船舶国籍 -->
              <div>
                <div class="ship-type-label">
                  <span>船舶国籍</span>
                </div>
                <div class="ship-type-checkbox">
                  <el-select
                    v-model="value1"
                    multiple
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
              <!-- 船舶尺寸(米) -->
              <div>
                <div class="ship-type-label">
                  <span>船舶尺寸(米)</span>
                  <el-button class="button-style">自定义长度</el-button>
                </div>
                <div class="ship-type-checkbox">
                  <el-checkbox label="0-50" name="type"></el-checkbox>
                  <el-checkbox label="50-100" name="type"></el-checkbox>
                  <el-checkbox label="100-200" name="type"></el-checkbox>
                  <el-checkbox label="大于200" name="type"></el-checkbox>
                </div>
              </div>
              <!-- 船舶航速(节)  -->
              <div>
                <div class="ship-type-label">
                  <span>船舶航速(节)</span>
                  <el-button class="button-style">自定义航速</el-button>
                </div>
                <div class="ship-type-checkbox">
                  <el-checkbox label="0-1" name="type"></el-checkbox>
                  <el-checkbox label="1-5" name="type"></el-checkbox>
                  <el-checkbox label="5-10" name="type"></el-checkbox>
                  <el-checkbox label="10-20" name="type"></el-checkbox>
                  <el-checkbox label="20以上" name="type"></el-checkbox>
                </div>
              </div>
              <!-- 船舶行驶状态  -->
              <div>
                <div class="ship-type-label">
                  <span>船舶行驶状态</span>
                </div>
                <div class="ship-type-checkbox">
                  <el-checkbox label="在航" name="type"></el-checkbox>
                  <el-checkbox label="停泊" name="type"></el-checkbox>
                </div>
              </div>
              <!-- 船舶信号源  -->
              <div>
                <div class="ship-type-label">
                  <span>船舶信号源</span>
                </div>
                <div class="ship-type-checkbox">
                  <el-checkbox label="AIS信号" name="type"></el-checkbox>
                  <el-checkbox label="雷达信号" name="type"></el-checkbox>
                  <el-checkbox label="北斗信号" name="type"></el-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import type { TabsPaneContext } from 'element-plus';

// 定义类型接口
interface TableData {
  location?: string;
  name?: string;
  MMSI?: number;
  type?: string;
  length?: string;
  weigth?: string;
  direction?: string;
  time?: string;
  [key: string]: any;
}

// 声明全局类型
declare global {
  interface Window {
    T: any;
  }
}

// 数据声明
const listIndex = ref(0);
const isStatic = ref(false);
const isChart = ref(false);
const isScreen = ref(false);
const activeName = ref('second');
const currentPage4 = ref(4);
const tableData = ref<TableData[]>([]);

// 生命周期钩子
onMounted(() => {
  isStatic.value = true;
});

// 方法声明
const changeList = (type: number) => {
  listIndex.value = type;
};

const detailList = () => {
  isStatic.value = true;
};

const cancel = () => {
  isStatic.value = false;
};

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};

const handleSizeChange = (val: number) => {
  console.log(`每页 ${val} 条`);
};

const handleCurrentChange = (val: number) => {
  console.log(`当前页: ${val}`);
};

const onStaticChart = () => {
  isChart.value = true;
};

const cancelChart = () => {
  isChart.value = false;
};

const onScreen = () => {
  isScreen.value = true;
};

const cancelScreen = () => {
  isScreen.value = false;
};
</script>

<style scoped>
.system-setup-radio {
  padding-left: 15px;
}
.button-chart {
  position: absolute;
  top: 14%;
  right: 5%;
  color: #fff;
}
.partition {
  padding: 5px;
}

.list-head {
  padding: 7px 10px;
}
.tabs-box {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}
.list-title {
  margin-top: 15px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  align-items: center;
}
.title-choose {
  width: 50%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #5fd3fc;
  position: relative;
  cursor: pointer;
}

.title-choose::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #5fd3fc;
}

.title-unchoose {
  width: 50%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  position: relative;
  cursor: pointer;
}

.title-unchoose::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}
.list-num {
  position: absolute;
  top: 0;
  left: 65%;
  background: #f56c6c;
  border: 1px solid #fff;
  border-radius: 10px;
  min-width: 24px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: #fff;
  padding: 0 4px;
  font-size: 14px;
}
.static-view {
  width: 43%;
  height: 46%;
  background: rgba(60, 65, 103, 0.95);
  color: white;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.55);
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0%;
  left: 8.7%;
}
.stctic-chart {
  width: 20%;
  height: 32%;
  background: rgba(60, 65, 103, 0.95);
  color: white;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.55);
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  bottom: 0%;
  right: 18.3%;
}
.static-screen {
  width: 26%;
  height: 61%;
  background: rgba(60, 65, 103, 0.95);
  color: white;
  box-shadow: 0px 0px 7px 1px rgba(0, 0, 0, 0.55);
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  top: 25%;
  left: 35%;
}
.static-head {
  display: flex;
  font-size: 16px;
  align-items: center;
  height: 40px;
}
.static-head > h5 {
  margin: 0;
  flex: 1;
  font-size: 1em;
  padding: 5px 8px;
  cursor: default;
}

.el-icon {
  margin-left: 5px;
}
.static-body {
  padding: 10px 15px;
  overflow: hidden;
}
:deep(.el-tabs .el-tabs__nav-scroll) {
  width: 300px !important;
  background-color: transparent !important;
  color: #ffffff;
}
:deep(.el-tabs__item) {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff !important;
  cursor: default;
  user-select: none;
  color: #b0b0b0;
  padding: 20px 5px 20px 5px;
  height: 40px !important;
  writing-mode: horizontal-tb !important;
  padding: 0 !important;
  border-top: 0;
}
/* 更改tabs切换标签下的蓝色下划线 */
:deep(.el-tabs__active-bar) {
  background-color: #5fd3fc !important;
}
/* 改变 element的el-tabs默认选中项 文字颜色 */
:deep(.el-tabs__item.is-active) {
  color: #5fd3fc !important;
  padding: 0 !important;
}
</style>