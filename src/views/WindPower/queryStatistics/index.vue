<template>
  <div class="flex-col">
    <el-dialog
      :title="title"
      v-model="visible"
      :modal="false"
      custom-class="customDialog"
      @close="closeDialog"
      @opened="init"
      :modal-append-to-body="true"
      width="75%"
    >
      <div class="box" v-if="title == 'AIS异常记录'">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="10">
            <el-col :span="10">
              <el-form-item label="时间" label-width="60px">
                <el-date-picker
                  v-model="form.time"
                  type="datetimerange"
                  range-separator="至"
                  style="width: 90%"
                  popper-class="elDatePicker"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="mmsi">
                <el-input
                  v-model="form.id"
                  placeholder="请输入mmsi"
                  type="text"
                  maxlength="30"
                  style="width: 90%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="船名">
                <el-input
                  v-model="form.id"
                  placeholder="请输入船舶名称"
                  type="text"
                  maxlength="30"
                  style="width: 90%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="" label-width="0">
                <el-button type="primary" size="small">查询</el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="异常类型">
                <el-select
                  v-model="form.rule"
                  placeholder="请选择"
                  popper-class="elDateSeletc"
                  clearable
                  style="width: 90%"
                >
                  <el-option :key="1" :value="1" label="进"></el-option>
                  <el-option :key="2" :value="2" label="出"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="" label-width="0">
                <el-button type="primary" size="small">筛选</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row :gutter="10" type="flex">
          <el-col :span="24">
            <el-table :data="tableData" style="width: 100%" height="500">
              <el-table-column prop="" label="MMSI" width="150" />
              <el-table-column prop="" label="英文名称" width="120" />
              <el-table-column prop="" label="中文名称" width="120" />
              <el-table-column prop="" label="航速" width="120" />
              <el-table-column prop="" label="航向" width="120" />
              <el-table-column prop="" label="异常类型" width="120" />
              <el-table-column prop="" label="异常发生时间" width="150" />
              <el-table-column prop="" label="异常时所在经度" width="150" />
              <el-table-column prop="" label="异常时所在纬度" width="150" />
              <el-table-column prop="" label="异常时所在区域名称" width="150" />
              <el-table-column prop="" label="异常时所在区域类型" width="150" />
              <el-table-column prop="" label="异常恢复时经度" width="150" />
              <el-table-column prop="" label="异常恢复时纬度" width="150" />
              <el-table-column
                prop=""
                label="定位"
                align="center"
                fixed="right"
                width="120"
              />
            </el-table>
            <pagination
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              popper-class="elDateSeletc"
              :background="false"
              @pagination="getList"
            />
          </el-col>
        </el-row>
      </div>

      <div class="box" v-if="title == '报警查询'">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row :gutter="10">
            <el-col :span="10">
              <el-form-item label="时间" label-width="60px">
                <el-date-picker
                  v-model="form.time"
                  type="datetimerange"
                  range-separator="至"
                  style="width: 90%"
                  popper-class="elDatePicker"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="warningTime"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="14">
              <el-row :gutter="10">
                <el-col :span="10">
                  <el-form-item label="报警区域">
                    <el-select
                      v-model="form.rule"
                      placeholder="全部类型"
                      popper-class="elDateSeletc"
                      style="width: 90%"
                      clearable
                    >
                      <el-option :key="1" :value="1" label="1"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="" label-width="0">
                    <el-select
                      v-model="form.rule"
                      placeholder="全部区域"
                      popper-class="elDateSeletc"
                      clearable
                    >
                      <el-option :key="1" :value="1" label="2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="报警规则">
                <el-select
                  v-model="form.rule"
                  placeholder="全部规则"
                  popper-class="elDateSeletc"
                  clearable
                  style="width: 90%"
                >
                  <el-option :key="1" :value="1" label="1"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="关键字">
                <el-input
                  v-model="form.id"
                  placeholder="请输入关键字"
                  type="text"
                  maxlength="30"
                  style="width: 90%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="处置状态">
                <el-select
                  v-model="form.rule"
                  placeholder="请选择"
                  popper-class="elDateSeletc"
                  clearable
                  style="width: 90%"
                >
                  <el-option :key="1" :value="1" label="1"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="" label-width="0">
                <el-button type="primary" size="small">查询</el-button>
                <el-button type="primary" size="small">导出</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row :gutter="10" type="flex">
          <el-col :span="24">
            <el-table :data="list" style="width: 100%" height="500">
              <el-table-column prop="mmsi" label="MMSI" min-width="150" />
              <el-table-column prop="shipName" label="船名" min-width="150" />
              <el-table-column
                prop="address"
                label="报警区域"
                min-width="150"
              />
              <el-table-column
                prop="eventContent"
                label="报警规则"
                min-width="150"
              />
              <el-table-column
                prop="warningTime"
                label="报警时间"
                min-width="150"
              >
                <template #default="scope">
                  <div>
                    {{ new Date(scope.rows.warningTime).toLocaleString() }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="" label="处理人" min-width="120" />
              <el-table-column prop="" label="处置状态" min-width="120" />
              <el-table-column prop="" label="处置时间" min-width="150" />
              <el-table-column
                prop=""
                label="操作"
                fixed="right"
                align="center"
                min-width="120"
              />
            </el-table>
            <pagination
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              popper-class="elDateSeletc"
              :background="false"
              @pagination="getWarningList"
            />
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>
  
<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { Place } from "@/utils/request.js";
import { warningList } from "@/api/map/earlyWarning";
import * as echarts from 'echarts';

// 定义类型接口
interface FormData {
  time: string;
  type: string;
  region: string;
  rule: string | number;
  keyword: string;
  id?: string | number;
}

interface QueryParams {
  pageSize: number;
  pageNum: number;
  isAsc: string;
  orderByColumn: string;
  gmtCreate: string | null;
  gmtModified: string | null;
  eventContent: string | null;
  envType: string | null;
  place: string | null;
}

interface TableItem {
  [key: string]: any;
}

// 定义props
const props = defineProps<{
  id: string;
}>();

// 定义响应式数据
const title = ref<string>("");
const visible = ref<boolean>(false);
const visibleRecord = ref<boolean>(false);
const titleRecord = ref<string>("");
const visibleToday = ref<boolean>(false);
const visibleEveryDay = ref<boolean>(false);
const total = ref<number>(0);
const tableData = ref<TableItem[]>([]);
const list = ref<TableItem[]>([]);
const rules = ref<Record<string, any>>({});

const form = reactive<FormData>({
  time: "",
  type: "",
  region: "",
  rule: "",
  keyword: "",
});

const queryParams = reactive<QueryParams>({
  pageSize: 1,
  pageNum: 10,
  isAsc: "desc",
  orderByColumn: "id",
  gmtCreate: null,
  gmtModified: null,
  eventContent: null,
  envType: null,
  place: null,
});

// 监听id变化
watch(() => props.id, (val) => {
  visible.value = true;
  if (val == "aisanomalyRecord") {
    title.value = "AIS异常记录";
  } else if (val == "alarmQuery") {
    title.value = "报警查询";
  } else {
    // 这里需要访问父组件的refsvalue属性，在Vue3中需要使用emit让父组件处理
    closeDialog();
    visible.value = false;
  }
});

// 生命周期钩子
onMounted(() => {
  queryParams.place = Place;
  getWarningList();
});

// 获取报警列表
const getWarningList = () => {
  // 暂时注释掉API调用，防止出现undefined错误
  // 在实际项目中，你应该确保warningList API正确实现并返回Promise
  console.log('获取报警列表，当前查询参数:', queryParams);
  
  // 模拟数据
  list.value = [];
  total.value = 0; // 确保total是number类型
  
  // warningList(queryParams).then((res) => {
  //   if (res.code == 200) {
  //     list.value = res.rows;
  //     total.value = res.total;
  //   }
  // });
};

// 警告时间变化
const warningTime = (e: [string, string]) => {
  console.log(e);
  const start = e[0].replace(/-/g, "/");
  const end = e[1].replace(/-/g, "/");
  queryParams.gmtCreate = start;
  queryParams.gmtModified = end;
  queryParams.pageNum = 1;
  list.value = [];
  getWarningList();
};

// 显示记录
const clickToShowRecord = (type: number) => {
  if (type == 1) {
    titleRecord.value = "近半年常来船舶列表";
  } else {
    titleRecord.value = "每年常来船舶列表";
  }
  visibleRecord.value = true;
};

// 今日
const clickToToday = () => {
  visibleToday.value = true;
};

// 每日
const clickToEveryDay = () => {
  visibleEveryDay.value = true;
};

// 获取列表
const getList = () => {};

// 关闭对话框
const closeDialog = () => {
  // 这里需要触发一个事件通知父组件重置refsvalue
  const parent = getCurrentInstance()?.parent;
  if (parent && parent.exposed) {
    parent.exposed.refsvalue = "";
  }
};

// 初始化
const init = () => {
  if (title.value == "截面流量" || title.value == "辖区进出记录") {
    setechartsFlow();
  }
};

// 设置图表
const setechartsFlow = () => {
  // 这里可以实现图表初始化
};

// 初始化1
const init1 = () => {
  const chart2 = document.getElementById("echartsFlow2");
  if (!chart2) return;
  
  const myChart2 = echarts.init(chart2);
  const xData = [
    "2023-02-16",
    "2023-02-17",
    "2023-02-18",
    "2023-02-19",
    "2023-02-20",
    "2023-02-21",
    "2023-02-22",
  ];
  const option = {
    grid: {
      left: "5%",
      right: "10%",
      top: "20%",
      bottom: "15%",
      containLabel: true,
    },
    tooltip: {
      show: true,
      trigger: "axis",
    },
    xAxis: [
      {
        type: "category",
        axisLine: {
          show: true,
          lineStyle: {
            color: "#fff",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "#fff",
          margin: 6,
        },
        splitLine: {
          show: false,
        },
        boundaryGap: ["5%", "5%"],
        data: xData,
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "流量趋势（小时）",
        position: "left",
        nameTextStyle: {
          color: "#FFFFFF",
          fontSize: 14,
          padding: [0, 0, 6, 16],
        },
        axisLabel: {
          color: "#fff",
          margin: 6,
        },
        splitLine: {
          lineStyle: {
            color: "#fff",
          },
        },
      },
      {
        type: "value",
        name: "流量趋势（20分钟）",
        position: "right",
        nameTextStyle: {
          color: "#FFFFFF",
          fontSize: 14,
          padding: [0, 0, 6, 16],
        },
        axisLabel: {
          color: "#fff",
          margin: 6,
        },
        splitLine: {
          lineStyle: {
            color: "#fff",
          },
        },
      },
    ],
    series: [
      {
        name: "流量趋势（小时）",
        type: "line",
        data: [0, 8, 11, 21, 12, 5, 12],
      },
      {
        name: "流量趋势（20分钟）",
        type: "line",
        data: [10, 8, 2, 2, 3, 5, 1],
      },
    ],
  };
  myChart2.setOption(option);
};
</script>

<style scoped>
.box {
  z-index: 1000;
  color: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
}

.box_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.box_title img {
  width: 20px;
  height: 20px;
}

.box_title > img:hover {
  cursor: pointer;
}

.condition {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}

.condition_item {
  margin-right: 20px;
}

.addBox_list_inp {
  width: 180px;
  height: 36px;
  margin-left: 20px;
  border: 1px solid #4376ec;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.elWidth {
  width: 180px;
  margin-left: 6px;
}

input {
  border: none;
  outline: none;
  background: #000000;
  color: #ffffff;
}

#echartsFlow1 {
  width: 100%;
  height: 250px;
}

#echartsFlow {
  width: 100%;
  height: 250px;
}

#echartsFlow2 {
  width: 100%;
  height: 500px;
}

.echartsName {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-col {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
}

.list-title {
  margin-top: 15px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  align-items: center;
}

.list-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 15px;
}

.row-line {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.title-choose {
  width: 50%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #5fd3fc;
  position: relative;
  cursor: pointer;
}

.title-choose::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #5fd3fc;
}

.title-unchoose {
  width: 50%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  position: relative;
  cursor: pointer;
}

.title-unchoose::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.list-num {
  position: absolute;
  top: 0;
  left: 65%;
  background: #409eff;
  border: 1px solid #fff;
  border-radius: 10px;
  min-width: 24px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: #fff;
  padding: 0 4px;
  font-size: 14px;
}

.nav-itemtxt {
  display: flex;
  flex-direction: row;
  color: #add8e6;
  font-size: 20px;
  font-weight: bold;
}

.nav-itemtxt1 {
  width: 100px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  font-size: 24px;
  color: #00ffff;
}

.nav-itemtxt2 {
  width: 50px;
  height: 30px;
  line-height: 30px;
  border-radius: 5px;
  text-align: center;
  background: rgba(0, 0, 0, 0.2);
  font-size: 24px;
  color: #409eff;
}

:deep(.el-dialog__title) {
  color: #fff;
}

:deep(.el-dialog.customDialog) {
  margin-top: 10vh !important;
  background: #3c4167 !important;
}

:deep(.el-form-item__label) {
  color: #fff;
}

:deep(.el-textarea__inner) {
  border: 1px solid rgb(94, 102, 160) !important;
  background-color: rgba(43, 46, 73) !important;
}

:deep(.el-textarea__inner::placeholder) {
  color: #a9a9a9 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #a9a9a9 !important;
}

:deep(.el-button--primary:hover),
:deep(.el-button--primary:focus) {
  background: rgb(103, 113, 183, 0.2) !important;
  border: 1px solid rgb(103, 113, 183, 0.2) !important;
}

:deep(.el-button--primary) {
  background: rgb(103, 113, 183) !important;
  border: 1px solid rgb(103, 113, 183) !important;
  border-color: rgb(103, 113, 183) !important;
}

:deep(.el-select__caret.el-input__icon.el-icon-arrow-up) {
  height: 100% !important;
  line-height: 100% !important;
}

:deep(.pagination-container) {
  background: none !important;
}

:deep(.el-form-item) {
  margin-bottom: 10px;
}

/* 表格 */
:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table__fixed-right::before) {
  height: 0;
}

:deep(.el-table th.el-table__cell.is-leaf) {
  border: none;
}

:deep(.el-table td.el-table__cell) {
  border: none;
}
</style>