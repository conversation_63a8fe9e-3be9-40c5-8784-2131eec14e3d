<template>
  <div id="base-map" class="map"></div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { initMap } from './mapMethods';
// 使用 onMounted 生命周期钩子
onMounted(() => {
  initMap('base-map');
});
</script>

<style scoped lang="scss">
.map {
  width: 100vw;
  height: 100vh;
  :deep(.dark-layer) {
    filter: contrast(102%) brightness(93%) saturate(103%) sepia(65%) grayscale(22%) invert(100%);
  }
}
</style>
