<template>
  <div class="content" :style="{ 'right': contentWidth }">
    <div v-for="(item, index) in list" :key="index">
      <transition>
        <div class="list" v-if="item.isShow" @click="showShip(item)">
          <div class="list_title">
            <div style="display: flex; align-items: center">
              <img src="@/static/ship.png" alt="" />
              <span>{{ item.shipName || item.mmsi }}</span>
            </div>
            <img src="@/assets/images/guanbihui.png" @click.stop="delWarn(item)" alt="" />
          </div>
          <div class="list_text">
            <span>MMSI:</span>
            <span>{{ item.mmsi }}</span>
          </div>
          <div class="list_text">
            <span>规则:</span>
            <span>{{ item.eventContent }}</span>
          </div>
          <div class="list_text">
            <span>地点:</span>
            <span>{{ item.address }}</span>
          </div>
          <div class="list_text">
            <span>时间:</span>
            <span v-if="item.warningTime">{{
              new Date(item.warningTime * 1000).toLocaleString()
            }}</span>
          </div>
          <div style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            ">
            <div class="wLeve" :class="changeColor(item.eventContent)">
              {{ item.eventContent }}
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch, defineProps, defineEmits } from 'vue';
import { Place } from "@/utils/request.js";

// 全局 T 对象声明
declare const T: any;

// 定义类型
interface AlarmItem {
  shipName?: string;
  mmsi: string;
  eventContent: string;
  address: string;
  warningTime: number;
  lon: number;
  lat: number;
  isShow: boolean;
  [key: string]: any;
}

// 定义props
const props = defineProps<{
  map: any;
  isPopup: boolean;
}>();

// 定义emits
const emit = defineEmits(['showShip']);

// 响应式数据
const list = ref<AlarmItem[]>([]);
const place = ref<number | null>(null);
const current = ref(0);
const path = ref('');
const websock = ref<WebSocket | null>(null);
const intervalObj = ref<number | null>(null); // 心跳定时器
const isShow = ref(true);
const timer = ref<number | null>(null);
const autoplay = ref(true);
const searchMarker = ref(null);
const onChuan = ref(null);
const isCache = ref(false);
const shipMarker = ref(null);
const marker = ref(null);
const contentWidth = ref('50px');

// 计算属性
const changeColor = computed(() => {
  return (val: string) => {
    let res = "";
    if (val.indexOf("红色") >= 0) {
      res = "red";
    } else if (val.indexOf("橙色") >= 0) {
      res = "orange";
    } else if (val.indexOf("蓝色") >= 0) {
      res = "blue";
    }
    return res;
  };
});

// 监听属性变化
watch(() => props.isPopup, (val) => {
  if (document.getElementById('contentWidth')) {
    contentWidth.value = window.getComputedStyle(document.getElementById('contentWidth')).width;
  } else {
    contentWidth.value = '50px';
  }
});

// 生命周期钩子
onMounted(() => {
  place.value = Place;
  // console.log('开始');
  const num = new Date().getTime();
  // 这里需要替换为实际的 websocket URL
  path.value = (window as any).$websockearlyurl + place.value + "/" + num;
  // path.value = "ws://*************:9120/wb/early/" + place.value + "/" + num;
  initWebSocket();
});

onBeforeUnmount(() => {
  if (websock.value) {
    websock.value.onclose = () => websock.value?.close();
  }
});

// 方法
function initWebSocket() {
  if (typeof WebSocket === "undefined") {
    console.log("您的浏览器不支持WebSocket");
  } else {
    console.log("您的浏览器支持WebSocket");

    if (websock.value != null) {
      websock.value.close();
      websock.value = null;
    }
    
    websock.value = new WebSocket(path.value);
    websock.value.onmessage = websocketonmessage;
    websock.value.onopen = websocketonopen;
    websock.value.onerror = websocketonerror;
    websock.value.onclose = websocketclose;

    // 创建心跳对象
    HeartCheck(websock.value, 50000); // 设置间隔50秒的心跳
  }
}

function websocketsend() {
  //数据发送
  // let msg=JSON.stringify(111111);
  // websock.value?.send(msg);
}

function websocketonopen() {
  console.log("告警消息websocket已打开");
}

function websocketonerror(e: Event) {
  console.log("告警消息websocket发生了错误", e);
}

function websocketonmessage(msg: MessageEvent) {
  //数据接收
  //这里处理response
  if (msg) {
    // console.log(msg,'警报');
    if (msg.data && msg.data !== "pong") {
      isShow.value = true;
      autoplay.value = true;
      
      // 数据超过二十条重置
      // if (list.value.length >= 20) {
      //   list.value = [];
      //   // localStorage.removeItem('warnList');
      // }
      
      const obj = JSON.parse(msg.data);
      obj.isShow = true;
      // console.log(obj);
      list.value.push(obj);
      
      const str = JSON.stringify(list.value);
      localStorage.setItem("warnList", str);
      
      setTimeout(() => {
        obj.isShow = false;
      }, 30000);
      
      if (timer.value) clearTimeout(timer.value);
      hideWarn();
      
      // setTimeout(() =>{
      //   speckText('您有新的报警信息，请及时处理');
      // },2000)
    }
  }
}

function websocketclose() {
  console.log("告警消息websocket已关闭");
  reconnect();
}

function reconnect() {
  console.log("重新连接");
  setTimeout(() => {
    initWebSocket();
  }, 1000);
}

/**
 * 心跳检测
 * @param websocket WebSocket对象
 * @param timeout 心跳间隔（单位毫秒）
 */
function HeartCheck(websocket: WebSocket, timeout: number) {
  console.log("告警消息心跳");
  // 清除心跳
  if (intervalObj.value) clearInterval(intervalObj.value);
  
  intervalObj.value = window.setInterval(() => {
    // 这里发送一个心跳，后端收到后，返回一个心跳消息
    // message 表示发送的是消息 heart表示心跳 则data必须是ping
    if (websocket.readyState === 1) {
      websocket.send("ping");
    }
  }, timeout);
}

function chanSwiper(e: any) {
  // console.log(e);
  // console.log(list.value.length);
  current.value = e.detail.current;
  if (current.value === list.value.length - 1) {
    autoplay.value = false;
  }
}

function hideWarn() {
  timer.value = window.setTimeout(() => {
    isShow.value = false;
    // current.value = 0;
    // list.value = [];
    // list.value.shift();
  }, 30000);
}

// 删除
function delWarn(val: AlarmItem) {
  val.isShow = false;
}

function showShip(item: AlarmItem) {
  props.map.panTo(new T.LngLat(item.lon, item.lat));
  onChuan.value = item;
  
  if (marker.value) {
    props.map.removeOverLay(marker.value);
  }
  
  const img = require("@/assets/images/jingzhun.png");
  const icon = new T.Icon({
    iconUrl: img,
    iconSize: new T.Point(25, 25),
    iconAnchor: new T.Point(12, 12),
  });
  
  //创建标注对象
  marker.value = new T.Marker(new T.LngLat(item.lon, item.lat), {
    icon: icon,
  });
  
  marker.value.addEventListener("click", function (e: Event) {
    emit("showShip", item.mmsi);
  });
  
  //向地图上添加标注
  props.map.addOverLay(marker.value);
  // emit("queryShip", item.mmsi);
}

function setWarnList() {
  const str = JSON.stringify(list.value);
  localStorage.setItem("warnList", str);
  isCache.value = true;
}

function stopCache() {
  isCache.value = false;
  localStorage.removeItem("warnList");
}

function speckText(str: string) {
  const utterThis = new window.SpeechSynthesisUtterance(str);
  window.speechSynthesis.speak(utterThis);
}
</script>

<style scoped>
.content {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  overflow: hidden;
  position: absolute;
  right: 50px;
  bottom: 10px;
  z-index: 888;
  margin-right: 10px;
}

.list {
  width: 300px;
  /* height: 150px; */
  background-color: #ffffff;
  border-left: solid 5px red;
  padding: 10px;
  margin-bottom: 20px;
}

.list:hover {
  cursor: pointer;
}

.list_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  margin-bottom: 10px;
}

.list_title img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.list_text {
  margin-bottom: 10px;
  display: flex;
}

.list_text>span:first-child {
  width: 60px;
  display: block;
}

.wLeve {
  padding: 4px 10px;
  border-radius: 6px;
  color: #ffffff;
}

.red {
  background-color: red;
}

.orange {
  background-color: orange;
}

.yellow {
  background-color: yellow;
}

.blue {
  background-color: blue;
}

.listBtn:hover {
  cursor: pointer;
}

.v-enter,
.v-leave-to {
  opacity: 0;
}

.v-enter-to,
.v-leave {
  opacity: 1;
}

.v-enter-active,
.v-leave-active {
  transition: all 0.3s;
}
</style>