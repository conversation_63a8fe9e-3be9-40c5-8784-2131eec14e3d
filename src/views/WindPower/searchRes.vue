<template>
  <div>
    <!-- <div class="searchHead">
      <div class="searchHead_left">
        <img src="@/assets/images/fdj.png" alt="" />
        <div>搜索结果</div>
      </div>
      <img
        class="close"
        @click="close"
        src="@/assets/images/guanbi.png"
        alt=""
      />
    </div> -->
    <div class="list">
      <div class="list_item" v-for="(item, index) in list" :key="index" @click="getShip(item)">
        {{ item.chineseName ? item.chineseName : item.name }}
      </div>
      <div v-if="list.length == 0">没有更多结果</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineProps } from 'vue';

// 声明全局window对象类型
declare const window: Window & {
  emitter?: any;
};

// 定义类型
interface ShipData {
  lon: number;
  lat: number;
  chineseName?: string;
  name?: string;
  cog: number;
  [key: string]: any;
}

// 定义 props
const props = defineProps<{
  list: ShipData[];
  map: any;
}>();

// 状态
const onData = ref<ShipData>({} as ShipData);

// 方法
const getShip = (val: ShipData) => {
  onData.value = val;

  const lng = val.lon;
  const lat = val.lat;

  // 使用OpenLayers地图API移动到指定位置
  if (props.map && props.map.getView) {
    props.map.getView().animate({
      center: [lng, lat],
      zoom: 12,
      duration: 1000
    });
  }

  // 触发船舶选中事件，显示船舶详情
  if (window.emitter) {
    window.emitter.emit('ship-selected', val);
  }
};

// 清理搜索相关资源
const closeSearch = () => {
  // 清除船舶选中状态
  if (window.emitter) {
    window.emitter.emit('clear-ship-selection');
  }
};

// 生命周期钩子
onMounted(() => {
  // 组件挂载时的初始化逻辑
});

onBeforeUnmount(() => {
  closeSearch();
});
</script>

<style scoped>
.list {
  height: 780px;
  overflow: auto;
}

.searchHead {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.searchHead_left {
  display: flex;
  align-items: center;
}

.searchHead_left>img {
  margin-right: 15px;
}

.searchHead img {
  width: 20px;
  height: 20px;
}

.close:hover {
  cursor: pointer;
}

.list_item {
  margin-bottom: 15px;
}

.list_item:hover {
  cursor: pointer;
  color: #457ae6;
}

::-webkit-scrollbar {
  width: 4px;
  height: 10px;
  background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #4376ec;
}
</style>