<template>
  <div class="box">
    <div class="entry borderR">
      <el-button type="primary" :icon="Edit" circle @click="addPoint"></el-button>
    </div>
    <!-- <div class="icons borderR">
      <div>图标</div>
      <div class="icons_img">
        <el-image style="width: 100%; height: 100%" :src="tools.imgUrl" fit="fill"></el-image>
      </div>
    </div> -->
    <div class="wireframe borderR">
      <div>线框</div>
      <div class="wireframe_color">
        <el-color-picker size="small" v-model="tools.borderColor" @change="changeTools"></el-color-picker>
      </div>
      <div class="wireframe_width">
        <el-input-number size="small" v-model="tools.borderWidth" :min="1" :max="10"
          @change="changeTools"></el-input-number>
      </div>
      <div>
        <el-switch v-model="tools.isSolid" active-text="实线" inactive-text="虚线" @change="changeTools">
        </el-switch>
      </div>
    </div>
    <div class="padColor borderR">
      <div>填充</div>
      <div class="padColor_color">
        <el-color-picker size="small" v-model="tools.paddColor" @change="changeTools"></el-color-picker>
      </div>
    </div>
    <div class="btns" v-if="isBtn">
      <el-button type="danger" :icon="Close" size="small" circle @click="close"></el-button>
      <el-button type="success" :icon="Check" size="small" circle @click="submit"></el-button>
    </div>

    <div class="textBox" v-if="isAdd">
      <div class="textinput">
        <el-input type="textarea" :rows="11" v-model="textarea" placeholder="格式30.25,121.25"> </el-input>
      </div>
      <div style="display: flex; justify-content: flex-end; padding-right: 10px">
        <el-button type="info" plain size="small" @click="canceAdd">取消</el-button>
        <el-button type="primary" size="small" @click="submitAdd">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Edit, Close, Check } from '@element-plus/icons-vue';

interface Tools {
  // imgUrl?: string;
  borderColor: string;
  borderWidth: number;
  isSolid: boolean;
  paddColor: string;
  [key: string]: any; // 允许其他属性
}

/**
 * 颜色工具函数 - 确保填充颜色有50%的透明度
 * @param color 输入的颜色 (hex, rgb, rgba)
 * @returns 带50%透明度的rgba颜色
 */
function ensureColorWithOpacity(color: string | null): string {
  if (!color) return 'rgba(64, 158, 255, 0.5)'; // 默认蓝色，50%透明
  
  // 已经是rgba格式，修改透明度为0.5
  if (color.startsWith('rgba')) {
    // 提取 rgba 中的 r, g, b 值
    const match = color.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*[\d.]+\s*\)/);
    if (match) {
      const [, r, g, b] = match;
      return `rgba(${r}, ${g}, ${b}, 0.5)`;
    }
  }
  
  // rgb格式转rgba
  if (color.startsWith('rgb(')) {
    // 提取 rgb 中的 r, g, b 值
    const match = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
    if (match) {
      const [, r, g, b] = match;
      return `rgba(${r}, ${g}, ${b}, 0.5)`;
    }
  }
  
  // 十六进制格式转rgba
  if (color.startsWith('#')) {
    let r = 0, g = 0, b = 0;
    // #RGB 格式
    if (color.length === 4) {
      r = parseInt(color[1] + color[1], 16);
      g = parseInt(color[2] + color[2], 16);
      b = parseInt(color[3] + color[3], 16);
    } 
    // #RRGGBB 格式
    else if (color.length === 7) {
      r = parseInt(color.substring(1, 3), 16);
      g = parseInt(color.substring(3, 5), 16);
      b = parseInt(color.substring(5, 7), 16);
    }
    return `rgba(${r}, ${g}, ${b}, 0.5)`;
  }
  
  // 其他情况，返回默认颜色
  return 'rgba(64, 158, 255, 0.5)';
}

// 完整定义props
const props = defineProps({
  isBtn: {
    type: Boolean,
    default: false
  }
});

// 完整定义所有可能的emit事件
const emit = defineEmits(['Tools', 'closeTools', 'subTools', 'addPoint']);

const tools = reactive<Tools>({
  // imgUrl: require("@/assets/plotting/pl1.png"),
  borderColor: "#409EFF", // 线框颜色，使用Element Plus默认蓝色
  borderWidth: 1,
  isSolid: true,
  paddColor: "",
});

const isAdd = ref(false);
const textarea = ref<string | null>(null);

// 改变选项后触发
const changeTools = () => {
  // 确保填充颜色有50%透明度
  if (tools.paddColor) {
    tools.paddColor = ensureColorWithOpacity(tools.paddColor);
  }
  
  emit('Tools', tools);
};

const close = () => {
  emit('closeTools');
};

const submit = () => {
  emit('subTools');
};

// 点击图标弹出添加坐标窗口
const addPoint = () => {
  isAdd.value = true;
  textarea.value = null;
};

// 取消添加
const canceAdd = () => {
  isAdd.value = false;
  textarea.value = null;
};

// 提交添加
const submitAdd = () => {
  if (textarea.value) {
    emit('addPoint', textarea.value);
    isAdd.value = false;
  } else {
    ElMessage.error("格式错误");
  }
};
</script>

<style scoped>
.box {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  color: #333333;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.borderR {
  border-right: 1px solid #e4e7ed;
}

.entry {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}

.icons {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  padding: 0 20px;
}

.icons_img {
  width: 36px;
  height: 36px;
  margin-top: 5px;
}

.wireframe {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.wireframe_color {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20px;
}

.wireframe_width {
  margin-right: 20px;
}

.padColor {
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.padColor_color {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20px;
}

.btns {
  margin: 0 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.textBox {
  position: absolute;
  top: -300px;
  left: 0;
  width: 500px;
  height: 300px;
  right: 0;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(95, 211, 252, 0.3);
}

.textinput {
  margin-bottom: 10px;
  width: 480px;
  height: 242px;
  margin: 10px auto;
  background-color: rgba(47, 79, 146, 0.3);
}

:deep(.el-switch__label) {
  color: #e6e6e6;
}

:deep(.el-switch__label) {
  color: #333333;
}

:deep(.el-textarea__inner) {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #dcdfe6;
}
</style>