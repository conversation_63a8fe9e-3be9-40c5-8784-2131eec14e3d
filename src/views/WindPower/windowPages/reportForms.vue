<template>
  <div class="box">
    <div class="box_title">
      <div style="display: flex; align-items: center">
        <img
          style="margin-right: 10px"
          src="@/static/wenjian.png"
          alt=""
        />
        报表统计
      </div>
      <img src="@/static/guanbi.png" @click="cancel" alt="" />
    </div>
    <div class="condition">
      <div class="condition_item">
        时间:
        <el-select v-model="days" placeholder="请选择" @change="changeTime">
          <el-option key="7" label="近7天" :value="7"> </el-option>
          <el-option key="15" label="近15天" :value="15"> </el-option>
        </el-select>
      </div>

      <el-button type="primary" size="default" :icon="Search">搜索</el-button>
    </div>
    <div style="display: flex; align-items: flex-start">
      <div style="width: 100%">
        <div style="display: flex; align-items: center">
          <div style="position: relative; width: 50%">
            <div id="echartsType"></div>
          </div>
          <div style="position: relative; width: 50%">
            <div id="echartsWarning"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { statement } from "@/api/map/map.js";
import * as echarts from 'echarts';
import { Search } from '@element-plus/icons-vue';

interface FormState {
  time: string;
  type: string;
  region: string;
  rule: string;
  keyword: string;
}

interface StatementResponse {
  code: number;
  data?: StatementData[];
}

interface StatementData {
  category: string;
  count: number;
}

interface StatementParams {
  type: number;
  days: number;
}

const form = reactive<FormState>({
  time: "",
  type: "",
  region: "",
  rule: "",
  keyword: "",
});

const tableData = ref<any[]>([]);
const days = ref<number>(7);

onMounted(() => {
  getStatement();
  getStatementWarn();
});

const changeTime = () => {
  getStatement();
  getStatementWarn();
};

const getStatement = async () => {
  try {
    const params: StatementParams = {
      type: 1,
      days: days.value,
    };
    
    const res = await statement(params) as StatementResponse;
    
    if (res.code === 200 && res.data) {
      const xData: string[] = [];
      const yData: number[] = [];
      
      for (let i = 0; i < res.data.length; i++) {
        xData.push(res.data[i].category);
        yData.push(res.data[i].count);
      }

      setechartsType(xData, yData);
    }
  } catch (error) {
    console.error('获取类型统计数据失败', error);
  }
};

const getStatementWarn = async () => {
  try {
    const params: StatementParams = {
      type: 2,
      days: days.value,
    };
    
    const res = await statement(params) as StatementResponse;
    
    if (res.code === 200 && res.data) {
      const xData: string[] = [];
      const yData: number[] = [];
      
      for (let i = 0; i < res.data.length; i++) {
        xData.push(res.data[i].category);
        yData.push(res.data[i].count);
      }

      setechartsWarn(xData, yData);
    }
  } catch (error) {
    console.error('获取警告统计数据失败', error);
  }
};

const setechartsType = (x: string[], y: number[]) => {
  const chart = document.getElementById("echartsType") as HTMLElement;
  const myChart = echarts.init(chart);
  
  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      backgroundColor: "rgba(9, 24, 48, 0.5)",
      borderColor: "rgba(75, 253, 238, 0.4)",
      textStyle: {
        color: "#CFE3FC",
      },
      borderWidth: 1,
    },
    grid: {
      top: "15%",
      right: "5%",
      left: "15%",
      bottom: "12%",
    },
    xAxis: [
      {
        type: "category",
        data: x,
        axisLine: {
          lineStyle: {
            color: "#FFFFFF",
          },
        },
        axisLabel: {
          margin: 10,
          color: "#e2e9ff",
          textStyle: {
            fontSize: 12,
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        name: "数量（艘）",
        axisLabel: {
          formatter: "{value}",
          color: "#e2e9ff",
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#FFFFFF",
          },
        },
        splitLine: {
          lineStyle: {},
        },
      },
    ],
    series: [
      {
        type: "bar",
        data: y,
        barWidth: "50%",
        itemStyle: {
          normal: {
            color: "#43BAFE",
          },
        },
        label: {
          normal: {
            show: true,
            lineHeight: 10,
            formatter: "{c}",
            position: "top",
            textStyle: {
              color: "#00D6F9",
              fontSize: 12,
            },
          },
        },
      },
    ],
  };
  
  myChart.setOption(option);
};

const setechartsWarn = (x: string[], y: number[]) => {
  const chart = document.getElementById("echartsWarning") as HTMLElement;
  const myChart = echarts.init(chart);
  
  const option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      backgroundColor: "rgba(9, 24, 48, 0.5)",
      borderColor: "rgba(75, 253, 238, 0.4)",
      textStyle: {
        color: "#CFE3FC",
      },
      borderWidth: 1,
    },
    grid: {
      top: "15%",
      right: "5%",
      left: "15%",
      bottom: "12%",
    },
    xAxis: [
      {
        type: "category",
        data: x,
        axisLine: {
          lineStyle: {
            color: "#FFFFFF",
          },
        },
        axisLabel: {
          margin: 10,
          color: "#e2e9ff",
          textStyle: {
            fontSize: 12,
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        name: "数量（个）",
        axisLabel: {
          formatter: "{value}",
          color: "#e2e9ff",
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: "#FFFFFF",
          },
        },
        splitLine: {
          lineStyle: {},
        },
      },
    ],
    series: [
      {
        type: "bar",
        data: y,
        barWidth: "50%",
        itemStyle: {
          normal: {
            color: "#43BAFE",
          },
        },
        label: {
          normal: {
            show: true,
            lineHeight: 10,
            formatter: "{c}",
            position: "top",
            textStyle: {
              color: "#00D6F9",
              fontSize: 12,
            },
          },
        },
      },
    ],
  };
  
  myChart.setOption(option);
};

const cancel = () => {
  emit('closeReport');
};

const emit = defineEmits(['closeReport']);
</script>
  
<style scoped>
.box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(60, 65, 103, 0.9);
  padding: 20px;
  z-index: 1000;
  color: #ffffff;
  min-width: 1300px;
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
}
.box_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.box_title img {
  width: 20px;
  height: 20px;
}
.box_title > img:hover {
  cursor: pointer;
}
.condition {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}
.condition_item {
  margin-right: 20px;
}
.addBox_list_inp {
  width: 180px;
  height: 36px;
  margin-left: 20px;
  border: 1px solid #4376ec;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}
.elWidth {
  width: 180px;
  margin-left: 6px;
}
input {
  border: none;
  outline: none;
  background: #000000;
  color: #ffffff;
}
#echartsType {
  width: 100%;
  height: 550px;
}
#echartsWarning {
  width: 100%;
  height: 550px;
}
.echartsName {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
</style>