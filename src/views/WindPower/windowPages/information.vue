<template>
  <div class="box">
    <div class="box_title">
      <div style="display: flex; align-items: center">
        <span class="iconfont icon-yichangshujuchaxun"></span>
        {{ itemId == "chooselatAndlong" ? "坐标转换工具" : "弧线计算" }}
      </div>
      <div>
        <i class="el-icon-minus"></i>
        <i class="el-icon-close el-icon" @click="cancel"></i>
        <!-- <img src="@/static/guanbi.png" @click="cancel" alt="" /> -->
      </div>
    </div>
    <!-- 经纬度转换 -->
    <div v-if="itemId === 'chooselatAndlong'">
      <div style="display: flex; width: 680px; flex-wrap: wrap">
        <div style="width: 50%">
          <p
            style="
              font-weight: bold;
              color: deepskyblue;
              margin-bottom: 10px;
              border-bottom: 1px solid rgba(164, 164, 164, 0.5);
              margin-left: 20px;
              padding-left: 15px;
            "
          >
            度
          </p>
          <div>
            <div style="display: flex; padding: 0 15px; margin-bottom: 10px">
              <span>经度</span>
              <div class="inp">
                <el-input type="text" />
              </div>
              <span>°E</span>
              <i class="el-icon-copy-document icon-style"></i>
            </div>
            <div style="display: flex; padding: 0 15px; margin-bottom: 10px">
              <span>纬度</span>
              <div class="inp">
                <el-input type="text" />
              </div>
              <span>°E</span>
              <i class="el-icon-copy-document icon-style"></i>
            </div>
          </div>
        </div>
        <div style="width: 50%">
          <p
            style="
              font-weight: bold;
              color: deepskyblue;
              margin-bottom: 10px;
              border-bottom: 1px solid rgba(164, 164, 164, 0.5);
              margin-left: 20px;
              padding-left: 15px;
            "
          >
            度
          </p>
          <div>
            <div style="display: flex; padding: 0 15px; margin-bottom: 10px">
              <span>经度</span>
              <div class="inp">
                <el-input type="text" />
              </div>
              <span>°E</span>
              <i class="el-icon-copy-document icon-style"></i>
            </div>
            <div style="display: flex; padding: 0 15px; margin-bottom: 10px">
              <span>纬度</span>
              <div class="inp">
                <el-input type="text" />
              </div>
              <span>°E</span>
              <i class="el-icon-copy-document icon-style"></i>
            </div>
          </div>
        </div>
        <div style="width: 50%">
          <p
            style="
              font-weight: bold;
              color: deepskyblue;
              margin-bottom: 10px;
              border-bottom: 1px solid rgba(164, 164, 164, 0.5);
              margin-left: 20px;
              padding-left: 15px;
            "
          >
            度
          </p>
          <div>
            <div style="display: flex; padding: 0 15px; margin-bottom: 10px">
              <span>经度</span>
              <div class="inp">
                <el-input type="text" />
              </div>
              <span>°E</span>
              <i class="el-icon-copy-document icon-style"></i>
            </div>
            <div style="display: flex; padding: 0 15px; margin-bottom: 10px">
              <span>纬度</span>
              <div class="inp">
                <el-input type="text" />
              </div>
              <span>°E</span>
              <i class="el-icon-copy-document icon-style"></i>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div style="float: right">
          <el-button
            class="el-button-primary btn-style"
            plain
            size="small"
            type="primary"
            >拾取</el-button
          >
          <el-button
            class="el-button-primary btn-style"
            type="primary"
            plain
            size="small"
            >定位</el-button
          ><el-button class="el-button-primary btn-style" plain size="small"
            >关闭</el-button
          >
        </div>
      </div>
    </div>
    <!-- 弧线计算 -->
    <div v-if="itemId === 'arcCalculation'">
      <table>
        <thead>
          <tr>
            <th>坐标点:</th>
            <td>经度</td>
            <td>纬度</td>
          </tr>
        </thead>
        <tbody>
          <tr>
            <th>圆心:</th>
            <td>
              <div>
                <el-input placeholder="请输入经度"></el-input>
              </div>
            </td>
            <td>-</td>
            <td>
              <div>
                <el-input placeholder="请输入纬度"></el-input>
              </div>
            </td>
          </tr>
          <tr>
            <th>第一弧点:</th>
            <td>
              <div>
                <el-input placeholder="请输入经度"></el-input>
              </div>
            </td>
            <td>-</td>
            <td>
              <div>
                <el-input placeholder="请输入纬度"></el-input>
              </div>
            </td>
          </tr>
          <tr>
            <th>第二弧点:</th>
            <td>
              <div>
                <el-input placeholder="请输入经度"></el-input>
              </div>
            </td>
            <td>-</td>
            <td>
              <div>
                <el-input placeholder="请输入纬度"></el-input>
              </div>
            </td>
          </tr>
          <tr>
            <th>半径:</th>
            <td>
              <div>
                <el-input placeholder="请输入经度"></el-input>
              </div>
            </td>
            <td>(海里)</td>
            <td>
              <!-- <div>
                <el-input placeholder="请输入纬度"></el-input>
              </div> -->
            </td>
          </tr>
          <tr>
            <th>步长:</th>
            <td>
              <div>
                <el-input placeholder="请输入经度"></el-input>
              </div>
            </td>
            <td>(度)</td>
            <td>
              <el-checkbox v-model="checked">顺时针</el-checkbox>
              <!-- <div>
                <el-input placeholder="请输入纬度"></el-input>
              </div> -->
            </td>
          </tr>
          <tr>
            <th>结果:</th>
            <td><textarea name="" id="" cols="30" rows="10"></textarea></td>
          </tr>
        </tbody>
      </table>
      <div style="float: right">
        <el-button class="el-button-primary" type="primary" plain size="small"
          >计算</el-button
        >
      </div>
    </div>
    <!-- <div class="condition">
      <el-button type="primary" size="medium" class="screen">添加</el-button>
      <el-button type="primary" size="medium" class="screen">重命名</el-button>
      <el-button type="danger" size="medium" class="delBtn">删除</el-button>
      <div class="condRoute">
        <div>当前路径：</div>
        <el-input type="text" class="condRoute_inp" />
      </div>
      <el-button type="primary" size="medium" class="screen"
        >上传文件</el-button
      >
    </div>
    <div
      style="display: flex; align-items: center; justify-content: space-between"
    >
      <div class="type">
        <div class="type_item">
          <img src="@/assets/images/file.png" alt="" />
          <div>图片</div>
        </div>
        <div class="type_item">
          <img src="@/assets/images/file.png" alt="" />
          <div>文件</div>
        </div>
      </div>
      <div class="fileBox"></div>
    </div> -->
  </div>
</template>
  
<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits } from 'vue';

interface FormState {
  time: string;
  type: string;
  region: string;
  rule: string;
  keyword: string;
}

interface TableRow {
  name: string;
  englishName: string;
  mmsi: string;
  type: string;
  gj: string;
  preNum: string;
  isin: string;
}

const props = defineProps<{
  itemId: string;
}>();

const emit = defineEmits(['closeInformation']);

const form = reactive<FormState>({
  time: "",
  type: "",
  region: "",
  rule: "",
  keyword: "",
});

const tableData = ref<TableRow[]>([
  {
    name: "基站",
    englishName: "基站",
    mmsi: "10000000",
    type: "其它类",
    gj: "",
    preNum: "",
    isin: "是",
  },
]);

const currentPage4 = ref(4);
const checked = ref(false);

const cancel = () => {
  emit('closeInformation');
};

const handleSizeChange = (val: number) => {
  console.log(`每页 ${val} 条`);
};

const handleCurrentChange = (val: number) => {
  console.log(`当前页: ${val}`);
};
</script>
  
<style scoped>
table .el-input,
table textarea {
  padding: 4px 6px;
  background-color: rgba(43, 46, 73, 0.9);
  border: 1px solid rgb(94, 102, 160);
  border-radius: 0.2em;
  caret-color: #afafaf;
  outline: none;
  color: white;
}
.el-input {
  font-size: 14px;
  display: inline-block;
  width: 100%;
  /* border: 1px solid red; */
}
table tr td {
  padding: 5px;
  text-align: center;
}
table thead tr {
  color: #88bfff;
}
.icon-style {
  margin-left: 15px;
}
.inp {
  margin-left: 15px;
  margin-right: 5px;
}
.label-style {
  width: 80px;
  text-align: right;
  margin-left: 10px;
  font-size: 14px;
  padding: 0 12px 0 0;
  color: rgb(253, 253, 253);
  font-weight: 500;
}
.partition {
  padding: 5px;
}
.delBtn {
  color: #fff;
  background-color: #f78989;
  border-color: #f78989;
}
.screen {
  color: #fff;
  background-color: rgb(103, 113, 183);
  border-color: rgb(103, 113, 183);
}
.screen:hover,
.screen:focus {
  background: rgb(142, 151, 217);
  border-color: rgb(142, 151, 217);
  color: var(--el-button-font-color);
}

.el-icon {
  margin-left: 5px;
}
.box {
  display: flex;
  flex-direction: column;
  background: rgba(60, 65, 103, 0.95);
  color: white;
  box-shadow: 0 0 7px 1px rgba(0, 0, 0, 0.55);
  overflow: hidden;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  z-index: 100000;
  width: 1010px;
  /* min-width: 1300px; */
  /* color: #ffffff; */
  /* background-color: rgb(60 65 103 / 95%); */
  /* box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5); */
}
.box_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.box_title img {
  width: 20px;
  height: 20px;
}
.box_title > img:hover {
  cursor: pointer;
}
.condition {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  margin-bottom: 20px;
}
.condition_item {
  margin-right: 20px;
  display: flex;
  align-items: center;
}
.addBox_list_inp {
  width: 180px;
  height: 36px;
  margin-left: 20px;
  border: 1px solid #4376ec;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}
.elWidth {
  width: 180px;
}
input {
  border: none;
  outline: none;

  background: #000000;
  color: #ffffff;
}
#echartsType {
  width: 100%;
  height: 250px;
}
#echartsLength {
  width: 100%;
  height: 250px;
}
#echartsFlow {
  width: 100%;
  height: 250px;
}
.echartsName {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}
.condRoute {
  width: 65%;
  display: flex;
  align-items: center;
  margin-left: 20px;
}
.condRoute > div {
  flex-shrink: 0;
}
.condRoute_inp {
  width: 85%;
}
.type {
  height: 500px;
  width: 25%;
  border: 1px solid #50587e;
}
.type_item {
  height: 40px;
  line-height: 40px;
  padding: 10px;
  display: flex;
  align-items: center;
  color: #ffb55a;
}
.type_item > img {
  width: 25px;
  height: 25px;
  margin-right: 10px;
}
.type_item:hover > div {
  margin-left: 10px;
  transition: all 0.5s;
}
.type_item:hover {
  cursor: pointer;
}
.fileBox {
  height: 500px;
  width: 65%;
  border: 1px solid #50587e;
}
</style>
<style scoped src="@/assets/datePark.css"></style>