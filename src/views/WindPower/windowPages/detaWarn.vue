<template>
  <div class="box">
    <div class="box_title">
      <div style="display: flex; align-items: center">
        <img src="@/static/wenjian.png" alt="" /> 详细报警列表
      </div>
      <img src="@/static/guanbi.png" alt="" @click="cancel" />
    </div>
    <div class="box_condition">
      <div>
        选择时间:
        <el-date-picker v-model="timeDate" type="datetimerange" range-separator="-" start-placeholder="开始日期"
          end-placeholder="结束日期" @change="chanTime" value-format="yyyy/MM/dd HH:mm:ss" popper-class="elDatePicker">
        </el-date-picker>
      </div>
      <div>
        报警区域:
        <el-select v-model="form.type" class="elWidth" placeholder="全部类型" popper-class="elDateSeletc">
          <!-- <el-option :key="1" :value="1" label="辖区"></el-option>
          <el-option :key="2" :value="2" label="锚地"></el-option> -->
        </el-select>
        <el-select v-model="form.region" class="elWidth" placeholder="全部区域" popper-class="elDateSeletc">
          <!-- <el-option v-for="item in typeList" :key="item.id" :value="item." label="红色预警"></el-option> -->
        </el-select>
      </div>
    </div>
    <div class="box_condition">
      <div>
        报警规则:
        <el-select v-model="form.rule" class="elWidth" placeholder="全部规则" popper-class="elDateSeletc"
          @change="changeRule">
          <el-option :key="1" value="红色预警" label="红色预警"></el-option>
          <el-option :key="2" value="橙色预警" label="橙色预警"></el-option>
          <el-option :key="3" value="蓝色预警" label="蓝色预警"></el-option>
        </el-select>
      </div>
      <div style="display: flex; align-items: center">
        关键字:
        <el-input v-model="form.keyword" class="elWidth"></el-input>
        <!-- <div class="addBox_list_inp">
            <input v-model="form.keyword" type="text" />
          </div> -->
      </div>

      <div class="box_condition_btns">
        <!-- <div class="box_condition_btn" style="margin-right: 10px">查询</div>
          <div class="box_condition_btn">导出</div> -->
        <el-button type="primary" size="mini">查询</el-button>
      </div>
    </div>
    <div>
      <el-table :data="tableData" height="500" :row-class-name="tableRowClassName">
        <el-table-column prop="mmsi" label="mmsi" width="160">
        </el-table-column>
        <el-table-column prop="name" label="船名" width="160">
        </el-table-column>

        <el-table-column prop="address" label="区域类型" width="150">
        </el-table-column>
        <el-table-column prop="rule" label="区域名称" width="150">
        </el-table-column>
        <el-table-column prop="time" label="报警规则" width="180">
        </el-table-column>
        <el-table-column prop="" label="报警名称" width="100">
        </el-table-column>
        <el-table-column prop="" label="报警时间" width="100">
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="">
            <el-button type="text" size="small" @click="openDetails">详情</el-button>
            <el-button type="text" size="small">定位</el-button>
            <el-button type="text" size="small">航迹</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 15px">
        <el-pagination :page-sizes="[10, 20, 30]" :page-size="100" layout="total, sizes, prev, pager, next, jumper"
          :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="queryParams.pageNum">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
  
<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { Place } from "@/utils/request.js";
import { warnList, warn, warningList } from "@/api/map/earlyWarning.js";

// 定义类型接口
interface FormState {
  time: string;
  type: string | number;
  region: string | number;
  rule: string;
  keyword: string;
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  isAsc: string;
  orderByColumn: string;
  place: number | null;
  gmtCreate: string | null;
  gmtModified: string | null;
  eventContent: string | null;
}

interface TableRowData {
  name?: string;
  mmsi?: string;
  address?: string;
  rule?: string;
  time?: string;
  [key: string]: any;
}

// 事件发射
const emit = defineEmits(['closeDateWarn']);

// 响应式数据
const isdetails = ref(false);
const tableData = ref<TableRowData[]>([]);
const tableDataItem = ref<any[]>([]);
const form = reactive<FormState>({
  time: "",
  type: "",
  region: "",
  rule: "",
  keyword: "",
});
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  isAsc: "desc",
  orderByColumn: "id",
  place: null,
  gmtCreate: null,
  gmtModified: null,
  eventContent: null,
});
const typeList = ref<any[]>([]);
const total = ref(0);
const timeDate = ref<[string, string] | null>(null);
const list = ref<any[]>([]);

// 方法声明
const handleSizeChange = (val: number) => {
  console.log(`每页 ${val} 条`);
  queryParams.pageSize = val;
  list.value = [];
  getList();
};

const handleCurrentChange = (val: number) => {
  console.log(`当前页: ${val}`);
  queryParams.pageNum = val;
  list.value = [];
  getList();
};

const chanTime = (e: [string, string]) => {
  console.log(e);
  queryParams.gmtCreate = e[0];
  queryParams.gmtModified = e[1];
  console.log(queryParams);
  queryParams.pageNum = 1;
  list.value = [];
  getList();
};

const changeRule = (e: string) => {
  console.log(e);
  queryParams.eventContent = e;
  queryParams.pageNum = 1;
  list.value = [];
  getList();
};

// 获取报警区域
const getwarn = () => {
  warn().then((res: any) => {
    if (res.code == 200) {
      const arr: any[] = [];
      res.data.forEach((ele: any) => {
        const obj = {
          label: ele.name,
          value: ele.id,
        };
        arr.push(obj);
      });
      typeList.value.push(arr);
    }
  });
};

const getList = () => {
  // warningList(queryParams).then((res: any) => {
  //   if (res.code == 200) {
  //     if (queryParams.pageNum == 1) {
  //       list.value = res.rows;
  //     } else {
  //       list.value.push(...res.rows);
  //     }
  //     total.value = res.total;
  //   }
  // });
};

const cancel = () => {
  emit("closeDateWarn");
};

const openDetails = () => {
  isdetails.value = true;
};

const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 == 0) {
    return "warning-row";
  }
  return "";
};

// 生命周期钩子
onMounted(() => {
  queryParams.place = Place;
  getwarn();
  getList();
});
</script>
  
<style scoped lang="less">
:deep(.el-table .warning-row) {
  background: rgba(7, 40, 79, 0.8);
}

.box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  /* background-color: #ffffff; */
  color: #ffffff;
  z-index: 1001;
  width: 60%;
  // background-color: rgba(31, 63, 130, 0.8);
  background-color: rgba(60, 65, 103, 0.95);
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
}

.box_title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  justify-content: space-between;
}

.box_title img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.box_title>img:hover {
  cursor: pointer;
}

.box_condition {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.box_condition>div {
  margin: 0 20px 10px 0;
}

.box_condition_btns {
  margin: 0;
  display: flex;
  align-items: center;
}

.box_condition_btn {
  width: 60px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  background-color: #6771b7;
  border-radius: 4px;
  font-size: 14px;
}

.box_condition_btn:hover {
  cursor: pointer;
}

.addBox_list_inp {
  width: 180px;
  height: 30px;
  margin-left: 20px;
  /* border: 1px solid #4376ec; */
  /* background-color: #000000; */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.elWidth {
  width: 120px;
  margin-left: 10px;
}

input {
  border: none;
  outline: none;
  /* background: #000000; */
  /* color: #ffffff; */
  /* background: #000000;
    color: #ffffff; */
}

/* /deep/ .el-pagination__total {
    color: #fff;
  }
  /deep/ .el-pagination__jump {
    color: #fff;
  }
  
  /deep/.el-table {
    background: #454A6C;
  }
  
  /deep/ .el-table th,
  /deep/ .el-table tr,
  /deep/ .el-table td {
    background-color: transparent;
    color: #ffffff;
  }
  /deep/ .el-table td:hover{
    background: #454A6C;
  } */
.detaBox {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  padding: 10px;
  z-index: 2000;
  /* background-color: #ffffff; */
  color: #ffffff;
  /* border: 1px solid #000000; */
  // background-color: rgba(31, 63, 130);
  background-color: rgba(60, 65, 103, 0.95);
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
  min-height: 800px;
}

.detaBox_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.detaBox_title img {
  width: 20px;
  height: 20px;
}

.detaBox_title>img:hover {
  cursor: pointer;
}

.detaBox_info {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.detaBox_info_item {
  flex: 0 0 33%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
}

/* .picker {
    height: 40px;
    background-color: #000000;
    color: #ffffff;
  } */

/* /deep/.el-input__inner {
    background: #000000 !important;
    border: 1px solid #000000;
    color: #fff;
    height: 40px;
  } */

.elDatePicker.el-picker-panel {
  color: #fff; //设置当前面板的月份的字体为白色，记为1
  background: #3c4167; //定义整体面板的颜色

  .el-picker-panel__sidebar {
    color: #fff;
    background: #3c4167;
  }

  .el-picker-panel__shortcut {
    color: #ffffff;
  }

  .el-picker-panel__shortcut:hover {
    color: #1890ff;
  }

  .el-picker-panel__icon-btn {
    //设置年份月份调节按钮颜色，记为2
    color: #ffffff;
  }

  .el-date-picker__header-label {
    //设置年月显示颜色，记为3
    color: #ffffff;
  }

  .el-date-table th {
    //设置星期颜色，记为4
    color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
    background: #3c4167;
  }

  .el-picker-panel__footer {
    color: #fff;
    background: #3c4167;
  }

  .el-button--text {
    color: #fff;
  }

  .el-button--mini.is-plain {
    color: #fff;
    background: #3c4167;
    border: 1px solid #fff;
  }

  .el-time-panel {
    background: #3c4167;
  }

  .el-time-spinner__item {
    color: #fff;
  }

  .el-time-spinner__item.active {
    color: #1890ff;
  }

  .el-time-spinner__item:hover {
    color: #1890ff;
    background: #3c4167;
  }

  .el-time-panel__btn.cancel {
    color: #fff;
  }

  .el-time-panel__btn.confirm {
    color: #fff;
    background: #3c4167;
    border: 1px solid #fff;
  }

  .available.in-range {
    color: #1890ff;
  }

  .el-year-table td .cell {
    color: #fff;
  }

  .el-year-table td.current:not(.disabled) .cell {
    color: #1890ff;
  }
}

.elDateSeletc.el-select-dropdown {
  color: #fff; //设置当前面板的月份的字体为白色，记为1
  background: #3c4167; //定义整体面板的颜色

  .el-select-dropdown__item {
    color: #fff;
    background: #3c4167;
  }

  .el-select-dropdown__item:hover {
    color: #1890ff;
    background: #3c4167;
  }

  .selected {
    color: #1890ff;
    background: #3c4167;
  }
}
</style>
  
  
  
  