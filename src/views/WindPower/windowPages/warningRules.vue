<template>
  <div class="flex-col">
    <div style="padding: 0 15px">
      <div
        style="
          padding: 0 0 15px 0;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
        "
      >
        <el-button
          type="primary"
          :icon="Tickets"
          @click="clickToAddEscort()"
          >概要信息</el-button
        >
      </div>
    </div>
    <div class="row-line"></div>

    <div class="collapseBox">
      <el-collapse v-model="activeNames">
        <el-collapse-item
          :name="index"
          v-for="(item, index) in collapseList"
          :key="index"
          @click="show_collapse(item)"
        >
          <template #title>
            <div class="collapsTit">
              <div>
                <i
                  class="el-icon-arrow-right iconcolor"
                  :class="{ active: item.isActive }"
                ></i
                >{{ item.name }}
              </div>
              <div>
                <el-switch
                  v-model="swiValue"
                  active-color="#13ce66"
                  inactive-color="#C1C3C7"
                >
                </el-switch>
              </div>
            </div>
          </template>
          <div class="collapsContent">
            <div v-for="(ite, i) in item.children" :key="i">
              {{ ite.title }}
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <el-dialog
      title="概要信息"
      v-model="dialogVisible"
      :modal="false"
      custom-class="customDialog"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="类型">
              <el-select
                v-model="formLast.id"
                placeholder="请选择分组"
                clearable
                style="width: 100%"
                popper-class="elDateSeletc"
              >
                <el-option
                  v-for="item in options"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="关键字">
              <el-input
                v-model="form.id"
                placeholder="关键字"
                type="text"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-button type="primary">查询</el-button>
        </el-row>
        <div
          style="
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
          "
        >
          <div style="color: red">注意:勾选表示接收此类报警数据</div>
        </div>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-table border v-loading="loading" :data="datalist">
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column
                label="规则类型"
                align="left"
                prop="ruleType"
                min-width="150"
              />
              <el-table-column
                label="报警名称"
                align="left"
                prop="warningName"
                min-width="100"
              />
              <el-table-column
                label="区域名称"
                align="left"
                prop="regionName"
                min-width="100"
              />
              <el-table-column
                label="区域类型"
                align="left"
                prop="regionType"
                min-width="100"
              />
              <el-table-column
                label="创建人"
                align="left"
                prop="name"
                min-width="100"
              />
              <el-table-column
                label="所属组织机构"
                align="left"
                prop="organization"
                min-width="100"
              />
              <el-table-column
                label="操作"
                fixed="right"
                min-width="100"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template #default="scope">
                  <el-button
                    size="small"
                    type="text"
                    @click="handleUpdate(scope.row)"
                    >查看</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>
  
<script lang="ts" setup>
import { ref, reactive } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { Tickets } from '@element-plus/icons-vue';

interface CollapseChild {
  title: string;
}

interface CollapseItem {
  name: string;
  isActive: boolean;
  children: CollapseChild[];
}

interface Option {
  id: string | number;
  name: string;
}

interface DataItem {
  ruleType: string;
  warningName: string;
  regionName: string;
  regionType: string;
  name: string;
  organization: string;
}

interface QueryParams {
  pageSize: number;
  pageNum: number;
}

interface FormState {
  id?: string;
}

const formRef = ref<FormInstance>();
const activeNames = ref<number[]>([]);
const collapseList = ref<CollapseItem[]>([
  {
    name: "观测截面",
    isActive: false,
    children: [
      {
        title: "过线船舶（观测截面）",
      },
    ],
  },
  {
    name: "禁止进入",
    isActive: false,
    children: [
      {
        title: "航道（自定义围栏）",
      },
      {
        title: "一级警戒区（自定义围栏）",
      },
      {
        title: "二级警戒区（自定义围栏）",
      },
      {
        title: "三级警戒区（自定义围栏）",
      },
    ],
  },
]);

const swiValue = ref(false);
const checked = ref(false);
const listIndex = ref(0);
const dialogVisible = ref(false);
const dialogVisibleLast = ref(false);
const form = reactive<FormState>({});
const formLast = reactive<FormState>({});
const rules = reactive<FormRules>({});
const rulesLst = reactive<FormRules>({});

const pickerOptions = {
  disabledDate(time: Date) {
    return time.getTime() > Date.now();
  },
  shortcuts: [
    {
      text: "今天",
      onClick(picker: any) {
        picker.$emit("pick", new Date());
      },
    },
    {
      text: "昨天",
      onClick(picker: any) {
        const date = new Date();
        date.setTime(date.getTime() - 3600 * 1000 * 24);
        picker.$emit("pick", date);
      },
    },
    {
      text: "一周前",
      onClick(picker: any) {
        const date = new Date();
        date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
        picker.$emit("pick", date);
      },
    },
  ],
};

const options = ref<Option[]>([]);
const loading = ref(false);
const datalist = ref<DataItem[]>([]);
const total = ref(0);
const queryParams = reactive<QueryParams>({
  pageSize: 1,
  pageNum: 10,
});

const getList = () => {
  // 实现获取列表数据的方法
};

const getShipList = () => {
  // 实现获取船舶列表的方法
};

const handleUpdate = (row: DataItem) => {
  // 实现更新操作的方法
};

const changeList = (type: number) => {
  listIndex.value = type;
};

const clickToAddEscort = () => {
  dialogVisible.value = true;
};

const addShip = () => {
  dialogVisibleLast.value = true;
};

const clearShip = () => {
  dialogVisibleLast.value = true;
};

const show_collapse = (item: CollapseItem) => {
  console.log(item);
  item.isActive = !item.isActive;
};
</script>
  
<style scoped>
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-btn {
  color: white;
  width: 45%;
  height: 36px;
  cursor: pointer;
  line-height: 36px;
  font-size: 14px;
  border: none;
  border-radius: 3px;
  text-align: center;
  background: rgb(103, 113, 183);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.list-title {
  margin-top: 15px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  align-items: center;
}

.list-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 15px;
}

.row-line {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.title-choose {
  width: 50%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #5fd3fc;
  position: relative;
  cursor: pointer;
}

.title-choose::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #5fd3fc;
}

.title-unchoose {
  width: 50%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  position: relative;
  cursor: pointer;
}

.title-unchoose::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
}

.list-num {
  position: absolute;
  top: 0;
  left: 65%;
  background: #409eff;
  border: 1px solid #fff;
  border-radius: 10px;
  min-width: 24px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: #fff;
  padding: 0 4px;
  font-size: 14px;
}
.collapseBox {
  padding: 10px;
}
.collapsTit,
.collapsContent {
  font-size: 16px;
  color: #ffffff;
}
.collapsTit {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.collapsContent {
  padding-left: 20px;
}
:deep(.el-dialog.customDialog) {
  margin-top: 20vh !important;
  background: #3c4167 !important;
  min-height: 500px;
}

:deep(.el-form-item__label) {
  color: #fff;
}

:deep(.el-textarea__inner) {
  border: 1px solid rgb(94, 102, 160) !important;
  background-color: rgba(43, 46, 73) !important;
}

:deep(.el-textarea__inner::placeholder) {
  color: #a9a9a9 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #a9a9a9 !important;
}

:deep(.el-button--primary:hover),
:deep(.el-button--primary:focus) {
  background: rgb(103, 113, 183, 0.2);
  border: 1px solid rgb(103, 113, 183, 0.2) !important;
}

:deep(.el-button--primary) {
  background: rgb(103, 113, 183);
  border: 1px solid rgb(103, 113, 183) !important;
  border-color: rgb(103, 113, 183);
}
:deep(.el-dialog__header) {
  padding: 10px;
}

:deep(.el-dialog__body) {
  padding: 10px;
}
:deep(.el-collapse-item__arrow) {
  display: none;
}
/* 表格 */
:deep(.el-table::before) {
    height: 0;
}

:deep(.el-table__fixed-right::before) {
    height: 0;
}

:deep(.el-table th.el-table__cell.is-leaf) {
    border: none;
}

:deep(.el-table td.el-table__cell) {
    border: none;
}

:deep(.el-table::before) {
    height: 0;
}

:deep(.el-table__fixed::before) {
    height: 0;
}

:deep(.el-table__fixed-right::before) {
    height: 0;
}

:deep(.el-form-item__label) {
    color: #ffffff;
    /* margin-right: 10px; */
}

:deep(.el-table__body-wrapper) {
    background: #3c4167;
}

:deep(.el-table__empty-text) {
    color: #ffffff;
}

:deep(.el-table .el-table__body tr.hover-row>td) {
    background-color: #3c4167 !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td) {
    background-color: #3c4167 !important;
}
</style>