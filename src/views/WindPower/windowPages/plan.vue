<template>
  <div class="box">
    <div class="title">
      <div style="display: flex; align-items: center">
        <img src="@/assets/images/wenjian.png" alt="" />
        未执行计划
      </div>
      <div class="close" @click="closePlan">
        <img src="@/assets/images/guanbi.png" alt="" />
      </div>
    </div>
    <div>
      <el-table
        :data="tableData"
        height="500"
        :row-class-name="tableRowClassName"
      >
        <el-table-column prop="mmsi" label="计划执行时间">
        </el-table-column>
        <el-table-column prop="name" label="创建时间">
        </el-table-column>

        <el-table-column prop="address" label="执行人">
        </el-table-column>
        <el-table-column prop="rule" label="执行情况">
        </el-table-column>
        <el-table-column prop="time" label="计划描述">
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default>
            <el-button type="text" size="small"
              >详情</el-button
            >
            <el-button type="text" size="small">定位</el-button>
            <el-button type="text" size="small">航迹</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 15px">
        <el-pagination
          :page-sizes="[10, 20, 30]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="1"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits } from 'vue';

interface TableRow {
  mmsi?: string;
  name?: string;
  address?: string;
  rule?: string;
  time?: string;
}

interface RowScope {
  row: TableRow;
  rowIndex: number;
}

const tableData = ref<TableRow[]>([]);
const emit = defineEmits(['closePlan']);

const closePlan = () => {
  emit('closePlan');
};

const tableRowClassName = ({ rowIndex }: RowScope) => {
  if (rowIndex % 2 === 0) {
    return "warning-row";
  }
  return '';
};
</script>

<style lang="less" scoped>
.box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(60, 65, 103, 0.95);
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
  width: 50%;
  padding: 10px;
  z-index: 1000;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ffffff;
    margin-bottom: 20px;
    img {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
    .close:hover {
      cursor: pointer;
    }
  }
}
</style>
