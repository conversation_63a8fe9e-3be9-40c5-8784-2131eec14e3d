<template>
  <div class="box">
    <div class="title">
      <div style="display: flex; align-items: center">
        <img src="@/assets/images/wenjian.png" alt="" />
        添加工作区
      </div>
      <div class="close" @click="close">
        <img src="@/assets/images/guanbi.png" alt="" />
      </div>
    </div>
    <div class="inpBox">
      <div class="inpTit">
        名称：
      </div>
      <el-input v-model="form.name" class="elWidth"></el-input>
    </div>
    <div class="inpBox">
      <div class="inpTit">
        经度：
      </div>
      <el-input v-model="form.longitude" class="elWidth"></el-input>
    </div>
    <div class="inpBox">
      <div class="inpTit">
        纬度：
      </div>
      <el-input v-model="form.latitude" class="elWidth"></el-input>
    </div>
    <div class="inpBox">
      <div class="inpTit">
        比例尺：
      </div>
      <el-input v-model="form.scale" class="elWidth"></el-input>
    </div>

    <div class="btns">
      <el-button type="primary" size="small" class="endBtn">定位</el-button>
      <el-button type="primary" size="small" class="endBtn">保存</el-button>
      <el-button type="primary" size="small" class="endBtn">取消</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, defineEmits } from 'vue';

interface WorkspaceForm {
  name: string;
  longitude: string;
  latitude: string;
  scale: string;
}

const emit = defineEmits(['closeWork']);

const form = reactive<WorkspaceForm>({
  name: '',
  longitude: '',
  latitude: '',
  scale: '',
});

const close = () => {
  emit('closeWork');
};
</script>

<style scoped lang="less">
.box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(60, 65, 103, 0.95);
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
  width: 300px;
  padding: 10px;
  z-index: 1000;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ffffff;
    margin-bottom: 20px;
    img {
      width: 20px;
      height: 20px;
      margin-right: 10px;
    }
    .close:hover{
      cursor: pointer;
    }
  }
  .inpBox {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #FFFFFF;
    .inpTit{
      width: 70px;
    }
    .elWidth {
      width: 180px;
      margin-left: 10px;
    }
  }

  .btns{
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>