<template>
  <div class="page">
    <div class="headBox">
      <div style="display: flex; align-items: center">
        <img src="@/assets/images/wenjian.png" alt="" />
        <div class="">详细列表</div>
      </div>
      <div class="close" @click="close">
        <img src="@/assets/images/guanbi.png" alt="" />
      </div>
    </div>
    <div class="content">
      <!-- <div class="condition">
        <div class="condition_item">
          船舶名称：
          <div class="condition_item_inp">
            <input
              type="text"
              placeholder="输入名称"
              class="inp"
              v-model="form.name"
            />
          </div>
        </div>
        <div class="condition_item">
          MMSI：
          <div class="condition_item_inp">
            <input
              type="text"
              placeholder="输入MMSI"
              class="inp"
              v-model="form.name"
            />
          </div>
        </div>
        <div class="condition_item">
          所在辖区：
          <div class="condition_item_inp">
            <el-select
              v-model="form.name"
              placeholder="请选择"
              
              style="height: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="condition_item">
          船舶类型：
          <div class="condition_item_inp">
            <el-select
              v-model="form.name"
              placeholder="请选择"
              :popper-append-to-body="false"
              style="height: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="condition_item">
          国籍：
          <div class="condition_item_inp">
            <el-select
              v-model="form.name"
              placeholder="请选择"
              :popper-append-to-body="false"
              style="height: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="condition">
        <div class="condition_item">
          船长(米)：
          <div style="display: flex; align-items: center">
            <div class="condition_item_inp">
              <input type="text" class="inp small" v-model="form.name" />
            </div>
            <div class="inpLine">-</div>
            <div class="condition_item_inp">
              <input type="text" class="inp small" v-model="form.name" />
            </div>
          </div>
        </div>
        <div class="condition_item">
          风险属性：
          <div class="condition_item_inp">
            <el-select
              v-model="form.name"
              placeholder="请选择"
              :popper-append-to-body="false"
              style="height: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="condition_item">
          优先顺序：
          <div class="condition_item_inp">
            <el-select
              v-model="form.name"
              placeholder="请选择"
              :popper-append-to-body="false"
              style="height: 100%"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="btn">查询</div>
        <div class="btn">重置</div>
        <div class="btn">导出</div>
        <div>
          <el-checkbox v-model="checked">本页船舶分布高亮</el-checkbox>
        </div>
      </div> -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="70px">
        <el-row :gutter="1">
          <el-col :span="4">
            <el-form-item label="船舶名称">
              <el-input v-model="form.id" placeholder="请输入名称" type="text" maxlength="30" style="width: 90%" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="MMSI">
              <el-input v-model="form.id" placeholder="请输入MMSI" type="text" maxlength="30" style="width: 90%" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="所在辖区">
              <el-select v-model="form.rule" placeholder="请选择" popper-class="elDateSeletc" style="width: 90%" clearable>
                <el-option :key="1" :value="1" label="西堠门"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="船舶类型">
              <el-select v-model="form.rule" placeholder="请选择" popper-class="elDateSeletc" style="width: 90%" clearable>
                <el-option :key="1" :value="1" label="客船"></el-option>
                <el-option :key="2" :value="2" label="货船"></el-option>
                <el-option :key="3" :value="3" label="液化船"></el-option>
                <el-option :key="4" :value="4" label="工作船"></el-option>
                <el-option :key="5" :value="5" label="其他"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="国籍">
              <el-select v-model="form.rule" placeholder="请选择" popper-class="elDateSeletc" style="width: 90%" clearable>
                <el-option :key="1" :value="1" label="中国"></el-option>
                <el-option :key="2" :value="2" label="中国台湾"></el-option>
                <el-option :key="3" :value="3" label="中国香港"></el-option>
                <el-option :key="4" :value="4" label="中国澳门"></el-option>
                <el-option :key="5" :value="5" label="其他"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="船长(米)">
              <el-input v-model="form.id" type="text" maxlength="30" style="width: 30%" />
              -
              <el-input v-model="form.id" type="text" maxlength="30" style="width: 30%" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="风险属性">
              <el-select v-model="form.rule" placeholder="请选择" popper-class="elDateSeletc" style="width: 90%" clearable>
                <el-option :key="1" :value="1" label="高风险"></el-option>
                <el-option :key="2" :value="2" label="中风险"></el-option>
                <el-option :key="3" :value="3" label="低风险"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="优先顺序">
              <el-select v-model="form.rule" placeholder="请选择" popper-class="elDateSeletc" style="width: 90%" clearable>
                <el-option :key="1" :value="1" label="必检船"></el-option>
                <el-option :key="2" :value="2" label="应检船"></el-option>
                <el-option :key="3" :value="3" label="可检船"></el-option>
                <el-option :key="4" :value="4" label="不可检船"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="" label-width="0">
              <el-button type="primary" size="small">查询</el-button>
              <el-button type="primary" size="small">重置</el-button>
              <!-- <el-button type="primary" size="mini">导出</el-button> -->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="tableBox">
        <el-table :data="tableData" :border="true" style="width: 100%" height="500">
          <el-table-column :filter-method="filterHandler" prop="name" label="中文船名" width="180" align="center">
          </el-table-column>
          <el-table-column prop="englishName" label="英文船名" width="180" align="center">
          </el-table-column>
          <el-table-column prop="FSCfx" label="FSC风险属性" width="180" align="center">
          </el-table-column>
          <el-table-column prop="FSCyx" label="FSC优先顺序" width="180" align="center">
          </el-table-column>
          <el-table-column prop="mmsi" label="MMSI" width="180" align="center">
          </el-table-column>
          <el-table-column prop="type" label="船舶类型" width="180" align="center">
          </el-table-column>
          <el-table-column prop="nationality" label="国籍" width="180" align="center">
          </el-table-column>
          <el-table-column prop="area" label="所在辖区" width="180" align="center">
          </el-table-column>
          <el-table-column prop="address" label="具体位置" width="180" align="center">
          </el-table-column>
          <el-table-column prop="vts" label="VTS服务区" width="180" align="center">
          </el-table-column>
          <el-table-column prop="status" label="船舶状态" width="180" align="center">
          </el-table-column>
          <el-table-column prop="phone" label="备注手机号" width="180" align="center">
          </el-table-column>
          <el-table-column prop="preNum" label="在船人数" width="180" align="center">
          </el-table-column>
          <el-table-column prop="goodsType" label="货物类型" width="180" align="center">
          </el-table-column>
          <el-table-column prop="goodsNum" label="货物数量" width="180" align="center">
          </el-table-column>
          <el-table-column prop="lat" label="经度" width="180">
          </el-table-column>
          <el-table-column prop="lon" label="纬度" width="180">
          </el-table-column>
          <el-table-column prop="cog" label="航速(节)" width="180">
          </el-table-column>
          <el-table-column prop="length" label="船长(米)" width="180">
          </el-table-column>
          <el-table-column prop="width" label="船宽(米)" width="180">
          </el-table-column>
          <el-table-column prop="time" label="更新时间" width="180">
          </el-table-column>
          <!-- <el-table-column fixed="right" label="操作" width="60">
            <template slot-scope="scope">
              <i class="el-icon-location-outline posit" style="color: #409EFF;"></i>
            </template>
          </el-table-column> -->
        </el-table>
        <div class="paging" style="">
          <el-pagination :page-sizes="[10, 20, 30, 40]" :page-size="100" layout="total, sizes, prev, pager, next, jumper"
            :total="tableData.length">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

interface FormState {
  id: string;
  rule: number | null;
}

interface TableRowData {
  name: string;
  englishName: string;
  FSCfx: string;
  FSCyx: string;
  mmsi: string;
  type: string;
  nationality: string;
  area: string;
  address: string;
  vts: string;
  status: string;
  phone: string;
  preNum: string;
  goodsType: string;
  goodsNum: string;
  lat: string;
  lon: string;
  cog: string;
  length: string;
  width: string;
  time: string;
}

const emit = defineEmits(['close']);

const formRef = ref<FormInstance>();
const form = reactive<FormState>({
  id: '',
  rule: null
});

const rules = reactive<FormRules>({
  // 如果需要添加验证规则，可以在此处定义
});

const tableData = ref<TableRowData[]>([
  {
    name: "远洋一号",
    englishName: "yuanyang",
    FSCfx: "-",
    FSCyx: "-",
    mmsi: "413406030",
    type: "货船",
    nationality: "中国",
    area: "西堠门",
    address: "其他",
    vts: "非VTS服务区",
    status: "锚泊",
    phone: "",
    preNum: "",
    goodsType: "",
    goodsNum: "",
    lat: "",
    lon: "",
    cog: "",
    length: "",
    width: "",
    time: "",
  },
  {
    name: "远洋一号",
    englishName: "yuanyang",
    FSCfx: "-",
    FSCyx: "-",
    mmsi: "413406030",
    type: "货船",
    nationality: "中国",
    area: "西堠门",
    address: "其他",
    vts: "非VTS服务区",
    status: "锚泊",
    phone: "",
    preNum: "",
    goodsType: "",
    goodsNum: "",
    lat: "",
    lon: "",
    cog: "",
    length: "",
    width: "",
    time: "",
  },
]);

const filterHandler = () => {
  // 实现过滤方法
};

const close = () => {
  emit('close');
};
</script>

<style scoped>
.page {
  width: 100%;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  background-color: rgba(63, 72, 110, 0.9);
  box-shadow: 0px 2px 4px 0px rgba(31, 63, 130, 0.5);
}

.headBox {
  display: flex;
  align-items: center;
  padding: 10px 20px 0 10px;
  margin-bottom: 10px;
  justify-content: space-between;
}

.close:hover {
  cursor: pointer;
}

.headBox img {
  width: 22px;
  height: 22px;
}

.content {
  padding: 10px 15px;
}

.condition {
  margin-bottom: 20px;
}

.condition,
.condition_item {
  display: flex;
  align-items: center;
  margin-right: 25px;
  padding-left: 10px;
}

.condition_item_inp {
  /* width: 178px; */
  height: 26px;
}

.inp {
  width: 148px;
  height: 26px;
  border: 1px solid rgb(94, 102, 160);
  padding: 0 15px;
  outline: none;
  /*清除input点击之后的黑色边框*/
  background: #2d2f4c;
  color: #ffffff;
  font-size: 12px;
  border-radius: 4px;
}

.small {
  width: 78px;
  height: 26px;
  padding: 0 15px;
}

.inpLine {
  margin: 0 6px;
}

.btn {
  width: 54px;
  height: 26px;
  background-color: #6771b7;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 12px;
}

.btn,
.posit:hover {
  cursor: pointer;
}

.paging {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}

input::placeholder {
  color: #ffffff;
}

input:focus {
  border: 2px solid rgb(142, 151, 217);
}

:deep(.el-dialog__title) {
  color: #fff;
}

:deep(.el-dialog.customDialog) {
  margin-top: 10vh !important;
  background: #3c4167 !important;
}

:deep(.el-form-item__label) {
  color: #fff;
}

:deep(.el-textarea__inner) {
  border: 1px solid rgb(94, 102, 160) !important;
  background-color: rgba(43, 46, 73) !important;
}

:deep(.el-textarea__inner::placeholder) {
  color: #a9a9a9 !important;
}

:deep(.el-input__inner::placeholder) {
  color: #a9a9a9 !important;
}

:deep(.el-button--primary:hover),
:deep(.el-button--primary:focus) {
  background: rgb(103, 113, 183, 0.2) !important;
  border: 1px solid rgb(103, 113, 183, 0.2) !important;
}

:deep(.el-button--primary) {
  background: rgb(103, 113, 183) !important;
  border: 1px solid rgb(103, 113, 183) !important;
  border-color: rgb(103, 113, 183) !important;
}

:deep(.el-select__caret.el-input__icon.el-icon-arrow-up) {
  height: 100% !important;
  line-height: 100% !important;
}

:deep(.pagination-container) {
  background: none !important;
}

:deep(.el-form-item) {
  margin-bottom: 10px;
}

/* 表格 */
:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table__fixed-right::before) {
  height: 0;
}

:deep(.el-table th.el-table__cell.is-leaf) {
  border: none;
}

:deep(.el-table td.el-table__cell) {
  border: none;
}

:deep(.el-table::before) {
  height: 0;
}

:deep(.el-table__fixed::before) {
  height: 0;
}

:deep(.el-table__fixed-right::before) {
  height: 0;
}

:deep(.el-form-item__label) {
  color: #ffffff;
  margin-right: 10px;
}

:deep(.el-table__body-wrapper) {
  background: #3c4167;
}

:deep(.el-table__empty-text) {
  color: #ffffff;
}

:deep(.el-table .el-table__body tr.hover-row>td) {
  background-color: #3c4167 !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td) {
  background-color: #3c4167 !important;
}
</style>

<!-- <style scoped src="@/assets/datePark.css"></style> -->