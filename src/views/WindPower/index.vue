<template>
  <!-- 外层容器 -->
  <div class="layout" :class="[isAdmin ? 'admT' : 'admF', sidebarLayoutClass]">
    <!-- 主内容区域，包含地图和绝对定位的元素 -->
    <div class="main-content">
      <!-- 地图容器 -->
      <div id="map" class="map"></div>

      <!-- 地图坐标层级信息 -->
      <div class="last-message-box">
        <div class="last-message">
          {{ mousePointlat }}，{{ mousePointlng }}，层级：{{ zoom }}
        </div>
      </div>

      <!-- 顶部悬浮工具栏 -->
      <div class="floating-toolbar" :class="{ 'toolbar-collapsed': isToolbarCollapsed }">
        <div class="toolbar-item" @click="toggleSearchBox" v-if="!showSearchBox">
          <el-icon><Search /></el-icon>
          <span>搜索</span>
        </div>
        <div class="toolbar-item" @click="showEnforcementShips" v-if="!showSearchBox">
          <el-icon><Ship /></el-icon>
          <span>执法船</span>
        </div>
        <div class="toolbar-item" @click="toggleWhiteList" v-if="!showSearchBox">
          <el-icon><Document /></el-icon>
          <span>白名单</span>
        </div>
        <div class="toolbar-item" @click="toggleSidebar" v-if="!showSearchBox">
          <el-icon><Menu /></el-icon>
          <span>菜单</span>
        </div>
        <div class="toolbar-collapse" @click="toggleToolbar">
          <el-icon><Tools /></el-icon>
        </div>
      </div>

      <!-- 搜索框 -->
      <div class="search-box" v-if="showSearchBox">
        <div class="search-input">
          <el-icon><Search /></el-icon>
          <input
            type="text"
            placeholder="请输入船名或MMSI进行搜索"
            v-model="searchVal"
            @keyup.enter="searchSub"
            :disabled="loading"
          />
          <div class="search-actions">
            <el-button type="primary" size="small" @click="searchSub" :loading="loading">
              <el-icon v-if="!loading"><Search /></el-icon>
            </el-button>
            <el-button size="small" @click="toggleSearchBox" :disabled="loading">
              <el-icon><Back /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 地图右侧工具栏 -->
      <rightContent ref="rightContentRef" @points="getPoint" @fun="setFun" @disposeLogShow="disposeLogShow"
        @choiceShip="changeChoiceShip" :isPopup="isPopupRight" :map="map" @changeLegendType="changeLegendType"
        @resetMap="handleResetMap" @resetLayers="handleResetLayers" @reloadShipData="handleReloadShipData">
      </rightContent>

      <!-- 其他绝对定位的弹窗或信息框 -->
      <!-- 查询统计 -->
      <query-statistics :id="refsvalue"></query-statistics>
      <!-- 系统工具 -->
      <!-- AIS异常 -->
      <anomaly v-if="isAnomaly" @closeAnomaly="closeAnomaly"></anomaly>
      <!-- 弧线计算 -->
      <information v-if="isInformation" @closeInformation="closeInformation" :itemId="itemId"></information>
      <!-- 添加工作区 -->
      <workSpace v-if="isWorkSpace" @closeWork="isWorkSpace = false" />
      <!-- 未执行计划 -->
      <plan v-if="isPlan" @closePlan="isPlan = false" />
      <!-- 视频监控 -->
      <videoMatrix v-if="isMatrix" @closeVid="isMatrix = false" />
      <!-- 右侧弹窗合集 -->
      <collection v-if="isCollection" :map="map" :list="chuan" :Collection="Collection"
        @closeCollection="isCollection = false"></collection>
      <!-- 历史预警弹窗 -->
      <historical ref="historicalRef" :isHistor="isHistor" @closeTabData="closeTabData" :map="map" @getIsDialog="getIsDialog">
      </historical>
      <dispose-log ref="disposelog" :isHistor="isDispose" @closeTabData="disposeLogClose">
      </dispose-log>
      <!-- 进出港记录 -->
      <record v-if="isRecord" @closeRecord="closeRecord"></record>
      <statistics :isStatistics="isStatistics" @closeStatis="closeStatistics"></statistics>
      <!-- 报表 -->
      <reportForms v-if="isReport" @closeReport="isReport = false" />
      <!-- 船舶档案 -->
      <shipAdmin v-if="isShipAdmin" @close="isShipAdmin = false" />

      <!-- 鼠标点击船舶信息 -->
      <shipInfo v-show="shipInfoShow" @closeShip="closeShipInfo" :info="onShip || {}" :shipInfo="{}" :map="map" ref="shipInfoRef"></shipInfo>

      <!-- 鼠标悬浮船舶信息 -->
      <div class="infoWindow" v-if="InfoShow" :style="{ left: tranLeft, top: tranTop }">
        <div class="infoWindow_title">
          {{
            infoData.chineseName ||
            infoData.englishName ||
            infoData.name ||
            infoData.mmsi
          }}
        </div>
        <div class="infoWindow_text">
          <div>MMSI:</div>
          <div>{{ infoData.mmsi }}</div>
        </div>
        <div class="infoWindow_text">
          <div>经度:</div>
          <div>{{ infoData.lat.toFixed(6) }}</div>
        </div>
        <div class="infoWindow_text">
          <div>纬度:</div>
          <div>{{ infoData.lon.toFixed(6) }}</div>
        </div>
        <div class="infoWindow_text">
          <div>航速:</div>
          <div>{{ typeof infoData.sog === 'number' ? Math.floor(infoData.sog) : 0 }}节</div>
        </div>
        <div class="infoWindow_text">
          <div>类型:</div>
          <div>{{ setShipType(infoData.shipType) }}</div>
        </div>
        <div class="infoWindow_text">
          <div>发动机状态:</div>
          <div v-if="infoData.status || infoData.status == 0">
            {{ getStatus(infoData.status) }}
          </div>
        </div>
        <div class="infoWindow_text">
          <div>更新时间:</div>
          <div>
            {{ new Date(infoData.time * 1000).toLocaleString() }}({{
              getTimeNow(new Date().getTime() - infoData.time * 1000)
            }})
          </div>
        </div>
      </div>
      <div class="infoWindow" v-if="envirShow" :style="{ left: tranLeft, top: tranTop }">
        <div class="infoWindow_title">{{ envirData.title }}</div>
        <div class="infoWindow_text">
          <div>名称:</div>
          <div>{{ envirData.title }}</div>
        </div>
        <div class="infoWindow_text">
          <div>类型:</div>
          <div>{{ envirData.type }}</div>
        </div>
        <div class="infoWindow_text">
          <div>备注:</div>
          <div>{{ envirData.remark }}</div>
        </div>
      </div>

       <!-- 预警处理对话框 -->
      <el-dialog title="预警处理" v-model="isDialogSHow" width="500px" custom-class="customDialog"
        @close="handleClose" :modal="false">
        <div class="diaBox" v-dialogDrag>
          <div class="shipInfo">
            <div class="shipInfo_item">预警船舶：{{ isDialogItem.shipName }}</div>
            <div class="shipInfo_item">MMSI：{{ isDialogItem.mmsi }}</div>
            <div class="shipInfo_item">
              预警时间：{{
                new Date(isDialogItem.warningTime * 1000).toLocaleString()
              }}
            </div>
            <div class="shipInfo_item">
              预警规则：{{ isDialogItem.eventContent }}
            </div>
            <div class="shipInfo_item">预警区域：{{ isDialogItem.address }}</div>
          </div>
          <div class="warnType">
            <div style="display: flex; align-items: center">
              <div class="title">异常类型：</div>
              <el-select v-model="isDialogItem.excepitionType" placeholder="请选择" class="elwidth"
                :disabled="isDialogSHowType == 1">
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.value" :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div style="display: flex; align-items: center">
              <div class="title">处置方式：</div>
              <el-select v-model="isDialogItem.handlingWay" placeholder="请选择" class="elwidth"
                :disabled="isDialogSHowType == 1">
                <el-option v-for="item in wayOptions" :key="item.value" :label="item.value" :value="item.value">
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="describe">
            <div class="title">详细描述：</div>
            <div class="describeText">
              <textarea v-model="isDialogItem.detail" class="textArea" :readonly="isDialogSHowType == 1"></textarea>
            </div>
          </div>
          <div class="measure">
            <div class="title">处置措施：</div>
            <el-input v-model="isDialogItem.treatmentMeasure" placeholder="请输入内容" class="elinp"
              :readonly="isDialogSHowType == 1"></el-input>
          </div>
          <div class="warnType">
            <div style="display: flex; align-items: center">
              <div class="title">纠正情况：</div>
              <el-select v-model="isDialogItem.correctTheSituation" placeholder="请选择" class="elwidth"
                :disabled="isDialogSHowType == 1">
                <el-option v-for="item in correctOptions" :key="item.value" :label="item.value" :value="item.value">
                </el-option>
              </el-select>
            </div>
            <div style="display: flex; align-items: center">
              <div class="title">是否处罚：</div>
              <el-select v-model="isDialogItem.punish" placeholder="请选择" class="elwidth" :disabled="isDialogSHowType == 1">
                <el-option v-for="item in punishOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="measure">
            <div class="title">备注：</div>
            <el-input v-model="isDialogItem.remark" placeholder="请输入内容" class="elinp"
              :readonly="isDialogSHowType == 1"></el-input>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="submitForm" v-if="isDialogSHowType == 2">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 船舶告警通知组件 -->
      <ShipAlarmNotification ref="shipAlarmRef" />

      <!-- 执法船列表组件 -->
      <GaShipList v-if="showGaShipList" @close="closeGaShipList" />

      <!-- 白名单列表组件 -->
      <WhiteList v-if="showWhiteList" @close="closeWhiteList" />

      <!-- 船舶详情面板 -->
      <ship-detail-panel
        v-model:visible="showShipDetail"
        :ship-data="selectedShipData"
        @close="handleShipDetailClose"
      />

      <!-- 搜索结果弹窗 -->
      <search-results-dialog
        v-model:visible="showSearchResults"
        :search-results="searchList"
        :map="map"
        @ship-selected="handleSearchResultShipSelected"
        @close="handleSearchResultsClose"
      />

    </div>

    <!-- 右侧侧边栏 (PopupRight) -->
    <PopupRight v-if="isPopupRight" :Popup="PopupType" :map="map" :searchList="searchList" :isCollapsed="isCollapsed"
      @closeCollection="isPopupRight = false" @isdrawer="getisdrawer" @renewData="renewData"
      @isCollapsedChange="handleCollapsedChange" @reloadShipData="handleReloadShipData"></PopupRight>

    <!-- 性能调试面板（仅开发环境） -->
    <!-- <PerformanceDebugPanel
      :visible="showPerformancePanel"
      @close="showPerformancePanel = false"
      @export="handlePerformanceDataExport"
    /> -->

    <!-- 性能调试面板切换按钮（仅开发环境） -->
    <!-- <div v-if="isDevelopment" class="performance-debug-toggle">
      <el-button
        type="primary"
        size="small"
        @click="togglePerformancePanel"
        title="切换性能调试面板 (Ctrl+Shift+P)"
      >
        性能监控
      </el-button>
    </div> -->

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch, defineAsyncComponent } from 'vue';
// import type { ComponentInternalInstance } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useUserStore } from '@/store/modules/user';
// import { Place } from "@/utils/request";
import { getShipList, shipHistory, envList, plotList } from "@/api/map/map";
import { getToken } from "@/utils/auth";
// import Sidebar from "@/layout/components/Sidebar/index.vue";
import { updateUserPwd } from "@/api/system/user";
import { getdispose, getdisposeDetail } from "@/api/wan/realTime";
import { getWhiteList } from "@/api/shipGrouping";
import { getShipAlarm } from "@/api/wan/shipAis"; // 导入船舶告警接口
import fen from "@/assets/fen.json";

// 导入船舶追踪功能
import { initShipTracking, loadShipAisData, loadShipAisDataOptimized, setFences } from './shipTracking';

// OpenLayers导入
import Map from 'ol/Map.js';
import View from 'ol/View.js';
import { defaults as Defaults } from 'ol/control.js';
import TileLayer from 'ol/layer/Tile.js';
import XYZ from 'ol/source/XYZ.js';
import VectorLayer from 'ol/layer/Vector.js';
import VectorSource from 'ol/source/Vector.js';
import { Style, Circle as CircleStyle, Fill, Stroke, Icon } from 'ol/style.js';
import { Draw, Modify, Snap, Select } from 'ol/interaction.js';
import { Point, LineString, Polygon } from 'ol/geom.js';
import Feature from 'ol/Feature.js';
import { fromLonLat, toLonLat } from 'ol/proj.js';
import Overlay from 'ol/Overlay.js';
import { getLength } from 'ol/sphere';
// 导入瓦片相关类型
import Tile from 'ol/Tile.js';
import { LoadFunction } from 'ol/Tile.js';
import ImageTile from 'ol/ImageTile.js';

// 如果存在mapUtils引入冲突，我们使用原有的
import mapUtils from '@/utils/mapUtils';

// Lazy load components
const ShipAlarmNotification = defineAsyncComponent(() => import('./components/ShipAlarmNotification.vue'));
const GaShipList = defineAsyncComponent(() => import('./components/GaShipList.vue'));
const WhiteList = defineAsyncComponent(() => import('./components/WhiteList.vue'));
const disposeLog = defineAsyncComponent(() => import('@/views/WindPower/monitor/disposeLog.vue'));
const reportForms = defineAsyncComponent(() => import('@/views/WindPower/windowPages/reportForms.vue'));
const videoMatrix = defineAsyncComponent(() => import('@/views/WindPower/video/videoMatrix.vue'));
const warningInfo = defineAsyncComponent(() => import('@/views/WindPower/windowPages/warningInfo.vue'));
const plan = defineAsyncComponent(() => import('@/views/WindPower/windowPages/plan.vue'));
const historical = defineAsyncComponent(() => import('@/views/WindPower/monitor/historical.vue'));
const record = defineAsyncComponent(() => import('@/views/WindPower/monitor/record.vue'));
const statistics = defineAsyncComponent(() => import('@/views/WindPower/monitor/statistics.vue'));
const anomaly = defineAsyncComponent(() => import('@/views/WindPower/riskWarn/anomaly.vue'));
const alarm = defineAsyncComponent(() => import('@/views/WindPower/alarm.vue'));
const searchRes = defineAsyncComponent(() => import('@/views/WindPower/searchRes.vue'));
const rightContent = defineAsyncComponent(() => import('@/views/WindPower/right-content.vue'));
const shipInfo = defineAsyncComponent(() => import('@/views/WindPower/shipInfo.vue'));
const PopupRight = defineAsyncComponent(() => import('@/views/WindPower/shipSupervision/popup.vue'));
const queryStatistics = defineAsyncComponent(() => import('@/views/WindPower/queryStatistics/index.vue'));
const information = defineAsyncComponent(() => import('@/views/WindPower/windowPages/information.vue'));
const workSpace = defineAsyncComponent(() => import('@/views/WindPower/windowPages/workspace.vue'));
const shipAdmin = defineAsyncComponent(() => import('@/views/WindPower/monitor/shipAdmin.vue'));
const PerformanceDebugPanel = defineAsyncComponent(() => import('@/components/PerformanceDebugPanel.vue'));
import InfoWindow from './components/InfoWindow.vue';
import emitter from '@/utils/bus'; // 确保导入emitter
import { Search, Ship, Menu, Tools, Back, Document } from '@element-plus/icons-vue';

// 在 import 区域添加
import ShipDetailPanel from './components/ShipDetailPanel.vue';
import SearchResultsDialog from './components/SearchResultsDialog.vue';

// 在 defineAsyncComponent 区域添加
const shipDetailPanel = defineAsyncComponent(() => import('./components/ShipDetailPanel.vue'));
const searchResultsDialog = defineAsyncComponent(() => import('./components/SearchResultsDialog.vue'));

// Global T object from window
declare const window: Window & {
  T: any;
  emitter?: any; // 添加emitter到window类型定义
  shipTracking?: any; // 添加shipTracking到window类型定义
};

// Types
interface ShipInfo {
  id?: number;
  mmsi: string;
  name?: string;
  chineseName?: string;
  englishName?: string;
  lat?: number;
  lon?: number;
  sog?: number;
  cog?: number;
  shipType?: number;
  status?: number;
  time?: number;
  destination?: string;
  eta?: number;
  draught?: number;
  region?: string;
}

interface UserStore {
  nickname: string;
  roles: string[];
  logout: () => Promise<void>;
}

interface DialogItem {
  shipName: string;
  mmsi: string;
  warningTime: number;
  eventContent: string;
  address: string;
  excepitionType: string;
  handlingWay: string;
  detail: string;
  treatmentMeasure: string;
  correctTheSituation: string;
  punish: boolean;
  remark: string;
}

interface PunishOption {
  value: string; // 修改为string类型
  label: string;
}

// 使用Pinia store
const userStore = useUserStore();
const router = useRouter();

// Define refs for component instances
const shipAlarmRef = ref(null);
const historicalRef = ref(null);
const disposelog = ref(null);
const rightContentRef = ref(null);
const shipInfoRef = ref(null);

// Define data properties
const map = ref(null);
const Collection = ref("");
const PopupType = ref("");
const isCollection = ref(false);
const isHistor = ref(false);
const isDispose = ref(false);
const isPopupRight = ref(false);
const isCollapsed = ref(false);
const isStatistics = ref(false);
const shipInfoShow = ref(false);
const isShipAdmin = ref(false);
const showSearchBox = ref(false); // 搜索框显示状态

// Ship info related data
const onShip = ref<ShipInfo | null>(null);
const InfoShow = ref(false);
const infoData = reactive<ShipInfo>({
  mmsi: '',
  lat: 0,
  lon: 0,
  sog: 0,
  shipType: 0,
  status: undefined,
  time: 0,
  chineseName: '',
  englishName: '',
  name: ''
});

// More state variables
const refsvalue = ref("");
const isAnomaly = ref(false);
const isInformation = ref(false);
const isWorkSpace = ref(false);
const isPlan = ref(false);
const isMatrix = ref(false);
const isReport = ref(false);
const isRecord = ref(false);
const visibleUser = ref(false);
const isAdmin = ref(false);
const itemId = ref("");
const timeYMD = ref("");
const timeHMS = ref("");
const mousePointlat = ref("");
const mousePointlng = ref("");
const zoom = ref("");
const searchVal = ref("");
const chuan = ref(null);
const loading = ref(false);
const isDialog = ref(false);
const isDialogSHow = ref(false);
const isDialogSHowType = ref(2);
const isDialogItem = reactive<DialogItem>({
  shipName: '',
  mmsi: '',
  warningTime: 0,
  eventContent: '',
  address: '',
  excepitionType: '',
  handlingWay: '',
  detail: '',
  treatmentMeasure: '',
  correctTheSituation: '',
  punish: false,
  remark: ''
});
const typeOptions = ref([]);
const wayOptions = ref([]);
const correctOptions = ref([]);
const punishOptions = ref<PunishOption[]>([
  { value: 'true', label: '是' }, // 修改为字符串
  { value: 'false', label: '否' }
]);
const formPwd = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});
const rulesPwd = reactive({
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
  confirmPassword: [{ required: true, message: '请确认新密码', trigger: 'blur' }]
});
const envirShow = ref(false);
const envirData = reactive({
  title: '',
  type: '',
  remark: ''
});
const tranLeft = ref('0px');
const tranTop = ref('0px');
const searchList = ref([]);
const isSidebarDrawer = ref(false); // 新增：存储侧边栏是否为抽屉模式
const showGaShipList = ref(false);
const showWhiteList = ref(false);

// 船舶详情相关状态
const showShipDetail = ref(false);
const selectedShipData = ref<ShipInfo | null>(null);

// 搜索结果相关状态
const showSearchResults = ref(false);

// 工具栏状态
const isToolbarCollapsed = ref(false);

// 性能调试面板相关状态
const isDevelopment = import.meta.env.VITE_APP_ENV === 'development';
const showPerformancePanel = ref(false);

// Create computed properties
const username = computed(() => userStore.nickname || '');
const userRole = computed(() => userStore.roles?.[0] || '普通用户');

// 新增：计算布局类
const sidebarLayoutClass = computed(() => {
  if (!isPopupRight.value) {
    return ''; // 侧边栏隐藏，无特殊类
  }
  // 侧边栏显示时，根据isSidebarDrawer状态决定
  return isSidebarDrawer.value ? 'has-sidebar-mini' : 'has-sidebar';
});

// Methods
const isShowNavWAn = (typeName) => {
  if (!typeName) return;

  // 根据不同的类型显示不同的内容
  switch (typeName) {
    case 'statistics':
      isStatistics.value = true;
      break;
    case 'realTime':
      // 显示实时预警
      isHistor.value = true;
      break;
    // 其他类型...
    default:
      // 未处理的导航类型
  }
};

const getPoint = (val) => {
  if (!map.value) return;

  // 如果val是坐标对象
  if (val && typeof val === 'object' && val.lat && val.lon) {
    // 设置地图中心点
    map.value.getView().setCenter([val.lon, val.lat]);
    map.value.getView().setZoom(10.5);
  }
  // 如果val是字符串或其他类型
  else if (val) {
    // 处理其他类型的位置信息
  }
};

// 活动绘图交互
let activeDrawInteraction = null;
let activeSelectInteraction = null;
let measureTooltipElement = null;
let measureTooltip = null;

// 在import区域添加测量工具类
import { MeasureUtils, addMeasureStyles } from '@/utils/measureUtils';
// 导入防抖工具函数
import { debounce } from '@/utils/index';
// 导入性能监控
import { performanceMonitor } from '@/utils/performanceMonitor';
import { performanceConfig, PerformanceMode } from '@/config/performance';
import '@/config/performance';

// 测量工具相关变量
let measureUtils: MeasureUtils | null = null;
let measureCleanupFn: (() => void) | null = null;
const isMeasuring = ref(false);

// 在onMounted钩子中添加清理代码
onMounted(() => {
  // 其他初始化代码...

  // 等待地图初始化完成后创建测量工具
  nextTick(() => {
    if (map.value) {
      // 添加测量样式
      addMeasureStyles();
      // 创建测量工具实例
      measureUtils = new MeasureUtils(map.value);
    }
  });

  // 移除错误位置的事件监听器注册，应该在onMounted中注册

});

// 优化的清除测量方法
const clearMeasurements = () => {
  try {
    if (measureUtils) {
      measureUtils.clearMeasurement();
    }

    // 清除清理函数
    if (measureCleanupFn) {
      try {
        measureCleanupFn();
      } catch (error) {
        console.warn('执行测量清理函数失败:', error);
      }
      measureCleanupFn = null;
    }

    // 通知右侧内容组件测量已结束
    isMeasuring.value = false;
  } catch (error) {
    console.error('清除测量失败:', error);
    isMeasuring.value = false;
  }
};

// 添加重置地图方法
const resetMap = () => {

  try {
    // 清除所有测量
    if (measureUtils) {
      measureUtils.clearMeasurement();
      measureCleanupFn = null;
    }

    // 确保清除所有可能的绘图交互
    clearDrawInteractions();

    // 清除向量图层中的所有要素
    if (map.value) {
      const layers = map.value.getLayers().getArray();
      layers.forEach(layer => {
        if (layer instanceof VectorLayer) {
          const source = layer.getSource();
          if (source && typeof source.clear === 'function') {
            source.clear();
          }
        }
      });

      // 重置地图视图到初始位置
      map.value.getView().animate({
        center: [121.9, 30.5], // 地图初始中心点
        zoom: 10.5,           // 地图初始缩放级别
        duration: 800
      });

      // 通知右侧内容组件测量已结束
      isMeasuring.value = false;

      // 显示重置成功提示
      ElMessageBox.alert('所有测量已清除，地图已返回初始位置', '地图已重置', {
        type: 'success',
        confirmButtonText: '确定'
      });
    }
  } catch (error) {
    ElMessageBox.alert('重置地图过程中发生错误，请刷新页面', '错误', {
      type: 'error',
      confirmButtonText: '确定'
    });
  }
};

// 在setFun方法中添加距离和面积测量逻辑
const setFun = (val: number) => {
  if (!map.value) {
    console.warn('地图未初始化，无法启动测量');
    return;
  }

  if (!measureUtils) {
    console.warn('测量工具未初始化，无法启动测量');
    return;
  }

  // 先清除之前的测量
  clearMeasurements();

  isMeasuring.value = true;

  try {
    if (val === 1) {
      // 距离测量
      console.log('启动距离测量');
      measureCleanupFn = measureUtils.startMeasureDistance();
    } else if (val === 2) {
      // 面积测量
      console.log('启动面积测量');
      measureCleanupFn = measureUtils.startMeasureArea();
    }
  } catch (error) {
    console.error('启动测量失败:', error);
    isMeasuring.value = false;
    // 显示用户友好的错误信息
    ElMessage.error('启动测量工具失败，请刷新页面后重试');
  }
};

const disposeLogShow = () => {
  isDispose.value = true;
};

const disposeLogClose = () => {
  isDispose.value = false;
};

const changeLegendType = (val) => {
  if (map.value && typeof val === 'number') {
    // 根据传入的值切换图例类型
    
    if (val >= 1 && val <= 4) {
      // 地图类型切换 (1:标准地图, 2:卫星图, 3:地形图, 4:海图)
      handleMapTypeChange(val);
    } else if (val >= 10 && val <= 15) {
      // 业务图层控制 (10:船舶, 11:电子围栏, 12:船舶图例, 13:港口, 14:锚地, 15:航道)
      handleLayerVisibilityChange(val);
    }
  }
};

// 新增 - 处理地图类型变更
const handleMapTypeChange = (type) => {
  try {
    if (!map.value) return;
    
    // 获取所有图层
    const layers = map.value.getLayers().getArray();
    
    // 根据类型筛选并设置可见性
    switch (type) {
      case 1: // 标准地图
        // 设置标准图层可见，其他基础图层不可见
        layers.forEach(layer => {
          const className = layer.getClassName ? layer.getClassName() : '';
          if (className === 'light-layer') {
            layer.setVisible(true);
          } else if (['satellite-layer', 'terrain-layer', 'sea-layer'].includes(className)) {
            layer.setVisible(false);
          }
        });
        break;
      case 2: // 卫星图
        layers.forEach(layer => {
          const className = layer.getClassName ? layer.getClassName() : '';
          if (className === 'satellite-layer') {
            layer.setVisible(true);
          } else if (['light-layer', 'terrain-layer', 'sea-layer'].includes(className)) {
            layer.setVisible(false);
          }
        });
        break;
      case 3: // 地形图
        layers.forEach(layer => {
          const className = layer.getClassName ? layer.getClassName() : '';
          if (className === 'terrain-layer') {
            layer.setVisible(true);
          } else if (['light-layer', 'satellite-layer', 'sea-layer'].includes(className)) {
            layer.setVisible(false);
          }
        });
        break;
      case 4: // 海图
        layers.forEach(layer => {
          const className = layer.getClassName ? layer.getClassName() : '';
          if (className === 'sea-layer') {
            layer.setVisible(true);
          } else if (['light-layer', 'satellite-layer', 'terrain-layer'].includes(className)) {
            layer.setVisible(false);
          }
        });
        break;
    }
  } catch (error) {
  }
};

// 新增 - 处理图层可见性变更
// 电子围栏显示状态
const isFenceVisible = ref(true);
// 船舶图例显示状态
const isShipLegendEnabled = ref(false);

const handleLayerVisibilityChange = (layerType) => {
  try {
    if (!map.value) return;

    // 图层类型与索引的映射
    const layerTypeMap = {
      10: 'ships', // 船舶图层
      12: 'shipLegend', // 船舶图例
    };

    const layerName = layerTypeMap[layerType];
    if (!layerName) return;

    // 根据不同的图层类型，切换对应图层的可见性
    switch (layerName) {
      case 'ships':
        // 切换船舶图层可见性 - 使用直接的图层控制方式
        toggleShipLayerVisibility();
        break;
      case 'shipLegend':
        // 切换船舶图例显示模式
        toggleShipLegendMode();
        break;
    }
  } catch (error) {
    console.error('图层可见性切换失败:', error);
  }
};

// 切换船舶图层可见性 - 独立控制，不影响其他图层
const toggleShipLayerVisibility = () => {
  try {
    if (!map.value) return;

    const layers = map.value.getLayers().getArray();
    let foundShipLayer = false;

    // 找到船舶图层并切换可见性
    layers.forEach(layer => {
      if (layer.get('name') === 'shipLayer' ||
          layer.get('layerType') === 'ships' ||
          (layer.getClassName && layer.getClassName() === 'ship-layer')) {
        const currentVisibility = layer.getVisible();
        layer.setVisible(!currentVisibility);
        foundShipLayer = true;
        console.log(`船舶图层可见性已切换为: ${!currentVisibility}`);
      }
    });

    if (!foundShipLayer) {
      console.warn('未找到船舶图层');
    }
  } catch (error) {
    console.error('切换船舶图层可见性失败:', error);
  }
};



// 切换船舶图例显示模式
const toggleShipLegendMode = () => {
  try {
    // 切换船舶图例状态
    isShipLegendEnabled.value = !isShipLegendEnabled.value;

    // 显示操作提示
    const message = isShipLegendEnabled.value ? '船舶图例模式已启用' : '船舶图例模式已关闭';
    ElMessage.success(message);

    // 调用船舶追踪模块的样式切换函数
    if (typeof window !== 'undefined' && window.shipTracking) {
      if (isShipLegendEnabled.value) {
        // 启用船舶图例模式
        window.shipTracking.enableShipLegendMode();
      } else {
        // 禁用船舶图例模式，恢复原始样式
        window.shipTracking.disableShipLegendMode();
      }
    } else {
      // 如果shipTracking模块不可用，直接调用本地的船舶样式切换函数
      updateShipStyles(isShipLegendEnabled.value);
    }
  } catch (error) {

    ElMessage.error('切换船舶图例模式失败，请重试');
    // 恢复状态
    isShipLegendEnabled.value = !isShipLegendEnabled.value;
  }
};

// 本地船舶样式更新函数（备用方案）
const updateShipStyles = (enableLegend: boolean) => {
  try {
    if (!map.value) return;

    const layers = map.value.getLayers().getArray();

    // 查找船舶图层
    layers.forEach(layer => {
      if (layer.get('name') === 'shipLayer' ||
          layer.get('layerType') === 'ships' ||
          (layer.getClassName && layer.getClassName() === 'ship-layer')) {

        const source = layer.getSource();
        if (source && typeof source.getFeatures === 'function') {
          const features = source.getFeatures();

          // 更新每个船舶要素的样式
          features.forEach(feature => {
            const shipData = feature.get('shipData');
            if (shipData && shipData.shipType !== undefined) {
              // 根据是否启用图例模式设置样式
              if (enableLegend) {
                // 使用图例颜色
                const legendColor = getShipLegendColor(shipData.shipType);
                feature.setStyle(createShipLegendStyle(legendColor));
              } else {
                // 恢复原始样式
                feature.setStyle(null); // 使用图层默认样式
              }
            }
          });
        }
      }
    });
  } catch (error) {
  }
};

// 根据船舶类型获取图例颜色
const getShipLegendColor = (shipType: number): string => {
  // 根据shipType字段映射到对应颜色
  if (shipType === 1 || (shipType >= 60 && shipType <= 69)) {
    return '#FFA500'; // 橙色客船
  } else if (shipType === 2 || (shipType >= 70 && shipType <= 74)) {
    return '#FFFF00'; // 黄色货船
  } else if (shipType === 3 || shipType === 30) {
    return '#0000FF'; // 蓝色渔船
  } else if (shipType === 4 || shipType === 33) {
    return '#8B4513'; // 深棕色作业船
  } else if (shipType === 5 || shipType === 52) {
    return '#800080'; // 紫色拖船
  } else {
    return '#D2B48C'; // 淡棕色其他类型
  }
};

// 创建船舶图例样式
const createShipLegendStyle = (color: string) => {
  return new Style({
    image: new CircleStyle({
      radius: 6,
      fill: new Fill({
        color: color
      }),
      stroke: new Stroke({
        color: '#fff',
        width: 2
      })
    })
  });
};

const changeChoiceShip = (val) => {
  if (!val) return;

  // 选择船舶的处理逻辑
  // 例如：显示船舶详情、高亮显示等
  if (val.name === '船舶') {
    // 处理船舶选择
  } else if (val.name === '标绘') {
    // 处理标绘选择
  }
  // 其他处理...
};

const getisdrawer = (val: boolean) => {
  isSidebarDrawer.value = val; // 更新isSidebarDrawer状态
};

const renewData = () => {
  // 刷新数据

  // 例如：重新获取船舶列表
  // if (map.value) {
  //   // 调用获取船舶数据的API
  //   getShipList({}).then(res => {
  //     if (res && res.data) {
  //       chuan.value = res.data;
  //       // 更新地图上的船舶标记等
  //     }
  //   }).catch(err => {
  //     console.error('获取船舶数据失败:', err);
  //   });
  // }
};

const closeAnomaly = () => {
  isAnomaly.value = false;
};

const closeInformation = () => {
  isInformation.value = false;
};

const closeRecord = () => {
  isRecord.value = false;
};

const closeStatistics = () => {
  isStatistics.value = false;
};

const closeTabData = () => {
  isHistor.value = false;
};

const getIsDialog = (val) => {
  if (val && typeof val === 'object') {
    // 处理对话框数据
    isDialogSHow.value = true;
    isDialogSHowType.value = val.type || 2;

    // 填充对话框数据
    if (val.data) {
      Object.assign(isDialogItem, val.data);
    }
  }
};

// 优化的搜索函数（使用防抖）
const performSearch = async (searchValue: string) => {
  if (!searchValue) {
    ElMessageBox.alert('请输入搜索内容', '提示');
    return;
  }

  try {
    // 显示加载状态
    loading.value = true;

    // 调用船舶AIS数据API获取所有船舶数据
    const { getShipAis } = await import('@/api/wan/shipAis');
    const response = await getShipAis({});

    let allShips = [];
    if (response && response.data && Array.isArray(response.data)) {
      allShips = response.data;
    } else if (response && response.rows && Array.isArray(response.rows)) {
      allShips = response.rows;
    }

    // 前端模糊搜索过滤
    const searchResults = allShips.filter(ship => {
      if (!ship) return false;

      const searchTerm = searchValue.toLowerCase().trim();

      // 搜索船名（中文名、英文名、通用名称）
      const shipName = (ship.chineseName || ship.englishName || ship.name || '').toLowerCase();
      const nameMatch = shipName.includes(searchTerm);

      // 搜索MMSI
      const mmsiMatch = String(ship.mmsi || '').includes(searchTerm);

      // 搜索呼号
      const callsignMatch = (ship.callsign || '').toLowerCase().includes(searchTerm);

      return nameMatch || mmsiMatch || callsignMatch;
    });

    // 根据搜索结果数量决定显示方式
    if (searchResults.length === 0) {
      ElMessage.warning('未找到相关船舶信息');
      return;
    } else if (searchResults.length === 1) {
      // 只有一个结果，直接显示船舶详情
      const ship = searchResults[0];
      showShipDetailDirectly(ship);
    } else {
      // 多个结果，显示搜索结果列表
      searchList.value = searchResults;
      showSearchResultsList();
    }

  } catch (error) {
    console.error('搜索失败:', error);
    ElMessage.error('搜索失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 直接显示船舶详情
const showShipDetailDirectly = (ship: ShipInfo) => {
  try {
    // 设置船舶数据并显示详情面板
    selectedShipData.value = ship;
    showShipDetail.value = true;

    // 如果船舶有坐标，将地图中心移动到船舶位置
    if (ship.lon && ship.lat && map.value) {
      map.value.getView().animate({
        center: [ship.lon, ship.lat],
        zoom: 12,
        duration: 1000
      });

      // 使用船舶追踪模块的选中功能来框选船舶
      selectShipOnMap(ship);
    }

    // 关闭搜索框
    showSearchBox.value = false;

    ElMessage.success(`已找到船舶：${ship.chineseName || ship.englishName || ship.name || ship.mmsi}`);
  } catch (error) {
    console.error('显示船舶详情失败:', error);
    ElMessage.error('显示船舶详情失败');
  }
};

// 在地图上选中并框选船舶
const selectShipOnMap = (ship: ShipInfo) => {
  try {
    // 优先使用直接调用方式，避免重复调用
    if (window.shipTracking && typeof window.shipTracking.selectShipByMMSI === 'function') {
      const success = window.shipTracking.selectShipByMMSI(ship.mmsi);
      if (success) {
        console.log(`成功选中船舶 MMSI: ${ship.mmsi}`);
        return;
      }
    }

    // 备用方案：通过事件总线通知
    if (window.emitter) {
      window.emitter.emit('select-ship-by-mmsi', ship.mmsi);
    }
  } catch (error) {
    console.error('选中船舶失败:', error);
  }
};

// 显示搜索结果列表
const showSearchResultsList = () => {
  try {
    // 显示搜索结果弹窗
    showSearchResults.value = true;

    // 关闭搜索框
    showSearchBox.value = false;

    ElMessage.success(`找到 ${searchList.value.length} 艘相关船舶`);
  } catch (error) {
    console.error('显示搜索结果列表失败:', error);
    ElMessage.error('显示搜索结果失败');
  }
};

const searchSub = () => {
  console.log('开始搜索:', searchVal.value);
  performSearch(searchVal.value);
};

// 工具栏相关方法
const toggleSearchBox = () => {
  showSearchBox.value = !showSearchBox.value;
  if (!showSearchBox.value) {
    searchVal.value = '';
  }
  console.log('搜索框状态切换为:', showSearchBox.value);
};

const toggleToolbar = () => {
  isToolbarCollapsed.value = !isToolbarCollapsed.value;
};

const showEnforcementShips = () => {
  showGaShipList.value = true;
};

const toggleWhiteList = () => {
  showWhiteList.value = !showWhiteList.value;
};


const toggleSidebar = () => {
  // 无论当前状态如何，都先设置类型
  PopupType.value = "leftContent";

  // 如果当前是关闭状态，则在打开时重置所有状态
  if (!isPopupRight.value) {
    isSidebarDrawer.value = false; // 重置抽屉状态
  }

  // 切换显示状态
  isPopupRight.value = !isPopupRight.value;
};

const closeGaShipList = () => {
  showGaShipList.value = false;
};

const closeWhiteList = () => {
  showWhiteList.value = false;
};

// 搜索结果相关方法
const handleSearchResultShipSelected = (ship: ShipInfo) => {
  try {
    // 关闭搜索结果弹窗
    showSearchResults.value = false;

    // 显示船舶详情并框选
    showShipDetailDirectly(ship);
  } catch (error) {
    console.error('处理搜索结果船舶选中失败:', error);
    ElMessage.error('选中船舶失败');
  }
};

const handleSearchResultsClose = () => {
  showSearchResults.value = false;
};

const getTimeNow = (time: number): string => {
  const days = Math.floor(time / (1000 * 60 * 60 * 24));
  const hours = Math.floor((time % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((time % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((time % (1000 * 60)) / 1000);

  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else {
    return `${seconds}秒前`;
  }
};

const setShipType = (type: number | undefined): string => {
  if (!type) return '未知';

  if (type >= 60 && type <= 69) {
    return "客船";
  } else if (type >= 70 && type <= 74) {
    return "货船";
  } else if (type === 33) {
    return "作业船";
  } else if (type === 52) {
    return "拖船";
  } else if (type === 30) {
    return "渔船";
  } else {
    return "其他";
  }
};

const getStatus = (val: number | undefined): string => {
  if (val === undefined) return '未知';

  if (val === 8 || val === 0) {
    return "航行";
  } else if (val === 5 || val === 1) {
    return "停泊";
  } else {
    return "未知";
  }
};

const closeShipInfo = () => {
  shipInfoShow.value = false;
  onShip.value = null;
};

const showShipInfo = (ship: ShipInfo) => {
  onShip.value = ship;
  shipInfoShow.value = true;
};

const changePWD = () => {
  isDialog.value = true;
};

const close = () => {
  isDialog.value = false;
  formPwd.oldPassword = '';
  formPwd.newPassword = '';
  formPwd.confirmPassword = '';
};

const submit = () => {
  // 密码修改提交逻辑
};

const handleClose = () => {
  isDialogSHow.value = false;
};

const submitForm = () => {
  // 预警处理提交逻辑
};

const logout = () => {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout().then(() => {
      router.push(`/login`);
    });
  }).catch(() => {});
};

// 激活指定的tab
const activateTab = (tabName: string) => {
  // 如果当前已经是激活状态，则隐藏侧边栏
  if (PopupType.value === tabName && isPopupRight.value) {
    isPopupRight.value = false;
    return;
  }

  // 设置要显示的组件类型


  // 如果是realTime，打开预警历史记录
  if (tabName === 'realTime') {
    isHistor.value = true;
  }

  // 确保PopupRight组件是显示的
  if (!isPopupRight.value) {
    setTimeout(() => {
      isPopupRight.value = true;
    }, 0);
  }
};

// 处理船舶告警
emitter.on('handle-ship-alarm', (alarm) => {

  // 打开预警处理对话框
  if (alarm) {
    // 显示预警处理对话框
    isDialogSHow.value = true;
    isDialogSHowType.value = 2; // 设置为编辑模式

    // 填充对话框数据
    isDialogItem.shipName = alarm.shipName || '';
    isDialogItem.mmsi = alarm.mmsi || '';
    isDialogItem.warningTime = alarm.createTime ? new Date(alarm.createTime).getTime() / 1000 : 0;
    isDialogItem.eventContent = alarm.eventContent || '';
    isDialogItem.address = alarm.address || '';

    // 默认值
    isDialogItem.excepitionType = '';
    isDialogItem.handlingWay = '';
    isDialogItem.detail = '';
    isDialogItem.treatmentMeasure = '';
    isDialogItem.correctTheSituation = '';
    isDialogItem.punish = false;
    isDialogItem.remark = '';
  }
});

// 防抖计时器
let debounceTimer: ReturnType<typeof setTimeout> | null = null;
let shipSelectionDebounceTimer: ReturnType<typeof setTimeout> | null = null;

/**
 * 防抖函数 - 限制函数调用频率
 * @param fn 需要防抖的函数
 * @param delay 延迟时间(ms)
 * @returns 防抖处理后的函数
 */
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): (...args: Parameters<T>) => void {
  return function(this: any, ...args: Parameters<T>): void {
    if (debounceTimer !== null) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(() => {
      fn.apply(this, args);
      debounceTimer = null;
    }, delay);
  };
}

// 添加事件处理函数
// 处理船舶选中事件（极简优化版本）
function handleShipSelected(shipData) {
  try {
    // 直接设置数据，无任何拷贝或延迟
    selectedShipData.value = shipData;
    showShipDetail.value = true;

  } catch (error) {
    console.error('处理船舶选中事件失败:', error);
  }
}

// 处理船舶取消选中事件
function handleShipUnselected(): void {
  showShipDetail.value = false;
  selectedShipData.value = null;
}

// 处理船舶详情关闭事件
function handleShipDetailClose(): void {
  showShipDetail.value = false;
  selectedShipData.value = null;
  // 通知地图清除选中
  if (window.emitter) {
    window.emitter.emit('clear-ship-selection');
  }
}

// 处理通过MMSI选中船舶的事件
function handleSelectShipByMMSI(mmsi: string): void {
  try {
    // 调用船舶追踪模块的选中功能
    const { selectShipByMMSI } = require('./shipTracking');
    const success = selectShipByMMSI(mmsi);

    if (!success) {
      console.warn(`未找到MMSI为 ${mmsi} 的船舶`);
    }
  } catch (error) {
    console.error('通过MMSI选中船舶失败:', error);
  }
}

// Lifecycle hooks
onMounted(() => {
  // 直接设置初始时间显示
  const now = new Date();
  timeYMD.value = now.toLocaleDateString();
  timeHMS.value = now.toLocaleTimeString();

  // 将emitter挂载到window对象上，供非Vue组件使用
  window.emitter = emitter;

  // 监听工具栏状态变化事件
  emitter.on('toolbar-state-change', handleToolbarStateChange);

  // 添加船舶选中事件监听
  emitter.on('ship-selected', handleShipSelected);
  emitter.on('ship-unselected', handleShipUnselected);

  // 添加通过MMSI选中船舶的事件监听
  emitter.on('select-ship-by-mmsi', handleSelectShipByMMSI);

  // 使用nextTick确保DOM已经渲染
  nextTick(() => {
    initMap();

    // 延迟2秒后启动AIS数据刷新，确保地图和图层已完全初始化
    setTimeout(() => {
      // 先立即加载一次船舶数据
      loadAisDataOptimized().catch(() => {
        // 静默处理错误
      });

      // 然后启动定时刷新
      startAisDataRefresh();

      // 启动船舶告警检查
      startShipAlarmCheck();

      // 启动Blob URL定时清理
      startBlobCleanup();

      // Canvas性能监控已移除 - 避免与OpenLayers冲突

      // 启动性能监控系统
      startPerformanceMonitoring();
    }, 2000);
  });

  // 添加窗口大小调整的防抖处理
  window.addEventListener('resize', handleResize);

  // 添加性能调试面板快捷键（仅开发环境）
  if (isDevelopment) {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl + Shift + P 切换性能调试面板
      if (event.ctrlKey && event.shiftKey && event.key === 'P') {
        event.preventDefault();
        togglePerformancePanel();
      }
    };
    document.addEventListener('keydown', handleKeyDown);

    // 在组件卸载时移除事件监听器
    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeyDown);
    });
  }
});



// 在onBeforeUnmount钩子中添加清理代码
onBeforeUnmount(() => {
  // 清除所有未释放的Blob URLs
  createdBlobUrls.value.forEach(url => {
    try {
      URL.revokeObjectURL(url);
    } catch (e) {
    }
  });
  createdBlobUrls.value = [];
  
  // 清除防抖计时器
  if (debounceTimer !== null) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }

  // 清除船舶选中防抖计时器
  if (shipSelectionDebounceTimer !== null) {
    clearTimeout(shipSelectionDebounceTimer);
    shipSelectionDebounceTimer = null;
  }

  // 销毁测量工具，释放资源
  if (measureUtils) {
    try {
      measureUtils.destroy();
      measureUtils = null;
    } catch (error) {
      console.warn('销毁测量工具失败:', error);
    }
  }

  // 清除测量清理函数
  if (measureCleanupFn) {
    try {
      measureCleanupFn();
    } catch (error) {
      console.warn('执行测量清理函数失败:', error);
    }
    measureCleanupFn = null;
  }

  // 清除所有InfoWindow的事件总线监听
  try {
    emitter.emit('hide-info-window');
  } catch (error) {
  }

  // 清除事件监听
  try {
    emitter.off('handle-ship-alarm');
  } catch (error) {

  }

  // 清除地图上可能存在的矩形工具
  try {
    if (map.value) {
      // 检查并移除所有绘图交互
      clearDrawInteractions();

      // 特别处理可能存在的矩形工具
      if (window.T) {
        // 获取所有矩形绘制工具并移除它们
        const allOverlays = map.value.getOverlays ? map.value.getOverlays() : [];
        if (Array.isArray(allOverlays)) {
          allOverlays.forEach((overlay: any) => {
            // 检查类名或实例类型以确定是否为矩形
            if ((overlay.CLASS_NAME &&
                (overlay.CLASS_NAME.includes('Rectangle') ||
                overlay.CLASS_NAME.includes('RectangleTool'))) ||
                (window.T && (
                  overlay instanceof window.T.Rectangle ||
                  overlay instanceof window.T.RectangleTool))) {
              map.value.removeOverlay(overlay);
            }
          });
        }
      }
    }
  } catch (error) {
  }

  // 清除地图实例和事件监听
  if (map.value) {
    try {
      // 清除地图实例
      map.value.setTarget(undefined);
      map.value = null;
    } catch (error) {
    }
  }

  // 清除AIS数据刷新定时器
  stopAisDataRefresh();

  // 清除定时器重启延迟
  if (timerRestartTimeout.value !== null) {
    clearTimeout(timerRestartTimeout.value);
    timerRestartTimeout.value = null;
  }

  // 重置定时器重启标志
  isTimerRestarting.value = false;

  // 清理错误恢复状态
  aisDataRetryCount.value = 0;
  isRecovering.value = false;
  isLoadingAisData.value = false;

  // 清理所有防抖处理器
  clearAllDebounceHandlers();

  // 清理所有事件监听器
  clearAllEventListeners();

  // 清除船舶告警检查定时器
  stopShipAlarmCheck();

  // 停止Blob URL定时清理
  stopBlobCleanup();

  // 清除window上挂载的emitter
  if (window.emitter) {
    window.emitter = undefined;
  }

  // 移除船舶选中事件监听
  if (window.emitter) {
    window.emitter.off('ship-selected', handleShipSelected);
    window.emitter.off('ship-unselected', handleShipUnselected);
    window.emitter.off('select-ship-by-mmsi', handleSelectShipByMMSI);
    window.emitter.off('toolbar-state-change', handleToolbarStateChange);
  }

  // 清除所有注册的事件监听器
  clearAllEventListeners();

  // 清理瓦片缓存和加载队列
  if (tileCache && typeof tileCache.clear === 'function') {
    tileCache.clear();
  }
  if (tileLoadQueue && typeof tileLoadQueue.clear === 'function') {
    tileLoadQueue.clear();
  }

  // 清理所有防抖处理器
  clearAllDebounceHandlers();

  // 移除窗口大小调整事件监听器
  window.removeEventListener('resize', handleResize);

  // 清理船舶跟踪相关资源
  const { cleanup } = require('./shipTracking');
  cleanup();

  // 停止性能监控
  stopPerformanceMonitoring();
});

// 处理重置地图事件
const handleResetMap = () => {
  if (measureUtils) {
    measureUtils.clearMeasurement();
    if (measureCleanupFn) {
      measureCleanupFn();
      measureCleanupFn = null;
    }
    measureUtils.clearAllTooltips();
  }

  // 验证并重新初始化vectorSource状态
  const currentVectorSource = mapUtils.getVectorSource();
  if (!currentVectorSource) {
    // 重新初始化地图以确保vectorSource正确设置
    setTimeout(() => {
      if (map.value) {
        // 重新设置vectorSource
        const layers = map.value.getLayers().getArray();
        const drawingLayer = layers.find(layer => layer.getClassName && layer.getClassName().includes('drawing'));
        if (drawingLayer) {
          const source = drawingLayer.getSource();
          if (source) {
            mapUtils.setVectorSource(source);
          }
        }
      }
      // 重新初始化船舶跟踪
      if (map.value) {
        initShipTracking(map.value);
      }
      // 重新初始化完成，数据加载由定时器负责
    }, 300);
  } else {
    // vectorSource状态正常，数据加载由定时器负责
  }
};

// 地图状态健康检查函数（静默模式，减少日志输出）
const checkMapHealth = () => {
  if (!map.value) {
    return false;
  }

  const vectorSource = mapUtils.getVectorSource();
  if (!vectorSource) {
    return false;
  }

  // 检查地图图层是否正常
  const layers = map.value.getLayers().getArray();
  if (layers.length === 0) {
    return false;
  }

  // 检查地图视图是否正常
  const view = map.value.getView();
  if (!view) {
    return false;
  }

  return true;
};

/**
 * 尝试地图状态恢复
 */
const attemptMapRecovery = async () => {
  try {
    // 1. 检查并重新初始化vectorSource
    let vectorSource = mapUtils.getVectorSource();
    if (!vectorSource && map.value) {
      const layers = map.value.getLayers().getArray();
      const drawingLayer = layers.find(layer =>
        layer.getClassName && (
          layer.getClassName().includes('drawing') ||
          layer.getClassName().includes('vector')
        )
      );

      if (drawingLayer) {
        const source = drawingLayer.getSource();
        if (source) {
          mapUtils.setVectorSource(source);
        }
      }
    }

    // 2. 重新初始化船舶跟踪
    if (map.value) {
      initShipTracking(map.value);
    }

    // 3. 延迟后重置重试计数，允许重新尝试加载
    setTimeout(() => {
      aisDataRetryCount.value = 0;
      isRecovering.value = false;

      // 显示恢复成功提示
      ElMessage.success('系统已尝试恢复，正在重新加载船舶数据...');

      // 恢复完成，数据加载由定时器负责
    }, 2000);

  } catch (error) {
    ElMessage.error('系统恢复失败，请手动刷新页面');
  }
};

// 处理重新加载船舶数据事件
const handleReloadShipData = () => {

  // 验证vectorSource和地图状态
  const currentVectorSource = mapUtils.getVectorSource();
  if (!currentVectorSource || !map.value) {
    console.warn('vectorSource或地图状态异常，重新初始化');
    // 重新初始化地图组件
    setTimeout(() => {
      if (map.value) {
        // 查找并重新设置vectorSource
        const layers = map.value.getLayers().getArray();
        const drawingLayer = layers.find(layer =>
          layer.getClassName && (
            layer.getClassName().includes('drawing') ||
            layer.getClassName().includes('vector')
          )
        );

        if (drawingLayer) {
          const source = drawingLayer.getSource();
          if (source) {
            mapUtils.setVectorSource(source);
            console.log('vectorSource已重新设置');
          }
        }

        // 重新初始化船舶跟踪
        initShipTracking(map.value);
      }

      // 初始化完成后立即加载一次船舶数据
      setTimeout(() => {
        if (!isLoadingAisData.value) {
          loadAisDataOptimized().catch(() => {
            // 静默处理错误
          });
        }
      }, 500);
    }, 300);
  } else {
    // 状态正常，立即加载船舶数据
    if (!isLoadingAisData.value) {
      loadAisDataOptimized().catch(() => {
        // 静默处理错误
      });
    }
  }
};

// initMap function now only appears once
function initMap() {
  const mapContainer = document.getElementById('map');
  if (!mapContainer) {
    return;
  }

  try {
    // 创建矢量数据源和绘图图层
    const vectorSource = new VectorSource();

    // 创建矢量图层用于绘制
    const drawingLayer = new VectorLayer({
      source: vectorSource,
      style: new Style({
        fill: new Fill({
          color: 'rgba(64, 158, 255, 0.2)'
        }),
        stroke: new Stroke({
          color: '#409EFF',
          width: 2
        }),
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({
            color: '#409EFF'
          })
        })
      })
    });



    // 创建优化的瓦片加载函数（简化版本以确保稳定性）
    const createTileLoadFunction = () => {
      return function(tile, src) {
        const img = tile.getImage();

        // 使用浏览器的fetch API以支持缓存控制
        fetch(src, {
          credentials: 'same-origin',
          cache: 'force-cache' // 强制使用缓存，如有缓存则不发起网络请求
        })
        .then(response => response.blob())
        .then(blob => {
          const blobUrl = createTrackedBlobUrl(blob);
          img.src = blobUrl;

          // 当图片加载完成后释放Blob URL
          img.onload = () => {
            revokeTrackedBlobUrl(blobUrl);
          };

          // 添加错误处理
          img.onerror = () => {
            revokeTrackedBlobUrl(blobUrl);
          };
        })
        .catch(() => {
          // 降级回退到标准加载方式
          img.src = src;
        });
      };
    };



    // Canvas性能监控已移除

    // 性能监控状态
    const performanceStats = ref({
      isMonitoring: false,
      currentMode: PerformanceMode.BALANCED,
      alertCount: 0,
      lastUpdate: 0
    });



    // 天地图API密钥
    const tiandituKey = '9dc4d6ee856d047e1a0caf2eec1c17f8';

    // 标准地图图层 (默认可见)
    const standardLayer = new TileLayer({
      source: new XYZ({
        url: `http://t4.tianditu.com/DataServer?T=vec_w&tk=${tiandituKey}&x={x}&y={y}&l={z}`,
        cacheSize: 512,
        crossOrigin: 'anonymous',
        wrapX: true,
      tileLoadFunction: createTileLoadFunction()
      }),
      className: 'light-layer',
      preload: 4,
      visible: true
    });

    // 卫星图层 (默认隐藏)
    const satelliteLayer = new TileLayer({
      source: new XYZ({
        url: `http://t4.tianditu.com/DataServer?T=img_w&tk=${tiandituKey}&x={x}&y={y}&l={z}`,
        cacheSize: 512,
        crossOrigin: 'anonymous',
        wrapX: true,
      tileLoadFunction: createTileLoadFunction()
      }),
      className: 'satellite-layer',
      preload: 4,
      visible: false
    });

    // 地形图层 (默认隐藏)
    const terrainLayer = new TileLayer({
      source: new XYZ({
        url: `http://t4.tianditu.com/DataServer?T=ter_w&tk=${tiandituKey}&x={x}&y={y}&l={z}`,
        cacheSize: 512,
        crossOrigin: 'anonymous',
        wrapX: true,
        tileLoadFunction: createTileLoadFunction()
      }),
      className: 'terrain-layer',
      preload: 4,
      visible: false
    });

    // 海图图层 (默认隐藏) - 这里使用OpenStreetMap作为示例
    const seaLayer = new TileLayer({
      source: new XYZ({
        url: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
        cacheSize: 512,
        crossOrigin: 'anonymous',
        wrapX: true,
        tileLoadFunction: createTileLoadFunction()
      }),
      className: 'sea-layer',
      preload: 4,
      visible: false
    });

    // 标注图层
    const labelLayer = new TileLayer({
      source: new XYZ({
        url: `http://t4.tianditu.com/DataServer?T=cva_w&tk=${tiandituKey}&x={x}&y={y}&l={z}`,
        cacheSize: 512,
        crossOrigin: 'anonymous',
        wrapX: true,
        tileLoadFunction: createTileLoadFunction()
      }),
      className: 'label-layer',
      preload: 4
    });

    // 创建船舶图层
    const shipLayerSource = new VectorSource({
      features: []
    });
    const shipLayer = new VectorLayer({
      source: shipLayerSource,
      className: 'ship-layer',
      properties: {
        name: 'shipLayer',
        layerType: 'ships'
      },
      style: (feature) => {
        // 船舶样式根据feature属性动态设置
        return new Style({
          image: new CircleStyle({
            radius: 6,
            fill: new Fill({
              color: 'rgba(255, 0, 0, 0.8)'
            }),
            stroke: new Stroke({
              color: '#fff',
              width: 2
            })
          })
        });
      }
    });

    // 创建电子围栏图层
    const fenceLayerSource = new VectorSource({
      features: []
    });
    const fenceLayer = new VectorLayer({
      source: fenceLayerSource,
      className: 'fence-layer',
      properties: {
        name: 'fenceLayer',
        layerType: 'fences'
      },
      style: new Style({
        fill: new Fill({
          color: 'rgba(100, 149, 237, 0.2)'
        }),
        stroke: new Stroke({
          color: '#6495ED',
          width: 2,
          lineDash: [5, 5]
        })
      })
    });

    // 创建港口图层
    const portLayerSource = new VectorSource({
      features: []
    });
    const portLayer = new VectorLayer({
      source: portLayerSource,
      className: 'port-layer',
      properties: {
        name: 'portLayer',
        layerType: 'ports'
      },
      style: new Style({
        image: new CircleStyle({
          radius: 8,
          fill: new Fill({
            color: 'rgba(0, 128, 0, 0.8)'
          }),
          stroke: new Stroke({
            color: '#fff',
            width: 2
          })
        })
      })
    });

    // 创建地图对象
    map.value = new Map({
      target: 'map',
      layers: [
        // 基础地图图层
        standardLayer,
        satelliteLayer,
        terrainLayer,
        seaLayer,
        labelLayer,
        
        // 业务图层
        shipLayer,
        fenceLayer,
        portLayer,
        drawingLayer
      ],
      view: new View({
        projection: 'EPSG:4326',
        center: [121.80, 30.05], // 修改默认中心点坐标
        zoom: 10.5,
        maxZoom: 20,
        minZoom: 1
      }),
      controls: Defaults({
        zoom: false,
        rotate: false
      })
    });

    // 设置地图工具中的vectorSource
    mapUtils.setMap(map.value);
    mapUtils.setVectorSource(vectorSource);

    // 确保地图完全加载后再初始化测量工具
    map.value.once('postrender', () => {

      // 添加测量样式
      addMeasureStyles();
      // 创建测量工具实例
      measureUtils = new MeasureUtils(map.value);
    });

    // 地图事件监听
    if (map.value) {
      // 鼠标移动事件，更新坐标显示（使用防抖优化，避免干扰船舶悬浮提示）
      const coordinateUpdateDebounced = debounce((coordinate: number[]) => {
        mousePointlng.value = coordinate[0].toFixed(6);
        mousePointlat.value = coordinate[1].toFixed(6);
      }, 100);

      map.value.on('pointermove', (e) => {
        // 只更新坐标，不干扰其他悬浮事件
        coordinateUpdateDebounced(e.coordinate);
      });

      // 缩放事件 - 更新缩放级别显示
      map.value.on('moveend', () => {
        zoom.value = map.value.getView().getZoom().toString();
      });

      // 初始化缩放级别显示
      zoom.value = map.value.getView().getZoom().toString();

      // 初始化船舶跟踪
      initShipTracking(map.value);

      // 加载电子围栏数据
      loadElectronicFences();
    }

  } catch (error) {
  }
}

/**
 * 加载电子围栏数据
 * @param showFence 是否显示电子围栏，默认为true
 */
const loadElectronicFences = (showFence = true) => {
  try {
    // 设置一个空的围栏数组，稍后会从API加载真实数据
    // electronicFences.value = [];

    // 更新AIS服务中的围栏数据
    // 确保电子围栏多边形是闭合的
    const fencesForTracking = electronicFences.value.map(fence => {
      // 如果多边形不是闭合的，添加闭合点
      const coords = [...fence.coordinates];
      if (coords.length > 0 && (coords[0][0] !== coords[coords.length-1][0] || coords[0][1] !== coords[coords.length-1][1])) {
        coords.push([coords[0][0], coords[0][1]]);
      }
      return coords;
    });

    setFences(fencesForTracking);
    
    // 设置电子围栏图层可见性
    setTimeout(() => {
      const layers = map.value.getLayers().getArray();
      layers.forEach(layer => {
        if (layer.get('name') === 'fenceLayer' || 
            layer.get('layerType') === 'fences' ||
            (layer.getClassName && layer.getClassName() === 'fence-layer')) {
          layer.setVisible(showFence);
        }
      });
      
      // 尝试在所有VectorLayer中寻找包含围栏要素的图层
      layers.forEach(layer => {
        if (layer instanceof VectorLayer) {
          const source = layer.getSource();
          if (source && typeof source.getFeatures === 'function') {
            const features = source.getFeatures();
            // 检查图层中是否有围栏要素
            const hasFenceFeatures = features.some(feature => 
              feature.get('fenceFeature') === true || 
              feature.get('envData') !== undefined);
            
            if (hasFenceFeatures) {
              layer.setVisible(showFence);
            }
          }
        }
      });
    }, 100); // 稍微延迟以确保图层已加载
  } catch (error) {
  }
};

/**
 * 停止AIS数据刷新
 */
const stopAisDataRefresh = () => {
  if (aisDataRefreshInterval.value !== null) {
    clearInterval(aisDataRefreshInterval.value);
    aisDataRefreshInterval.value = null;
  }
};

// Watch
watch(() => searchVal.value, (newVal, oldVal) => {
  // Implementation of watch for searchVal...
});

// 切换侧边栏展开/收起状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  isPopupRight.value = true; // 确保侧边栏是可见的
};

// 处理组件内部折叠状态变化
const handleCollapsedChange = (val: boolean) => {
  if (isCollapsed.value !== val) {
    isCollapsed.value = val;
  }
};

// 定义电子围栏数据类型
interface ElectronicFence {
  id: number | string;
  name: string;
  coordinates: number[][];
  // 其他围栏属性...
}

// 在data属性部分添加
const electronicFences = ref<ElectronicFence[]>([]);
const aisDataRefreshInterval = ref<number | null>(null);
const aisRefreshRate = ref(7000); // 默认7秒刷新一次
const shipAlarmInterval = ref<number | null>(null); // 船舶告警检查定时器

// AIS数据优化相关状态
const isToolbarExpanded = ref(false); // 工具栏是否展开
const normalRefreshRate = 7000; // 正常刷新间隔（7秒）
const slowRefreshRate = 15000; // 慢速刷新间隔（15秒）
// const maxShipCount = 1000; // 最大船舶数量限制
const lastShipData = ref(new Map<string, any>()); // 上次船舶数据缓存

// 定时器管理状态
const isTimerRestarting = ref(false); // 防止定时器重复重启
const timerRestartTimeout = ref<number | null>(null); // 定时器重启延迟

// 错误恢复和重试机制状态
const aisDataRetryCount = ref(0); // AIS数据加载重试计数
const MAX_RETRY_COUNT = 3; // 最大重试次数
const isRecovering = ref(false); // 是否正在进行错误恢复
const lastSuccessfulLoad = ref(0); // 上次成功加载的时间戳
const isLoadingAisData = ref(false); // 是否正在加载AIS数据，防止并发请求

// 瓦片加载优化相关变量
const tileLoadQueue = new Map(); // 瓦片加载队列
const tileCache = new Map(); // 瓦片缓存
const MAX_TILE_CACHE_SIZE = 100; // 最大缓存瓦片数量
const TILE_CACHE_EXPIRE_TIME = 300000; // 瓦片缓存过期时间（5分钟）
let activeTileLoads = 0; // 当前活跃的瓦片加载数量
const MAX_CONCURRENT_LOADS = 6; // 最大并发加载数量

// 统一防抖处理机制
const debounceHandlersMap = new Map(); // 存储防抖处理器
const DEBOUNCE_DELAYS = {
  SEARCH: 300,           // 搜索防抖延迟
  TOOLBAR_TOGGLE: 150,   // 工具栏切换防抖延迟
  MAP_INTERACTION: 100,  // 地图交互防抖延迟
  SHIP_HOVER: 50,        // 船舶悬浮防抖延迟
  RESIZE: 200,           // 窗口大小调整防抖延迟
  API_CALL: 500          // API调用防抖延迟
};

// 在方法部分添加以下方法
/**
 * 加载船舶AIS数据并显示在地图上（原始版本，保持兼容性）
 */
const loadAisData = async () => {
  try {
    // console.log('加载AIS数据...');
    const shipList = await loadShipAisData();
    // console.log(`加载了 ${shipList.length} 条船舶数据`);
  } catch (error) {
    // console.error('加载AIS数据失败:', error);
  }
};

/**
 * 优化的AIS数据加载函数（增量更新和数量限制）
 * 包含重试机制和错误恢复，防止并发请求
 */
const loadAisDataOptimized = async () => {
  // 防止并发请求
  if (isLoadingAisData.value) {
    return;
  }

  // 设置加载标志
  isLoadingAisData.value = true;

  try {
    // 检查地图健康状态（静默检查）
    if (!checkMapHealth()) {
      return;
    }

    const shipList = await loadShipAisDataOptimized();

    // 加载成功，重置重试计数和更新成功时间
    aisDataRetryCount.value = 0;
    lastSuccessfulLoad.value = Date.now();
    isRecovering.value = false;

  } catch (error) {
    // 触发重试机制（不等待，避免阻塞）
    handleAisDataLoadError(error);
  } finally {
    // 确保加载标志被重置
    isLoadingAisData.value = false;
  }
};

/**
 * 处理AIS数据加载错误，实现重试机制（防止并发）
 */
const handleAisDataLoadError = (error: any) => {
  // 如果正在恢复中，不进行重试
  if (isRecovering.value) {
    return;
  }

  aisDataRetryCount.value++;

  if (aisDataRetryCount.value <= MAX_RETRY_COUNT) {
    const retryDelay = Math.min(2000 * aisDataRetryCount.value, 10000); // 最大延迟10秒

    // 显示用户友好的重试提示
    if (aisDataRetryCount.value === 1) {
      ElMessage.warning('船舶数据加载失败，正在重试...');
    }

    // 延迟重试，使用非递归方式
    setTimeout(() => {
      // 检查是否仍需要重试（避免在延迟期间状态发生变化）
      if (!isLoadingAisData.value && !isRecovering.value) {
        loadAisDataOptimized().catch(() => {
          // 静默处理重试错误
        });
      }
    }, retryDelay);

  } else {
    // 超过最大重试次数，进入恢复模式
    isRecovering.value = true;

    // 显示错误提示和建议
    ElMessage.error({
      message: '船舶数据加载失败，请检查网络连接或刷新页面',
      duration: 5000,
      showClose: true
    });

    // 尝试地图状态恢复（异步执行，不等待）
    attemptMapRecovery().catch(() => {
      // 静默处理恢复错误
    });
  }
};

/**
 * 开始定时刷新AIS数据（智能调用策略，防止重复启动）
 */
const startAisDataRefresh = () => {
  // 先清除可能存在的定时器
  stopAisDataRefresh();

  // 如果正在重启定时器，不执行
  if (isTimerRestarting.value) {
    return;
  }

  try {
    // 根据工具栏状态设置刷新间隔
    const currentRefreshRate = isToolbarExpanded.value ? slowRefreshRate : normalRefreshRate;
    aisRefreshRate.value = currentRefreshRate;

    // 设置定时刷新（不立即执行，避免重复请求）
    aisDataRefreshInterval.value = window.setInterval(() => {
      // 只在没有正在加载且没有正在恢复时执行
      if (!isLoadingAisData.value && !isRecovering.value) {
        loadAisDataOptimized().catch(() => {
          // 静默处理错误
        });
      }
    }, aisRefreshRate.value);

  } catch (error) {
    // 静默处理错误
  }
};

/**
 * 检查船舶告警状态
 */
const checkShipAlarm = async () => {
  try {
    const res = await getShipAlarm();
    if (res.code === 200 && res.data && res.data.length > 0) {
      // 有船舶告警信息，通过ShipAlarmNotification组件显示
      if (shipAlarmRef.value) {
        shipAlarmRef.value.showAlarms(res.data);
      } else {
        // 如果组件未挂载，通过事件总线发送数据
        emitter.emit('ship-alarm-triggered', res.data);
      }
    }
  } catch (error) {
  }
};

// 开始船舶告警检查
const startShipAlarmCheck = () => {
  // 先清除可能存在的定时器
  stopShipAlarmCheck();

  // 先立即检查一次
  checkShipAlarm().catch(err => {
  });

  // 设置定时检查 (每3分钟检查一次)
  shipAlarmInterval.value = window.setInterval(() => {
    checkShipAlarm().catch(err => {
    });
  }, 3 * 60 * 1000); // 3分钟

};

// 停止船舶告警检查
const stopShipAlarmCheck = () => {
  if (shipAlarmInterval.value !== null) {
    clearInterval(shipAlarmInterval.value);
    shipAlarmInterval.value = null;
  }
};

/**
 * 启动性能监控系统
 */
const startPerformanceMonitoring = () => {
  try {
    // 启动性能监控
    performanceMonitor.start();

    // 更新性能监控状态
    performanceStats.value.isMonitoring = true;
    performanceStats.value.currentMode = performanceConfig.getCurrentMode();
    performanceStats.value.lastUpdate = Date.now();

    // 订阅性能警告事件
    performanceMonitor.subscribe('alert', (alert) => {
      performanceStats.value.alertCount++;
      console.warn('性能警告:', alert);

      // 在开发环境下显示更详细的警告信息
      if (import.meta.env.VITE_APP_ENV === 'development') {
        ElMessage.warning(`性能警告: ${alert.message}`);
      }
    });

    // 订阅性能指标事件
    performanceMonitor.subscribe('metric', (metric) => {
      performanceStats.value.lastUpdate = Date.now();
      // Canvas性能指标记录已移除
    });

    console.log('性能监控系统已启动');
  } catch (error) {
  }
};

/**
 * 停止性能监控系统
 */
const stopPerformanceMonitoring = () => {
  try {
    performanceMonitor.stop();
    performanceStats.value.isMonitoring = false;
  } catch (error) {
  }
};

/**
 * 切换性能模式
 */
const switchPerformanceMode = (mode: PerformanceMode) => {
  try {
    performanceConfig.setMode(mode);
    performanceStats.value.currentMode = mode;

    // 重启性能监控以应用新配置
    if (performanceStats.value.isMonitoring) {
      performanceMonitor.stop();
      performanceMonitor.start();
    }

    ElMessage.success(`性能模式已切换至: ${mode}`);
  } catch (error) {
    ElMessage.error('切换性能模式失败');
  }
};

/**
 * 导出性能数据
 */
const exportPerformanceData = () => {
  try {
    const data = {
      metrics: performanceMonitor.getMetrics(),
      alerts: performanceMonitor.getAlerts(),
      config: {
        monitor: performanceConfig.getMonitorConfig(),
        optimization: performanceConfig.getOptimizationConfig()
      },
      stats: performanceStats.value,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `windpower-performance-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);

    ElMessage.success('性能数据已导出');
  } catch (error) {
    ElMessage.error('导出性能数据失败');
  }
};

/**
 * 处理性能调试面板的数据导出
 */
const handlePerformanceDataExport = (data: any) => {
  console.log('性能数据导出:', data);
  ElMessage.success('性能数据已通过调试面板导出');
};

/**
 * 切换性能调试面板显示状态
 */
const togglePerformancePanel = () => {
  if (isDevelopment) {
    showPerformancePanel.value = !showPerformancePanel.value;
  }
};

// Canvas性能监控函数已移除

/**
 * 监听工具栏状态变化，调整AIS数据刷新频率
 * @param expanded 工具栏是否展开
 */
const handleToolbarStateChange = (expanded: boolean) => {
  // 防止并发重启
  if (isTimerRestarting.value) {
    console.log('定时器正在重启中，忽略此次状态变化');
    return;
  }

  const wasExpanded = isToolbarExpanded.value;
  isToolbarExpanded.value = expanded;

  // 如果状态发生变化，安全地重新启动AIS数据刷新
  if (wasExpanded !== expanded) {
    // 设置重启标志
    isTimerRestarting.value = true;

    // 清除可能存在的重启延迟
    if (timerRestartTimeout.value !== null) {
      clearTimeout(timerRestartTimeout.value);
      timerRestartTimeout.value = null;
    }

    // 安全停止现有定时器
    try {
      stopAisDataRefresh();
    } catch (error) {
      // 静默处理错误
    }

    // 延迟重启，避免频繁切换导致的问题
    timerRestartTimeout.value = window.setTimeout(() => {
      try {
        const newRefreshRate = expanded ? slowRefreshRate : normalRefreshRate;
        aisRefreshRate.value = newRefreshRate;

        // 使用startAisDataRefresh方法重新启动，确保逻辑一致
        startAisDataRefresh();
      } catch (error) {
        // 静默处理错误
      } finally {
        // 重置重启标志
        isTimerRestarting.value = false;
        timerRestartTimeout.value = null;
      }
    }, 150); // 150ms延迟，避免频繁切换
  }
};

// 添加clearDrawInteractions函数定义
// 清除绘图交互
function clearDrawInteractions() {
  if (activeDrawInteraction) {
    map.value.removeInteraction(activeDrawInteraction);
    activeDrawInteraction = null;
  }

  if (activeSelectInteraction) {
    map.value.removeInteraction(activeSelectInteraction);
    activeSelectInteraction = null;
  }

  if (measureTooltipElement && measureTooltipElement.parentNode) {
    measureTooltipElement.parentNode.removeChild(measureTooltipElement);
    measureTooltipElement = null;
  }

  if (measureTooltip) {
    map.value.removeOverlay(measureTooltip);
    measureTooltip = null;
  }
}

// 工具栏功能（使用防抖优化）
// 注意：toggleSearchBox 函数已在上面定义，这里不需要重复定义

// 在setup区域声明一个变量用于跟踪创建的Blob URLs
const createdBlobUrls = ref<string[]>([]);

// Blob URL生命周期管理
const blobUrlLifecycle = new Map<string, { url: string, createdAt: number, isActive: boolean }>();
const blobCleanupInterval = ref<number | null>(null);
const BLOB_CLEANUP_INTERVAL = 30000; // 30秒清理一次
const BLOB_MAX_AGE = 300000; // 5分钟过期时间

/**
 * 创建并跟踪Blob URL
 * @param blob Blob对象
 * @returns Blob URL
 */
const createTrackedBlobUrl = (blob: Blob): string => {
  const blobUrl = URL.createObjectURL(blob);
  const now = Date.now();

  // 添加到跟踪列表
  createdBlobUrls.value.push(blobUrl);

  // 添加到生命周期管理
  blobUrlLifecycle.set(blobUrl, {
    url: blobUrl,
    createdAt: now,
    isActive: true
  });

  return blobUrl;
};

/**
 * 释放并移除Blob URL
 * @param blobUrl 要释放的Blob URL
 */
const revokeTrackedBlobUrl = (blobUrl: string): void => {
  try {
    URL.revokeObjectURL(blobUrl);

    // 从跟踪列表中移除
    const index = createdBlobUrls.value.indexOf(blobUrl);
    if (index > -1) {
      createdBlobUrls.value.splice(index, 1);
    }

    // 从生命周期管理中移除
    blobUrlLifecycle.delete(blobUrl);
  } catch (e) {
  }
};

/**
 * 定时清理过期的Blob URLs
 */
const cleanupExpiredBlobUrls = (): void => {
  const now = Date.now();
  const expiredUrls: string[] = [];

  // 安全检查：确保 blobUrlLifecycle 是 Map 实例
  if (blobUrlLifecycle && typeof blobUrlLifecycle.forEach === 'function') {
    blobUrlLifecycle.forEach((info, url) => {
      if (now - info.createdAt > BLOB_MAX_AGE) {
        expiredUrls.push(url);
      }
    });
  }

  expiredUrls.forEach(url => {
    revokeTrackedBlobUrl(url);
  });

  if (expiredUrls.length > 0) {
  }
};

/**
 * 启动Blob URL定时清理
 */
const startBlobCleanup = (): void => {
  if (blobCleanupInterval.value !== null) {
    clearInterval(blobCleanupInterval.value);
  }

  blobCleanupInterval.value = window.setInterval(() => {
    cleanupExpiredBlobUrls();
  }, BLOB_CLEANUP_INTERVAL);
};

/**
 * 停止Blob URL定时清理
 */
const stopBlobCleanup = (): void => {
  if (blobCleanupInterval.value !== null) {
    clearInterval(blobCleanupInterval.value);
    blobCleanupInterval.value = null;
  }
};

/**
 * 确保防抖处理器Map正确初始化
 */
const ensureDebounceMapInitialized = (): void => {
  if (!debounceHandlersMap || typeof debounceHandlersMap.has !== 'function') {
    console.warn('debounceHandlersMap未正确初始化，重新创建');
    // 重新创建Map实例
    const newMap = new Map();
    // 如果原来的Map有数据，尝试迁移
    if (debounceHandlersMap && typeof debounceHandlersMap.forEach === 'function') {
      try {
        debounceHandlersMap.forEach((value, key) => {
          newMap.set(key, value);
        });
      } catch (error) {
        console.warn('迁移防抖处理器失败:', error);
      }
    }
    debounceHandlersMap = newMap;
  }
};

/**
 * 创建或获取防抖处理器（增强版）
 * @param key 防抖处理器的唯一标识
 * @param func 要防抖的函数
 * @param delay 防抖延迟时间
 * @param immediate 是否立即执行
 * @returns 防抖处理器
 */
const createDebounceHandler = (key: string, func: Function, delay: number, immediate: boolean = false) => {
  // 参数验证
  if (!key || typeof key !== 'string') {
    console.error('防抖处理器key必须是非空字符串');
    return func;
  }

  if (typeof func !== 'function') {
    console.error('防抖处理器func必须是函数');
    return func;
  }

  if (typeof delay !== 'number' || delay < 0) {
    console.error('防抖延迟必须是非负数');
    delay = 300; // 使用默认延迟
  }

  // 确保Map正确初始化
  ensureDebounceMapInitialized();

  try {
    // 检查是否已存在相同key的处理器
    if (debounceHandlersMap.has(key)) {
      const existingHandler = debounceHandlersMap.get(key);
      if (existingHandler && typeof existingHandler === 'function') {
        return existingHandler;
      } else {
        // 如果存在但无效，移除并重新创建
        console.warn(`防抖处理器 ${key} 状态异常，重新创建`);
        debounceHandlersMap.delete(key);
      }
    }

    // 创建新的防抖处理器
    const debouncedFunc = debounce(func, delay, immediate);

    // 添加错误处理包装
    const wrappedFunc = (...args: any[]) => {
      try {
        return debouncedFunc(...args);
      } catch (error) {
        console.error(`防抖处理器 ${key} 执行失败:`, error);
      }
    };

    debounceHandlersMap.set(key, wrappedFunc);
    return wrappedFunc;

  } catch (error) {
    console.error(`创建防抖处理器 ${key} 失败:`, error);
    return func; // 降级处理，返回原函数
  }
};

/**
 * 清理防抖处理器（增强版）
 * @param key 防抖处理器的唯一标识
 */
const clearDebounceHandler = (key: string): void => {
  if (!key || typeof key !== 'string') {
    console.warn('清理防抖处理器时key无效');
    return;
  }

  ensureDebounceMapInitialized();

  try {
    if (debounceHandlersMap.has(key)) {
      const handler = debounceHandlersMap.get(key);

      // 如果防抖函数有cancel方法，调用它来取消待执行的函数
      if (handler && typeof handler.cancel === 'function') {
        handler.cancel();
      }

      debounceHandlersMap.delete(key);
      console.log(`防抖处理器 ${key} 已清理`);
    }
  } catch (error) {
    console.error(`清理防抖处理器 ${key} 失败:`, error);
  }
};

/**
 * 清理所有防抖处理器（增强版）
 */
const clearAllDebounceHandlers = (): void => {
  ensureDebounceMapInitialized();

  try {
    // 先取消所有待执行的防抖函数
    debounceHandlersMap.forEach((handler, key) => {
      if (handler && typeof handler.cancel === 'function') {
        try {
          handler.cancel();
        } catch (error) {
          console.warn(`取消防抖处理器 ${key} 失败:`, error);
        }
      }
    });

    // 清空Map
    debounceHandlersMap.clear();
    console.log('所有防抖处理器已清理');

  } catch (error) {
    console.error('清理所有防抖处理器失败:', error);
    // 降级处理：重新创建Map
    debounceHandlersMap = new Map();
  }
};

/**
 * 获取防抖处理器统计信息
 */
const getDebounceHandlerStats = (): { count: number; keys: string[] } => {
  ensureDebounceMapInitialized();

  try {
    const keys = Array.from(debounceHandlersMap.keys());
    return {
      count: keys.length,
      keys: keys
    };
  } catch (error) {
    console.error('获取防抖处理器统计失败:', error);
    return { count: 0, keys: [] };
  }
};

/**
 * 窗口大小调整处理函数
 */
const handleResize = () => {
  const debouncedResize = createDebounceHandler('windowResize', () => {
    if (map.value) {
      map.value.updateSize();
    }
  }, DEBOUNCE_DELAYS.RESIZE);
  debouncedResize();
};

// 事件监听器管理
const eventListeners = new Map<string, { target: any, event: string, handler: Function }>();

/**
 * 注册事件监听器（增强版）
 * @param id 监听器唯一标识
 * @param target 目标对象
 * @param event 事件名称
 * @param handler 事件处理函数
 * @param options 事件选项
 */
const registerEventListener = (id: string, target: any, event: string, handler: Function, options?: any): void => {
  // 参数验证
  if (!id || typeof id !== 'string') {
    console.error('事件监听器ID必须是非空字符串');
    return;
  }

  if (!target) {
    console.error('事件监听器目标对象不能为空');
    return;
  }

  if (!event || typeof event !== 'string') {
    console.error('事件名称必须是非空字符串');
    return;
  }

  if (typeof handler !== 'function') {
    console.error('事件处理函数必须是函数');
    return;
  }

  try {
    // 如果已存在同ID的监听器，先移除
    if (eventListeners.has(id)) {
      console.warn(`事件监听器 ${id} 已存在，将先移除旧的监听器`);
      removeEventListener(id);
    }

    // 创建错误处理包装的处理函数
    const wrappedHandler = (...args: any[]) => {
      try {
        return handler(...args);
      } catch (error) {
        console.error(`事件监听器 ${id} 执行失败:`, error);
      }
    };

    // 添加事件监听器
    let success = false;
    if (target && typeof target.addEventListener === 'function') {
      target.addEventListener(event, wrappedHandler, options);
      success = true;
    } else if (target && typeof target.on === 'function') {
      target.on(event, wrappedHandler);
      success = true;
    } else {
      console.error(`目标对象不支持事件监听: ${id}`);
      return;
    }

    if (success) {
      // 记录监听器信息
      eventListeners.set(id, {
        target,
        event,
        handler: wrappedHandler,
        originalHandler: handler,
        options,
        registeredAt: Date.now()
      });
      console.log(`事件监听器 ${id} 注册成功`);
    }

  } catch (error) {
    console.error(`注册事件监听器 ${id} 失败:`, error);
  }
};

/**
 * 移除事件监听器（增强版）
 * @param id 监听器唯一标识
 */
const removeEventListener = (id: string): void => {
  if (!id || typeof id !== 'string') {
    console.warn('移除事件监听器时ID无效');
    return;
  }

  try {
    const listener = eventListeners.get(id);
    if (listener) {
      const { target, event, handler, options } = listener;

      // 移除事件监听器
      let success = false;
      if (target && typeof target.removeEventListener === 'function') {
        target.removeEventListener(event, handler, options);
        success = true;
      } else if (target && typeof target.off === 'function') {
        target.off(event, handler);
        success = true;
      } else if (target && typeof target.un === 'function') {
        target.un(event, handler);
        success = true;
      } else {
        console.warn(`目标对象不支持移除事件监听: ${id}`);
      }

      // 从记录中删除
      eventListeners.delete(id);

      if (success) {
        console.log(`事件监听器 ${id} 移除成功`);
      }
    } else {
      console.warn(`事件监听器 ${id} 不存在`);
    }
  } catch (error) {
    console.error(`移除事件监听器 ${id} 失败:`, error);
  }
};

/**
 * 清除所有事件监听器（增强版）
 */
const clearAllEventListeners = (): void => {
  try {
    const listenerIds = Array.from(eventListeners.keys());
    let successCount = 0;
    let errorCount = 0;

    listenerIds.forEach(id => {
      try {
        removeEventListener(id);
        successCount++;
      } catch (error) {
        console.error(`清除事件监听器 ${id} 失败:`, error);
        errorCount++;
      }
    });

    console.log(`事件监听器清理完成: 成功 ${successCount} 个, 失败 ${errorCount} 个`);

    // 确保Map被清空
    if (eventListeners.size > 0) {
      console.warn('仍有事件监听器未清理，强制清空');
      eventListeners.clear();
    }

  } catch (error) {
    console.error('清除所有事件监听器失败:', error);
    // 降级处理：重新创建Map
    eventListeners.clear();
  }
};

/**
 * 获取事件监听器统计信息
 */
const getEventListenerStats = (): { count: number; listeners: Array<{ id: string; event: string; registeredAt: number }> } => {
  try {
    const listeners: Array<{ id: string; event: string; registeredAt: number }> = [];

    eventListeners.forEach((listener, id) => {
      listeners.push({
        id,
        event: listener.event,
        registeredAt: listener.registeredAt || 0
      });
    });

    return {
      count: listeners.length,
      listeners: listeners
    };
  } catch (error) {
    console.error('获取事件监听器统计失败:', error);
    return { count: 0, listeners: [] };
  }
};

// 在index.vue中添加监听right-content组件发出的resetLayers事件
// 定义handleResetLayers方法
const handleResetLayers = () => {
  // 确保电子围栏可见性状态为true
  isFenceVisible.value = true;
  // 重新加载电子围栏并确保显示
  loadElectronicFences(true);
};

</script>

<style scoped lang="less">
.head-txt {
  font-size: 20px;
  color: #fff;
  margin-left: 10px;
  white-space: nowrap;
}

.head-right {
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
  justify-content: flex-end;

  span:nth-of-type(1) {
    height: 100%;
  }
}

.head-right-flex {
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  span:nth-of-type(1) {
    height: 100%;
  }
}

.head-right-txt {
  line-height: 100%;
  font-size: 16px;
  margin-right: 10px;
  color: #c4c4c4;
  cursor: pointer;
}

.head-right-txt1 {
  line-height: 100%;
  font-size: 16px;
  margin-right: 15px;
  color: #fff;
  cursor: pointer;
}

.head-right-txt3 {
  line-height: 100%;
  font-size: 12px;
  margin-right: 15px;
  color: #fff;
  margin-bottom: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.head-right-line {
  width: 1px;
  height: 100%;
  background: #c4c4c4;
  margin: 0 15px;
}

.hoverline:hover {
  .hoverlinechild {
    text-decoration: underline;
  }
}


.top-nav-buttons {
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: rgba(48, 65, 86, 0.85); /* 与侧边栏保持一致的背景色 */
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px); /* 添加模糊效果 */
}

.top-nav-buttons .el-button-group {
  display: flex;
}

.top-nav-buttons .el-button--primary {
  background-color: rgba(31, 45, 61, 0.9);
  border-color: rgba(31, 45, 61, 0.9);
  color: #bfcbd9;
  transition: all 0.3s;
  padding: 8px 15px;
}

.top-nav-buttons .el-button--primary:hover,
.top-nav-buttons .el-button--primary:focus {
  background-color: #263445;
  border-color: #263445;
  color: #f4f4f5;
}

.top-nav-buttons .el-button--primary.active {
  background-color: rgba(64, 158, 255, 0.2);
  border-color: #409eff;
  color: #f4f4f5;
}
</style>

<style lang="less">
.elPopover.el-popper {
  color: #fff;
  background: #3c4167;
  margin-top: 0;
}

// 移除 .map 的 margin-right 和 transition
.map {
  width: 100%; // 宽度占满父容器
  height: 100%; // 高度占满父容器
  :deep(.dark-layer) {
    filter: blur(0px) grayscale(0%) sepia(100%) invert(95%) saturate(0%);
  }
  :deep(.label-layer) {
    filter: invert(100%) brightness(250%);
  }
  :deep(.light-layer) {
    filter: brightness(100%) saturate(100%);
  }
}
</style>

<style scoped src="@/assets/datePark.css"></style>
<style scoped src="@/assets/index.css"></style>

<style scoped>
.popup-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  z-index: 1000;
  pointer-events: none;
  transition: all 0.3s ease-in-out;
}

.popup-container > * {
  pointer-events: auto;
}

.popup-container.collapsed :deep(.content) {
  transform: translateX(100%);
  opacity: 0;
  visibility: hidden;
}

.collapse-button {
  position: absolute;
  left: -90px; /* 增加宽度以容纳文本 */
  top: 50%;
  margin-top: 20px;
  transform: translateY(-50%);
  width: 90px; /* 增加宽度 */
  height: 40px;
  background-color: rgba(187, 226, 236, 0.3);
  border-radius: 4px 0 0 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 1010;
  color: #646464;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  padding: 0 10px;
  border-left: 3px solid #409eff;
}

.collapse-button:hover {
  background-color: rgba(64, 158, 255, 0.3);
  color: #fff;
  width: 100px;
}

.collapse-button i {
  font-size: 18px;
  transition: transform 0.3s;
  margin-right: 5px;
}

.collapse-button:hover i {
  transform: scale(1.1);
}

/* 测量工具提示样式 */
.tooltip {
  position: relative;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  opacity: 0.7;
  position: relative;
  pointer-events: none;
}

.tooltip-measure {
  opacity: 1;
  font-weight: bold;
}

.tooltip-measure:before {
  border-top: 6px solid rgba(0, 0, 0, 0.7);
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  content: "";
  position: absolute;
  bottom: -6px;
  margin-left: -6px;
  left: 50%;
}

/* 电子围栏样式 */
.fence-control {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.fence-button {
  width: 40px;
  height: 40px;
  background-color: rgba(64, 158, 255, 0.8);
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  cursor: pointer;
  font-size: 20px;
  transition: all 0.3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.fence-button:hover {
  background-color: rgba(64, 158, 255, 1);
  transform: scale(1.05);
}

.fence-panel {
  position: absolute;
  top: 70px;
  right: 15px;
  width: 300px;
  background-color: white;
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.fence-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.fence-panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.fence-panel-content {
  margin-bottom: 15px;
}

.fence-panel-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 绘制样式 */
.ol-draw-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  user-select: none;
  pointer-events: none;
  z-index: 1100;
}

/* 闪烁动画 - 用于新创建的围栏 */
@keyframes fence-blink {
  0% { opacity: 0.4; }
  50% { opacity: 0.9; }
  100% { opacity: 0.4; }
}

.new-fence {
  animation: fence-blink 2s infinite;
}

/* 悬浮工具栏样式 */
.floating-toolbar {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 1001;
  display: flex;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: auto;
}

.toolbar-collapsed .toolbar-item {
  display: none;
}

.toolbar-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
  white-space: nowrap;
}

.toolbar-item:not(:last-child) {
  border-right: 1px solid #ebeef5;
}

.toolbar-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.toolbar-item .el-icon {
  font-size: 18px;
  margin-right: 5px;
}

.toolbar-collapse {
  padding: 10px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
  border-left: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.toolbar-collapse:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.toolbar-collapse .el-icon {
  font-size: 20px;
}


/* 搜索框样式 - 添加 right 过渡 */
.search-box {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 1002;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px 15px;
  width: 400px;
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-input {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.search-input .el-icon {
  color: #909399;
  font-size: 16px;
}

.search-input input {
  flex: 1;
  border: none;
  outline: none;
  padding: 5px 0;
  color: #606266;
  background: transparent;
  font-size: 14px;
}

.search-actions {
  display: flex;
  gap: 12px;
}

.search-actions .el-button {
  padding: 8px;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.search-actions .el-icon {
  margin: 0;
  font-size: 20px;
  color: #606266;
  transition: all 0.3s;
}

.search-actions .el-button:hover .el-icon {
  color: #409eff;
  transform: scale(1.2);
}

/* rightContent 容器样式 - 添加 right 过渡 */
:deep(.right-content-container) { /* 假设 rightContent 组件有根元素 */
  position: absolute;
  top: 70px; /* 或其他合适的位置 */
  right: 15px;
  z-index: 1000;
}

/* 主要布局样式 */
.layout {
  position: relative;
  width: 100%;
  height: 100vh; /* 确保占满视口高度 */
  overflow: hidden;
}

.main-content {
  width: 100%; /* 宽度占满100% */
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* 其他可能需要调整的绝对定位元素 */
.last-message-box {
    position: fixed;
    bottom: 20px;
    right: 320px; /* 侧边栏宽度 + 间距 */
    z-index: 1000;
    padding: 12px;
    font-size: 12px;
}

.infoWindow {
  position: absolute;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 4px;
  z-index: 1001; /* 比地图高 */
  pointer-events: none; /* 默认不捕获事件 */
  font-size: 12px;
  min-width: 150px;
}

.infoWindow_title {
  font-weight: bold;
  margin-bottom: 5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
  padding-bottom: 5px;
}

.infoWindow_text {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
}

/* 确保其他弹窗如 el-dialog, historical, dispose-log 等的样式和定位不受影响 */

/* 地图类型样式 */
:deep(.light-layer) {
  filter: brightness(100%) saturate(100%);
}

:deep(.satellite-layer) {
  filter: brightness(105%) contrast(110%);
}

:deep(.terrain-layer) {
  filter: brightness(100%) saturate(95%);
}

:deep(.sea-layer) {
  filter: brightness(100%) saturate(105%) hue-rotate(190deg);
}

:deep(.label-layer) {
  /* 标注层适用于所有地图类型 */
  filter: invert(0%);
  opacity: 0.8;
}

:deep(.ship-layer) {
  /* 船舶图层样式 */
  z-index: 10;
}

:deep(.fence-layer) {
  /* 电子围栏图层样式 */
  z-index: 5;
}

:deep(.port-layer) {
  /* 港口图层样式 */
  z-index: 7;
}

/* 性能调试面板切换按钮样式 */
.performance-debug-toggle {
  position: fixed;
  top: 60px;
  right: 10px;
  z-index: 9999;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.performance-debug-toggle:hover {
  opacity: 1;
}

</style>
