import { getShipAis } from '@/api/wan/shipAis';
import OLMap from 'ol/Map';
import VectorSource from 'ol/source/Vector';
import VectorLayer from 'ol/layer/Vector';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import Polygon from 'ol/geom/Polygon';
import { Style, Icon, Fill, Stroke, Circle as CircleStyle } from 'ol/style';
import { getLength } from 'ol/sphere';
import LineString from 'ol/geom/LineString';
import Overlay from 'ol/Overlay';
import mapUtils from '@/utils/mapUtils';

// 颜色定义
const COLORS = {
  red: '#d81e06',
  yellow: '#f4ea2a',
  green: '#1afa29'
};

// 使用内联SVG直接定义超锐角三角形图标 - 增强方向性
const SVG_ICONS = {
  // 红色超锐角三角形
  red: `<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
    <polygon points="50,30 65,99 35,99" fill="${COLORS.red}"/>
  </svg>`,
  // 黄色超锐角三角形
  yellow: `<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
    <polygon points="50,30 65,99 35,99" fill="${COLORS.yellow}"/>
  </svg>`,
  // 绿色超锐角三角形
  green: `<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
    <polygon points="50,30 65,99 35,99" fill="${COLORS.green}"/>
  </svg>`,
  police : `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="720" height="720" viewBox="0 0 720 720">
  <defs>
    <style>
      .cls-1 {
        clip-path: url(#clip-画板_1);
      }

      .cls-2 {
        fill: #ff6868;
      }

      .cls-3 {
        fill: #d12828;
      }

      .cls-4 {
        fill: rgba(255,255,255,0);
      }
    </style>
    <clipPath id="clip-画板_1">
      <rect width="720" height="720"/>
    </clipPath>
  </defs>
  <g id="画板_1" data-name="画板 – 1" class="cls-1">
    <rect class="cls-4" width="720" height="720"/>
    <g id="组_159" data-name="组 159" transform="translate(-91.067 4.924)">
      <path id="路径_17" data-name="路径 17" class="cls-2" d="M458.125,9.387Q579.739,148.128,567.779,278.978c-2.953,31.7-5.939,66.344-8.744,101.55l-1.3,16.291c-8.252,105.6-14.863,213.14-15.257,259.765H511.968V688h-106.8V656.583H373.719q-11.713-268.845-21.655-351.635c-1.821-14.322-2.92-21.163-3.987-26.183Q338.75,153.632,458.109,9.387Z" transform="translate(0 0)"/>
      <path id="路径_18" data-name="路径 18" class="cls-3" d="M558.613,454.892q-4.971-107.342-42.064-134.361c-21.491-23.345-63.21-26.774-91.28,0-34.566,31.138-41.391,99.418-41.391,134.361Q412.153,417,436.556,417h72.512q32.753,13.608,49.545,37.9Z" transform="translate(-11.931 -95.626)"/>
      <path id="路径_19" data-name="路径 19" class="cls-3" d="M414.525,789.87m26.249,0H519.52q26.249,0,26.249,26.249v52.2q0,26.249-26.249,26.249H440.774q-26.249,0-26.249-26.249v-52.2Q414.525,789.87,440.774,789.87Z" transform="translate(-21.957 -255.31)"/>
    </g>
  </g>
</svg>`
};

// 将SVG转换为data URL
const ICON_PATHS = {
  red: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(SVG_ICONS.red),
  yellow: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(SVG_ICONS.yellow),
  green: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(SVG_ICONS.green),
  police: 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(SVG_ICONS.police)
};

// 添加电子围栏类型定义
interface ElectronicFence {
  coordinates: number[][];
  name?: string;
  type?: string;
  remark?: string;
  id?: number | string;
}

// 修改fencePolygons的类型定义
let fencePolygons: ElectronicFence[] = [];

// 船舶要素图层和地图实例
let shipVectorLayer: VectorLayer | null = null;
let mapInstance: OLMap | null = null;

// 存储船舶弹窗对象
const shipPopupOverlays: Overlay[] = [];
// 存储船舶要素
const shipFeatures: Feature[] = [];
// 悬停提示弹窗
let hoverTooltip: Overlay | null = null;
let hoverTooltipElement: HTMLElement | null = null;

// 保存缩放等级用于动态调整图标大小
let currentZoom: number = 10;

// 船舶图例模式状态
let isShipLegendModeEnabled: boolean = false;

// 添加围栏悬浮提示元素和覆盖物
let fenceTooltipElement: HTMLElement | null = null;
let fenceTooltipOverlay: Overlay | null = null;

// 添加全局emitter声明，确保TypeScript不报错
declare global {
  interface Window {
    emitter: any;
    shipTracking?: {
      enableShipLegendMode: () => void;
      disableShipLegendMode: () => void;
    };
  }
}

// 添加当前选中的船舶
let selectedShip: Feature | null = null;
let selectionLayer: VectorLayer | null = null;

// 添加执法船列表缓存
let gaShipListCache: string[] = [];
let hasLoadedGaShipList: boolean = false;

// 添加常量定义地图层级阈值
const ZOOM_LEVELS = {
  DOTS: 10.49, // 9.5及以下显示圆点
  content: 11.5, 
  NORMAL: 12 // 9.5-12显示正常图标，12以上缩放
};

// 船舶图例颜色映射
const SHIP_LEGEND_COLORS = {
  1: '#FFA500',  // 橙色客船
  2: '#FFFF00',  // 黄色货船
  3: '#0000FF',  // 蓝色渔船
  4: '#8B4513',  // 深棕色作业船
  5: '#800080',  // 紫色拖船
  6: '#D2B48C',  // 淡棕色其他类型
  // 扩展映射，支持更多shipType值
  60: '#FFA500', 61: '#FFA500', 62: '#FFA500', 63: '#FFA500', 64: '#FFA500',
  65: '#FFA500', 66: '#FFA500', 67: '#FFA500', 68: '#FFA500', 69: '#FFA500', // 客船范围
  70: '#FFFF00', 71: '#FFFF00', 72: '#FFFF00', 73: '#FFFF00', 74: '#FFFF00', // 货船范围
  30: '#0000FF',  // 渔船
  33: '#8B4513',  // 作业船
  52: '#800080'   // 拖船
};

/**
 * 获取执法船列表，只加载一次
 * @returns 执法船MMSI列表
 */
async function loadGaShipList(): Promise<string[]> {
  // 如果已经加载过执法船列表，直接返回缓存
  if (hasLoadedGaShipList) {
    return gaShipListCache;
  }

  try {
    const { getGaShipList } = await import('@/api/wan/GaShip');
    const gaShipResponse = await getGaShipList({});
    
    if (gaShipResponse && gaShipResponse.data && Array.isArray(gaShipResponse.data)) {
      // 提取执法船的MMSI号，并转为字符串确保一致性
      gaShipListCache = gaShipResponse.data.map(ship => String(ship.mmsi));
    } else if (gaShipResponse && gaShipResponse.rows && Array.isArray(gaShipResponse.rows)) {
      // 如果数据在rows字段中
      gaShipListCache = gaShipResponse.rows.map(ship => String(ship.mmsi));
    } else {
      gaShipListCache = [];
    }
    
    // 标记为已加载
    hasLoadedGaShipList = true;
    
    return gaShipListCache;
  } catch (error) {
    console.error('获取执法船列表失败:', error);
    return [];
  }
}

/**
 * 清除执法船列表缓存，强制下次重新加载
 */
export function clearGaShipListCache(): void {
  hasLoadedGaShipList = false;
  gaShipListCache = [];
}

/**
 * 启用船舶图例模式
 */
export function enableShipLegendMode(): void {
  isShipLegendModeEnabled = true;

  // 更新所有船舶的样式
  updateAllShipStyles();
}

/**
 * 禁用船舶图例模式，恢复原始样式
 */
export function disableShipLegendMode(): void {
  isShipLegendModeEnabled = false;

  // 更新所有船舶的样式
  updateAllShipStyles();
}

/**
 * 获取船舶图例颜色
 * @param shipType 船舶类型
 * @returns 对应的颜色值
 */
function getShipLegendColor(shipType: number | undefined): string {
  if (shipType === undefined) return SHIP_LEGEND_COLORS[6]; // 默认其他类型

  // 直接查找映射表
  if (SHIP_LEGEND_COLORS[shipType as keyof typeof SHIP_LEGEND_COLORS]) {
    return SHIP_LEGEND_COLORS[shipType as keyof typeof SHIP_LEGEND_COLORS];
  }

  // 如果没有直接映射，根据范围判断
  if (shipType >= 60 && shipType <= 69) {
    return SHIP_LEGEND_COLORS[1]; // 客船
  } else if (shipType >= 70 && shipType <= 74) {
    return SHIP_LEGEND_COLORS[2]; // 货船
  } else {
    return SHIP_LEGEND_COLORS[6]; // 其他类型
  }
}

/**
 * 创建船舶图例样式
 * @param color 颜色值
 * @param isPolice 是否为执法船
 * @param heading 船舶航向
 * @returns Style 样式对象
 */
function createShipLegendStyle(color: string, isPolice: boolean = false, heading?: number): Style {
  // 执法船不使用图例颜色，保持原有样式
  if (isPolice) {
    return createShipIconStyle('police', heading);
  }

  // 根据当前缩放级别创建相应的图例样式，保持与原有逻辑一致
  if (currentZoom <= ZOOM_LEVELS.DOTS) {
    // 低缩放级别使用圆点（与原有逻辑一致）
    return new Style({
      image: new CircleStyle({
        radius: 3,
        fill: new Fill({
          color: color
        }),
        stroke: new Stroke({
          color: '#FFFFFF',
          width: 1
        })
      })
    });
  } else {
    // 正常缩放级别使用三角形图标（保持原有的三角形样式，只改变颜色）
    return createColoredTriangleStyle(color, heading);
  }
}

/**
 * 创建带颜色的三角形样式
 * @param color 颜色值
 * @param heading 船舶航向
 * @returns Style 样式对象
 */
function createColoredTriangleStyle(color: string, heading?: number): Style {
  // 创建SVG三角形图标，使用指定颜色
  const triangleSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
    <polygon points="50,30 65,99 35,99" fill="${color}"/>
  </svg>`;

  const triangleDataUrl = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(triangleSvg);

  const rotation = heading ? (heading * Math.PI / 180) : 0;

  // 根据缩放级别调整比例（与原有逻辑保持一致）
  let baseScale = 0.25;
  let zoomAdjust = 1.0;

  if (currentZoom > ZOOM_LEVELS.NORMAL) {
    // 高缩放级别时图标变小
    zoomAdjust = Math.max(0.5, 13 / (currentZoom + 3));
  }

  const finalScale = baseScale * zoomAdjust;

  return new Style({
    image: new Icon({
      src: triangleDataUrl,
      scale: finalScale,
      rotation: rotation,
      rotateWithView: true,
      anchor: [0.5, 0.5],
      anchorXUnits: 'fraction',
      anchorYUnits: 'fraction'
    })
  });
}

/**
 * 更新所有船舶的样式
 */
function updateAllShipStyles(): void {
  shipFeatures.forEach(feature => {
    updateShipStyle(feature);
  });
}

/**
 * 更新特定船舶的样式，确保其符合当前缩放级别的要求
 * @param feature 要更新的船舶特征
 */
function updateShipStyle(feature: Feature): void {
  const shipData = feature.get('shipData');
  if (!shipData) return;

  // 判断是否为执法船
  const isPoliceShip = shipData.isPolice === true;

  try {
    // 如果启用了船舶图例模式，使用图例样式
    if (isShipLegendModeEnabled) {
      const legendColor = getShipLegendColor(shipData.shipType);
      const legendStyle = createShipLegendStyle(legendColor, isPoliceShip, shipData.cog);
      feature.setStyle(legendStyle);
    } else {
      // 使用原始样式
      if (currentZoom <= ZOOM_LEVELS.DOTS) {
        // 低缩放级别使用圆点
        const circleStyle = createShipCircleStyle(isPoliceShip);
        feature.setStyle(circleStyle);
      } else {
        // 正常缩放级别使用图标
        const iconType = isPoliceShip ? 'police' : determineShipIconType([shipData.lon, shipData.lat]);
        const style = createShipIconStyle(iconType, shipData.cog);
        feature.setStyle(style);
      }
    }
  } catch (error) {
    // 静默处理错误，避免控制台输出过多信息
  }
}

/**
 * 根据当前缩放级别更新所有船舶图标的比例
 */
function updateAllShipIconsScale() {
  shipFeatures.forEach(feature => {
    updateShipStyle(feature);
  });
}

/**
 * 监听地图缩放事件
 */
export function setupZoomListener(map: OLMap): void {
  if (!map) return;
  
  // 监听视图变化事件
  map.getView().on('change:resolution', () => {
    // 获取新的缩放级别
    const newZoom = map.getView().getZoom() || 10;
    
    // 只有当缩放级别跨越阈值时才更新样式
    const oldBelowDots = currentZoom <= ZOOM_LEVELS.DOTS;
    const newBelowDots = newZoom <= ZOOM_LEVELS.DOTS;
    const oldAboveNormal = currentZoom > ZOOM_LEVELS.NORMAL;
    const newAboveNormal = newZoom > ZOOM_LEVELS.NORMAL;
    
    // 更新当前缩放级别
    currentZoom = newZoom;
    
    // 如果跨越了阈值，则更新所有船舶图标
    if (oldBelowDots !== newBelowDots || oldAboveNormal !== newAboveNormal) {
      updateAllShipIconsScale();
    }
  });
}

/**
 * 初始化船舶跟踪
 * @param map 地图实例
 */
export function initShipTracking(map: OLMap): void {
  mapInstance = map;

  // 验证SVG图标
  validateSvgIcons();

  // 记录初始缩放级别
  currentZoom = map.getView().getZoom() || 10;

  // 清除可能存在的未清除的绘图交互
  clearAllDrawInteractions();

  // 验证并获取vectorSource
  let vectorSource = mapUtils.getVectorSource();
  if (!vectorSource) {
    console.warn('mapUtils中的vectorSource未初始化，尝试从地图图层中查找');
    // 尝试从地图的现有图层中找到vectorSource
    const layers = map.getLayers().getArray();
    const drawingLayer = layers.find(layer =>
      layer.getClassName && (
        layer.getClassName().includes('drawing') ||
        layer.getClassName().includes('vector')
      )
    );

    if (drawingLayer && drawingLayer instanceof VectorLayer) {
      vectorSource = drawingLayer.getSource();
      if (vectorSource) {
        mapUtils.setVectorSource(vectorSource);
        console.log('从地图图层中找到并设置vectorSource');
      }
    }

    // 如果仍然没有找到，创建一个新的
    if (!vectorSource) {
      console.warn('未找到现有vectorSource，创建新的vectorSource');
      vectorSource = new VectorSource();
      mapUtils.setVectorSource(vectorSource);
    }
  }

  // 使用验证后的vectorSource创建船舶图层
  shipVectorLayer = new VectorLayer({
    source: vectorSource,
    style: new Style({
      // 默认样式，实际会被每个要素自己的样式覆盖
      image: new Icon({
        src: ICON_PATHS.green,
        scale: 0.3
      })
    }),
    zIndex: 10 // 确保船舶显示在上方
  });

  // 添加船舶图层到地图
  map.addLayer(shipVectorLayer);

  // Canvas性能优化已移除 - 避免与OpenLayers冲突

  // 创建悬停提示元素
  createHoverTooltip();

  // 添加鼠标移动监听器用于悬停提示
  map.on('pointermove', handlePointerMove);

  // 设置缩放事件监听
  setupZoomListener(map);

  // 初始化选择图层
  const selectionSource = new VectorSource();
  selectionLayer = new VectorLayer({
    source: selectionSource,
    style: new Style({
      stroke: new Stroke({
        color: '#FF0000', // 红色选择框
        width: 2,
        lineDash: [5, 5] // 虚线样式
      })
    }),
    zIndex: 11 // 确保选择框在船舶图标上方
  });
  map.addLayer(selectionLayer);

  // 添加点击事件监听
  map.on('click', handleMapClick);

  // 添加清除选中事件监听
  if (window.emitter) {
    window.emitter.on('clear-ship-selection', clearSelection);
  }


}

/**
 * 清除所有可能存在的绘图交互
 */
function clearAllDrawInteractions() {
  if (!mapInstance) return;
  
  // 获取地图上所有的交互
  const interactions = mapInstance.getInteractions().getArray();
  
  // 找到并移除Draw类型的交互
  const drawInteractions = interactions.filter(interaction => 
    interaction.constructor.name.includes('Draw') || 
    interaction.constructor.name.includes('Select') || 
    interaction.constructor.name.includes('Modify'));
  
  drawInteractions.forEach(interaction => {
    mapInstance?.removeInteraction(interaction);
  });
  

}

/**
 * 创建悬停提示元素
 */
function createHoverTooltip(): void {
  if (!mapInstance) return;
  
  // 如果已存在，先移除
  if (hoverTooltip) {
    mapInstance.removeOverlay(hoverTooltip);
  }
  
  // 创建提示元素
  hoverTooltipElement = document.createElement('div');
  hoverTooltipElement.className = 'ship-hover-tooltip';
  hoverTooltipElement.style.cssText = `
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    pointer-events: none;
    z-index: 1000;
    max-width: 300px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    white-space: nowrap;
    transform: translate(-50%, -130%);
  `;
  
  // 创建提示覆盖物
  hoverTooltip = new Overlay({
    element: hoverTooltipElement,
    offset: [0, 0],
    positioning: 'bottom-center',
    stopEvent: false
  });
  
  // 添加到地图
  mapInstance.addOverlay(hoverTooltip);
}

// 防抖处理鼠标移动事件（优化版本）
let hoverDebounceTimer: ReturnType<typeof setTimeout> | null = null;
const HOVER_DEBOUNCE_DELAY = 50; // 悬浮防抖延迟
const FENCE_HOVER_DEBOUNCE_DELAY = 50; // 围栏悬浮防抖延迟

/**
 * 处理鼠标移动事件以显示悬停提示
 */
function handlePointerMove(event: any): void {
  if (!mapInstance || !hoverTooltip || !hoverTooltipElement) {
    console.warn('船舶悬浮提示组件未初始化:', {
      mapInstance: !!mapInstance,
      hoverTooltip: !!hoverTooltip,
      hoverTooltipElement: !!hoverTooltipElement
    });
    return;
  }

  // 防抖处理，减少频繁的像素检测
  if (hoverDebounceTimer) {
    clearTimeout(hoverDebounceTimer);
  }

  hoverDebounceTimer = setTimeout(() => {
    try {
      // 默认隐藏提示
      hoverTooltip.setPosition(undefined);

      // 检查鼠标是否悬停在船舶要素上，使用优化的像素检测
      let shipFeature = null;
      let allFeatures = [];

      mapInstance.forEachFeatureAtPixel(
        event.pixel,
        (feature) => {
          allFeatures.push({
            type: feature.get('type'),
            hasShipData: !!feature.get('shipData'),
            featureType: feature.get('featureType'),
            mmsi: feature.get('mmsi')
          });

          // 检查是否为船舶要素（通过type属性或shipData属性判断）
          if (feature.get('type') === 'ship' || feature.get('shipData')) {
            shipFeature = feature;
            return true; // 找到船舶要素，停止迭代
          }
          return false;
        },
        {
          hitTolerance: 8 // 增加点击容差，提高检测精度
        }
      );

      // 调试信息：记录检测到的要素
      if (allFeatures.length > 0) {
      }

      if (shipFeature) {
        // 获取船舶数据
        const shipData = shipFeature.get('shipData');
        if (!shipData) return;

        // 更新提示内容（优化字符串处理）
        const name = shipData.name || shipData.chineseName || shipData.englishName || '未知';
        const mmsi = shipData.mmsi || '未知';
        const shipType = formatShipType(shipData.shipType);
        const sog = typeof shipData.sog === 'number' ? `${shipData.sog.toFixed(1)}节` : '未知';
        const heading = typeof shipData.heading === 'number' ? `${shipData.heading.toFixed(1)}°` :
                      (typeof shipData.cog === 'number' ? `${shipData.cog.toFixed(1)}°` : '未知');

        // 使用模板字符串优化性能
        hoverTooltipElement.innerHTML = `<strong>${name}</strong><br>MMSI: ${mmsi}<br>类型: ${shipType}<br>航速: ${sog}<br>航向: ${heading}`;

        // 显示提示
        const geometry = shipFeature.getGeometry();
        if (geometry instanceof Point) {
          hoverTooltip.setPosition(geometry.getCoordinates());
        }
      }
    } catch (error) {
      console.warn('船舶悬浮提示处理失败:', error);
    }
  }, HOVER_DEBOUNCE_DELAY);
}

/**
 * 创建围栏悬浮提示元素
 */
function createFenceTooltip(): void {
  if (!mapInstance) return;
  
  // 如果已存在，先移除
  if (fenceTooltipOverlay) {
    mapInstance.removeOverlay(fenceTooltipOverlay);
    fenceTooltipOverlay = null;
  }
  
  if (fenceTooltipElement) {
    if (fenceTooltipElement.parentNode) {
      fenceTooltipElement.parentNode.removeChild(fenceTooltipElement);
    }
    fenceTooltipElement = null;
  }
  
  try {
    // 创建提示元素
    fenceTooltipElement = document.createElement('div');
    fenceTooltipElement.className = 'fence-tooltip';
    
    // 创建提示覆盖物
    fenceTooltipOverlay = new Overlay({
      element: fenceTooltipElement,
      offset: [15, 0], // 稍微增加偏移，避免鼠标正好悬停在提示上
      positioning: 'center-left',
      stopEvent: false, // 确保事件可以穿透
      className: 'fence-tooltip-overlay' // 添加类名便于调试
    });
    
    // 确保DOM元素已创建
    document.body.appendChild(fenceTooltipElement);
    
    // 先将元素设置为不可见
    fenceTooltipElement.style.display = 'none';
    
    // 添加到地图
    mapInstance.addOverlay(fenceTooltipOverlay);
    

  } catch (error) {
    console.error('创建电子围栏提示元素失败:', error);
  }
}

/**
 * 设置电子围栏
 * @param fences 电子围栏数据
 */
export function setFences(fences: ElectronicFence[] | number[][][]): void {
  try {
    if (!mapInstance) {
      console.error('地图未初始化，无法设置电子围栏');
      return;
    }
    
    // 储存围栏数据
    // 如果是简单的坐标数组，转换为ElectronicFence对象数组
    if (fences.length > 0 && Array.isArray(fences[0]) && Array.isArray(fences[0][0])) {
      // 这是坐标数组格式
      fencePolygons = (fences as number[][][]).map((coords, index) => ({
        coordinates: coords,
        name: `围栏 ${index + 1}`,
        type: '默认围栏',
        remark: ''
      }));
    } else {
      // 这是ElectronicFence对象数组格式
      fencePolygons = fences as ElectronicFence[];
    }
    
    // 清除现有围栏图层（注意：不要清除船舶图层）
    // 查找并移除现有的围栏图层
    const layers = mapInstance.getLayers().getArray();
    const existingFenceLayer = layers.find(layer =>
      layer.get('layerType') === 'fences' ||
      layer.getClassName && layer.getClassName() === 'fence-layer'
    );
    if (existingFenceLayer) {
      mapInstance.removeLayer(existingFenceLayer);
    }
    
    // 如果没有围栏数据，直接返回
    if (!fencePolygons || !Array.isArray(fencePolygons) || fencePolygons.length === 0) {

      return;
    }
    
    // 创建围栏悬浮提示
    createFenceTooltip();
    
    // 创建围栏图层
    const source = new VectorSource();
    
    // 绘制每个围栏
    fencePolygons.forEach((fence, index) => {
      const coordinates = fence.coordinates;
      if (!coordinates || !Array.isArray(coordinates) || coordinates.length < 3) {
        console.warn(`围栏 ${index} 数据无效，至少需要3个点`);
        return;
      }
      
      // 创建多边形
      const polygon = new Polygon([coordinates]);
      
      // 创建要素
      const feature = new Feature({
        geometry: polygon,
        fenceId: fence.id || index,
        name: fence.name || `围栏 ${index + 1}`,
        type: fence.type || '默认围栏',
        remark: fence.remark || '',
        fenceFeature: true // 添加标记，方便识别围栏要素
      });
      
      // 设置要素样式
      feature.setStyle(new Style({
        fill: new Fill({
          color: 'rgba(0, 123, 255, 0.2)'
        }),
        stroke: new Stroke({
          color: '#007bff',
          width: 2
        })
      }));
      
      // 添加要素到数据源
      source.addFeature(feature);
    });
    
    // 创建围栏矢量图层
    const fenceVectorLayer = new VectorLayer({
      source: source,
      zIndex: 90, // 置于船舶图层下方
      properties: {
        layerType: 'fences'
      },
      style: function(feature) {
        return new Style({
          fill: new Fill({
            color: 'rgba(0, 123, 255, 0.2)'
          }),
          stroke: new Stroke({
            color: '#007bff',
            width: 2
          })
        });
      }
    });

    // 添加围栏图层到地图
    mapInstance.addLayer(fenceVectorLayer);
    
    // 监听地图移动事件，移动时隐藏提示
    mapInstance.on('movestart', () => {
      if (fenceTooltipOverlay) {
        fenceTooltipOverlay.setPosition(undefined);
      }
    });
    
    // 监听鼠标移动事件
    mapInstance.on('pointermove', handleFenceHover);
    

    
  } catch (error) {
    console.error('设置电子围栏失败:', error);
  }
}

// 防抖处理围栏悬停事件
let fenceHoverDebounceTimer: ReturnType<typeof setTimeout> | null = null;

/**
 * 处理围栏鼠标悬停
 */
function handleFenceHover(event: any): void {
  if (!mapInstance || !fenceTooltipOverlay || !fenceTooltipElement) return;

  // 拖动时隐藏提示
  if (event.dragging) {
    fenceTooltipOverlay.setPosition(undefined);
    // 确保元素不可见
    if (fenceTooltipElement) {
      fenceTooltipElement.style.display = 'none';
    }
    return;
  }

  // 防抖处理，减少频繁的像素检测
  if (fenceHoverDebounceTimer) {
    clearTimeout(fenceHoverDebounceTimer);
  }

  fenceHoverDebounceTimer = setTimeout(() => {
    // 检查鼠标是否悬停在围栏要素上，使用优化的像素检测
    let hoveredFeature: Feature | null = null;

    mapInstance.forEachFeatureAtPixel(
      event.pixel,
      (feature: Feature) => {
        // 检查是否为围栏要素
        if (feature.get('fenceFeature') === true) {
          hoveredFeature = feature;
          return true; // 找到围栏要素，停止迭代
        }
        return false;
      },
      {
        hitTolerance: 3 // 增加点击容差，减少精确像素检测
      }
    );
  
  // 更新鼠标样式
  mapInstance.getTargetElement().style.cursor = hoveredFeature ? 'pointer' : '';
  
  // 如果有悬停的围栏要素，显示提示
  if (hoveredFeature) {
    const name = hoveredFeature.get('name') || '未命名围栏';
    const type = hoveredFeature.get('type') || '';
    const remark = hoveredFeature.get('remark') || '';
    
    // 更新提示内容
    let tooltipContent = `<div><strong>${name}</strong></div>`;
    if (type) {
      tooltipContent += `<div>类型: ${type}</div>`;
    }
    if (remark) {
      tooltipContent += `<div>备注: ${remark}</div>`;
    }
    
    // 更新内容
    if (fenceTooltipElement) {
      fenceTooltipElement.innerHTML = tooltipContent;
      fenceTooltipElement.style.display = 'block'; // 确保元素可见
    }
    
    // 设置提示位置为鼠标位置
    fenceTooltipOverlay.setPosition(event.coordinate);
    
    // 阻止事件冒泡和默认行为，防止其他处理器接收到事件
    if (event.originalEvent) {
      event.originalEvent.stopPropagation();
      event.originalEvent.preventDefault();
    }
    
    // 禁止触发InfoWindow相关的事件
    event.stopPropagation && event.stopPropagation();
    
    // 确保禁用所有window.emitter事件
    if (window.emitter) {
      try {
        // 拦截任何可能的show-info-window事件
        window.emitter.off('show-info-window');
      } catch (e) {}
    }
  } else {
    // 没有悬停在围栏上，隐藏提示
    fenceTooltipOverlay.setPosition(undefined);
    
      // 确保元素不可见
      if (fenceTooltipElement) {
        fenceTooltipElement.style.display = 'none';
      }
    }
  }, FENCE_HOVER_DEBOUNCE_DELAY);
}

// 在组件销毁时清理围栏提示
export function cleanupFenceTooltip(): void {
  try {
    // 移除tooltip覆盖物
    if (mapInstance && fenceTooltipOverlay) {
      mapInstance.removeOverlay(fenceTooltipOverlay);
    }
    
    // 从DOM中移除元素
    if (fenceTooltipElement && fenceTooltipElement.parentNode) {
      fenceTooltipElement.parentNode.removeChild(fenceTooltipElement);
    }
    
    // 清空引用
    fenceTooltipOverlay = null;
    fenceTooltipElement = null;
    
    // 移除事件监听
    if (mapInstance) {
      try {
        // @ts-ignore - 由于TypeScript的限制，我们需要忽略类型检查来移除事件监听
        mapInstance.un('pointermove', handleFenceHover);
        // @ts-ignore
        mapInstance.un('movestart');
      } catch (error) {
        console.warn('移除围栏鼠标事件监听失败:', error);
      }
    }
    
    // 移除可能存在的相关样式
    const styleElements = document.querySelectorAll('style');
    styleElements.forEach(style => {
      if (style.textContent && style.textContent.includes('fence-tooltip')) {
        try {
          style.parentNode?.removeChild(style);
        } catch (e) {}
      }
    });
    

  } catch (error) {
    console.error('清理电子围栏提示资源时发生错误:', error);
  }
}

// 船舶数据缓存和优化相关变量
let lastShipDataCache = new Map<string, any>();
const MAX_SHIP_COUNT = 1000; // 最大船舶数量限制

// 并发控制变量
let isLoadingShipData = false; // 是否正在加载船舶数据

// 要素池化管理
const featurePool = new Map<string, Feature[]>(); // 按类型分组的要素池
const MAX_POOL_SIZE = 200; // 每种类型的最大池大小
const activeFeatures = new Set<Feature>(); // 当前活跃的要素

// Canvas性能优化相关代码已移除 - 避免与OpenLayers冲突





/**
 * 从要素池获取要素
 * @param type 要素类型
 * @returns Feature对象或null
 */
function getFeatureFromPool(type: string): Feature | null {
  const pool = featurePool.get(type);
  if (pool && pool.length > 0) {
    const feature = pool.pop()!;
    activeFeatures.add(feature);
    return feature;
  }
  return null;
}



/**
 * 将要素返回到池中
 * @param feature 要素对象
 * @param type 要素类型
 */
function returnFeatureToPool(feature: Feature, type: string): void {
  if (!activeFeatures.has(feature)) {
    return; // 不是活跃要素，忽略
  }

  activeFeatures.delete(feature);

  // 清理要素数据
  feature.setGeometry(undefined);
  feature.setStyle(undefined);
  feature.getProperties && Object.keys(feature.getProperties()).forEach(key => {
    feature.unset(key);
  });

  // 获取或创建对应类型的池
  let pool = featurePool.get(type);
  if (!pool) {
    pool = [];
    featurePool.set(type, pool);
  }

  // 如果池未满，添加到池中
  if (pool.length < MAX_POOL_SIZE) {
    pool.push(feature);
  }
  // 如果池已满，让要素被垃圾回收
}

/**
 * 创建或复用船舶要素
 * @param ship 船舶数据
 * @param isPoliceShip 是否为执法船
 * @returns Feature对象
 */
function createOrReuseShipFeature(ship: any, isPoliceShip: boolean): Feature {
  const featureType = isPoliceShip ? 'police-ship' : 'ship';

  // 尝试从池中获取要素
  let feature = getFeatureFromPool(featureType);

  if (!feature) {
    // 池中没有可用要素，创建新的
    feature = new Feature();
    activeFeatures.add(feature);
  }

  // 设置要素属性
  const coordinates: [number, number] = [ship.lon, ship.lat];
  feature.setGeometry(new Point(coordinates));
  feature.set('name', ship.name || ship.chineseName || ship.englishName || ship.mmsi);
  feature.set('mmsi', ship.mmsi);
  feature.set('shipData', { ...ship, isPolice: isPoliceShip });
  feature.set('type', 'ship');
  feature.set('isPolice', isPoliceShip);
  feature.set('featureType', featureType);

  return feature;
}

/**
 * 清理要素池，移除过多的要素
 */
function cleanupFeaturePool(): void {
  let totalCleaned = 0;

  featurePool.forEach((pool, type) => {
    if (pool.length > MAX_POOL_SIZE) {
      const excess = pool.length - MAX_POOL_SIZE;
      pool.splice(MAX_POOL_SIZE, excess);
      totalCleaned += excess;
    }
  });

  if (totalCleaned > 0) {
  }
}

/**
 * 加载船舶AIS数据
 * @returns 船舶数据数组
 */
export async function loadShipAisData(): Promise<any[]> {
  if (!mapInstance) {
    return [];
  }
  
  // 获取vectorSource
  const vectorSource = mapUtils.getVectorSource();
  if (!vectorSource) {
    return [];
  }
  
  try {
    // 先获取执法船列表（只会加载一次）
    const gaShipList = await loadGaShipList();
    
    // 获取所有船舶AIS数据
    const response = await getShipAis({});
    
    if (response && response.data && Array.isArray(response.data)) {
      const shipList = response.data;
      
      // 清除现有船舶标记
      clearShips();
      
      // 在地图上添加船舶标记，并标识执法船
      shipList.forEach(ship => {
        if (ship && typeof ship.lon === 'number' && typeof ship.lat === 'number') {
          // 判断是否为执法船 - 将MMSI转为字符串再比较
          const isPoliceShip = gaShipList.includes(String(ship.mmsi));
          // 添加船舶标记
          addShipToMap(ship, isPoliceShip);
        } else {
          console.warn('船舶数据缺少经纬度信息:', ship);
        }
      });
      
      // 确保应用当前缩放级别的样式
      updateAllShipIconsScale();
      

      return shipList;
    } else if (response && response.rows && Array.isArray(response.rows)) {
      // 如果数据在rows字段中（与您提供的JSON结构匹配）
      const shipList = response.rows;
      
      // 清除现有船舶标记
      clearShips();
      
      // 在地图上添加船舶标记，并标识执法船
      shipList.forEach(ship => {
        if (ship && typeof ship.lon === 'number' && typeof ship.lat === 'number') {
          // 判断是否为执法船 - 将MMSI转为字符串再比较
          const isPoliceShip = gaShipList.includes(String(ship.mmsi));
          // 添加船舶标记
          addShipToMap(ship, isPoliceShip);
        } else {
          console.warn('船舶数据缺少经纬度信息:', ship);
        }
      });
      
      // 确保应用当前缩放级别的样式
      updateAllShipIconsScale();
      

      return shipList;
    } else {

      return [];
    }
  } catch (error) {
    return [];
  }
}

/**
 * 优化的船舶AIS数据加载函数（增量更新和数量限制）
 * @returns 船舶数据数组
 */
export async function loadShipAisDataOptimized(): Promise<any[]> {
  // 防止并发请求
  if (isLoadingShipData) {
    return [];
  }

  if (!mapInstance) {
    return [];
  }

  // 获取vectorSource
  const vectorSource = mapUtils.getVectorSource();
  if (!vectorSource) {
    return [];
  }

  // 设置加载标志
  isLoadingShipData = true;

  try {
    // 先获取执法船列表（只会加载一次）
    const gaShipList = await loadGaShipList();

    // 获取所有船舶AIS数据
    const response = await getShipAis({});

    if (response && response.data && Array.isArray(response.data)) {
      return processShipDataOptimized(response.data, gaShipList);
    } else if (response && response.rows && Array.isArray(response.rows)) {
      return processShipDataOptimized(response.rows, gaShipList);
    } else {
      return [];
    }
  } catch (error) {
    return [];
  } finally {
    // 确保加载标志被重置
    isLoadingShipData = false;
  }
}

/**
 * 处理船舶数据优化（增量更新和数量限制）
 * @param shipList 船舶数据列表
 * @param gaShipList 执法船列表
 * @returns 处理后的船舶数据数组
 */
function processShipDataOptimized(shipList: any[], gaShipList: string[]): any[] {
  const currentTime = Date.now();
  const newShipData = new Map<string, any>();
  const validShips: any[] = [];


  // 过滤有效船舶数据并建立新的数据映射
  shipList.forEach(ship => {
    if (ship && typeof ship.lon === 'number' && typeof ship.lat === 'number' && ship.mmsi) {
      const mmsi = String(ship.mmsi);
      ship.lastUpdateTime = currentTime;
      newShipData.set(mmsi, ship);
      validShips.push(ship);
    }
  });

  // 限制船舶数量，优先保留最新的船舶
  let finalShips = validShips;
  if (validShips.length > MAX_SHIP_COUNT) {
    // 按时间排序，保留最新的船舶
    finalShips = validShips
      .sort((a, b) => (b.time || 0) - (a.time || 0))
      .slice(0, MAX_SHIP_COUNT);

  }

  // 检查是否需要增量更新
  const needsFullUpdate = shouldPerformFullUpdate(newShipData);

  if (needsFullUpdate) {
    // 执行全量更新
    clearShips();
    finalShips.forEach(ship => {
      const isPoliceShip = gaShipList.includes(String(ship.mmsi));
      addShipToMap(ship, isPoliceShip);
    });
  } else {
    // 执行增量更新
    performIncrementalUpdate(newShipData, gaShipList);
  }

  // 更新缓存
  lastShipDataCache = newShipData;

  // 确保应用当前缩放级别的样式
  updateAllShipIconsScale();

  return finalShips;
}

/**
 * 判断是否需要执行全量更新
 * @param newShipData 新的船舶数据
 * @returns 是否需要全量更新
 */
function shouldPerformFullUpdate(newShipData: Map<string, any>): boolean {
  // 如果是首次加载或缓存为空，执行全量更新
  if (lastShipDataCache.size === 0) {
    return true;
  }

  // 如果船舶数量变化超过30%，执行全量更新
  const sizeDiff = Math.abs(newShipData.size - lastShipDataCache.size);
  const changeRatio = sizeDiff / Math.max(lastShipDataCache.size, 1);

  return changeRatio > 0.3;
}

/**
 * 执行增量更新
 * @param newShipData 新的船舶数据
 * @param gaShipList 执法船列表
 */
function performIncrementalUpdate(newShipData: Map<string, any>, gaShipList: string[]): void {
  const vectorSource = mapUtils.getVectorSource();
  if (!vectorSource) return;

  // 安全检查：确保 newShipData 是有效的 Map 对象
  if (!newShipData || typeof newShipData.forEach !== 'function') {
    console.error('performIncrementalUpdate: newShipData 不是有效的 Map 对象', newShipData);
    return;
  }

  let addedCount = 0;
  let updatedCount = 0;
  let removedCount = 0;

  // 移除不存在的船舶
  const featuresToRemove: any[] = [];
  shipFeatures.forEach(feature => {
    const mmsi = feature.get('mmsi');
    if (mmsi && !newShipData.has(String(mmsi))) {
      featuresToRemove.push(feature);
    }
  });

  featuresToRemove.forEach(feature => {
    vectorSource.removeFeature(feature);
    const index = shipFeatures.indexOf(feature);
    if (index > -1) {
      shipFeatures.splice(index, 1);
    }

    // 将要素返回到池中
    const featureType = feature.get('featureType') || 'ship';
    returnFeatureToPool(feature, featureType);

    removedCount++;
  });

  // 添加或更新船舶
  newShipData.forEach((ship, mmsi) => {
    const existingFeature = shipFeatures.find(f => String(f.get('mmsi')) === mmsi);
    const isPoliceShip = gaShipList.includes(mmsi);

    if (existingFeature) {
      // 更新现有船舶
      const oldShip = lastShipDataCache.get(mmsi);
      if (!oldShip || hasShipDataChanged(ship, oldShip)) {
        updateShipOnMap(existingFeature, ship, isPoliceShip);
        updatedCount++;
      }
    } else {
      // 添加新船舶
      addShipToMap(ship, isPoliceShip);
      addedCount++;
    }
  });
}

/**
 * 检查船舶数据是否发生变化
 * @param newShip 新船舶数据
 * @param oldShip 旧船舶数据
 * @returns 是否发生变化
 */
function hasShipDataChanged(newShip: any, oldShip: any): boolean {
  return (
    newShip.lon !== oldShip.lon ||
    newShip.lat !== oldShip.lat ||
    newShip.cog !== oldShip.cog ||
    newShip.sog !== oldShip.sog ||
    newShip.time !== oldShip.time
  );
}

/**
 * 更新地图上的船舶
 * @param feature 船舶要素
 * @param ship 新的船舶数据
 * @param isPoliceShip 是否为执法船
 */
function updateShipOnMap(feature: any, ship: any, isPoliceShip: boolean): void {
  // 更新位置
  const newCoordinates: [number, number] = [ship.lon, ship.lat];
  const geometry = feature.getGeometry();
  if (geometry) {
    geometry.setCoordinates(newCoordinates);
  }

  // 更新船舶数据
  feature.set('shipData', { ...ship, isPolice: isPoliceShip });

  // 更新样式（如果航向发生变化）
  const iconType = isPoliceShip ? 'police' : determineShipIconType(newCoordinates);

  if (currentZoom <= ZOOM_LEVELS.DOTS) {
    const circleStyle = createShipCircleStyle(isPoliceShip);
    feature.setStyle(circleStyle);
  } else {
    const shipStyle = createShipIconStyle(iconType, ship.cog);
    feature.setStyle(shipStyle);
  }
}

/**
 * 清除所有船舶标记
 */
function clearShips(): void {
  const vectorSource = mapUtils.getVectorSource();
  if (!vectorSource) return;
  
  // 移除所有船舶要素并返回到池中
  shipFeatures.forEach(feature => {
    vectorSource.removeFeature(feature);

    // 将要素返回到池中
    const featureType = feature.get('featureType') || 'ship';
    returnFeatureToPool(feature, featureType);
  });
  shipFeatures.length = 0;
  
  // 清除所有弹窗
  shipPopupOverlays.forEach(overlay => {
    mapInstance?.removeOverlay(overlay);
  });
  shipPopupOverlays.length = 0;
}

/**
 * 添加船舶到地图
 * @param ship 船舶AIS数据
 * @param isPoliceShip 是否为执法船
 */
function addShipToMap(ship: any, isPoliceShip: boolean = false): void {
  if (!mapInstance) {
    console.error('地图实例未初始化');
    return;
  }
  
  const vectorSource = mapUtils.getVectorSource();
  if (!vectorSource) {
    console.error('矢量源未初始化');
    return;
  }
  
  if ((!ship.lon && ship.lon !== 0) || (!ship.lat && ship.lat !== 0)) {
    console.error('船舶缺少经纬度信息:', ship);
    return;
  }
  
  // 船舶坐标
  const coordinates: [number, number] = [ship.lon, ship.lat];
  
  // 判断船舶与电子围栏的关系，选择适当的图标
  const iconType = isPoliceShip ? 'police' : determineShipIconType(coordinates);
  

  
  try {
    // 创建或复用船舶要素
    const shipFeature = createOrReuseShipFeature(ship, isPoliceShip);

    if (!shipFeature) {
      console.error(`创建船舶要素失败: ${ship.mmsi}`);
      return;
    }

    // 根据当前缩放级别设置合适的样式
    if (currentZoom <= ZOOM_LEVELS.DOTS) {
      // 低缩放级别使用圆点
      const circleStyle = createShipCircleStyle(isPoliceShip);
      if (circleStyle) {
        shipFeature.setStyle(circleStyle);
      }
    } else {
      // 正常缩放级别使用图标
      const shipStyle = createShipIconStyle(iconType, ship.cog);
      if (shipStyle) {
        shipFeature.setStyle(shipStyle);
      }
    }

    // 验证vectorSource状态
    if (!vectorSource || typeof vectorSource.addFeature !== 'function') {
      console.error('vectorSource状态异常，无法添加船舶要素');
      return;
    }

    // 添加要素到图层
    vectorSource.addFeature(shipFeature);
    shipFeatures.push(shipFeature);

    // 创建弹窗（可选，失败不影响主要功能）
    try {
      createShipPopup(shipFeature);
    } catch (popupError) {
      console.warn(`创建船舶弹窗失败: ${ship.mmsi}`, popupError);
    }

  } catch (error) {
    console.error(`添加船舶 ${ship.mmsi} 到地图时发生错误:`, error);

    // 记录详细的错误信息用于调试
    console.error('船舶数据:', ship);
    console.error('地图实例状态:', !!mapInstance);
    console.error('vectorSource状态:', !!vectorSource);

    // 如果是关键错误，可以触发恢复机制
    if (error instanceof Error && error.message && error.message.includes('vectorSource')) {
      console.warn('检测到vectorSource相关错误，可能需要重新初始化');
    }
  }
}

/**
 * 为船舶创建弹窗
 * @param shipFeature 船舶要素
 */
function createShipPopup(shipFeature: Feature): void {
  if (!mapInstance) return;
  
  const shipData = shipFeature.get('shipData');
  if (!shipData) return;
  
  // 创建弹窗元素
  const popupElement = document.createElement('div');
  popupElement.className = 'ol-popup ship-popup';
  popupElement.innerHTML = createPopupContent(shipData);
  
  // 创建弹窗覆盖物
  const popup = new Overlay({
    element: popupElement,
    positioning: 'bottom-center',
    stopEvent: false,
    offset: [0, -15]
  });
  
  // 添加到弹窗数组
  shipPopupOverlays.push(popup);
  
  // 添加到地图
  mapInstance.addOverlay(popup);

  // 移除重复的点击事件监听器，避免与主要的handleMapClick冲突
  
  // 添加关闭按钮事件
  const closeButton = popupElement.querySelector('.popup-close');
  if (closeButton) {
    closeButton.addEventListener('click', () => {
      popup.setPosition(undefined);
    });
  }
}

/**
 * 创建弹窗内容
 * @param ship 船舶数据
 * @returns HTML内容
 */
function createPopupContent(ship: any): string {
  const name = ship.name || ship.chineseName || ship.englishName || '未知';
  const mmsi = ship.mmsi || '未知';
  const shipType = formatShipType(ship.shipType);
  const sog = typeof ship.sog === 'number' ? `${ship.sog.toFixed(1)}节` : '未知';
  const cog = typeof ship.cog === 'number' ? `${ship.cog.toFixed(1)}°` : '未知';
  const time = ship.time ? new Date(ship.time * 1000).toLocaleString() : '未知';
  
  return `
    <div class="popup-content">
      <h3>${name}</h3>
      <p><strong>MMSI:</strong> ${mmsi}</p>
      <p><strong>船舶类型:</strong> ${shipType}</p>
      <p><strong>位置:</strong> ${ship.lat.toFixed(6)}, ${ship.lon.toFixed(6)}</p>
      <p><strong>航速:</strong> ${sog}</p>
      <p><strong>航向:</strong> ${cog}</p>
      <p><strong>更新时间:</strong> ${time}</p>
      <button class="popup-close">关闭</button>
    </div>
  `;
}

/**
 * 格式化船舶类型
 * @param type 船舶类型代码
 * @returns 船舶类型描述
 */
function formatShipType(type: number | string | undefined): string {
  if (type === undefined) return '未知';
  
  // 转为数字处理
  const typeNum = typeof type === 'string' ? parseInt(type, 10) : type;
  
  if (typeNum >= 60 && typeNum <= 69) {
    return "客船";
  } else if (typeNum >= 70 && typeNum <= 74) {
    return "货船";
  } else if (typeNum === 33) {
    return "作业船";
  } else if (typeNum === 52) {
    return "拖船";
  } else if (typeNum === 30) {
    return "渔船";
  } else {
    return "其他";
  }
}

/**
 * 判断船舶图标类型（基于与电子围栏的距离关系）
 * @param point 船舶坐标 [lon, lat]
 * @returns 图标类型：'red', 'yellow', 'green'
 */
function determineShipIconType(point: [number, number]): 'red' | 'yellow' | 'green' {
  // 如果没有围栏数据，默认使用绿色图标
  if (fencePolygons.length === 0) {
    return 'green';
  }
  
  // 默认使用绿色图标（距离围栏2公里以外）
  let iconType: 'red' | 'yellow' | 'green' = 'green';
  let minDistance = Infinity;
  
  // 遍历所有电子围栏
  for (const polygon of fencePolygons) {
    // 如果点在任一围栏内，使用红色图标
    if (isPointInPolygon(point, polygon.coordinates)) {
      return 'red';
    }
    
    // 计算点到围栏的距离
    const distance = calculateDistanceToPolygon(point, polygon.coordinates);
    
    // 更新最小距离
    if (distance < minDistance) {
      minDistance = distance;
    }
  }
  
  // 根据最小距离决定图标颜色
  if (minDistance <= 2) {
    // console.log(`点 [${point[0]}, ${point[1]}] 距离围栏 ${minDistance.toFixed(2)} 公里，使用黄色图标`);
    iconType = 'yellow';
  } else {
    // console.log(`点 [${point[0]}, ${point[1]}] 距离围栏 ${minDistance.toFixed(2)} 公里，使用绿色图标`);
  }
  
  return iconType;
}

/**
 * 判断点是否在多边形内（使用光线投射算法）
 * @param point 点坐标 [lon, lat]
 * @param polygon 多边形坐标 [[lon1, lat1], [lon2, lat2], ...]
 * @returns 是否在多边形内
 */
function isPointInPolygon(point: [number, number], polygon: number[][]): boolean {
  // 使用光线投射算法判断点是否在多边形内
  const x = point[0], y = point[1];
  let inside = false;
  
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i][0], yi = polygon[i][1];
    const xj = polygon[j][0], yj = polygon[j][1];
    
    const intersect = ((yi > y) !== (yj > y)) &&
        (x < (xj - xi) * (y - yi) / (yj - yi) + xi);
    
    if (intersect) inside = !inside;
  }
  
  return inside;
}

/**
 * 计算点到多边形的最短距离
 * @param point 点坐标 [lon, lat]
 * @param polygon 多边形坐标 [[lon1, lat1], [lon2, lat2], ...]
 * @returns 最短距离（公里）
 */
function calculateDistanceToPolygon(point: [number, number], polygon: number[][]): number {
  // 如果点在多边形内，距离为0
  if (isPointInPolygon(point, polygon)) {
    return 0;
  }

  // 计算点到多边形每条边的最短距离
  let minDistance = Infinity;
  
  for (let i = 0; i < polygon.length; i++) {
    const start = polygon[i];
    const end = polygon[(i + 1) % polygon.length]; // 循环到第一个点，闭合多边形
    
    // 计算点到线段的最短距离
    const distance = calculateDistanceToLineSegment(point, start, end);
    minDistance = Math.min(minDistance, distance);
  }
  
  return minDistance;
}

/**
 * 计算点到线段的最短距离（使用haversine公式计算地理距离）
 * @param point 点坐标 [lon, lat]
 * @param lineStart 线段起点 [lon, lat]
 * @param lineEnd 线段终点 [lon, lat]
 * @returns 最短距离（公里）
 */
function calculateDistanceToLineSegment(
  point: [number, number], 
  lineStart: number[], 
  lineEnd: number[]
): number {
  // 创建点的几何对象
  const pointGeom = new Point(point);
  
  // 创建线段
  const lineString = new LineString([lineStart, lineEnd]);
  
  // 获取线上最近的点
  const closestPoint = lineString.getClosestPoint(point);
  
  // 计算两点之间的距离（米）
  const distanceMeters = getHaversineDistance(
    point[1], point[0],  // 注意：lat, lon 顺序
    closestPoint[1], closestPoint[0]
  );
  
  // 转换为公里
  return distanceMeters / 1000;
}

/**
 * 使用Haversine公式计算两个地理坐标点之间的距离
 * @param lat1 第一个点的纬度
 * @param lon1 第一个点的经度
 * @param lat2 第二个点的纬度
 * @param lon2 第二个点的经度
 * @returns 距离（米）
 */
function getHaversineDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371000; // 地球半径，单位米
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * 将角度转换为弧度
 * @param degrees 角度
 * @returns 弧度
 */
function toRadians(degrees: number): number {
  return degrees * Math.PI / 180;
}

/**
 * 创建船舶图标样式
 * @param iconType 图标类型: 'red', 'yellow', 'green', 'police'
 * @param heading 船舶航向
 * @returns Style 样式对象
 */
function createShipIconStyle(iconType: 'red' | 'yellow' | 'green' | 'police', heading: number | undefined): Style {
  // 检查图标类型是否有效
  if (!ICON_PATHS[iconType]) {
    console.error(`无效的图标类型: ${iconType}, 使用默认绿色图标`);
    iconType = 'green';
  }
  
  const rotation = heading ? (heading * Math.PI / 180) : 0;
  
  // 根据图标类型和缩放级别调整比例
  let baseScale = 0.25; // 默认比例
  
  if (iconType === 'police') {
    baseScale = 0.04; // 执法船图标比例减小为0.05
  }
  
  // 根据缩放级别调整比例
  let zoomAdjust = 1.0;
  if (currentZoom > ZOOM_LEVELS.NORMAL) {
    // 高缩放级别时图标变小
    zoomAdjust = Math.max(0.5, 13 / (currentZoom + 3));
  } else if (currentZoom > ZOOM_LEVELS.DOTS && currentZoom <= ZOOM_LEVELS.NORMAL) {
    // 中等缩放级别保持正常大小
    zoomAdjust = 1.0;
  }
  
  const finalScale = baseScale * zoomAdjust;
  
  const style = new Style({
    image: new Icon({
      src: ICON_PATHS[iconType],
      scale: finalScale,
      rotation: rotation, // 根据航向旋转
      rotateWithView: true, // 随视图旋转
      anchor: [0.5, 0.5], // 锚点设置为中心
      // rotation: iconType === 'police' ? 0 : rotation, // 执法船图标不旋转
      // rotateWithView: iconType !== 'police', // 执法船图标不随视图旋转
      // anchor: [0.5, 0.5],
      anchorXUnits: 'fraction',
      anchorYUnits: 'fraction'
    })
  });
  
  return style;
}

// 修改地图点击处理函数，确保弹出详情窗口
function handleMapClick(event: any): void {
  if (!mapInstance) return;
  
  const clickedFeature = mapInstance.forEachFeatureAtPixel(event.pixel, 
    (feature: Feature | any) => {
      if (feature instanceof Feature) {
        return feature;
      }
      return null;
    });
  
  // 如果点击了船舶
  if (clickedFeature && clickedFeature instanceof Feature && clickedFeature.get('type') === 'ship') {
    // 如果已经选中了这艘船，取消选中
    if (selectedShip === clickedFeature) {
      clearSelection();
      return;
    }
    
    // 选中新的船舶
    selectShip(clickedFeature);
  } else {
    // 点击空白处，清除选中
    clearSelection();
  }
}

// 修改选中船舶的函数
function selectShip(feature: Feature): void {
  if (!selectionLayer) return;

  // 清除之前的选中
  clearSelection();

  // 设置新的选中船舶
  selectedShip = feature;

  // 创建选中框
  const geometry = feature.getGeometry();
  if (geometry instanceof Point) {
    const coordinates = geometry.getCoordinates();

    // 根据缩放级别调整框的大小（调整为更小的尺寸，避免圈中多个船舶）
    let boxSize = 0.009; // 默认大小，从0.008减小到0.003

    // 根据缩放级别调整
    if (currentZoom <= ZOOM_LEVELS.DOTS) {
      boxSize = 0.004; // 点模式下框稍大，从0.01减小到0.004
    } else if ( (ZOOM_LEVELS.content <= currentZoom) && (currentZoom <= ZOOM_LEVELS.NORMAL)) {
      boxSize = 0.0035; 
    }else if (currentZoom > ZOOM_LEVELS.NORMAL) {
      boxSize = 0.002; // 高缩放级别框更小，从0.002减小到0.001
    }

    // 执法船图标稍大，选中框也稍大一点
    const isPoliceShip = feature.get('isPolice') === true;
    if (isPoliceShip) {
      boxSize *= 1.7; // 执法船框稍大，从1.5减小到1.2
    }
    
    // 创建选中框的四条线
    const lines = [
      // 上边
      [[coordinates[0] - boxSize, coordinates[1] + boxSize], [coordinates[0] + boxSize, coordinates[1] + boxSize]],
      // 右边
      [[coordinates[0] + boxSize, coordinates[1] + boxSize], [coordinates[0] + boxSize, coordinates[1] - boxSize]],
      // 下边
      [[coordinates[0] + boxSize, coordinates[1] - boxSize], [coordinates[0] - boxSize, coordinates[1] - boxSize]],
      // 左边
      [[coordinates[0] - boxSize, coordinates[1] - boxSize], [coordinates[0] - boxSize, coordinates[1] + boxSize]]
    ];
    
    // 为每条边创建一个Feature
    lines.forEach(line => {
      const lineFeature = new Feature({
        geometry: new LineString(line)
      });
      
      // 设置红色选择框样式
      lineFeature.setStyle(new Style({
        stroke: new Stroke({
          color: '#FF0000', // 红色选择框
          width: 2,
          lineDash: [5, 5] // 虚线样式
        })
      }));
      
      selectionLayer?.getSource()?.addFeature(lineFeature);
    });
    
    // 触发船舶选中事件，确保显示详情弹窗
    const shipData = feature.get('shipData');
    if (shipData && window.emitter) {
      try {
        // 极简数据传递，只传递必要的显示属性，避免任何拷贝操作
        const minimalShipData = {
          mmsi: shipData.mmsi || '未知',
          name: shipData.name || shipData.chineseName || shipData.englishName || '未知',
          shipType: shipData.shipType,
          lon: shipData.lon,
          lat: shipData.lat,
          sog: shipData.sog,
          cog: shipData.cog,
          heading: shipData.heading,
          status: shipData.status,
          time: shipData.time,
          isPolice: shipData.isPolice || false
        };

        // 立即发送事件，不使用setTimeout避免额外开销
        window.emitter.emit('ship-selected', minimalShipData);
      } catch (error) {
        console.error('触发船舶选中事件失败:', error);
      }
    }
  }
}

// 添加清除选中的函数
function clearSelection(): void {
  if (!selectionLayer) return;
  
  selectionLayer.getSource()?.clear();
  selectedShip = null;
  
  // 触发取消选中事件
  if (window.emitter) {
    window.emitter.emit('ship-unselected');
  }
}

// 在 onBeforeUnmount 中添加事件清理
export function cleanup(): void {
  // 移除事件监听
  if (window.emitter) {
    window.emitter.off('clear-ship-selection', clearSelection);
  }
  
  // 清除选中状态
  clearSelection();
  
  // 清除图层
  if (selectionLayer && mapInstance) {
    mapInstance.removeLayer(selectionLayer);
    selectionLayer = null;
  }
  
  // 清除执法船列表缓存
  clearGaShipListCache();

  // 清理要素池
  cleanupFeaturePool();

  // 清空活跃要素集合
  activeFeatures.clear();

  // 清空要素池
  featurePool.clear();

  // Canvas相关清理代码已移除
}

// 添加SVG图标验证函数
function validateSvgIcons() {
  // 静默验证所有图标是否正确解析
  Object.keys(ICON_PATHS).forEach(key => {
    // 创建测试图片验证图标是否有效
    const img = new Image();
    img.src = ICON_PATHS[key as keyof typeof ICON_PATHS];
  });
}

/**
 * 创建船舶圆点样式（用于低缩放级别）
 * @param isPolice 是否为执法船
 * @returns Style 样式对象
 */
function createShipCircleStyle(isPolice: boolean): Style {
  return new Style({
    image: new CircleStyle({
      radius: 3, // 将圆点大小从5减小到3
      fill: new Fill({
        color: isPolice ? '#FF0000' : '#00FF00' // 执法船红色，普通船绿色
      }),
      stroke: new Stroke({
        color: '#FFFFFF',
        width: 1
      })
    })
  });
}

/**
 * 通过MMSI选中船舶
 * @param mmsi 船舶MMSI号
 */
export function selectShipByMMSI(mmsi: string): boolean {
  if (!shipVectorLayer || !mmsi) return false;

  try {
    const source = shipVectorLayer.getSource();
    if (!source) return false;

    const features = source.getFeatures();

    // 查找匹配MMSI的船舶要素
    const targetFeature = features.find(feature => {
      const shipData = feature.get('shipData');
      return shipData && String(shipData.mmsi) === String(mmsi);
    });

    if (targetFeature) {
      // 选中找到的船舶
      selectShip(targetFeature);

      // 触发船舶选中事件
      if (window.emitter) {
        const shipData = targetFeature.get('shipData');
        window.emitter.emit('ship-selected', shipData);
      }

      return true;
    }

    return false;
  } catch (error) {
    console.error('通过MMSI选中船舶失败:', error);
    return false;
  }
}

// 将船舶图例功能暴露到window对象，供其他模块调用
if (typeof window !== 'undefined') {
  if (!window.shipTracking) {
    window.shipTracking = {};
  }

  window.shipTracking.enableShipLegendMode = enableShipLegendMode;
  window.shipTracking.disableShipLegendMode = disableShipLegendMode;
  window.shipTracking.selectShipByMMSI = selectShipByMMSI;
}