<template>
    <div class="flex-col">
        <div style="padding:15px;">
            <div class="flex-row" style="justify-content:space-between;width:100%;">
                <!-- <el-button size="mini" type="primary" icon="el-icon-plus" style="width:40%;"
                    @click="addGroup()">添加分组</el-button> -->
                <el-button size="small" type="primary" :icon="Plus" style="width:100%;"
                    @click="addShip()">添加船舶</el-button>
            </div>
            <!-- <div class="flex-row" style="justify-content:center;width:100%;margin-top:10px;">
                <el-button size="mini" type="primary" icon="el-icon-upload2" @click="addShip()"
                    style="width:100%;">导出选中船舶检查数据</el-button>
            </div>
            <div style="padding: 15px 0;">
                <el-checkbox v-model="checked">隐藏其他船舶</el-checkbox>
            </div> -->
        </div>
        <div class="row-line"></div>
        <div class="list-items">
            <el-scrollbar ref="scrollformRef" style="height: 77vh;width:100%;">
                <div style="width:100%;display: flex;flex-direction: column;">
                    <div v-for="(item, index) in cateList" :key="index" class="nav-item">
                        <div>{{ item.shipName }}</div>
                        <div style="display: flex;flex-direction: row;align-items: center;">
                            <el-icon style="cursor: pointer;margin-right: 10px;" @click="clickToView(item)"><View /></el-icon>
                            <el-icon style="cursor: pointer;" @click="clickToClear(item)"><Delete /></el-icon>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
        </div>
        <el-dialog title="修改船舶分组" v-model="dialogVisible" :modal="false" custom-class="customDialog">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="分组名称">
                            <el-input v-model="form.id" placeholder="请输入分组名称" type="text" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="父级分组">
                            <el-select v-model="form.id" placeholder="父级分组" clearable style="width: 100%;"
                                popper-class="elDateSeletc">
                                <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="检查日期">
                            <el-date-picker v-model="form.id" align="right" type="date" placeholder="选择日期"
                                popper-class="elDatePicker" style="width: 100%;" value-format="yyyy-MM-dd"
                                :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="审核时间">
                            <el-date-picker v-model="form.id" align="right" type="date" placeholder="选择日期"
                                popper-class="elDatePicker" style="width: 100%;" value-format="yyyy-MM-dd"
                                :picker-options="pickerOptions">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item label="责任方">
                            <el-input v-model="form.id" placeholder="请输入责任方" type="text" maxlength="30" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="分组颜色">
                            <el-color-picker v-model="form.id"></el-color-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="24">
                        <el-form-item label="备注">
                            <el-input type="textarea" :rows="5" placeholder="请输入备注" v-model="form.id"
                                maxlength="200"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" size="small" @click="dialogVisible = false">保 存</el-button>
                    <el-button size="small" @click="dialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <el-dialog v-dialogDrag title="白名单" v-model="dialogVisibleLast" :modal="false" custom-class="customDialog"
            width="500px">
            <el-form ref="formLastRef" :model="formLast" :rules="rulesLst" label-width="130px">
                <!-- <el-form-item label="添加分组">
                    <el-select v-model="formLast.id" placeholder="请选择分组" clearable style="width: 100%;"
                        popper-class="elDateSeletc">
                        <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="数据源">
                    <el-select v-model="formLast.id" placeholder="请选择数据源" clearable style="width: 100%;"
                        popper-class="elDateSeletc">
                        <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input type="textarea" :rows="5" placeholder="请输入备注" v-model="formLast.id"
                        maxlength="200"></el-input>
                </el-form-item> -->
                <el-form-item label="船舶MMSI" prop="mmsi">
                    <el-input type="text" maxlength="50" placeholder="请输入船舶MMSI" v-model="formLast.mmsi">
                        <template #append>
                            <el-button :icon="Search" @click="remoteMethod"></el-button>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item label="船名" prop="shipName">
                    <el-input type="text" maxlength="100" placeholder="请输入船名" v-model="formLast.shipName"></el-input>
                </el-form-item>
                <el-form-item label="船只类型" prop="type">
                    <el-radio v-model="formLast.type" :label="1">自有船只</el-radio>
                    <el-radio v-model="formLast.type" :label="2">工作船只</el-radio>
                    <el-radio v-model="formLast.type" :label="0">其他</el-radio>
                </el-form-item>
                <el-form-item label="白名单生效时间" prop="startTime">
                    <el-date-picker v-model="formLast.startTime" align="right" type="datetime" placeholder="选择日期"
                        popper-class="elDatePicker" style="width: 100%;" value-format="yyyy/MM/dd HH:mm:ss"
                        :picker-options="pickerOptions"></el-date-picker>
                </el-form-item>
                <el-form-item label="白名单失效时间" prop="endTime">
                    <el-date-picker v-model="formLast.endTime" align="right" type="datetime" placeholder="选择日期"
                        popper-class="elDatePicker" style="width: 100%;" value-format="yyyy/MM/dd HH:mm:ss"
                        :picker-options="pickerOptions"></el-date-picker>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <!-- <el-button type="primary" size="mini" @click="dialogVisibleLast = false">查 询</el-button> -->
                    <el-button type="primary" size="small" @click="submit">保 存</el-button>
                    <el-button size="small" @click="dialogVisibleLast = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
  
<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { Plus, View, Delete, Search } from '@element-plus/icons-vue';
import { getWhiteList, clearWhiteList, addWhiteList, getWhiteListDetail, detailByMMSI, editWhiteList } from "@/api/shipGrouping";
import type { FormInstance } from 'element-plus';
import useModalService from '@/utils/modal'; // 假设你有一个这样的服务用来替代 $modal

// 定义类型
interface ShipItem {
    id: number;
    shipName: string;
    mmsi: string;
    type: number;
    startTime: string;
    endTime: string;
    [key: string]: any;
}

interface Option {
    id: string | number;
    name: string;
}

interface QueryParams {
    pageNum: number;
    pageSize: number;
}

// 使用 modal 服务
const modal = useModalService();

// 响应式状态
const checked = ref(false);
const dialogVisible = ref(false);
const dialogVisibleLast = ref(false);
const cateList = ref<ShipItem[]>([]);
const total = ref(0);
const form = reactive<Record<string, any>>({});
const formLast = reactive<ShipItem>({
    id: null,
    shipName: '',
    mmsi: '',
    type: null,
    startTime: '',
    endTime: '',
});
const rules = reactive({});
const queryParmary = reactive<QueryParams>({
    pageNum: 1,
    pageSize: 500,
});
const rulesLst = reactive({
    shipName: [
        { required: true, message: '请输入船名', trigger: 'blur' }
    ],
    mmsi: [
        { required: true, message: '请输入船舶MMSI', trigger: 'blur' }
    ],
    type: [
        { required: true, message: '请选择船只类型', trigger: 'blur' }
    ],
    startTime: [
        { required: true, message: '请选择白名单生效时间', trigger: 'blur' }
    ],
    endTime: [
        { required: true, message: '请选择白名单失效时间', trigger: 'blur' }
    ],
});
const defaultProps = reactive({
    children: "children",
    label: "title"
});
const pickerOptions = reactive({
    disabledDate(time: Date) {
        return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
    },
});
const options = ref<Option[]>([]);

// DOM 引用
const scrollformRef = ref<HTMLElement>();
const formRef = ref<FormInstance>();
const formLastRef = ref<FormInstance>();

// 方法
const remoteMethod = () => {
    if (formLast.mmsi !== '') {
        const data = {
            mmsi: formLast.mmsi,
        };
        detailByMMSI(data).then((response) => {
            if (response.data) {
                formLast.shipName = response.data.localName ? response.data.localName : response.data.shipNameEn;
            } else {
                modal.msgWarning("未查询到该船舶信息");
            }
        });
    } else {
        modal.msgWarning("请输入船舶MMSI");
    }
};

const clickToView = (row: ShipItem) => {
    rest();
    dialogVisibleLast.value = true;
    getWhiteListDetail(row.mmsi).then(response => {
        Object.assign(formLast, response.data);
    });
};

const clickToClear = (row: ShipItem) => {
    modal.confirm('是否确认删除').then(() => {
        return clearWhiteList(row.id);
    }).then(() => {
        queryParmary.pageNum = 1;
        getList();
        modal.msgSuccess("删除成功");
    }).catch(() => { });
};

const getList = () => {
    const data = {
        pageNum: queryParmary.pageNum,
        pageSize: queryParmary.pageSize,
    };
    getWhiteList(data).then(response => {
        total.value = response.total;
        cateList.value = response.rows;
    });
};

const getMoreList = () => {
    const data = {
        pageNum: queryParmary.pageNum,
        pageSize: queryParmary.pageSize,
    };
    getWhiteList(data).then(response => {
        total.value = response.total;
        cateList.value = [...cateList.value, ...response.rows];
    });
};

const addGroup = () => {
    rest();
    dialogVisible.value = true;
};

const addShip = () => {
    rest();
    dialogVisibleLast.value = true;
};

const rest = () => {
    Object.assign(formLast, {
        id: null,
        shipName: '',
        mmsi: '',
        type: null,
        startTime: '',
        endTime: '',
    });
    nextTick(() => {
        formLastRef.value?.resetFields();
    });
};

const submit = () => {
    formLastRef.value?.validate(valid => {
        if (!valid) return;
        
        if (new Date(formLast.startTime) > new Date(formLast.endTime)) {
            modal.msgWarning("生效时间不能大于失效时间");
            return;
        }
        
        const data = {
            id: formLast.id,
            shipName: formLast.shipName,
            mmsi: formLast.mmsi,
            type: formLast.type,
            startTime: formLast.startTime,
            endTime: formLast.endTime,
        };
        
        const request = formLast.id ? editWhiteList(data) : addWhiteList(data);
        
        request.then(() => {
            modal.msgSuccess("操作成功");
            queryParmary.pageNum = 1;
            getList();
            dialogVisibleLast.value = false;
        });
    });
};

// 生命周期钩子
onMounted(() => {
    if (scrollformRef.value) {
        const scrollbar = scrollformRef.value as any;
        scrollbar.handleScroll = () => {
            const wrap = scrollbar.wrap;
            scrollbar.moveY = wrap.scrollTop * 100 / wrap.clientHeight;
            scrollbar.moveX = wrap.scrollLeft * 100 / wrap.clientWidth;
            const poor = wrap.scrollHeight - wrap.clientHeight;
            if (poor === parseInt(wrap.scrollTop.toString()) || poor === Math.ceil(wrap.scrollTop) || poor === Math.floor(wrap.scrollTop)) {
                if (queryParmary.pageNum * queryParmary.pageSize < total.value) {
                    queryParmary.pageNum++;
                    getMoreList();
                }
            }
        };
    }
    getList();
});
</script>
  
<style scoped>
.flex-row {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-btn {
    color: white;
    width: 45%;
    height: 36px;
    cursor: pointer;
    line-height: 36px;
    font-size: 14px;
    border: none;
    border-radius: 3px;
    text-align: center;
    background: rgb(103, 113, 183);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.row-line {
    width: 100%;
    height: 1px;
    background-color: #e4e4e4;
    margin: 15px 0;
}

.nav-item {
    width: 100%;
    height: 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    cursor: pointer;
}

.list-items {
    width: 100%;
    /* overflow-y: auto; */
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 10px;
}
</style>
<style lang="less">
.elDatePicker.el-picker-panel {
    color: #fff; //设置当前面板的月份的字体为白色，记为1
    background: #3c4167; //定义整体面板的颜色


    .el-picker-panel__sidebar {
        color: #fff; //设置当前面板的月份的字体为白色，记为1
        background: #3c4167; //定义整体面板的颜色
    }

    .el-picker-panel__shortcut {
        color: #ffffff;
    }

    .el-picker-panel__shortcut:hover {
        color: #1890ff;
    }

    .el-picker-panel__icon-btn {
        //设置年份月份调节按钮颜色，记为2
        color: #ffffff;
    }

    .el-date-picker__header-label {
        //设置年月显示颜色，记为3
        color: #ffffff;
    }

    .el-date-table th {
        //设置星期颜色，记为4
        color: #ffffff;
    }

}

.elDateSeletc.el-select-dropdown {
    color: #fff; //设置当前面板的月份的字体为白色，记为1
    background: #3c4167; //定义整体面板的颜色

    .el-select-dropdown__item {
        color: #fff;
        background: #3c4167;
    }

    .el-select-dropdown__item:hover {
        color: #1890ff;
        background: #3c4167;
    }

    .selected {
        color: #1890ff;
        background: #3c4167;
    }

}
</style>