<template>
  <div style="position: relative; " class="box">
    <div class="content" :class="isdrawer ? 'drawer' : 'open'">
      <el-tabs
        :tab-position="tabPosition"
        type="border-card"
        :stretch="true"
        v-model="activeName"
        class="eTabs"
        @tab-click="handleClick"
      >
        <el-tab-pane
          v-for="(item, index) in funList"
          :key="index"
          :name="item.Popup"
        >
          <template #label>
            <div
              :class="activeName == item.Popup ? 'box-center' : 'box-center-no'"
              style="cursor: pointer"
            >
              <span
                class="iconfont icon-yichangshujuchaxun"
                style="margin-right: 0"
              ></span>
              <span style="margin-top: 5px">{{ item.name }}</span>
            </div>
          </template>
          <div style="display: flex; flex-direction: column">
            <div class="right-title">
              <div
                style="display: flex; flex-direction: row; align-items: center"
              >
                <el-icon style="font-size: 16px; margin-right: 10px"><Star /></el-icon>
                <div style="font-size: 14px">{{ item.name }}</div>
              </div>
              <el-icon class="del-icon" @click="closeList(index)"><Close /></el-icon>
            </div>
            <!-- <component :is="item.Popup"></component> -->
            <div>
              <!-- head -->
              <div class="history-head">
                <el-form ref="formRef" :model="form" label-width="80px">
                  <el-form-item>
                    <div>
                      <el-radio v-model="radio" label="1">船舶</el-radio>
                      <el-radio v-model="radio" label="2">区域</el-radio>
                    </div>
                  </el-form-item>

                  <div></div>
                  <el-form-item label="开始">
                    <el-date-picker
                      v-model="startTime"
                      type="datetime"
                      placeholder="选择日期时间"
                      style="width: 100%"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label="结束">
                    <el-date-picker
                      v-model="endTime"
                      type="datetime"
                      placeholder="选择日期时间"
                      style="width: 100%"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <div>
                    <el-form-item>
                      <div style="display: flex">
                        <el-button size="small" type="primary"
                          >选择船舶</el-button
                        >
                        <el-button size="small" type="primary"
                          >开始回放</el-button
                        >
                      </div>
                    </el-form-item>
                  </div>
                </el-form>
                <div>
                  <el-icon><Document /></el-icon>
                  <span>按船舶回放</span>
                </div>
              </div>
              <!-- body -->
              <div class="history-body"></div>
              <!-- footer -->
              <div class="history-footer">
                <el-button type="primary">清除</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="btn" @click="changedrawer">
        <el-icon v-if="!isdrawer"><ArrowRight /></el-icon>
        <el-icon v-else><ArrowLeft /></el-icon>
      </div>
    </div>
  </div>
</template>
    
<script setup lang="ts">
import { ref, reactive, watch, onMounted, defineProps, defineEmits } from 'vue';
import { Star, Close, Document, ArrowRight, ArrowLeft } from '@element-plus/icons-vue';
import shipList from "@/views/monitor/shipList";
import electronicFence from "@/views/riskWarn/electronicFence";
import systemSetting from "@/views/riskWarn/systemSetting";
import videoList from "@/views/video/videoList";
import flllow from "@/views/monitor/follow";
import realTime from "@/views/monitor/realTime";
import playback from "@/views/video/playback";
import leftContent from "@/views/left-content";
import warningRules from "@/views/windowPages/warningRules";
import type { TabsPaneContext, FormInstance } from 'element-plus';

// 定义类型
interface TabItem {
  name: string;
  Popup: string;
}

interface FormData {
  name: string;
  region: string;
  date1: string;
  date2: string;
  delivery: boolean;
  type: string[];
  resource: string;
  desc: string;
}

// 定义 props
const props = defineProps<{
  map: any;
  list: any[];
  Popup: string;
}>();

// 定义 emits
const emit = defineEmits(['closeCollection']);

// 响应式数据
const activeName = ref('');
const funList = ref<TabItem[]>([]);
const isdrawer = ref(false);
const tabPosition = ref('left');
const radio = ref('1');
const startTime = ref('');
const endTime = ref('');
const formRef = ref<FormInstance>();

const form = reactive<FormData>({
  name: '',
  region: '',
  date1: '',
  date2: '',
  delivery: false,
  type: [],
  resource: '',
  desc: '',
});

// 监听属性变化
watch(() => props.Popup, (val) => {
  if (vaileRepeat(val)) {
    getTabs(val);
  }
});

// 方法
// 获取标签页
const getTabs = (val: string) => {
  const item: TabItem = {
    name: '',
    Popup: '',
  };
  
  if (val === 'historyTrackPlayback') {
    item.name = '历史航迹回放';
  }
  
  item.Popup = val;
  funList.value.push(item);
  activeName.value = val.toString();
};

// 查重
const vaileRepeat = (val: string): boolean => {
  const arr: string[] = [];
  funList.value.forEach((e) => {
    arr.push(e.Popup);
  });
  
  if (arr.length > 0) {
    if (arr.indexOf(val) !== -1) {
      activeName.value = val.toString();
      return false;
    }
    return true;
  } else {
    return true;
  }
};

// 关闭
const closeList = (num: number) => {
  funList.value.splice(num, 1);
  if (funList.value.length === 0) {
    emit('closeCollection');
  } else {
    activeName.value = funList.value[0].Popup.toString();
  }
};

// 切换打开/关闭抽屉
const changedrawer = () => {
  isdrawer.value = !isdrawer.value;
};

// 切换Tabs 标签页
const handleClick = (tab: TabsPaneContext, event: Event) => {
  // console.log(tab, event);
};

// 生命周期钩子
onMounted(() => {
  getTabs(props.Popup);
});
</script>
    
<style scoped>
.box-center {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.box-center::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: rgb(134, 142, 208);
}

.box-center-no {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

:deep(.el-tabs .el-tabs__nav-scroll) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 50px;
  background-color: rgb(42 47 83);
  color: #ffffff;
}

:deep(.el-tabs__item) {
  color: #ffffff !important;
  height: 150px;
  display: flex;
  writing-mode: vertical-rl;
  padding: 20px 5px 20px 5px;
  flex-wrap: nowrap;
  cursor: default;
  user-select: none;
  color: #b0b0b0;
  width: 100%;
  position: relative;
  background: rgb(42, 47, 83);
  /* 
  
  flex: 0 0 auto; */
}

.icon-anchor-full {
  font-size: 5px;
}

.box {
  background-color: #3c4167;
}

.eTabs {
  width: 350px;
  height: 100%;
}

.content {
  /* width: 350px; */
  height: 100%;
  /* background: rgba(0, 0, 0, 0.5); */
  position: fixed;
  right: 0;
  top: 0px;
  z-index: 1000;
  display: flex;
  flex-direction: row;
  /* padding-right: 40px; */
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.content-txt1 {
  font-size: 14px;
  color: #409eff;
}

.line {
  width: 100%;
  height: 1px;
  margin: 10px auto;
  background: #d5d5d5;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  padding-right: 8px;
  color: #4386c6;
}

.custom-tree-node-label {
  display: block;
  width: 125px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.shipNum {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.del-icon {
  cursor: pointer;
  font-size: 18px;
  color: #999;
}

.del-icon:hover {
  color: #f56c6c;
}

.history-head {
  padding: 10px;
}

.history-body {
  padding: 10px;
  min-height: 200px;
}

.history-footer {
  padding: 10px;
  display: flex;
  justify-content: flex-end;
}

.btn {
  position: absolute;
  right: 350px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #3c4167;
  color: #fff;
  padding: 10px 5px;
  cursor: pointer;
  border-radius: 4px 0 0 4px;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
}

.drawer {
  right: -350px;
}

.open {
  right: 0;
}
</style>

<style scoped src="@/assets/datePark.css"></style>
<style scoped>
::v-deep .el-tabs--border-card {
  border: none;
}

.del-icon {
  font-size: 20px;
  color: #d9d9d9;
  cursor: pointer;
}

.del-icon:hover {
  color: #fff;
}

.right-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 18px;
  height: 50px;
  margin-bottom: 15px;
  padding: 0 15px;
  color: #d9d9d9;
}

::v-deep .el-tabs--left .el-tabs__header.is-left {
  margin-right: 0;
}

::v-deep .el-tabs__content {
  padding: 0;
}

::v-deep .el-tabs--left.el-tabs--border-card .el-tabs__header.is-left {
  border-right: none;
}

::v-deep.el-tabs--left.el-tabs--border-card .el-tabs__item.is-left.is-active {
  border: none;
  padding: 0;
  margin: 0;
}

::v-deep .el-tabs--left.el-tabs--border-card .el-tabs__item.is-left {
  border: none;
  padding: 0;
  margin: 0;
}
.history-head {
  /* border: 1px solid green; */
  padding: 10px;
}
.history-body {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
  flex: 1;
  height: 600px;
}
.history-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  text-align: right !important;
  padding: 5px !important;
}
::v-deep.el-form-item__content {
  margin-left: 0px !important;
}
</style>