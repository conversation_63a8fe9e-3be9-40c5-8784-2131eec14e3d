<template>
  <div>
    <div id="git-user-binding">
      <h4 class="provider-desc">你可以修改/绑定的第三方账号：</h4>
      <div id="authlist" class="user-bind">
        <!-- <a class="third-app" href="#" title="使用 微信 账号授权登录" @click="authUrl('wechat')">
          <div class="git-other-login-icon">
            <svg-icon icon-class="wechat" />
          </div>
          <span class="app-name">WeiXin</span>
        </a>
        <a class="third-app" href="#" title="使用 MaxKey 账号授权登录" @click="authUrl('maxkey')">
          <div class="git-other-login-icon">
            <svg-icon icon-class="maxkey" />
          </div>
          <span class="app-name">MaxKey</span>
        </a>
        <a class="third-app" href="#" title="使用 TopIam 账号授权登录" @click="authUrl('topiam')">
          <div class="git-other-login-icon">
            <svg-icon icon-class="topiam" />
          </div>
          <span class="app-name">TopIam</span>
        </a>
        <a class="third-app" href="#" title="使用 Gitee 账号授权登录" @click="authUrl('gitee')">
          <div class="git-other-login-icon">
            <svg-icon icon-class="gitee" />
          </div>
          <span class="app-name">Gitee</span>
        </a>
        <a class="third-app" href="#" title="使用 GitHub 账号授权登录" @click="authUrl('github')">
          <div class="git-other-login-icon">
            <svg-icon icon-class="github" />
          </div>
          <span class="app-name">Github</span>
        </a> -->
        <a class="third-app" href="#" title="使用 浙政钉 账号授权登录" @click="showDingTalkQR">
          <div class="git-other-login-icon">
            <svg t="1749003362841" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4412" width="32" height="32">
              <path d="M577.30755 17.490362l330.83835 190.587859A131.086893 131.086893 0 0 1 973.801707 321.674378v380.651369a131.086893 131.086893 0 0 1-65.655807 113.596157l-330.83835 190.575374a131.086893 131.086893 0 0 1-130.862173 0l-330.838351-190.575374A131.086893 131.086893 0 0 1 49.95122 702.325747v-380.651369a131.086893 131.086893 0 0 1 65.655806-113.596157l330.838351-190.575374a131.086893 131.086893 0 0 1 130.862173 0z m-58.851773 198.852576c-133.7336 0-257.554535 39.16377-358.778585 105.743427C152.448686 489.528024 216.64381 664.435393 350.427348 797.931788a663.986327 663.986327 0 0 0 155.107006 115.356466c65.006615-31.211165 126.430188-74.095306 180.962336-128.502609 130.212981-129.913354 194.507981-299.077869 191.162144-462.424622-101.286473-66.75444-225.269705-106.018086-359.203057-106.018085z m-200.125991 79.238906s128.390249 58.81432 216.193499 91.024241c87.815734 32.334767 164.14576 48.701902 154.283031 90.412504-0.848944 3.533104-2.908881 8.764095-5.830246 15.043781h0.062423l-0.374534 0.599255c-16.966389 36.417187-61.236306 107.765911-61.236306 107.76591s-0.062422-0.174783-0.24969-0.424471l-12.946391 22.596883h62.397361l-119.076837 158.777439 27.066323-108.065538h-49.063952l17.016327-71.473568a710.353633 710.353633 0 0 0-49.438485 14.119931s-26.154956 15.343409-75.28133-29.525762c0 0-33.146257-29.288557-13.932665-36.59197 8.152356-3.108632 39.650664-7.066208 64.457299-10.412045 33.508307-4.569315 54.132645-6.941363 54.132644-6.941363s-103.271503 1.523105-127.77851-2.309626c-24.507007-3.845216-55.580843-44.881655-62.210094-80.924309 0 0-10.212293-19.787879 22.022598-10.412045 32.222407 9.375834 165.76874 36.47961 165.76874 36.47961S350.6271 421.92464 339.091453 408.878373c-11.560616-13.033783-33.995201-71.111519-31.08632-106.792123 0 0 1.2859-8.88894 10.349622-6.516891z" fill="#4DADFF" p-id="4413"></path>
            </svg>
          </div>
          <span class="app-name">浙政钉</span>
        </a>
      </div>
    </div>

    <!-- QR Code Dialog -->
    <el-dialog
      v-model="qrDialogVisible"
      title="浙政钉扫码绑定"
      width="380px"
      class="qr-dialog"
      :close-on-click-modal="false"
    >
      <div class="qr-container">
        <div v-if="qrCodeLoading" class="qr-loading">
          <el-icon class="qr-loading-icon"><Loading /></el-icon>
          <p>正在加载二维码...</p>
        </div>
        <div v-else-if="qrCodeError" class="qr-error">
          <el-icon class="qr-error-icon"><CircleClose /></el-icon>
          <p>二维码加载失败</p>
          <el-button type="primary" size="small" @click="initQrCode">重试</el-button>
        </div>
        <iframe
          v-else
          ref="qrIframe"
          :src="qrLoginUrl"
          class="qr-iframe"
          frameborder="0"
          scrolling="no"
        ></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { authUnlock, authBinding } from '@/api/system/social/auth';
import { propTypes } from '@/utils/propTypes';
import useUserStore from '@/store/modules/user';
import { CircleClose, Loading } from '@element-plus/icons-vue';
import { updateUserProfile } from '@/api/system/user';


const userStore = useUserStore();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const props = defineProps({
  auths: propTypes.any.isRequired
});
const auths = computed(() => props.auths);
const emits = defineEmits(['refresh']);

const qrDialogVisible = ref(false);
const qrCodeLoading = ref(false);
const qrCodeError = ref(false);
const qrIframe = ref<HTMLIFrameElement | null>(null);
const qrLoginUrl = ref(import.meta.env.VITE_APP_DINGDING_API);

const unlockAuth = (row: any) => {
  ElMessageBox.confirm('您确定要解除"' + row.source + '"的账号绑定吗？')
    .then(() => {
      return authUnlock(row.id);
    })
    .then((res: any) => {
      if (res.code === 200) {
        proxy?.$modal.msgSuccess('解绑成功');
        proxy?.$tab.refreshPage();
      } else {
        proxy?.$modal.msgError(res.msg);
      }
    })
    .catch(() => {});
};

const authUrl = (source: string) => {
  authBinding(source, useUserStore().tenantId).then((res: any) => {
    if (res.code === 200) {
      window.location.href = res.data;
    } else {
      proxy?.$modal.msgError(res.msg);
    }
  });
};

// 显示浙政钉二维码
const showDingTalkQR = () => {
  qrDialogVisible.value = true;
  initQrCode();
};

// 初始化二维码
const initQrCode = () => {
  qrCodeLoading.value = true;
  qrCodeError.value = false;

  setTimeout(() => {
    try {
      qrCodeLoading.value = false;

      // 不在这里重复添加监听器，监听器已在onMounted中添加
    } catch (error) {
      console.error('Failed to initialize QR code:', error);
      qrCodeLoading.value = false;
      qrCodeError.value = true;
    }
  }, 1500);
};

// 添加处理状态标记，防止重复处理
const isProcessingBinding = ref(false);

// 处理二维码扫码结果消息
const handleQrCodeMessage = async (event: MessageEvent) => {
  // 验证消息来源和对话框状态
  if (!qrDialogVisible.value || !event.origin || isProcessingBinding.value) {
    return;
  }

  const data = event.data;

  if (data.code) {
    console.log('Received message from QR code iframe:', data.code);
    isProcessingBinding.value = true;
    qrDialogVisible.value = false;

    try {
      // 获取钉钉用户信息
      await userStore.getDingTalkUserInfo(data.code);

      // 获取用户信息成功，更新用户资料
      proxy?.$modal.msgSuccess('绑定成功');
      emits('refresh');

    } catch (error) {
      console.error('Error processing QR code result:', error);
      proxy?.$modal.msgError('绑定失败，请重试');
    } finally {
      isProcessingBinding.value = false;
    }
  }
};

// 组件卸载时清除事件监听器
onUnmounted(() => {
  window.removeEventListener('message', handleQrCodeMessage);
});

// 组件挂载时设置事件监听
onMounted(() => {
  // 增强iframe监听 - 只在绑定页面添加一次
  if (window.addEventListener) {
    window.addEventListener('message', handleQrCodeMessage, false);
  }
});
</script>

<style type="text/css">
.user-bind .third-app {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-width: 80px;
  float: left;
}

.user-bind {
  font-size: 1rem;
  text-align: start;
  height: 50px;
  margin-top: 10px;
}

.git-other-login-icon > img {
  height: 32px;
}

a {
  text-decoration: none;
  cursor: pointer;
  color: #005980;
}

.provider-desc {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Liberation Sans', 'PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', 'Wenquanyi Micro Hei', 'WenQuanYi Zen Hei', 'ST Heiti', SimHei, SimSun,
    'WenQuanYi Zen Hei Sharp', sans-serif;
  font-size: 1.071rem;
}

td > img {
  height: 20px;
  width: 20px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 5px;
}

/* QR Code Dialog Styles */
.qr-dialog :deep(.el-dialog__body) {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.qr-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.qr-iframe {
  width: 100%;
  height: 320px;
  border-radius: 8px;
  border: none;
  overflow: hidden;
}

.qr-loading, .qr-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 320px;
  background-color: rgba(248, 248, 248, 0.5);
  border-radius: 8px;
  padding: 20px;
}

.qr-loading p, .qr-error p {
  margin-top: 15px;
  color: rgba(0, 0, 0, 0.7);
  text-align: center;
}

.qr-loading-icon {
  font-size: 40px;
  color: #2c5282;
  animation: spin 1.5s linear infinite;
}

.qr-error-icon {
  font-size: 40px;
  color: #e53e3e;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
