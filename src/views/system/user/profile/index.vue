<template>
  <div class="p-2">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <template #header>
            <div class="clearfix">
              <span>个人信息</span>
            </div>
          </template>
          <div>
            <div class="text-center">
              <userAvatar @refresh="getUser" />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="user" />用户名称
                <div class="pull-right">{{ state.user.userName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone" />手机号码
                <div class="pull-right">{{ state.user.phonenumber }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email" />用户邮箱
                <div class="pull-right">{{ state.user.email }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />所属部门
                <div v-if="state.user.deptName" class="pull-right">{{ state.user.deptName }} / {{ state.postGroup }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="peoples" />所属角色
                <div class="pull-right">{{ state.roleGroup }}</div>
              </li>
              <li class="list-group-item">
                <svg t="1749003362841" viewBox="0 0 1150 800" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4412" width="16" height="16"><path d="M577.30755 17.490362l330.83835 190.587859A131.086893 131.086893 0 0 1 973.801707 321.674378v380.651369a131.086893 131.086893 0 0 1-65.655807 113.596157l-330.83835 190.575374a131.086893 131.086893 0 0 1-130.862173 0l-330.838351-190.575374A131.086893 131.086893 0 0 1 49.95122 702.325747v-380.651369a131.086893 131.086893 0 0 1 65.655806-113.596157l330.838351-190.575374a131.086893 131.086893 0 0 1 130.862173 0z m-58.851773 198.852576c-133.7336 0-257.554535 39.16377-358.778585 105.743427C152.448686 489.528024 216.64381 664.435393 350.427348 797.931788a663.986327 663.986327 0 0 0 155.107006 115.356466c65.006615-31.211165 126.430188-74.095306 180.962336-128.502609 130.212981-129.913354 194.507981-299.077869 191.162144-462.424622-101.286473-66.75444-225.269705-106.018086-359.203057-106.018085z m-200.125991 79.238906s128.390249 58.81432 216.193499 91.024241c87.815734 32.334767 164.14576 48.701902 154.283031 90.412504-0.848944 3.533104-2.908881 8.764095-5.830246 15.043781h0.062423l-0.374534 0.599255c-16.966389 36.417187-61.236306 107.765911-61.236306 107.76591s-0.062422-0.174783-0.24969-0.424471l-12.946391 22.596883h62.397361l-119.076837 158.777439 27.066323-108.065538h-49.063952l17.016327-71.473568a710.353633 710.353633 0 0 0-49.438485 14.119931s-26.154956 15.343409-75.28133-29.525762c0 0-33.146257-29.288557-13.932665-36.59197 8.152356-3.108632 39.650664-7.066208 64.457299-10.412045 33.508307-4.569315 54.132645-6.941363 54.132644-6.941363s-103.271503 1.523105-127.77851-2.309626c-24.507007-3.845216-55.580843-44.881655-62.210094-80.924309 0 0-10.212293-19.787879 22.022598-10.412045 32.222407 9.375834 165.76874 36.47961 165.76874 36.47961S350.6271 421.92464 339.091453 408.878373c-11.560616-13.033783-33.995201-71.111519-31.08632-106.792123 0 0 1.2859-8.88894 10.349622-6.516891z" fill="#4DADFF" p-id="4413"></path></svg>浙政钉绑定
                <div class="pull-right">
                  <el-tag v-if="state.user.ddTenantId && state.user.ddAccountId" type="success" size="small">已绑定</el-tag>
                  <el-tag v-else type="info" size="small">未绑定</el-tag>
                </div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />创建日期
                <div class="pull-right">{{ state.user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <template #header>
            <div class="clearfix">
              <span>基本资料</span>
            </div>
          </template>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本资料" name="userinfo">
              <userInfo :user="userForm" @refresh="getUser" />
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd @refresh="getUser" />
            </el-tab-pane>
            <el-tab-pane label="第三方应用" name="thirdParty">
              <thirdParty :auths="state.auths" @refresh="getUser" />
            </el-tab-pane>
            <el-tab-pane label="在线设备" name="onlineDevice">
              <onlineDevice :devices="state.devices" />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Profile" lang="ts">
import UserAvatar from './userAvatar.vue';
import UserInfo from './userInfo.vue';
import ResetPwd from './resetPwd.vue';
import ThirdParty from './thirdParty.vue';
import OnlineDevice from './onlineDevice.vue';
import { getAuthList } from '@/api/system/social/auth';
import { getUserProfile } from '@/api/system/user';
import { getOnline } from '@/api/monitor/online';
import { UserVO } from '@/api/system/user/types';

const activeTab = ref('userinfo');
interface State {
  user: Partial<UserVO>;
  roleGroup: string;
  postGroup: string;
  auths: any;
  devices: any;
}
const state = ref<State>({
  user: {},
  roleGroup: '',
  postGroup: '',
  auths: [],
  devices: []
});

const userForm = ref({});

const getUser = async () => {
  try {
    const res = await getUserProfile();
    state.value.user = res.data.user;
    // 深拷贝用户数据以避免引用问题
    userForm.value = JSON.parse(JSON.stringify(res.data.user));
    state.value.roleGroup = res.data.roleGroup;
    state.value.postGroup = res.data.postGroup;
    
    // 手动触发视图更新
    state.value = { ...state.value };
    
    console.log('用户信息已更新', state.value.user);
  } catch (error) {
    console.error('获取用户信息失败', error);
  }
};

const getAuths = async () => {
  const res = await getAuthList();
  state.value.auths = res.data;
};
const getOnlines = async () => {
  const res = await getOnline();
  state.value.devices = res.rows;
};

onMounted(() => {
  getUser();
  getAuths();
  getOnlines();
});
</script>
